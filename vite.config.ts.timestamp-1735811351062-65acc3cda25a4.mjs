// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/vite@4.4.11_@types+node@20.8.4_sass@1.69.2/node_modules/vite/dist/node/index.js";
import vue from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/@vitejs+plugin-vue@4.4.0_vite@4.4.11_@types+node@20.8.4_sass@1.69.2__vue@3.4.32_typescript@5.2.2_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import * as path from "path";
import {
  createStyleImportPlugin,
  ElementPlusResolve
} from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/vite-plugin-style-import@2.0.0_vite@4.4.11_@types+node@20.8.4_sass@1.69.2_/node_modules/vite-plugin-style-import/dist/index.mjs";
import AutoImport from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/unplugin-auto-import@0.16.6_@vueuse+core@10.5.0_vue@3.4.32_typescript@5.2.2___rollup@3.29.4/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/unplugin-vue-components@0.25.2_@babel+parser@7.24.8_rollup@3.29.4_vue@3.4.32_typescript@5.2.2_/node_modules/unplugin-vue-components/dist/vite.mjs";
import { ElementPlusResolver } from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/unplugin-vue-components@0.25.2_@babel+parser@7.24.8_rollup@3.29.4_vue@3.4.32_typescript@5.2.2_/node_modules/unplugin-vue-components/dist/resolvers.mjs";

// config/unocss.ts
import { presetUno, presetAttributify, presetIcons } from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/unocss@0.56.5_postcss@8.4.31_rollup@3.29.4_vite@4.4.11_@types+node@20.8.4_sass@1.69.2_/node_modules/unocss/dist/index.mjs";
import Unocss from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/unocss@0.56.5_postcss@8.4.31_rollup@3.29.4_vite@4.4.11_@types+node@20.8.4_sass@1.69.2_/node_modules/unocss/dist/vite.mjs";
import transformerAttributifyJsx from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/@unocss+transformer-attributify-jsx@0.56.5/node_modules/@unocss/transformer-attributify-jsx/dist/index.mjs";
var unocss_default = () => Unocss({
  presets: [presetUno(), presetAttributify(), presetIcons()],
  transformers: [transformerAttributifyJsx()],
  rules: [
    [
      "w-f",
      {
        width: "100%"
      }
    ],
    [
      "h-f",
      {
        height: "100%"
      }
    ],
    [
      "wh-100",
      {
        width: "100%",
        height: "100%"
      }
    ],
    [
      "absolute-center",
      {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: `translate(-50%, -50%)`
      }
    ],
    [
      "x-center",
      {
        position: "absolute",
        left: "50%",
        transform: `translateX(-50%)`
      }
    ],
    [
      "y-center",
      {
        position: "absolute",
        top: "50%",
        transform: `translateY(-50%)`
      }
    ],
    [
      "flex-center",
      {
        display: "flex",
        "justify-content": "center",
        "align-items": "center"
      }
    ],
    [
      "text-primary",
      {
        color: "#29CCA0"
      }
    ],
    [
      "family",
      {
        "font-family": "PingFangSC-Regular, PingFang SC, sans-serif"
      }
    ],
    [
      "family-medium",
      {
        "font-family": "PingFangSC-Medium, PingFang SC, sans-serif"
      }
    ],
    [
      "ellipsis",
      {
        overflow: "hidden",
        "text-overflow": "ellipsis",
        "white-space": "nowrap"
      }
    ],
    [
      "circle",
      {
        "border-radius": "50%"
      }
    ]
  ]
});

// vite.config.ts
import { loadEnv } from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/vite@4.4.11_@types+node@20.8.4_sass@1.69.2/node_modules/vite/dist/node/index.js";
import { vitePluginFakeServer } from "file:///C:/Users/<USER>/Desktop/project/pv-om-web/node_modules/.pnpm/vite-plugin-fake-server@2.1.1/node_modules/vite-plugin-fake-server/dist/index.mjs";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Desktop\\project\\pv-om-web";
var pathSrc = path.resolve(__vite_injected_original_dirname, "src");
var vite_config_default = ({ mode }) => {
  return defineConfig({
    base: loadEnv(mode, process.cwd()).VITE_APP_BASE_PATH,
    resolve: {
      //设置别名
      alias: {
        "@": pathSrc
      }
    },
    plugins: [
      vue(),
      unocss_default(),
      createStyleImportPlugin({
        resolves: [ElementPlusResolve()],
        libs: [
          {
            libraryName: "element-plus",
            esModule: true,
            resolveStyle: (name) => {
              return `element-plus/theme-chalk/${name}.css`;
            },
            ensureStyleFile: true
          }
        ]
      }),
      AutoImport({
        include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.md$/],
        imports: [
          "vue",
          "vue-router",
          {
            "@vueuse/core": ["useMouse", ["useFetch", "useMyFetch"]],
            axios: [["default", "axios"]],
            "await-to-js": ["to"]
          }
        ],
        eslintrc: {
          enabled: true,
          filepath: "./.eslintrc-auto-import.json"
        },
        resolvers: [ElementPlusResolver()],
        dts: path.resolve("types", "auto-imports.d.ts")
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        dts: path.resolve("types", "components.d.ts")
      }),
      vitePluginFakeServer({
        enableDev: false
      })
    ],
    server: {
      host: true,
      port: 8081,
      open: true,
      proxy: {
        "/dev-api": {
          // target: 'http://*************:8065', // 张孟羽电脑
          target: "https://tsyhtest.spic-iset.com/pvom",
          // 测试环境
          // target: 'https://tsyh.spic.com.cn/pvom', // 生产环境
          changeOrigin: true,
          headers: {
            referer: "https://tsyhtest.spic-iset.com"
          },
          rewrite: (path2) => path2.replace(/^\/dev-api/, "")
        }
      },
      hmr: { overlay: false }
    },
    define: {
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: "false"
    },
    build: { chunkSizeWarningLimit: 3e3 }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
