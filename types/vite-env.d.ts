/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<any, any, any>
  export default component
}

declare module '*.png'
declare module 'element-plus/dist/locale/zh-cn.mjs'
declare module 'vue3-count-to'

declare module '@iset/icon'
declare module '@iset/element'
declare module '@iset/chart'
declare module '@iset/auth-interceptor'
declare module 'qs'
declare module 'js-sha1'
declare module 'big.js'
declare const Udesk: any
