type Obj = Record<string, any>
type Loading = boolean | [Ref<boolean>]

interface TableData {
  data: Record<string, any>[]
  total: number
}

interface Page {
  pageNum: number
  pageSize: number
}

interface RequestObj {
  url: string
  method?: 'get' | 'post' | 'delete'
  data?: any | null
  headers?: Record<string, any> | null
  loading?: Loading
  useMessage?: boolean
  responseType?: 'json' | 'blob'
}

interface ResponseObj {
  data?: any
  code?: string | number
  response: any
}
