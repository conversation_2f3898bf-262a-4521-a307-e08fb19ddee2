import { AxiosRequestConfig } from 'axios'
/**
 * 自定义扩展axios模块
 * <AUTHOR>
 */
interface BaseRequestConfig extends AxiosRequestConfig {
  loading?: Loading
}
interface InternalAxiosRequestConfig {
  loading?: any
}
declare module 'axios' {
  export interface AxiosInstance {
    <T = any>(config: BaseRequestConfig): Promise<T>
    request<T = any>(config: BaseRequestConfig): Promise<T>
    get<T = any>(url: string, config?: BaseRequestConfig): Promise<T>
    delete<T = any>(url: string, config?: BaseRequestConfig): Promise<T>
    head<T = any>(url: string, config?: BaseRequestConfig): Promise<T>
    post<T = any>(
      url: string,
      data?: any,
      config?: BaseRequestConfig
    ): Promise<T>
    put<T = any>(
      url: string,
      data?: any,
      config?: BaseRequestConfig
    ): Promise<T>
    patch<T = any>(
      url: string,
      data?: any,
      config?: BaseRequestConfig
    ): Promise<T>
  }
}
