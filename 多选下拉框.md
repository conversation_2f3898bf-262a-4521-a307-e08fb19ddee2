```vue
<script setup lang="ts">
import { ref } from "vue";

const formData = ref({
  value: [],
});

const options = [
  {
    value: "Option1",
    label:
      "你还可以通过附加的命令行选项直接指定项目名称和你想要使用的模板你还可以通过附加的命令行选项直接指定项目名称和你想要使用的模板你还可以通过附加的命令行选项直接指定项目名称和你想要使用的模板你还可以通过附加的命令行选项直接指定项目名称和你想要使用的模板",
  },
  {
    value: "Option2",
    label: "你还可以通",
  },
  {
    value: "Option3",
    label: "你还可以通过附加的命",
  },
  {
    value: "Option4",
    label:
      "你还可以通过附加的命令行选项直接指定项目名称和你想要使用的模板你还可以通过附加的命令行选项直接指定项目名称和你想要使用的模板",
  },
  {
    value: "Option5",
    label: "你还可以通过附加的命令行选项直接指定项目名称和你想要使用的模板",
  },
];

const disabledTooltip = ref(false);
const onMouseOver = ($event: any) => {
  const clientWidth = $event.target.clientWidth;
  const scrollWidth = $event.target.scrollWidth;
  disabledTooltip.value = !(scrollWidth > clientWidth);
};
</script>

<template>
  <el-form
    :model="formData"
    label-width="80px"
    style="width: 400px; margin-top: 300px"
  >
    <el-form-item label="下拉框框">
      <el-select v-model="formData.value" placeholder="请选择" multiple>
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <el-tooltip
            :content="item.label"
            placement="top"
            :show-after="500"
            :hide-after="0"
            :disabled="disabledTooltip"
            popper-class="tooltip-width"
          >
            <span
              style="
                display: inline-block;
                max-width: 280px;
                overflow: hidden;
                text-overflow: ellipsis;
              "
              @mouseover="onMouseOver($event)"
              >{{ item.label }}</span
            >
          </el-tooltip>
        </el-option>
        <template #tag>
          <el-tag
            v-for="(item, index) in formData.value"
            closable
            @close="formData.value!.splice(index, 1)"
          >
            <el-tooltip
              :content="options.find((e) => e.value === item)?.label"
              placement="top"
              :show-after="500"
              :hide-after="0"
              :disabled="disabledTooltip"
              popper-class="tooltip-width"
            >
              <span
                style="
                  display: inline-block;
                  max-width: 102px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
                @mouseover="onMouseOver($event)"
                >{{ options.find((e) => e.value === item)?.label }}</span
              >
            </el-tooltip>
          </el-tag>
        </template>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<style>
.tooltip-width {
  max-width: 400px;
}
</style>
```

