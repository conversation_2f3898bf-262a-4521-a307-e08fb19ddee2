import { defineFakeRoute } from 'vite-plugin-fake-server/client'
// import { fakerZH_CN as faker } from '@faker-js/faker'

export default defineFakeRoute([
  // 列表
  {
    url: '/dev-api/operate/inspectionMainTemplateController/getInspectionMainTemplateList',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {
          records: (function () {
            return Array.from({ length: 2 }).map((_, i) => {
              return {
                id: i + 1,
                insTemName: 'string',
                insTemNo: 'string',
                insTemType: 1,
                updateUser: 'string_12',
                updateTime: '2021-01-01 12:00:00',
                inspectionClassDtoList: [
                  {
                    className: 'string',
                    classNum: 0,
                    inspectionSubclassDtoList: [
                      {
                        isRequired: 0,
                        standardName: 'string',
                        standardResult: {},
                        standardType: 'string',
                        subclassName: 'string',
                        subclassNum: 0
                      }
                    ]
                  }
                ],
                operationInfos: 'string',
                operationInfosDtoList: [
                  {
                    operationCode: 'string',
                    operationName: 'string'
                  }
                ]
              }
            })
          })(),
          total: 2
        },
        message: 'ok',
        success: true
      }
    }
  },
  // 删除
  {
    url: '/dev-api/operate/inspectionMainTemplateController/deleteInspectionMainTemplate?id=1',
    method: 'delete',
    response: () => {
      return {
        code: 200,
        data: {},
        message: 'ok',
        success: true
      }
    }
  },
  // 详情
  {
    url: '/dev-api/operate/inspectionMainTemplateController/getInspectionMainTemplateById?id=1',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: {
          id: 1,
          insTemName: 'string',
          insTemNo: 'string',
          insTemType: 1,
          insTemVersion: '1.2',
          versionList: ['1.0', '1.1', '1.2'],
          updateUser: 'string_12',
          updateTime: '2021-01-01 12:00:00',
          inspectionClassDtoList: [
            {
              className: '巡检大类名称',
              classNum: 0,
              inspectionSubclassDtoList: [
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: '20',
                  standardType: '1',
                  subclassName: 'string',
                  subclassNum: 0
                },
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: '100',
                  standardType: '2',
                  subclassName: 'string',
                  subclassNum: 0
                },
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: '3',
                  standardType: '3',
                  subclassName: 'string',
                  subclassNum: 0
                },
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: ['是', '否'],
                  standardType: '4',
                  subclassName: 'string',
                  subclassNum: 0
                },
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: ['A', 'B', 'C'],
                  standardType: '5',
                  subclassName: 'string',
                  subclassNum: 0
                },
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: '1',
                  standardType: '6',
                  subclassName: 'string',
                  subclassNum: 0
                }
              ]
            }
          ],
          operationInfos: 'string',
          operationInfosDtoList: [
            {
              operationCode: '1',
              operationName: '公司一'
            }
          ]
        },
        message: 'ok',
        success: true
      }
    }
  },
  // 版本详情
  {
    url: '/dev-api/operate/inspectionMainTemplateController/getInspectionMainTemplateByIdAndVersion',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {
          id: 1,
          insTemName: 'string',
          insTemNo: 'string',
          insTemType: 1,
          insTemVersion: '1.2',
          versionList: ['1.0', '1.1', '1.2'],
          updateUser: 'string_12',
          updateTime: '2021-01-01 12:00:00',
          inspectionClassDtoList: [
            {
              className: '巡检大类名称',
              classNum: 0,
              inspectionSubclassDtoList: [
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: '20',
                  standardType: '1',
                  subclassName: 'string',
                  subclassNum: 0
                },
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: '100',
                  standardType: '2',
                  subclassName: 'string',
                  subclassNum: 0
                },
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: '3',
                  standardType: '3',
                  subclassName: 'string',
                  subclassNum: 0
                },
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: ['是', '否'],
                  standardType: '4',
                  subclassName: 'string',
                  subclassNum: 0
                },
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: ['A', 'B', 'C'],
                  standardType: '5',
                  subclassName: 'string',
                  subclassNum: 0
                },
                {
                  isRequired: 0,
                  standardName: 'string',
                  standardResult: '1',
                  standardType: '6',
                  subclassName: 'string',
                  subclassNum: 0
                }
              ]
            }
          ],
          operationInfos: 'string',
          operationInfosDtoList: [
            {
              operationCode: '1',
              operationName: '公司一'
            }
          ]
        },
        message: 'ok',
        success: true
      }
    }
  },
  // 生效范围下拉框
  {
    url: '/dev-api/operate/operationUser/getOperatorsInfoListByUser',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: [
          { companyCode: '1', companyName: '公司一' },
          { companyCode: '2', companyName: '公司二' },
          { companyCode: '3', companyName: '公司三公司三公司三公司三公司三' },
          {
            companyCode: '4',
            companyName: '公司四公司四公司四公司四公司四公司四公司四'
          }
        ],
        message: 'ok',
        success: true
      }
    }
  },
  // 新增
  {
    url: '/dev-api/operate/inspectionMainTemplateController/addInspectionMainTemplate',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: null,
        message: 'ok',
        success: true
      }
    }
  },
  // 编辑
  {
    url: '/dev-api/operate/inspectionMainTemplateController/updateInspectionMainTemplate',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: null,
        message: 'ok',
        success: true
      }
    }
  },
  // 典型票列表
  {
    url: '/dev-api/operate/modelTicketController/getModelTicketList',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {
          records: (function () {
            return Array.from({ length: 2 }).map((_, i) => {
              return {
                id: i + 1,
                modelName: 'string',
                modelNo: 'string',
                modelType: 1,
                updateUser: 'string_12',
                updateTime: '2021-01-01 12:00:00',
                operationInfos: 'string',
                operationInfosDtoList: [
                  {
                    operationCode: 'string',
                    operationName: 'string'
                  }
                ]
              }
            })
          })(),
          total: 2
        },
        message: 'ok',
        success: true
      }
    }
  },
  // 删除典型票
  {
    url: '/dev-api/operate/modelTicketController/deleteModelTicket?id=1',
    method: 'delete',
    response: () => {
      return {
        code: 200,
        data: {},
        message: 'ok',
        success: true
      }
    }
  },
  // 典型票详情
  {
    url: '/dev-api/operate/modelTicketController/getModelTicketById?id=1',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: {
          id: 1,
          modelData: 'string',
          modelName: 'string',
          modelNo: 'string',
          modelTemplateDto: {
            jsaTicketDto: {
              homeworkRisk: '1_2_3_5',
              homeworkType: '1_2_3_4_6',
              processRiskDtoList: [
                {
                  control: 'string',
                  process: 'string',
                  riskDesc: 'string',
                  riskLevel: 'string',
                  riskNum: 1
                },
                {
                  control: 'string',
                  process: 'string',
                  riskDesc: 'string',
                  riskLevel: 'string',
                  riskNum: 2
                },
                {
                  control: 'string',
                  process: 'string',
                  riskDesc: 'string',
                  riskLevel: 'string',
                  riskNum: 2
                }
              ]
            },
            safeStepDtoList: [
              {
                safeNum: 1,
                safeStepContext: 'string1',
                safeType: 1
              },
              {
                safeNum: 2,
                safeStepContext: 'string1',
                safeType: 1
              },
              {
                safeNum: 3,
                safeStepContext: 'string1',
                safeType: 1
              },
              {
                safeNum: 2,
                safeStepContext: 'string3',
                safeType: 2
              },
              {
                safeNum: 3,
                safeStepContext: 'string3',
                safeType: 3
              },
              {
                safeNum: 4,
                safeStepContext: 'string3',
                safeType: 4
              },
              {
                safeNum: 5,
                safeStepContext: 'string3',
                safeType: 5
              }
            ]
          },
          modelType: 1,
          operationInfoDtoList: [
            {
              operationCode: '1',
              operationName: '公司一'
            }
          ],
          operationInfos: 'string'
        },
        message: 'ok',
        success: true
      }
    }
  },
  // 一种票改造 获取模板列表
  {
    url: '/dev-api/operate/modelTicketController/getTemplateByType',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: [
          {
            id: 1,
            modelName: '模板一'
          }
        ],
        message: 'ok',
        success: true
      }
    }
  },
  // 一种票改造 获取模板详情
  {
    url: '/dev-api/operate/modelTicketController/getModelTicketById',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: {
          id: 1,
          modelData: 'string',
          modelName: 'string',
          modelNo: 'string',
          modelTemplateDto: {
            jsaTicketDto: {
              homeworkRisk: '1_2_3_5',
              homeworkType: '1_2_3_4_6',
              processRiskDtoList: [
                {
                  control: 'string',
                  process: 'string',
                  riskDesc: 'string',
                  riskLevel: 'string',
                  riskNum: 1
                },
                {
                  control: 'string',
                  process: 'string',
                  riskDesc: 'string',
                  riskLevel: 'string',
                  riskNum: 2
                },
                {
                  control: 'string',
                  process: 'string',
                  riskDesc: 'string',
                  riskLevel: 'string',
                  riskNum: 2
                }
              ]
            },
            safeStepDtoList: [
              {
                safeNum: 1,
                safeStepContext: 'string1',
                safeType: 1
              },
              {
                safeNum: 2,
                safeStepContext: 'string1',
                safeType: 1
              },
              {
                safeNum: 3,
                safeStepContext: 'string1',
                safeType: 1
              },
              {
                safeNum: 2,
                safeStepContext: 'string3',
                safeType: 2
              },
              {
                safeNum: 3,
                safeStepContext: 'string3',
                safeType: 3
              },
              {
                safeNum: 4,
                safeStepContext: 'string3',
                safeType: 4
              },
              {
                safeNum: 5,
                safeStepContext: 'string3',
                safeType: 5
              }
            ]
          },
          modelType: 1,
          operationInfoDtoList: [
            {
              operationCode: '1',
              operationName: '公司一'
            }
          ],
          operationInfos: 'string'
        },
        message: 'ok',
        success: true
      }
    }
  }
])
