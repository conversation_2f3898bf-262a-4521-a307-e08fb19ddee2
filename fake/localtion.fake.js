import { defineFakeRoute } from 'vite-plugin-fake-server/client'
// import { fakerZH_CN as faker } from '@faker-js/faker'

export default defineFakeRoute([
  // 列表
  {
    url: '/dev-api/operate/reportManage/getReportManageList',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {
          records: (function () {
            return Array.from({ length: 2 }).map((_, i) => {
              return {
                id: i + 1,
                stationCode: 'stationCode',
                stationName: 'stationName'
              }
            })
          })(),
          total: 2
        },
        message: 'ok',
        success: true
      }
    }
  },
  // 详情
  {
    url: '/dev-api/operate/reportManage/getReportManageDetail',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: {
          id: 1,
          stationCode: 'stationCode',
          stationName: 'stationName'
        },
        message: 'ok',
        success: true
      }
    }
  }
])
