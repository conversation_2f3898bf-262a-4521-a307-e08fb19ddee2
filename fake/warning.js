import { defineFakeRoute } from 'vite-plugin-fake-server/client'
// import { fakerZH_CN as faker } from '@faker-js/faker'

export default defineFakeRoute([
  {
    url: '/dev-api/operate/foreWarningController/getForeWarningList',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {
          records: (function () {
            return Array.from({ length: 8 }).map((_, i) => {
              return {
                id: i + 1,
                notificationGroup: 0,
                notificationMode: i + 1,
                operationCompanyCode: 'string',
                operationCompanyName: 'string',
                warnName: i + 1,
                warnRuleConditions: 'string',
                warnRuleDesc: 'string',
                warnRuleTime: 0,
                warnRuleTimeType: 0,
                warnSendRule: 0,
                warnState: i === 0 ? 1 : 0,
                warnType: 1,
                updateUser: 'string_1',
                updateTime: '2000-01-01'
              }
            })
          })(),
          total: 100
        },
        message: 'ok',
        success: true
      }
    }
  },
  // 详情
  {
    url: '/dev-api/operate/foreWarningController/getForeWarningById',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: {
          id: 1,
          notificationGroup: '',
          notificationMode: 1,
          operationCompanyCode: '',
          operationCompanyName: '',
          warnName: 1,
          warnRuleConditions: '',
          warnRuleDesc: '',
          warnRuleTime: '',
          warnRuleTimeType: '',
          warnSendRule: '',
          warnState: 1,
          warnType: 1,
          updateUser: '张飞_1',
          updateTime: '2000-01-01'
        },
        message: 'ok',
        success: true
      }
    }
  },
  // 预警记录
  {
    url: '/dev-api/operate/foreWarningRecordsController/getForeWarningRecordList',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {
          records: (function () {
            return Array.from({ length: 8 }).map((_, i) => {
              return {
                id: i + 1,
                notificationGroup: 0,
                notificationMode: i + 1,
                operationCompanyCode: 'string',
                operationCompanyName: 'string',
                warnName: i + 1,
                warnRuleConditions: 'string',
                warnRuleDesc: 'string',
                warnRuleTime: 0,
                warnRuleTimeType: 0,
                warnSendRule: 0,
                warnState: i === 0 ? 1 : 0,
                warnType: 1,
                updateUser: 'string_1',
                updateTime: '2000-01-01'
              }
            })
          })(),
          total: 100
        },
        message: 'ok',
        success: true
      }
    }
  },
  {
    url: '/dev-api/operate/warning/update',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {},
        message: 'ok',
        success: true
      }
    }
  }
])
