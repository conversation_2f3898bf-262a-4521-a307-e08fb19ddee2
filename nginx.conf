user  root;
worker_processes  1;
events {
    worker_connections  10240;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    #keepalive_timeout  0;
    keepalive_timeout  65;
    server_tokens off;
    server {
        listen       8064;
        server_name  localhost;
        location / {
            root /usr/local/nginx/html/;
            try_files $uri $uri/ /index.html;
            index  index.html /index.htm;
        }
        location @router {
            rewrite ^.*$ /index.html last;
        }
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}
