{"name": "pv-om-web", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:uat": "vite build --mode uat", "build:prod": "vite build --mode production", "uat": "pnpm run build:uat", "build:check": "vue-tsc && vite build", "release": "standard-version && git push --follow-tags", "prepare": "husky install", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "prettier": "prettier --write \"./**/*.{html,vue,ts,js,json,md}\"", "lint:style": "stylelint \"./**/*.{css,scss,vue,html}\" --fix", "commit": "git-cz"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iset/auth-interceptor": "^1.0.9", "@iset/chart": "^0.1.9", "@iset/element": "^0.1.9", "@iset/icon": "^0.1.4", "@iset/themes": "^0.1.1", "@types/markdown-it": "^14.1.2", "@vueuse/core": "^10.11.1", "await-to-js": "^3.0.0", "axios": "^1.8.1", "big.js": "^6.2.2", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-plus": "^2.9.5", "gm-crypto": "^0.1.12", "highlight.js": "^11.11.1", "install": "^0.13.0", "js-sha1": "^0.6.0", "markdown-it": "^14.1.0", "path-to-regexp": "^6.3.0", "pinia": "^2.3.1", "pv-om-web": "link:", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@faker-js/faker": "^8.4.1", "@types/node": "^20.17.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@unocss/transformer-attributify-jsx": "^0.56.5", "@vitejs/plugin-vue": "^4.6.2", "commitizen": "^4.3.1", "commitlint-config-cz": "^0.13.3", "consola": "^3.4.0", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.4.0", "dayjs": "^1.11.13", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.32.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "prettier": "^3.5.2", "sass": "^1.85.1", "standard-version": "^9.5.0", "stylelint": "^15.11.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^13.1.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-less": "^2.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-prettier": "^4.1.0", "typescript": "^5.7.3", "unocss": "^0.56.5", "unplugin-auto-import": "^0.16.7", "unplugin-icons": "^0.17.4", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.9", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-style-import": "^2.0.0", "vue-tsc": "^1.8.27"}, "lint-staged": {"**/*.{vue,js,ts}": ["pnpm run lint", "pnpm run prettier"], "**/*.{vue,css,sass}": ["pnpm run lint:style"]}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": ".cz-config.cjs"}}}