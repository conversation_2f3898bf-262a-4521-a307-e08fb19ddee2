import {
  createRouter,
  createWebHistory,
  RouteRecordRaw,
  createWebHashHistory
} from 'vue-router'
import Layout, { LayoutEmpty } from '@/layout'

const BASE_PATH: string = import.meta.env.VITE_APP_BASE_PATH
const routes: Array<RouteRecordRaw> = [
  {
    path: '/no-auth',
    name: 'NoAuth',
    meta: {
      title: '无权限访问',
      keepAlive: false,
      requireAuth: false
    },
    component: () => import('@/pages/noAuth.vue')
  },
  {
    path: '/ops-company',
    name: 'opsCompany',
    meta: {
      title: '运维商管理',
      keepAlive: true,
      requireAuth: true
    },
    component: Layout,
    children: [
      {
        path: '',
        name: 'opsCompanyIndex',
        component: () => import('@/pages/opsCompany/index.vue')
      },
      {
        path: 'add',
        name: 'opsCompanyAdd',
        meta: {
          title: '新增运维商',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/opsCompany/add.vue')
      },
      {
        path: 'station-group/add',
        name: 'stationGroupAdd',
        meta: {
          title: '新增电站组',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'stationGroupAddIndex',
            component: () => import('@/pages/opsCompany/addStationGroup.vue')
          },
          {
            path: 'select-station',
            name: 'stationGroupAddSelectStation',
            meta: {
              title: '手动关联电站',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/opsCompany/selectStation.vue')
          }
        ]
      },
      {
        path: 'station-group/edit/:id',
        name: 'stationGroupEdit',
        meta: {
          title: '电站组详情',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'stationGroupEditIndex',
            component: () => import('@/pages/opsCompany/addStationGroup.vue')
          },
          {
            path: 'select-station',
            name: 'StationGroupEditSelectStation',
            meta: {
              title: '手动关联电站',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/opsCompany/selectStation.vue')
          }
        ]
      }
    ]
  },
  {
    path: '/ops-company-old',
    name: 'opsCompanyOld',
    meta: {
      title: '运维商管理(旧)',
      keepAlive: true,
      requireAuth: true
    },
    component: Layout,
    children: [
      {
        path: '',
        name: 'opsCompanyOldIndex',
        component: () => import('@/pages/opsCompany_old/ocIndex.vue')
      },
      {
        path: 'ocInfo-old',
        name: 'ocInfoOld',
        meta: {
          title: {
            prop: 'pageType',
            type: 'query',
            data: {
              add: '新建运维商',
              handle: '查看详情'
            }
          },
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/opsCompany_old/ocInfo.vue')
      }
    ]
  },
  {
    path: '/ops-project',
    name: 'opsProject',
    meta: {
      title: '运维项目管理',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/opsProject/index.vue')
  },
  {
    path: '/ops-project/add',
    name: 'opsProjectAdd',
    meta: {
      title: '新增运维项目',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/opsProject/projectAdd.vue')
  },
  {
    path: '/ops-project/add/electric',
    name: 'opsProjectAddElectric',
    meta: {
      title: '运维电站选择',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/opsProject/projectElectric.vue')
  },
  {
    path: '/ops-project/detail/:projectCode',
    name: 'opsProjectDetail',
    meta: {
      title: '运维项目详情',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/opsProject/projectDetail.vue')
  },
  {
    path: '/ops-project/detail/:projectCode/electric',
    name: 'opsProjectDetailElectric',
    meta: {
      title: '运维电站选择',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/opsProject/projectElectric.vue')
  },
  {
    path: '/inspection',
    name: 'InspectionLdeger',
    component: Layout,
    children: [
      {
        path: '',
        name: 'InspectionLdegerIndex',
        meta: {
          title: '巡检工单台账',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/inspectionLdeger/index.vue')
      }
    ]
  },
  {
    path: '/inspection/:id',
    name: 'inspectionDetail',
    meta: {
      title: '巡检工单详情',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/inspectionLdeger/detail/index.vue')
  },
  {
    path: '/overhaul',
    name: 'Overhaul',
    component: Layout,
    children: [
      {
        path: '',
        name: 'OverhaulIndex',
        meta: {
          title: '检修工单台账',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/overhaul/index.vue')
      }
    ]
  },
  {
    path: '/overhaul/:id',
    name: 'overhaulDetail',
    meta: {
      title: '检修工单详情',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/overhaul/detail/index.vue')
  },
  {
    path: '/station/:tab?',
    name: 'Station',
    component: Layout,
    children: [
      {
        path: '',
        meta: {
          title: {
            prop: 'tab',
            data: {
              all: '全部运维电站',
              okay: '优选电站',
              await: '需处理电站'
            }
          },
          keepAlive: true,
          requireAuth: true
        },
        name: 'StationIndex',
        component: () => import('@/pages/stationOverview/soIndex.vue')
      }
    ]
  },
  {
    path: '/station/:tab/detail/:cdsta',
    name: 'stationDetail',
    meta: {
      title: '电站详情',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/stationOverview/soDetail.vue')
  },
  {
    path: '/device',
    name: 'Device',
    component: Layout,
    children: [
      {
        path: '',
        name: 'DeviceIndex',
        meta: {
          title: '设备台账',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/deviceOverview/index.vue')
      }
    ]
  },
  {
    path: '/device/detail/:deviceId',
    name: 'DeviceDetail',
    meta: {
      title: '设备台账详情',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/deviceOverview/detail.vue')
  },
  {
    path: '/station-group',
    name: 'StationGroup',
    component: Layout,
    children: [
      {
        path: '',
        meta: {
          title: '权限配置',
          keepAlive: true,
          requireAuth: true
        },
        name: 'StationGroupIndex',
        component: () => import('@/pages/stationGroup/index.vue')
      },
      {
        path: 'show',
        name: 'StationGroupShow',
        meta: {
          title: '权限查看',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/stationGroup/show.vue')
      }
    ]
  },
  {
    path: '/station-group-old',
    name: 'StationGroupOld',
    meta: {
      title: '电站组管理(旧)',
      keepAlive: true,
      requireAuth: true
    },
    component: Layout,
    children: [
      {
        path: '',
        name: 'StationGroupIndexOld',
        component: () => import('@/pages/stationGroup_old/index.vue')
      }
    ]
  },
  {
    path: '/fault-list',
    name: 'FaultList',
    component: Layout,
    children: [
      {
        path: '/fault-list',
        name: 'FaultListIndex',
        meta: {
          title: '故障列表',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/faultList/index.vue')
      }
    ]
  },
  // 故障库
  {
    path: '/fault-library',
    name: 'faultLibrary',
    meta: {
      title: '故障库',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/faultLibrary/index.vue')
  },
  // 故障知识
  {
    path: '/fault-knowledge',
    name: 'faultKnowledge',
    meta: {
      title: '故障知识管理',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/faultKnowledge/index.vue')
  },
  {
    path: '/fault-types',
    name: 'FaultTypes',
    meta: {
      title: '选择故障类型',
      keepAlive: true,
      requireAuth: false
    },
    component: () => import('@/pages/udesk/faultTypes.vue')
  },
  {
    path: '/fault-detail',
    name: 'FaultDetail',
    meta: {
      title: '故障知识详情',
      keepAlive: true,
      requireAuth: false
    },
    component: () => import('@/pages/udesk/faultDetail.vue')
  },
  {
    path: '/tools',
    name: 'Tools',
    component: Layout,
    children: [
      {
        path: '',
        name: 'ToolsIndex',
        meta: {
          title: '工器具管理',
          keepAlive: true,
          requireAuth: false
        },
        component: () => import('@/pages/tools/index.vue')
      }
    ]
  },
  {
    path: '/fault-detail',
    name: 'FaultDetail',
    meta: {
      title: '故障知识详情',
      keepAlive: true,
      requireAuth: false
    },
    component: () => import('@/pages/udesk/faultDetail.vue')
  },
  // 事故 & 隐患
  {
    path: '/problem/:type?',
    name: 'Problem',
    meta: {
      title: {
        prop: 'type',
        data: {
          accident: '事故管理',
          trouble: '隐患管理'
        }
      },
      keepAlive: true,
      requireAuth: false
    },
    component: Layout,
    children: [
      {
        path: '',
        name: 'ProblemIndex',
        component: () => import('@/pages/problem/index.vue')
      }
    ]
  },
  // 运维人员管理
  {
    path: '/ops-personnel',
    name: 'OpsPersonnel',
    meta: {
      title: '运维人员管理',
      keepAlive: true,
      requireAuth: true
    },
    component: Layout,
    children: [
      {
        path: '',
        name: 'OpsPersonnelIndex',
        component: () => import('@/pages/opsPersonnel/opIndex.vue')
      },
      {
        path: 'detail',
        name: 'OpsPersonnelDetail',
        meta: {
          title: {
            prop: 'pageStatus',
            type: 'query',
            data: {
              add: '新建运维人员',
              detail: '查看详情'
            }
          },
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/opsPersonnel/opDetail.vue')
      }
    ]
  },
  // 库存管理
  {
    path: '/store',
    redirect: '/store/material',
    component: Layout,
    children: [
      {
        path: 'material',
        name: 'StoreMaterial',
        meta: {
          title: '物资管理',
          keepAlive: true,
          requireAuth: false
        },
        component: () => import('@/pages/store/material/index.vue')
      },
      {
        path: 'inventory',
        name: 'StoreInventory',
        meta: {
          title: '库存管理',
          keepAlive: true,
          requireAuth: false
        },
        component: () => import('@/pages/store/inventory/index.vue')
      },
      {
        path: 'enter',
        name: 'StoreEnter',
        meta: {
          title: '入库管理',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'StoreEnterIndex',
            component: () => import('@/pages/store/enter/index.vue')
          },
          {
            path: 'detail',
            name: 'StoreEnterDetail',
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/store/enter/detail.vue')
          },
          {
            path: 'add',
            name: 'StoreEnterAdd',
            meta: {
              title: '新建入库单',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/store/enter/add.vue')
          }
        ]
      },
      {
        path: 'out',
        name: 'StoreOut',
        meta: {
          title: '出库管理',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'StoreOutIndex',
            component: () => import('@/pages/store/out/index.vue')
          },
          {
            path: 'detail',
            name: 'StoreOutDetail',
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/store/out/detail.vue')
          },
          {
            path: 'add',
            name: 'StoreOutAdd',
            meta: {
              title: '新建出库单',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/store/out/add.vue')
          }
        ]
      },
      {
        path: 'num',
        name: 'StoreNum',
        meta: {
          title: '盘点管理',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'StoreNumIndex',
            component: () => import('@/pages/store/num/index.vue')
          },
          {
            path: '/store/num/detaile',
            name: 'StoreNumDetaile',
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/store/num/detaile.vue')
          },
          {
            path: '/store/num/add',
            name: 'StoreNumAdd',
            meta: {
              title: '新建盘点单',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/store/num/add.vue')
          }
        ]
      }
    ]
  },
  // 班组 & 培训 & 考试
  {
    path: '/educate',
    redirect: '/educate/team',
    component: Layout,
    children: [
      {
        path: 'team',
        name: 'EducateTeam',
        meta: {
          title: '班组管理',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'EducateTeamIndex',
            component: () => import('@/pages/educate/team/index.vue')
          },
          {
            path: 'add',
            name: 'EducateTeamAdd',
            meta: {
              title: '新建班组',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/educate/team/add.vue')
          },
          {
            path: 'detail',
            name: 'EducateTeamDetail',
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/educate/team/add.vue')
          }
        ]
      },
      {
        path: 'train',
        name: 'EducateTrain',
        meta: {
          title: '培训管理',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'EducateTrainIndex',
            component: () => import('@/pages/educate/train/index.vue')
          },
          {
            path: 'trainInfo',
            name: 'TrainInfo',
            meta: {
              title: {
                prop: 'pageStatus',
                type: 'query',
                data: {
                  add: '新建培训',
                  edit: '查看详情',
                  handle: '处理'
                }
              },
              keepAlive: true,
              requireAuth: false
            },
            component: () => import('@/pages/educate/train/trainInfo.vue')
          }
        ]
      },
      {
        path: 'exam',
        name: 'EducateExam',
        meta: {
          title: '考试管理',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'EducateExamIndex',
            component: () => import('@/pages/educate/exam/index.vue')
          },
          {
            path: 'detail',
            name: 'EducateExamDetail',
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/educate/exam/detail.vue')
          },
          {
            path: 'add',
            name: 'EducateExamAdd',
            meta: {
              title: '新建考试',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/educate/exam/add.vue')
          }
        ]
      }
    ]
  },
  // 工作票 & JAS票 & 操作票
  {
    path: '/ticket',
    redirect: '/ticket/work',
    component: Layout,
    children: [
      {
        path: 'work',
        name: 'TicketWork',
        meta: {
          title: '一种工作票',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'TicketWorkIndex',
            component: () => import('@/pages/ticket/work/index.vue')
          },
          {
            path: '/ticket/work/detail/:id',
            component: LayoutEmpty,
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'TicketWorkDetail',
                meta: {
                  title: '',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/work/detail.vue')
              },
              {
                path: 'supply',
                name: 'TicketWorkDetailSupply',
                meta: {
                  title: '补充信息',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/work/supply.vue')
              },
              {
                path: 'handleInfo/:pageStatus/:opid?',
                component: () => import('@/pages/ticket/handle/handleInfo.vue'),
                meta: {
                  title: '查看详情',
                  keepAlive: true,
                  requireAuth: false
                }
              }
            ]
          },
          {
            path: '/ticket/work/add/:id?',
            name: 'TicketWorkAdd',
            meta: {
              title: '新建工作票',
              keepAlive: true,
              requireAuth: true
            },
            component: LayoutEmpty,
            children: [
              {
                path: '',
                name: 'TicketWorkAddIndex',
                component: () => import('@/pages/ticket/work/add.vue')
              },
              {
                path: '/ticket/work/dx/:type?/:id?',
                name: 'TicketWorkDx',
                meta: {
                  title: {
                    prop: 'type',
                    data: {
                      add: '新建典型票',
                      edit: '编辑典型票',
                      detail: '查看详情'
                    }
                  },
                  keepAlive: true,
                  requireAuth: true
                },
                component: () => import('@/pages/templates/dxForm.vue')
              }
            ]
          }
        ]
      },
      {
        path: 'JSA',
        name: 'TicketJSA',
        meta: {
          title: 'JSA票',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'TicketJSAIndex',
            component: () => import('@/pages/ticket/JSA/index.vue')
          },
          {
            path: '/ticket/JSA/JSAInfo/:pageStatus/:id?/:type?',
            component: LayoutEmpty,
            meta: {
              title: {
                prop: 'pageStatus',
                type: 'params',
                data: {
                  add: '新建JSA票',
                  look: '查看详情'
                }
              },
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'JSAInfo',
                component: () => import('@/pages/ticket/JSA/JSAInfo.vue')
              },
              {
                path: 'supplyInfo',
                name: 'supplyInfo',
                meta: {
                  title: {
                    prop: 'pageType',
                    type: 'query',
                    data: {
                      supply: '补充信息'
                    }
                  },
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/JSA/supplyInfo.vue')
              }
            ]
          }
        ]
      },
      {
        path: 'two',
        name: 'TicketTwo',
        meta: {
          title: '二种工作票',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'TicketTwoIndex',
            component: () => import('@/pages/ticket/two/index.vue')
          },
          {
            path: '/ticket/two/detail/:id',
            component: LayoutEmpty,
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'TicketTwoDetail',
                meta: {
                  title: '',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/two/detail.vue')
              },
              {
                path: 'supply',
                name: 'TicketTwoDetailSupply',
                meta: {
                  title: '补充信息',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/two/supply.vue')
              },
              {
                path: 'lookorder/:ids',
                component: LayoutEmpty,
                meta: {
                  title: '查看详情',
                  keepAlive: true,
                  requireAuth: false
                },
                children: [
                  {
                    path: '',
                    name: '',
                    meta: {
                      title: '',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/workorder/lookorder.vue')
                  },
                  {
                    path: 'ticket/two/add',
                    name: '',
                    meta: {
                      title: '新建工作票',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/ticket/two/add.vue')
                  }
                ]
              },
              {
                path: 'handleInfo/:pageStatus/:opid?',
                component: () => import('@/pages/ticket/handle/handleInfo.vue'),
                meta: {
                  title: '查看详情',
                  keepAlive: true,
                  requireAuth: false
                }
              }
            ]
          },
          {
            path: '/ticket/two/add/:id?',
            name: 'TicketTwoAdd',
            meta: {
              title: '新建工作票',
              keepAlive: true,
              requireAuth: true
            },
            component: LayoutEmpty,
            children: [
              {
                path: '',
                name: 'TicketTwoAddIndex',
                component: () => import('@/pages/ticket/two/add.vue')
              },
              {
                path: '/ticket/two/dx/:type?/:id?',
                name: 'TicketTwoDx',
                meta: {
                  title: {
                    prop: 'type',
                    data: {
                      add: '新建典型票',
                      edit: '编辑典型票',
                      detail: '查看详情'
                    }
                  },
                  keepAlive: true,
                  requireAuth: true
                },
                component: () => import('@/pages/templates/dxForm.vue')
              }
            ]
          }
        ]
      },
      {
        path: 'handle',
        name: 'TicketHandle',
        meta: {
          title: '操作票',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'TicketHandleIndex',
            component: () => import('@/pages/ticket/handle/index.vue')
          },
          {
            path: '/ticket/handle/handleInfo/:pageStatus/:opid?',
            component: LayoutEmpty,
            meta: {
              title: {
                prop: 'pageStatus',
                type: 'params',
                data: {
                  add: '新建操作票',
                  look: '查看详情'
                }
              },
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'handleInfo',
                component: () => import('@/pages/ticket/handle/handleInfo.vue')
              },
              {
                path: '/ticket/handle/dx/:pageStatus/:type?/:id?/:opid?',
                name: 'TicketHandleDx',
                meta: {
                  title: {
                    prop: 'type',
                    data: {
                      add: '新建典型票',
                      edit: '编辑典型票',
                      detail: '查看详情'
                    }
                  },
                  keepAlive: true,
                  requireAuth: true
                },
                component: () => import('@/pages/templates/dxForm.vue')
              },
              // 跳一种票
              {
                path: 'ticketWorkDetail/:id',
                component: LayoutEmpty,
                meta: {
                  title: '查看详情',
                  keepAlive: true,
                  requireAuth: false
                },
                children: [
                  {
                    path: '',
                    name: '',
                    meta: {
                      title: '',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/ticket/work/detail.vue')
                  },
                  {
                    path: 'supply',
                    name: '',
                    meta: {
                      title: '补充信息',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/ticket/work/supply.vue')
                  }
                ]
              },
              // 跳二种票
              {
                path: 'ticketTwoDetail/:id',
                component: LayoutEmpty,
                meta: {
                  title: '查看详情',
                  keepAlive: true,
                  requireAuth: false
                },
                children: [
                  {
                    path: '',
                    name: '',
                    meta: {
                      title: '',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/ticket/two/detail.vue')
                  },
                  {
                    path: 'supply',
                    name: '',
                    meta: {
                      title: '补充信息',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/ticket/two/supply.vue')
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  // 审批工作台 & 审批权限管理
  {
    path: '/approval',
    redirect: '/approval/workbench',
    component: Layout,
    children: [
      {
        path: 'workbench',
        name: 'ApprovalWorkbench',
        meta: {
          title: '审批工作台',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'ApprovalWorkbenchIndex',
            component: () => import('@/pages/approval/workbench/index.vue')
          },
          {
            path: 'detail/:id',
            component: LayoutEmpty,
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'TicketWorkDetailApproval',
                meta: {
                  title: '',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/work/detail.vue')
              },
              {
                path: 'supply',
                name: 'TicketWorkDetailSupplyApproval',
                meta: {
                  title: '补充信息',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/work/supply.vue')
              }
            ]
          },
          {
            path: 'two/detail/:id',
            component: LayoutEmpty,
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'TwoWorkDetailApproval',
                meta: {
                  title: '',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/two/detail.vue')
              },
              {
                path: 'supply',
                name: 'TwoWorkDetailSupplyApproval',
                meta: {
                  title: '补充信息',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/two/supply.vue')
              }
            ]
          },
          {
            path: 'JSAInfo/:pageStatus/:id?/:type?',
            component: LayoutEmpty,
            meta: {
              title: {
                prop: 'pageStatus',
                type: 'params',
                data: {
                  add: '新建JSA票',
                  look: '查看详情'
                }
              },
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'JSAInfoApproval',
                component: () => import('@/pages/ticket/JSA/JSAInfo.vue')
              },
              {
                path: 'supplyInfo',
                name: 'supplyInfoApproval',
                meta: {
                  title: {
                    prop: 'pageType',
                    type: 'query',
                    data: {
                      supply: '补充信息'
                    }
                  },
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/JSA/supplyInfo.vue')
              }
            ]
          },
          {
            path: 'feedback/detail/:id',
            component: LayoutEmpty,
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'feedbackApproval',
                meta: {
                  title: '',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/safeRange/feedback/detail.vue')
              }
            ]
          },
          {
            path: '/approval/emergency/:type/:id',
            component: () => import('@/pages/emergency/detail.vue'),
            meta: {
              title: {
                prop: 'type',
                data: {
                  detail: '查看详情',
                  edit: '编辑应急演练'
                }
              },
              keepAlive: true,
              requireAuth: false
            }
          },
          {
            path: 'lookorder/:ids',
            component: LayoutEmpty,
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: '',
                meta: {
                  title: '',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/workorder/lookorder.vue')
              },
              {
                path: 'ticket/two/add',
                name: '',
                meta: {
                  title: '新建工作票',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/two/add.vue')
              }
            ]
          },
          {
            path: 'handleInfo/:pageStatus/:opid?',
            component: LayoutEmpty,
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: '',
                meta: {
                  title: '',
                  keepAlive: true,
                  requireAuth: false
                },
                component: () => import('@/pages/ticket/handle/handleInfo.vue')
              }
            ]
          },
          {
            path: 'location/:id',
            name: 'WorkbenchLocationDetail',
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/location/detail.vue')
          }
        ]
      },
      {
        path: 'authority',
        name: 'ApprovalAuthority',
        meta: {
          title: '审批权限管理',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'ApprovalAuthorityIndex',
            component: () => import('@/pages/approval/authority/index.vue')
          }
        ]
      }
    ]
  },
  // 低效电站统计
  {
    path: '/station-low',
    component: Layout,
    children: [
      {
        path: '',
        name: 'StationLow',
        meta: {
          title: '低效电站统计',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/stationLow/index.vue')
      }
    ]
  },
  // 检修及时率统计报表
  {
    path: '/operation',
    redirect: '/operation/report',
    component: Layout,
    children: [
      {
        path: 'report',
        name: 'OperationReport',
        meta: {
          title: '检修及时率统计报表',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'OperationReportIndex',
            component: () => import('@/pages/operation/report/index.vue')
          }
        ]
      }
    ]
  },
  // 运维电站评分
  {
    path: '/score/plantRating',
    component: Layout,
    children: [
      {
        path: '',
        name: 'plantRating',
        meta: {
          title: '运维电站评分',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/score/plantRating/index.vue')
      }
    ]
  },
  // 运维商评分
  {
    path: '/score/operatorRating',
    component: Layout,
    children: [
      {
        path: '',
        name: 'operatorRating',
        meta: {
          title: '运维商评分',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/score/operatorRating/index.vue')
      }
    ]
  },
  // 运维报表
  {
    path: '/operation-report',
    meta: {
      title: '运维报表',
      keepAlive: true,
      requireAuth: true
    },
    component: Layout,
    children: [
      {
        path: '',
        name: 'operationReport',
        component: () => import('@/pages/operationReport/index.vue')
      }
    ]
  },
  // 报表管理
  {
    path: '/reportForms',
    component: Layout,
    children: [
      {
        path: '/reportForms/dayReport',
        name: 'ReportFormsDayReport',
        component: () => import('@/pages/reportForms/dayReport/index.vue'),
        meta: {
          title: '生产日报表',
          keepAlive: true,
          requireAuth: true
        }
      },
      {
        path: '/reportForms/monthlyReport',
        name: 'ReportFormsMonthlyReport',
        component: () => import('@/pages/reportForms/monthlyReport/index.vue'),
        meta: {
          title: '生产月报表',
          keepAlive: true,
          requireAuth: true
        }
      },
      {
        path: '/reportForms/companyReport',
        name: 'ReportFormsCompanyReport',
        component: () => import('@/pages/reportForms/companyReport/index.vue'),
        meta: {
          title: '项目公司日报',
          keepAlive: true,
          requireAuth: true
        }
      },
      {
        path: '/reportForms/projectStatistics',
        name: 'ReportFormsProjectStatistics',
        component: () =>
          import('@/pages/reportForms/projectStatistics/index.vue'),
        meta: {
          title: '项目公司-电站类别统计',
          keepAlive: true,
          requireAuth: true
        }
      },
      {
        path: '/reportForms/operationStatistics',
        name: 'ReportFormsOperationStatistics',
        component: () =>
          import('@/pages/reportForms/operationStatistics/index.vue'),
        meta: {
          title: '运维公司-电站类别统计',
          keepAlive: true,
          requireAuth: true
        }
      }
    ]
  },
  // 计划管理
  {
    path: '/plan-management',
    meta: {
      title: '计划管理',
      keepAlive: true,
      requireAuth: true
    },
    component: Layout,
    children: [
      {
        path: '',
        name: 'planIndex',
        component: () => import('@/pages/planManagement/planIndex.vue')
      },
      {
        path: '/plan-management/planInfo/:pageStatus/:id?',
        component: () => import('@/pages/planManagement/planInfo.vue'),
        meta: {
          title: {
            prop: 'pageStatus',
            type: 'params',
            data: {
              add: '新建计划',
              edit: '编辑计划',
              look: '查看详情'
            }
          },
          keepAlive: true,
          requireAuth: false
        }
      }
    ]
  },
  // 安全范围
  // 经验反馈单
  {
    path: '/safe-range',
    redirect: '/safe-range/feedback',
    component: Layout,
    children: [
      {
        path: 'feedback',
        name: 'FeedbackIndex',
        meta: {
          title: '经验反馈单管理',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'Feedback',
            component: () => import('@/pages/safeRange/feedback/index.vue')
          },
          {
            path: '/safe-range/feedback/detail/:id',
            name: 'FeedbackDetail',
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: false
            },
            component: () => import('@/pages/safeRange/feedback/detail.vue')
          },
          {
            path: '/safe-range/feedback/add/:pageStatus/:id?',
            name: 'FeedbackAdd',
            meta: {
              // title: '新建经验反馈单',
              title: {
                prop: 'pageStatus',
                type: 'params',
                data: {
                  add: '新建经验反馈单',
                  edit: '编辑经验反馈单'
                }
              },
              keepAlive: true,
              requireAuth: true
            },
            component: () => import('@/pages/safeRange/feedback/add.vue')
          }
        ]
      }
    ]
  },
  // 应急管理
  {
    path: '/emergency',
    name: 'Emergency',
    meta: {
      title: '应急演练管理',
      keepAlive: true,
      requireAuth: false
    },
    component: Layout,
    children: [
      {
        path: '',
        name: 'EmergencyIndex',
        component: () => import('@/pages/emergency/index.vue')
      },
      {
        path: ':type/:id',
        component: () => import('@/pages/emergency/detail.vue'),
        meta: {
          title: {
            prop: 'type',
            data: {
              detail: '查看详情',
              edit: '编辑应急演练'
            }
          },
          keepAlive: true,
          requireAuth: false
        }
      }
    ]
  },
  // 缺陷列表
  {
    path: '/deeect',
    redirect: '/deeect/listIndex',
    component: Layout,
    children: [
      {
        path: 'listIndex',
        name: 'DeeectList',
        meta: {
          title: '缺陷列表',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'DeeectListIndex',
            component: () => import('@/pages/deeect/index.vue')
          },
          {
            path: '/deeect/deeectInfo/:pageStatus/:id?',
            component: LayoutEmpty,
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'DeeectInfo',
                component: () => import('@/pages/deeect/info.vue')
              },
              {
                path: 'lookorder/:ids',
                component: LayoutEmpty,
                meta: {
                  title: '查看详情',
                  keepAlive: true,
                  requireAuth: false
                },
                children: [
                  {
                    path: '',
                    component: () => import('@/pages/workorder/lookorder.vue')
                  },
                  {
                    path: 'ticket/two/add',
                    name: '',
                    meta: {
                      title: '新建工作票',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/ticket/two/add.vue')
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  // 工单列表
  {
    path: '/workorder',
    redirect: '/workorder/listIndex',
    component: Layout,
    children: [
      {
        path: 'listIndex',
        name: 'WorkorderList',
        meta: {
          title: '工单列表',
          keepAlive: true,
          requireAuth: true
        },
        component: LayoutEmpty,
        children: [
          {
            path: '',
            name: 'WorkorderListIndex',
            component: () => import('@/pages/workorder/index.vue')
          },
          // 工单 maintenanceOrder-运维工单 onSiteOrder-巡检工单 cleanOrder-清洗工单
          {
            path: '/workorder/addNewOrder',
            component: LayoutEmpty,
            meta: {
              title: '新建工单',
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'AddNewOrder',
                component: () => import('@/pages/workorder/addNewOrder.vue')
              }
            ]
          },
          {
            path: '/workorder/lookorder/:ids',
            component: LayoutEmpty,
            meta: {
              title: '查看详情',
              keepAlive: true,
              requireAuth: false
            },
            children: [
              {
                path: '',
                name: 'Lookorder',
                component: () => import('@/pages/workorder/lookorder.vue')
              },
              {
                path: 'ticket/work/add',
                name: '',
                meta: {
                  title: '新建一种工作票',
                  keepAlive: true,
                  requireAuth: true
                },
                component: () => import('@/pages/ticket/work/add.vue')
              },
              {
                path: 'ticket/two/add',
                name: '',
                meta: {
                  title: '新建二种工作票',
                  keepAlive: true,
                  requireAuth: true
                },
                component: () => import('@/pages/ticket/two/add.vue')
              },
              {
                path: 'ticketTwo/detail/:id',
                component: LayoutEmpty,
                meta: {
                  title: '查看详情',
                  keepAlive: true,
                  requireAuth: false
                },
                children: [
                  {
                    path: '',
                    name: '',
                    meta: {
                      title: '',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/ticket/two/detail.vue')
                  },
                  {
                    path: 'supply',
                    name: '',
                    meta: {
                      title: '补充信息',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/ticket/two/supply.vue')
                  }
                ]
              },
              {
                path: 'ticket/work/detail/:id',
                component: LayoutEmpty,
                meta: {
                  title: '查看详情',
                  keepAlive: true,
                  requireAuth: false
                },
                children: [
                  {
                    path: '',
                    name: '',
                    meta: {
                      title: '',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/ticket/work/detail.vue')
                  },
                  {
                    path: 'supply',
                    name: '',
                    meta: {
                      title: '补充信息',
                      keepAlive: true,
                      requireAuth: false
                    },
                    component: () => import('@/pages/ticket/work/supply.vue')
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/order/create',
    name: 'orderCreate',
    meta: {
      title: '创建工单',
      keepAlive: false,
      requireAuth: true
    },
    component: () => import('@/pages/workorder/outside/orderCreate.vue')
  },
  {
    path: '/order/detail',
    name: 'orderDetail',
    meta: {
      title: '工单详情',
      keepAlive: false,
      requireAuth: true
    },
    component: () => import('@/pages/workorder/outside/orderDetail.vue')
  },
  // 预警管理
  {
    path: '/warning',
    redirect: '/warning/index',
    meta: {
      title: '预警管理',
      keepAlive: true,
      requireAuth: true
    },
    component: Layout,
    children: [
      {
        path: 'index',
        name: 'WarningIndex',
        component: () => import('@/pages/warning/index.vue')
      },
      {
        path: 'add',
        name: 'WarningAdd',
        meta: {
          title: '新建预警',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/warning/edit.vue')
      },
      {
        path: 'edit/:id',
        name: 'WarningEdit',
        meta: {
          title: '编辑预警',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/warning/edit.vue')
      }
    ]
  },
  {
    path: '/warning-record',
    redirect: '/warning-record/index',
    meta: {
      title: '预警记录',
      keepAlive: true,
      requireAuth: true
    },
    component: Layout,
    children: [
      {
        path: 'index',
        name: 'WarningRecordIndex',
        component: () => import('@/pages/warning/record.vue')
      },
      {
        path: 'add',
        name: 'WarningRecordAdd',
        meta: {
          title: '新建预警',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/warning/edit.vue')
      }
    ]
  },
  // 考核管理
  {
    path: '/examine-management',
    meta: {
      title: '考核列表',
      keepAlive: true,
      requireAuth: true
    },
    component: Layout,
    children: [
      {
        path: '',
        name: 'examineIndex',
        component: () => import('@/pages/examineManagement/examineIndex.vue')
      }
    ]
  },
  // 模板管理
  {
    path: '/templates',
    name: 'Templates',
    meta: {
      title: '巡检模板管理',
      keepAlive: true,
      requireAuth: true
    },
    redirect: '/templates/xj',
    component: Layout,
    children: [
      {
        path: 'xj',
        name: 'TemplatesXj',
        component: () => import('@/pages/templates/xjIndex.vue')
      },
      {
        path: 'xj/:type',
        name: 'TemplatesXjType',
        meta: {
          title: {
            prop: 'type',
            data: {
              add: '新建模板',
              edit: '编辑模板',
              detail: '查看详情'
            }
          },
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/templates/xjForm.vue')
      }
    ]
  },
  {
    path: '/templates/dx',
    name: 'TemplatesDx',
    meta: {
      title: '典型票管理',
      keepAlive: true,
      requireAuth: true
    },
    component: Layout,
    children: [
      {
        path: '',
        name: 'TemplatesDxIndex',
        component: () => import('@/pages/templates/dxIndex.vue')
      },
      {
        path: ':type',
        name: 'TemplatesDxType',
        meta: {
          title: {
            prop: 'type',
            data: {
              add: '新建典型票',
              edit: '编辑典型票',
              detail: '查看详情'
            }
          },
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/templates/dxForm.vue')
      }
    ]
  },
  {
    path: '/location',
    name: 'Location',
    meta: {
      title: '上报管理',
      keepAlive: true,
      requireAuth: true
    },
    redirect: '/location/index',
    component: Layout,
    children: [
      {
        path: 'index',
        name: 'LocationIndex',
        component: () => import('@/pages/location/index.vue')
      },
      {
        path: 'detail/:id',
        name: 'LocationDetail',
        meta: {
          title: '查看详情',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/location/detail.vue')
      }
    ]
  },
  // AI预警分析记录
  {
    path: '/warningAnalysisRecord',
    name: 'WarningAnalysisRecord',
    meta: {
      title: 'AI预警分析记录',
      keepAlive: true,
      requireAuth: true
    },
    redirect: '/warningAnalysisRecord/index',
    component: Layout,
    children: [
      {
        path: 'index',
        name: 'WarningAnalysisIndex',
        component: () => import('@/pages/warningAnalysis/index.vue')
      },
      {
        path: 'detail',
        name: 'WarningAnalysisDetail',
        meta: {
          title: '电站详情',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/warningAnalysis/detail.vue')
      },
      {
        path: 'work',
        name: 'WarningAnalysisWork',
        meta: {
          title: '工单详情',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/warningAnalysis/work.vue')
      }
    ]
  },
  // 互动反馈台账
  {
    path: '/feedbackLedger',
    name: 'feedbackLedger',
    meta: {
      title: '互动反馈台账',
      keepAlive: true,
      requireAuth: true
    },
    redirect: '/feedbackLedger/index',
    component: Layout,
    children: [
      {
        path: 'index',
        name: 'feedbackLedgerIndex',
        component: () => import('@/pages/feedbackLedger/index.vue')
      },
      {
        path: 'detail',
        name: 'feedbackLedgerDetail',
        meta: {
          title: '电站详情',
          keepAlive: true,
          requireAuth: true
        },
        component: () => import('@/pages/feedbackLedger/detail.vue')
      }
    ]
  }
]

if (import.meta.env.MODE === 'development') {
  routes.unshift({
    path: '/',
    name: 'Index',
    meta: {
      title: '首页',
      keepAlive: true,
      requireAuth: true
    },
    component: () => import('@/pages/indexPage.vue')
  })
}

const router = createRouter({
  history: createWebHashHistory(BASE_PATH),
  routes,
  scrollBehavior: () => ({ left: 0, top: 0 })
})

export default router
