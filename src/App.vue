<script setup lang="ts">
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
// import request from '@/utils/request'

// const auth = ref(false)
const router = useRouter()
router.beforeEach(async (to, from, next) => {
  // 防止无限跳转
  if (to.matched.length === 0 && to.fullPath.includes(from.fullPath)) {
    router.back()
    // const section = to.fullPath.replace(from.fullPath, '')
    // if (from.fullPath.includes(section)) {
    //   router.back()
    // }
  }
  // 不需要权限页面列表
  const fullPathStarts = [
    '/ops-personnel',
    '/ops-project',
    '/station/okay',
    '/fault-types',
    '/fault-detail',
    '/station/order-create',
    '/station/order-detail',
    '/fault-library',
    '/fault-knowledge',
    '/store/material',
    '/educate',
    '/approval',
    '/operation',
    '/reportForms',
    '/plan-management',
    '/safe-range/feedback',
    '/emergency',
    '/ops-company/station-group/add/select-station'
  ]
  if (
    import.meta.env.DEV ||
    to.name === 'Index' ||
    to.name === 'NoAuth' ||
    to.name === 'StationGroupEditSelectStation' ||
    fullPathStarts.some((e: string) => to.fullPath.startsWith(e))
  ) {
    next()
  } else {
    next()
    // try {
    //   const { data } = await request({
    //     url: '/operate/safeZoneController/getExistCode',
    //     method: 'get',
    //     loading: false
    //   })
    //   auth.value = data.data || false
    // } catch (e) {
    //   next('/no-auth?path=' + to.path)
    // }
    // if (auth.value) {
    //   if (to.fullPath.startsWith('/station-group')) {
    //     next()
    //   } else {
    //     next()
    //     // try {
    //     //   const { data } = await request({
    //     //     url: '/operate/safeZoneController/getStationCount',
    //     //     method: 'get',
    //     //     loading: false
    //     //   })
    //     //   const stationsNum = data.data || 0
    //     //   if (stationsNum) {
    //     //     next()
    //     //   } else {
    //     //     next('/no-auth?path=' + to.path)
    //     //   }
    //     // } catch (e) {
    //     //   next('/no-auth?path=' + to.path)
    //     // }
    //   }
    // } else {
    //   next('/no-auth?path=' + to.path)
    // }
  }
})
onMounted(() => {})
</script>
<template>
  <el-config-provider :locale="zhCn" size="default">
    <RouterView />
  </el-config-provider>
</template>
<style lang="scss" scoped>
.logo {
  padding: 1.5em;
  height: 6em;
  transition: filter 300ms;
  will-change: filter;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
