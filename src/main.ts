import { createApp } from 'vue'
import '@/assets/styles/reset.css'
import 'uno.css'
import App from './App.vue'
import store from './store'
import router from '@/router'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import VisIcons from '@iset/icon'
import VisComponents from '@iset/element'
import '@iset/element/lib/css/style.css'
import VisCharts from '@iset/chart'
import '@iset/themes/dist/element-plus/index.css'
import '@/assets/styles/common.scss'
import directives from '@/directives'
// import userName from '@/hooks/userName.ts'
// import AuthInterceptor from '@iset/auth-interceptor'
import '@/utils/jsExtend'
import { getUrlParams } from '@/utils/util'

const mode = import.meta.env.MODE

// 测试环境 cookie
// if (mode === 'development') {
//   const token =
//     'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJsb2dpbiIsInBhdGgiOiIvIiwidG9rZW5LZXkiOiI5MmE4NzViZmYzODFhOTE5OWNlMTUxZDA0ODAxMjA0NjlkMTM1Nzc4MGNhZjRhZTM4ODZkMWRkMjg1Yjk1ZmM5IiwibmJmIjoxNzQxNjc4NDAwLCJkb21haW4iOiJzcGljLWlzZXQuY29tIiwiaXNzIjoiZHJhY28iLCJ0ZW5hbnRJZCI6MSwiZXhwaXJlX3RpbWUiOjE4MDAsImV4cCI6MTc0MTY4MDIwMCwiaWF0IjoxNzQxNjc4NDAwfQ.Lom1mat5RItXP3RzldEWHVEQ4q2Z5J36vEHm-iWhHe4'
//   document.cookie = `tsydev_spic_identify_uat=${token}; path=/`
// }

// if (mode !== 'development') {
//   new AuthInterceptor({
//     portalOnly: true
//   })
// }

//获取uc链接中的menuCode，并设置到localStorage
const setMenuCode = () => {
  const obj = getUrlParams()
  const val = obj?._m || ''
  const menuCodeStr = decodeURIComponent(val)
  const menuCodeList = menuCodeStr.split('::')
  const menuCode = menuCodeList?.length > 1 ? menuCodeList[1] : ''
  localStorage.setItem('menuCode', menuCode)
}
setMenuCode()

const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.directive('preventReClick', {
  mounted: function (el, binding) {
    el.addEventListener('click', () => {
      const events = el.style.pointerEvents
      if (events == '') {
        el.style.pointerEvents = 'none'
        setTimeout(() => {
          el.style.pointerEvents = ''
        }, binding.value || 1000)
      }
    })
  }
})
// userName()
app.use(store)
app.use(router)
app.use(VisIcons)
app.use(VisComponents)
app.use(VisCharts)
app.use(directives)

app.mount('#app')
export const appInstance = app
