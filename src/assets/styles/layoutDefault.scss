.page-container {
  background: #f6f8fa;
  position: relative;
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  height: 60px;
  display: flex;
  align-items: center;
  background: #fff;
  box-shadow: 0 0 8px 0 rgb(21 102 80 / 10%);
  .header-title {
    padding-left: 24px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    .backIcon {
      position: relative;
      top: 2px;
    }
    .goback {
      color: #333;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;
    }
    .detailLine {
      color: #e4e4e4;
      margin: 0 6px 0 12px;
    }
    a {
      color: #999;
      margin-left: 6px;
    }
    span {
      margin-left: 6px;
    }
  }
}

.page-main {
  margin: 24px;
  height: calc(100vh - 108px);
  border-radius: 8px;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  position: relative;
  overflow: hidden;
  .page-search {
    padding: 24px 24px 0 24px;
    margin-bottom: 8px;
    background: #fff;
    .el-form .el-form-item {
      display: flex;
      margin-right: 0;
      margin-bottom: 15px;
      &:deep(label) {
        font-weight: normal;
        padding: 0;
      }
      &:deep(.el-select) {
        width: 100%;
      }
    }
    .search-buttons .el-form-item {
      float: right;
    }
  }
  .main {
    padding: 24px;
    box-sizing: border-box;
    background: #fff;
    height: 100%;
    position: relative;
  }
}

.page-footer {
  display: flex;
  justify-content: flex-end;
  padding-right: 15px;
  margin: 0 24px;
  margin-bottom: 10px;
  height: 48px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
}

.info-base {
  padding: 24px 24px 12px 24px;
  margin: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  position: relative;
  overflow: hidden;
}
.info-base + .info-base {
  margin-top: 0px;
}

.info-tab {
  padding: 24px;
  margin: 0 24px 24px;
  height: calc(100vh - 307px);
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  position: relative;
  overflow: hidden;
}

.page-main,
.info-tab {
  .el-tabs {
    width: 100%;
    height: 100%;
    :deep(.el-tabs__content) {
      height: calc(100% - 54px);
      position: relative;
    }
  }
  .el-tab-pane {
    height: 100%;
    position: relative;
  }
}

.page-main .operate,
.info-base .operate,
.info-tab .operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
  & > p,
  & > span {
    font-size: 15px;
    font-style: normal;
    font-weight: 600;
    line-height: 32px;
    color: #666;
    span {
      margin-left: 8px;
      color: #2acba0;
      font-weight: 500;
    }
  }
  &.end {
    justify-content: flex-end;
  }
}

.page-main .operate .el-icon {
  margin-right: 4px !important;
  font-weight: bold;
}
.vis-table-pagination {
  .table-operate {
    .el-button.is-link {
      color: #29cca0;
    }
    .el-button.is-link:hover {
      color: var(--el-button-hover-link-text-color);
    }
    .el-button + .el-button {
      margin-left: 5px;
    }
  }
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  margin: 24px 24px 0 24px;
  color: rgba(0, 0, 0, 0.85);
  padding-left: 4px;
}

.page-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding: 0 24px;
  .operate-title {
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);
    padding-left: 4px;
  }
}

.page-wrapper {
  display: flex;
  .sidebar {
    width: 256px;
    height: calc(100% - 48px);
    flex: none;
    border-radius: 8px;
    box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
    background: #fff;
    margin: 24px 0 24px 24px;
    overflow: hidden;
    .header {
      display: flex;
      justify-content: space-between;
      height: 61px;
      align-items: center;
      padding: 0 16px;
      border-bottom: solid 1px #f4f4f4;
      .tilte {
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }
    .el-scrollbar {
      padding: 16px;
      .el-tree {
        width: 224px;
      }
    }
  }
  & > .info-tab {
    flex: auto;
    height: calc(100% - 48px);
    margin-top: 24px;
    padding: 0;
    .el-tabs {
      --el-tabs-header-height: 61px;
      :deep(.el-tabs__nav-wrap) {
        padding: 0 24px;
      }
      :deep(.el-tab-pane) {
        padding: 9px 24px 24px;
      }
      :deep(.el-tabs__content) {
        height: calc(100% - 76px);
        position: relative;
        .el-tab-pane > .el-scrollbar {
          padding-right: 15px;
          .el-scrollbar__bar.is-horizontal {
            display: none !important;
          }
        }
      }
    }
  }
}

.info-base {
  .el-scrollbar {
    :deep(.el-scrollbar__bar.is-horizontal) {
      display: none !important;
    }
  }
}
