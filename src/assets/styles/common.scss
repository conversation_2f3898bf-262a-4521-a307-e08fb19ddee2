@import './elEment.scss';

:root {
  --el-loading-fullscreen-spinner-size: 32px !important;
}

#app {
  height: 100%;
  min-height: 100vh;
}

html::-webkit-scrollbar {
  width: 6px;
}
html::-webkit-scrollbar-thumb {
  background-color: #0003;
  border-radius: 10px;
  transition: all 0.2s ease-in-out;
}
html::-webkit-scrollbar-track {
  border-radius: 10px;
}
.vis-table-pagination {
  .table-operate {
    .el-button.is-link {
      color: #29cca0;
    }
    .el-button.is-link:hover {
      color: var(--el-button-hover-link-text-color);
    }
    .el-button + .el-button {
      margin-left: 5px;
    }
  }
}
.el-descriptions__label:not(.is-bordered-label) {
  margin-right: 0 !important;
}
.el-tabs .el-tabs__item {
  padding: 0;
  padding-left: 20px;
}
.el-tabs.el-tabs--card .el-tabs__item {
  padding: 0 20px;
}
.el-form-item .el-select,
.el-form-item .el-cascader {
  width: 100%;
}
.el-form-item .el-date-editor {
  width: 100% !important;
}

.no-padding {
  padding: 0 !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  text-decoration: none;
  color: #29cca0;
  cursor: pointer;
}
a:hover {
  text-decoration: underline;
}
div:focus {
  outline: none;
}

.clearfix {
  &::after {
    display: block;
    clear: both;
    height: 0;
    font-size: 0;
    visibility: hidden;
    content: ' ';
  }
}

/* 滚动条的滑块 */
.scrollbar-wrapper::-webkit-scrollbar-thumb {
  display: none !important;
}
.detailTitle {
  font-size: 14px;
  font-weight: 600;
  color: #000;
  margin: 15px 0 10px 0;
}
.highColor {
  color: #ff0000;
  background-color: rgba(255, 0, 0, 0.1);
  padding: 6px 12px;
  border-radius: 10px;
}
.midColor {
  color: #fb891e;
  background-color: rgba(251, 137, 30, 0.1);
}
.lowColor {
  color: #29cca0;
  background-color: rgba(41, 204, 160, 0.1);
}
.cancelColor {
  color: #7a8a99;
  background-color: #f0f5fa;
}
.awaitColor {
  color: #5c42ff;
  background-color: rgba(92, 66, 255, 0.1);
}
.doingColor {
  color: #108cff;
  background: rgba(16, 140, 255, 0.1);
}
.doingColor,
.cancelColor,
.lowColor,
.awaitColor,
.midColor,
.highColor {
  display: flex;
  padding: 1px 12px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
}
.shinkWidth {
  width: 40px;
}
.el-table {
  --el-table-text-color: #333 !important;
}
.el-form-item__label {
  color: #333 !important;
}
.el-descriptions__label {
  color: #666 !important;
}
.el-input__inner::placeholder {
  color: #999 !important;
}
.el-loading-spinner .el-loading-text {
  color: #8a8a8a !important;
  font-size: 16px !important;
}
.el-popper {
  max-width: 80%;
}

.el-select-tags-wrapper {
  .el-tag.is-closable {
    // padding-right: 0px;
    // display: inline;
    // white-space: normal;
    // word-break: break-word;
    // padding: 0;
    // margin-right: 10px;
    // background: #ecf5ff;
    // color: #666;
    color: #666;
  }
  .el-tag .el-tag__close {
    // margin-left: 3px;
    // vertical-align: middle;
    color: var(--el-color-primary);
    &:hover {
      background-color: var(--el-color-primary);
    }
  }
  .el-select__tags-text {
    &.el-tooltip__trigger {
      color: var(--el-color-primary);
    }
  }
}
.el-select__collapse-tag {
  // padding-right: 0px !important;
  // display: inline !important;
  // white-space: normal !important;
  // word-break: break-word !important;
  // background: initial;
  // color: #666;
  // margin-bottom: 5px;
  color: #666;
  .el-tag.is-closable {
    // padding-right: 0px;
    // display: inline;
    // white-space: normal;
    // word-break: break-word;
    // padding: 0;
    // margin-right: 10px;
    // background: #ecf5ff;
    // color: #666;
    // border: none;
    color: #666;
  }
  .el-tag .el-tag__close {
    // margin-left: 3px;
    // vertical-align: middle;
    color: var(--el-color-primary);
    &:hover {
      background-color: var(--el-color-primary);
    }
  }
}
.el-tag__content {
  display: inline;
}
.el-select__tags-text {
  display: inline !important;
  overflow: initial !important;
  text-overflow: initial !important;
  white-space: initial !important;
}
.popper-width-540px {
  width: 540px !important;
}
.el-upload-list__item {
  transition: none !important;
}
.el-form--inline .el-form-item {
  width: 100%;
}
.tagWidth {
  width: 100px;
}
.el-textarea .el-input__count {
  bottom: -15px !important;
  color: #bbc2ca !important;
  background: rgba(255, 255, 255, 0) !important;
}

.el-select__wrapper.is-filterable {
  .el-select__input {
    margin-left: 0 !important;
  }
}

.el-dialog__headerbtn .el-dialog__close {
  color: var(--el-text-color-placeholder) !important;
}
.el-dialog__headerbtn:hover .el-dialog__close {
  color: var(--el-color-primary) !important;
}

.el-select .el-select__tags-text {
  color: #666 !important;
}
.el-select .el-tag__close {
  color: #666 !important;
}

.el-table .cell .el-form-item {
  margin: 0 !important;
}

.el-table .cell .el-form-item .el-form-item__error {
  position: relative;
  top: 0;
}
