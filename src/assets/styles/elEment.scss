// 按钮
.el-button {
  font-weight: normal !important;
  &:focus-visible {
    outline: none !important;
  }
}
// 表单
.el-form {
  .el-input__wrapper {
    height: 40px !important;
    line-height: 40px !important;
  }
  .el-select__wrapper {
    min-height: 40px !important;
    line-height: 40px !important;
  }
  .el-form-item--default .el-form-item__label {
    height: 40px !important;
    line-height: 40px !important;
  }
  .el-form-item__label {
    font-weight: 400 !important;
    color: rgba(0, 0, 0, 0.85) !important;
  }
  .el-input__inner {
    color: rgba(0, 0, 0, 0.85) !important;
  }
  .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.25) !important;
  }
  .el-textarea__inner::placeholder {
    color: rgba(0, 0, 0, 0.25) !important;
  }
  .el-date-editor.el-input {
    height: 40px !important;
    line-height: 40px !important;
  }
  .el-input.is-disabled .el-input__wrapper {
    background-color: rgba(240, 240, 240, 1) !important;
  }
  .el-input.is-disabled .el-input__inner {
    -webkit-text-fill-color: rgba(0, 0, 0, 0.45) !important;
  }
  .el-input.is-disabled .el-input__inner::placeholder {
    -webkit-text-fill-color: rgba(0, 0, 0, 0.25) !important;
  }
  .el-form-item--default {
    margin-bottom: 24px !important;
  }
  .el-date-editor .el-range-input {
    color: rgba(0, 0, 0, 0.85) !important;
  }
  .el-date-editor .el-range-input::placeholder {
    -webkit-text-fill-color: rgba(0, 0, 0, 0.25) !important;
  }
  .el-range-separator {
    color: rgba(0, 0, 0, 0.25) !important;
  }
  .el-date-editor .el-range__icon {
    color: rgba(0, 0, 0, 0.25) !important;
  }
  .el-range-editor.is-disabled {
    background-color: rgba(240, 240, 240, 1) !important;
  }
  .el-range-editor.is-disabled input {
    background-color: rgba(240, 240, 240, 1) !important;
    color: rgba(0, 0, 0, 0.45) !important;
  }
  .el-form-item .el-form-item {
    margin-bottom: 0 !important;
    margin-right: 0 !important;
  }
}
// 表格
.el-table thead > tr {
  background-color: #f9f9f9 !important;
}
thead > .el-table--default .cell {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: rgba(0, 0, 0, 0.85) !important;
}
.el-table--default .el-table__cell {
  padding: 12px 0 !important;
  font-size: 14px !important;
  line-height: 24px !important;
  color: rgba(0, 0, 0, 0.85) !important;
  .cell {
    min-height: 23px !important;
  }
}
.el-table td.el-table__cell {
  border-bottom: 1px solid #f4f4f4 !important;
}

// tabs切换
.el-tabs__nav-wrap::after {
  background-color: #f4f4f4 !important;
}
.el-tabs__item {
  font-weight: normal !important;
}
.el-tabs__item.is-active {
  font-weight: 500 !important;
}
.el-tabs .el-tabs__item {
  padding-left: 32px !important;
  font-size: 14px !important;
}
.el-tabs--top .el-tabs__item.is-top:nth-child(2) {
  padding-left: 0 !important;
}
// 弹窗
.el-dialog__title {
  font-weight: 500 !important;
}
.el-dialog.vis-dialog {
  padding: 0;
  .el-dialog__header {
    height: 54px;
    line-height: 54px;
    padding: 0 0 0 15px;
    position: relative;
    .el-dialog__headerbtn {
      top: 0;
    }
  }
  .el-dialog__header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: var(--el-dialog-width);
    height: 1px;
    background: #e5e6eb;
  }
  .el-dialog__body {
    padding: 24px;
    padding-bottom: 0;
  }
  .el-dialog__footer {
    padding: 15px;
    border-top: solid 1px #e5e6eb;
  }
}

.el-message-box__status + .el-message-box__message {
  padding-left: 21px !important;
}
.el-popconfirm__icon.el-icon {
  font-size: 16px !important;
}

.el-message-box--center {
  .el-message-box__title,
  .el-message-box__container {
    justify-content: left !important;
  }
  .el-message-box__btns {
    justify-content: right !important;
    padding-top: 24px !important;
  }
}

.el-input-number.no-button {
  width: 100%;
  .el-input__wrapper {
    padding: 1px 11px;
  }
  .el-input-number__decrease,
  .el-input-number__increase {
    display: none;
  }
  input.el-input__inner {
    text-align: left;
  }
}
