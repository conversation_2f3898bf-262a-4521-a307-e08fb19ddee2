import { ticket as ticketApi } from '@/api/index.ts'
export default function () {
  // 角色人员下拉列表
  /**
   * JS-0001 工作票签发人
   * JS-0002 工作许可人
   * JS-0003 值班负责人
   * JS-0004 值长
   * JS-0005 工作负责人
   * JS-0006 经验反馈单审核人
   * JS-0007 经验反馈单批准人
   * JS-0008 应急演练审核人
   * */
  const roleList1 = ref<any[]>([]) // 工作票签发人
  const roleList2 = ref<any[]>([]) // 工作许可人
  const roleList3 = ref<any[]>([]) // 值班负责人
  const roleList4 = ref<any[]>([]) // 值长
  const roleList5 = ref<any[]>([]) // 工作负责人
  const roleList6 = ref<any[]>([]) // 经验反馈单审核人
  const roleList7 = ref<any[]>([]) // 经验反馈单批准人
  const roleList8 = ref<any[]>([]) // 应急演练审核人

  const getRoles = async () => {
    // 角色人员下拉列表
    try {
      const { data } = await ticketApi.getRoleUserList({})
      roleList1.value = data['JS-0001'] || []
      roleList2.value = data['JS-0002'] || []
      roleList3.value = data['JS-0003'] || []
      roleList4.value = data['JS-0004'] || []
      roleList5.value = data['JS-0005'] || []
      roleList6.value = data['JS-0006'] || []
      roleList7.value = data['JS-0007'] || []
      roleList8.value = data['JS-0008'] || []
    } catch (e) {}
  }
  onMounted(() => {
    getRoles()
  })

  return {
    roleList1,
    roleList2,
    roleList3,
    roleList4,
    roleList5,
    roleList6,
    roleList7,
    roleList8
  }
}
