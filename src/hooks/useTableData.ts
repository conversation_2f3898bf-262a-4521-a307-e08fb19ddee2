export default function (
  getTableData: { (data: Obj, loading: Loading): Promise<ResponseObj> },
  searchData: Obj = {},
  options: Obj = {}
) {
  const { callback, hasPages = true, immediate = true } = options
  const tableData = reactive<TableData>({
    data: [],
    total: 0
  })

  const tablePage = reactive<Page>({
    pageNum: 1,
    pageSize: 10
  })

  const tableLoading = ref(false)

  const changeData = async (reset: boolean | 'delete' = false) => {
    if (reset && reset !== 'delete') {
      tablePage.pageNum = 1
      tablePage.pageSize = 10
    }
    if (reset && reset === 'delete') {
      if (tablePage.pageSize === 0) return
      const totalPage = Math.ceil(tableData.total / tablePage.pageSize)
      tablePage.pageNum =
        tablePage.pageNum > totalPage ? totalPage : tablePage.pageNum
    }
    const requestData = hasPages ? { ...searchData, ...tablePage } : searchData
    const { data } = await getTableData(requestData, [tableLoading])
    tableData.total = hasPages ? data?.data?.total || data?.total || 0 : 0
    tableData.data = hasPages
      ? data?.data?.records || data?.records || []
      : data?.data || data || []
    callback && callback(tableData)
  }

  const changeSize = async (params: Obj) => {
    tablePage.pageNum = 1
    tablePage.pageSize = params.pageSize
    await changeData()
  }

  const changeCurrent = async (params: Obj) => {
    tablePage.pageNum = params.currentPage
    tablePage.pageSize = params.pageSize
    await changeData()
  }

  onMounted(() => {
    immediate && changeData(true)
  })

  return {
    tableData,
    tablePage,
    tableLoading,
    changeCurrent,
    changeSize,
    changeData
  }
}

/**
const searchData = reactive<Obj>({
  pageNum: 1,
  pageSize: 10
})
const handleSearch = async (val: any) => {
  Object.keys(searchData).forEach((key) => {
    searchData[key] = val[key]
  })
  changeData(true)
}
const columns = []
const getTableData = async (data: Obj, loading: Loading) => {
  return await api.post(
    '请求地址',
    data,
    loading
  )
}
const { tableData, tablePage, tableLoading, changeCurrent, changeSize, changeData } =
  useTableData(getTableData, searchData)
</script>
<template>
  <TablePagination
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :show-overflow-tooltip="true"
    background
    :columns="columns"
    :data="tableData.data"
    :total="tableData.total"
    :loading="tableLoading"
    :current-page="tablePage.pageNum"
    :page-size="tablePage.pageSize"
    class="table-pagination"
    @handle-size-change="changeSize"
    @handle-current-change="changeCurrent"
  >
  </TablePagination>
</template>
 */
