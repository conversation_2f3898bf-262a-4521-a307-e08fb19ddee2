import { baseApi } from '@/api/index.ts'
import { getCookieValue } from '@/utils'

export default function () {
  const getUser = async () => {
    try {
      const { data } = await baseApi.getUser()
      if (getCookieValue('userNameId') !== data) {
        localStorage.removeItem('PVOM_COMPANY_CODE')
        localStorage.removeItem('PVOM_COMPANY_ID')
        localStorage.removeItem('PVOM_COMPANY_NAME')
      }
      document.cookie = 'userNameId=' + (data || '')
      document.cookie = 'userName=' + (data?.split('_')?.[0] || '')
    } catch (e: any) {
      document.cookie = 'userNameId=' + ''
      document.cookie = 'userName=' + ''
    }
  }
  getUser()
}
