import request from '@/utils/request'

export default function (loading?: any) {
  const projectColumns: Record<string, any>[] = [
    {
      prop: 'operationProjectCode',
      label: '运维项目编码',
      minWidth: '120'
    },
    {
      prop: 'operationProjectName',
      label: '运维项目名称',
      minWidth: '120'
    },
    {
      prop: 'assetSaleCompanyName',
      label: '资产公司'
    },
    {
      prop: 'operationCompanyName',
      label: '运维商'
    },
    {
      prop: 'serviceStatus',
      label: '服务状态',
      formatter: (row: any) => {
        return (
          { 1: '待开始', 2: '服务中', 3: '已结束' }?.[
            row.serviceStatus as number
          ] ||
          row.serviceStatus ||
          '--'
        )
      }
    },
    {
      prop: 'totalCapins',
      label: '装机容量(kW)',
      minWidth: '120'
    }
  ]
  const projectTotal = ref<number>(0)
  const projectData = ref<Record<string, any>[]>([])
  const projectSearchData = reactive({
    assetSaleCompanyName: '',
    operationCompanyName: '',
    operationProjectCode: '',
    operationProjectName: '',
    serviceStatus: ''
  })
  const projectPage = reactive({
    pageNum: 1,
    pageSize: 10
  })
  onMounted(async () => {
    getProjectData()
  })
  const getProjectData = async () => {
    try {
      const { data } = await request({
        url: '/operate/operation-project/getOperationProjectList',
        method: 'post',
        data: {
          ...projectSearchData,
          ...projectPage
        },
        loading: loading
      })
      projectTotal.value = data?.data?.total || 0
      projectData.value = data?.data?.records || []
    } catch (e) {
      projectTotal.value = 0
      projectData.value = []
    }
  }
  const projectSizeChange = (params: Record<string, any>) => {
    projectPage.pageNum = 1
    projectPage.pageSize = params.pageSize
    getProjectData()
  }
  const projectCurrentChange = (params: Record<string, any>) => {
    projectPage.pageNum = params.currentPage
    projectPage.pageSize = params.pageSize
    getProjectData()
  }
  return {
    projectColumns,
    projectTotal,
    projectData,
    projectPage,
    projectSearchData,
    getProjectData,
    projectSizeChange,
    projectCurrentChange
  }
}
