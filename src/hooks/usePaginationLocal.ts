type Data = Record<string, any>
interface ResponseData {
  data: Data[]
  pageNum: number
  pageSize: number
}
export interface PageObjectLocal {
  pageNum: number
  pageSize: number
}

export default function ({
  pageNum = 1,
  pageSize = 20
}: Partial<ResponseData> = {}) {
  const data = ref<Data[]>([])
  const localData = ref<Data[]>([])
  const pageObject = reactive<PageObjectLocal>({
    pageNum,
    pageSize
  })
  const handleSliceData = (reponseData: Data[]) => {
    data.value = [...reponseData]
    const { pageNum, pageSize } = pageObject
    const offer = (pageNum - 1) * pageSize
    localData.value = data.value.slice(offer, offer + pageSize)
  }
  const selectedSizeChange = async (params: Record<string, any>) => {
    pageObject.pageNum = 1
    pageObject.pageSize = params.pageSize
    handleSliceData(data.value)
  }
  const selectedCurrentChange = async (params: Record<string, any>) => {
    pageObject.pageNum = params.currentPage
    pageObject.pageSize = params.pageSize
    handleSliceData(data.value)
  }
  return {
    localData,
    handleSliceData,
    selectedSizeChange,
    selectedCurrentChange,
    pageObject
  }
}
