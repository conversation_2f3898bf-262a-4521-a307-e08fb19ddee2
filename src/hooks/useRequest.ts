import request from '@/utils/request'

export default function () {
  const _request = async ({
    url,
    method = 'get',
    data = null,
    headers = null,
    loading,
    useMessage = true,
    responseType = 'json'
  }: RequestObj): Promise<ResponseObj> => {
    const requestData = data
    try {
      const response: any = await request({
        url,
        method,
        loading,
        data: method === 'post' ? requestData : null,
        params: method === 'get' ? requestData : null,
        headers: headers || { 'Content-Type': 'application/json' },
        responseType
      })
      const { data, status } = response
      if (typeof data.code === 'undefined' && status === 200)
        return { response }
      if (data.code == 200 || data.code == 201) {
        return { data: data.data, code: data.code, response }
      } else {
        useMessage &&
          ElMessage({
            message: data.message || data.msg,
            type: 'error'
          })
        return Promise.reject(data)
      }
    } catch (e: any) {
      ElMessage({
        message: e.message,
        type: 'error'
      })
      return Promise.reject(e)
    }
  }

  const get = async (
    urlOrRequestObj: string | RequestObj,
    data: any = null,
    loading: Loading = false
  ) => {
    if (typeof urlOrRequestObj === 'string') {
      return _request({
        url: urlOrRequestObj,
        method: 'get',
        data,
        loading
      })
    } else {
      return _request(urlOrRequestObj)
    }
  }

  const post = async (
    urlOrRequestObj: string | RequestObj,
    data: any = null,
    loading: Loading = false
  ) => {
    if (typeof urlOrRequestObj === 'string') {
      return _request({
        url: urlOrRequestObj,
        method: 'post',
        data,
        loading
      })
    } else {
      return _request({ ...urlOrRequestObj, method: 'post' })
    }
  }

  const del = async (
    urlOrRequestObj: string | RequestObj,
    data: any = null,
    loading: Loading = false
  ) => {
    if (typeof urlOrRequestObj === 'string') {
      return _request({
        url: urlOrRequestObj,
        method: 'delete',
        data,
        loading
      })
    } else {
      return _request({ ...urlOrRequestObj, method: 'delete' })
    }
  }
  return {
    get,
    post,
    del
  }
}
