<script lang="ts" setup>
import useCompanyData from './useCompanyData.ts'

const { companyData } = useCompanyData()
const model = defineModel<string>({ required: true })
defineProps({
  item: {
    type: Object,
    default: () => {}
  }
})
</script>
<template>
  <el-select-v2
    v-model="model"
    :options="
      companyData.map((item: any) => ({
        label: item.companyName,
        value: item.companyCode
      }))
    "
    :placeholder="item?.placeholder || `请选择${item.label}`"
    clearable
    :filterable="true"
  />
</template>
