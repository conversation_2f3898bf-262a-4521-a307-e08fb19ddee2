<script lang="ts" setup>
type Value = number | string | boolean
interface Item {
  label: string
  value?: Value
  color?: string
  textColor?: string
}
interface Props {
  value?: Value
  options?: Record<string, Item | string> | string[] | Item[]
  disabledKey?: Value | null
}
const props = withDefaults(defineProps<Props>(), {
  value: 0,
  options: () => {
    return ['禁用', '启用']
  },
  disabledKey: null
})

const disabledKey = computed(() => {
  if (props.disabledKey === null) {
    if (Array.isArray(props.options)) {
      return 0
    } else {
      return Object.keys(props.options).sort()[0]
    }
  }
  return props.disabledKey
})

const item = computed<Item>(() => {
  let valueCopy: number | string
  if (typeof props.value === 'boolean') {
    valueCopy = props.value ? 1 : 0
  } else {
    valueCopy = props.value
  }

  let v: Item | string
  if (!Array.isArray(props.options)) {
    v = props.options[valueCopy as string]
  } else {
    if (
      typeof props.options[0] === 'object' &&
      props.options[0].hasOwnProperty('value')
    ) {
      v = props.options.find((e) => {
        if (typeof e === 'object') {
          return e.value === valueCopy
        }
        return false
      }) as Item
    } else {
      v = props.options[valueCopy as number]
    }
  }
  if (typeof v === 'object') {
    return {
      label: v.label || '',
      color: v.color || (valueCopy !== disabledKey.value ? '#3284ff' : '#999'),
      textColor: v.textColor || 'inherit'
    }
  }
  return {
    label: v,
    color: valueCopy !== disabledKey.value ? '#3284ff' : '#999',
    textColor: 'inherit'
  }
})
</script>
<template>
  <div class="spic-status">
    <span class="dot"></span>
    <span class="label">{{ item.label }}</span>
  </div>
</template>
<style lang="scss" scoped>
.spic-status {
  font-size: 14px;
  display: inline-block;
  .dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: v-bind('item.color');
  }
  .label {
    margin-left: 6px;
    color: v-bind('item.textColor');
  }
}
</style>
