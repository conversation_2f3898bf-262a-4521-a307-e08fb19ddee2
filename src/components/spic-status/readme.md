# 状态组件

### 基本使用

默认俩种状态，value 值支持 number | string | boolean 类型，默认值小的作为禁用状态，可传参数 disabledKey 配置禁用状态的值。可传入 options 参数，配置显示文字及状态颜色。

```javascript
import SpicStatus from '@/components/spic-status'

<SpicStatus value="value" :disabledKey="1" />
```

### 多状态

传入 options 配置多状态，disabledKey 将失效，options支持数组和对象，详细配置看源码 interface 定义

```javascript
import SpicStatus from '@/components/spic-status'

<SpicStatus
  :value="value"
  :options="[
    { value: 10, label: '禁用', color: 'red', textColor: 'red' },
    { value: 20, label: '启用', color: 'blue', textColor: 'blue' },
    { value: 30, label: '启用2', color: 'grey', textColor: 'grey' }
  ]"
/>
```
