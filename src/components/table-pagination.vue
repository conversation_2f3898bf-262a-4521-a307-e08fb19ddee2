<template>
  <el-table
    ref="tablePagination"
    v-loading="loading"
    :data="data"
    v-bind="$attrs"
    :height="height || defaultHeight"
    :header-cell-style="() => ({ background: '#F6F8FA' })"
    class="vis-table-pagination"
    @selection-change="handleSelectionChange"
  >
    <template v-for="(item, ind) in columns">
      <el-table-column
        v-if="item.prop === 'index'"
        :key="ind + (currentPage - 1) * pageSize + 1"
        type="index"
        :label="item.label || '序号'"
        :fixed="item.fixed"
        :width="item.width || 85"
        :index="item?.indexMethod || indexMethod"
      >
      </el-table-column>
      <el-table-column
        v-else-if="item.prop === 'selection'"
        :key="item.prop + 'selection'"
        type="selection"
        :reserve-selection="item.reserve ? true : false"
        :selectable="item.selectable || null"
        :width="item.width || 55"
      />
      <el-table-column
        v-else-if="item.prop === 'radio-selection'"
        :key="item.prop + 'radio-selection'"
        :width="item.width || 36"
        class-name="radio-selection"
        :align="`center`"
        :show-overflow-tooltip="false"
      />
      <el-table-column
        v-else-if="item.prop === 'expand'"
        :key="item.prop + ind"
        type="expand"
        :width="item.width || 55"
      >
        <template #default="{ row, column, $index }">
          <slot
            name="expand"
            :row="row"
            :column="column"
            :index="$index"
          ></slot>
        </template>
      </el-table-column>
      <el-table-column
        v-else-if="item.type === 'image-new'"
        :key="item.prop + 'image-new'"
        :width="item.width || 100"
        :label="item.label"
        :show-overflow-tooltip="false"
        class-name="image-cell"
      >
        <template #default="{ row }">
          <SpicUpload
            v-if="row[item.prop]?.length"
            v-model="row[item.prop]"
            type="image"
            :limit="20"
            :disabled="true"
          />
          <template v-else>--</template>
        </template>
      </el-table-column>
      <el-table-column
        v-else-if="item.type === 'image'"
        :key="item.prop + 'image'"
        :width="item.width || 100"
        :label="item.label"
        :show-overflow-tooltip="false"
        class-name="image-cell"
      >
        <template #default="{ row }">
          <template v-if="row[item.prop]">
            <el-image
              v-if="typeof item.src !== 'function'"
              :src="
                item.baseUrl ? item.baseUrl + row[item.prop] : row[item.prop]
              "
              :preview-src-list="
                typeof item.previewSrcList === 'function'
                  ? item.previewSrcList(row)
                  : undefined
              "
              :preview-teleported="true"
              style="width: 70px; margin: 0 3px"
            />
            <template v-else>
              <el-image
                v-for="image in item.src(row)"
                :key="image"
                :src="item.baseUrl ? item.baseUrl + image : image"
                :preview-src-list="
                  typeof item.previewSrcList === 'function'
                    ? item.previewSrcList(row)
                    : undefined
                "
                :preview-teleported="true"
                style="width: 70px; margin: 0 3px"
              />
            </template>
          </template>
          <template v-else>--</template>
        </template>
      </el-table-column>
      <el-table-column
        v-else-if="item.prop === 'priority' || item.prop === 'workState'"
        :key="item.prop + 'priority'"
        :width="item.width || 90"
        :label="item.label"
      >
        <template #default="{ row, column, $index }">
          <slot :name="item.prop" :row="row" :column="column" :index="$index">
            <div v-if="item.prop === 'priority'">
              <span v-if="row[item.prop] == '高'" class="highColor shinkWidth">
                高
              </span>
              <span
                v-else-if="row[item.prop] == '中'"
                class="midColor shinkWidth"
              >
                中
              </span>
              <span
                v-else-if="row[item.prop] == '低'"
                class="lowColor shinkWidth"
              >
                低
              </span>
              <span v-else :class="{ 'lowColor shinkWidth': row[item.prop] }">{{
                row[item.prop] || '--'
              }}</span>
            </div>
            <div v-if="item.prop === 'workState'">
              <span v-if="row[item.prop] == '草稿'" class="midColor">草稿</span>
              <span v-else-if="row[item.prop] == '待处理'" class="highColor"
                >待处理</span
              >
              <span v-else-if="row[item.prop] == '处理中'" class="doingColor"
                >处理中</span
              >
              <span v-else-if="row[item.prop] == '等待中'" class="awaitColor"
                >等待中</span
              >
              <span v-else-if="row[item.prop] == '已取消'" class="cancelColor"
                >已取消</span
              >
              <span v-else-if="row[item.prop] == '已完成'" class="lowColor"
                >已完成</span
              >
              <span v-else class="lowColor">{{ row[item.prop] }}</span>
            </div>
          </slot>
        </template>
      </el-table-column>
      <el-table-column
        v-else-if="item.type === 'tag'"
        :key="item.prop + 'tag'"
        :min-width="item?.minWidth"
        :label="item.label"
      >
        <template #default="{ row }">
          <slot :name="item.prop" :row="row">
            <el-tag
              v-if="row[item.prop]"
              :effect="item.effect || 'plain'"
              :type="item.options?.[row[item.prop]] || 'success'"
              :color="item.colors?.[row[item.prop]] || undefined"
              >{{ row[item.prop] }}</el-tag
            >
            <template v-else>--</template>
          </slot>
        </template>
      </el-table-column>
      <el-table-column
        v-else-if="item.prop === 'status'"
        :key="item.prop + 'status'"
        :min-width="item?.minWidth"
        :label="item.label"
      >
        <template #default="{ row }">
          <slot :name="item.prop" :row="row">
            <SpicStatus
              :value="row[item.prop]"
              :options="item.options"
              :disabled-key="20"
            ></SpicStatus>
          </slot>
        </template>
      </el-table-column>
      <el-table-column
        v-else-if="item.type === 'html'"
        :key="item.prop + 'html'"
        :min-width="item?.minWidth"
        :label="item.label"
      >
        <template #default="{ row }">
          <slot :name="item.prop" :row="row">
            <div
              v-html="item.formatter ? item.formatter(row) : row[item.prop]"
            ></div>
          </slot>
        </template>
      </el-table-column>
      <el-table-column
        v-else-if="item.type === 'href'"
        :key="item.prop + 'href'"
        :min-width="item?.minWidth"
        :label="item.label"
      >
        <template #default="{ row }">
          <a
            :href="typeof item.href === 'function' ? item.href(row) : undefined"
            target="_blank"
          >
            {{ row[item.prop] }}
          </a>
        </template>
      </el-table-column>
      <el-table-column
        v-else-if="item.type === 'click'"
        :key="item.prop + 'click'"
        :min-width="item?.minWidth"
        :label="item.label"
      >
        <template #default="{ row }">
          <p
            class="cursor-pointer"
            :style="`color: ${item.color || '#29cca0'}`"
            @click.stop="
              () =>
                typeof item.click === 'function' ? item.click(row) : undefined
            "
          >
            {{ row[item.prop] }}
          </p>
        </template>
      </el-table-column>
      <el-table-column
        v-else-if="item.type === 'array'"
        :key="item.prop + 'array'"
        :min-width="item?.minWidth"
        :label="item.label"
        :show-overflow-tooltip="false"
      >
        <template #default="{ row }">
          <el-tooltip
            placement="top"
            :disabled="
              Array.isArray(row[item.prop]) && row[item.prop].length > 3
                ? false
                : Array.isArray(row[item.prop]) &&
                  row[item.prop].some(
                    (e: any) =>
                      e.workOrderName?.length > 12 || e.workName?.length > 12
                  )
                ? false
                : true
            "
            popper-class="tooltip-style"
          >
            <template #content>
              <div class="tooltip-content">
                <a
                  v-for="(value, index) in row[item.prop]"
                  :key="item.prop + index"
                  target="_blank"
                  :href="
                    item.emit === 'href'
                      ? typeof item.href === 'function'
                        ? item.href(value)
                        : undefined
                      : undefined
                  "
                  @click="
                    () => {
                      item.emit === 'click' && typeof item.href === 'function'
                        ? item.href(value)
                        : item.emit === 'click' &&
                          typeof item.href !== 'function'
                        ? $emit('array-item-click', value)
                        : null
                    }
                  "
                >
                  {{ value.workOrderName || value.workName }}
                </a>
              </div>
            </template>
            <div class="tooltip-wrapper">
              <template
                v-if="
                  Array.isArray(row[item.prop]) && row[item.prop].length > 0
                "
              >
                <a
                  v-for="(value, index) in row[item.prop].slice(0, 3)"
                  :key="item.prop + index"
                  target="_blank"
                  :href="
                    item.emit === 'href'
                      ? typeof item.href === 'function'
                        ? item.href(value)
                        : undefined
                      : undefined
                  "
                  @click="
                    () => {
                      item.emit === 'click' && typeof item.href === 'function'
                        ? item.href(value)
                        : item.emit === 'click' &&
                          typeof item.href !== 'function'
                        ? $emit('array-item-click', value)
                        : null
                    }
                  "
                >
                  {{ value.workOrderName || value.workName }}
                </a>
              </template>
              <template v-else>--</template>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        v-else-if="item.slotName"
        :key="ind"
        :align="item?.align"
        :fixed="item.fixed"
        :sortable="item.sortable"
        :label="item.label"
        :min-width="item?.minWidth"
        :width="item?.width"
        :column-key="item?.columnKey"
        :filters="item?.filters"
        :filter-method="item?.filterMethod"
        :filter-placement="item?.filterPlacement"
        :formatter="item?.formatter || formatter"
        :show-overflow-tooltip="false"
      >
        <template #header="{ column, $index }">
          <slot
            :name="`${item.slotName}-header`"
            :column="column"
            :index="$index"
            >{{ item.label }}</slot
          >
        </template>
        <template #default="{ row, column, $index }">
          <slot
            :name="item.slotName"
            :row="row"
            :column="column"
            :index="$index"
          >
          </slot>
        </template>
      </el-table-column>
      <el-table-column
        v-else
        :key="item"
        :fixed="item.fixed"
        :sortable="item.sortable"
        :prop="item.prop"
        :label="item.label"
        :min-width="item?.minWidth"
        :width="item?.width"
        :column-key="item?.columnKey"
        :filters="item?.filters"
        :filter-method="item?.filterMethod"
        :filter-placement="item?.filterPlacement"
        :formatter="item?.formatter || formatter"
      >
        <template v-if="item.children && item.children.length">
          <el-table-column
            v-for="children in item.children"
            :key="children.key"
            :prop="children.prop"
            :label="children.label"
            :min-width="children?.minWidth"
            :column-key="children?.columnKey"
            :filters="children?.filters"
            :filter-method="children?.filterMethod"
            :filter-placement="children?.filterPlacement"
            :formatter="children?.formatter || formatter"
          />
        </template>
      </el-table-column>
    </template>
  </el-table>
  <el-pagination
    v-if="total"
    ref="paginationRef"
    m-t-16px
    justify-end
    :current-page="currentPage"
    :page-size="pageSize"
    v-bind="$attrs"
    :total="total"
    :pager-count="5"
    :page-count="pageCount"
    :prev-text="
      !($attrs.layout as string).includes('pager') ? '上一页' : undefined
    "
    :next-text="
      !($attrs.layout as string).includes('pager') ? '下一页' : undefined
    "
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>
<script lang="ts" setup>
import SpicStatus from '@/components/spic-status'
import { ElTable } from 'element-plus'
import { debounce } from '@/utils'
import useSearchFormStore from '@/store/searchForm'

const searchFormStore = useSearchFormStore()

const tablePagination = ref<any>()
const paginationRef = ref<any>()
const defaultHeight = ref<number | string>('auto')

type PropsType = {
  columns: any[]
  data: Record<string, any>[]
  total?: number
  pageSize?: number
  currentPage?: number
  multipleSelection?: any[]
  loading?: boolean
  pageCount?: number
  height?: number | string
  employeeIdArr?: any
  isMultiple?: any
}
const props = withDefaults(defineProps<PropsType>(), {
  total: 0,
  pageSize: 0,
  currentPage: 1,
  multipleSelection: undefined,
  loading: false,
  pageCount: undefined,
  height: undefined,
  employeeIdArr: undefined,
  isMultiple: true
})
const total = ref(props?.total || 0)
let data: any = ref(props.data)
const multipleSelection = ref<any[]>(props.multipleSelection || [])
const currentPage = ref(props.currentPage || 1)
const pageSize = ref(props.pageSize || 8)
const indexMethod = (index: number) => {
  return index + (currentPage.value - 1) * pageSize.value + 1
}
const formatter = (_row: any, _column: any, cellValue: any) => {
  const isNull = [undefined, null, ''].includes(cellValue)
  return !isNull ? cellValue : '--'
}
const selectID = (val: any) => {
  nextTick(() => {
    data.value.forEach((item: any) => {
      if (val.length) {
        if (val.includes(item.id)) {
          tablePagination.value.toggleRowSelection(item, true)
        }
      } else {
        tablePagination.value.clearSelection()
      }
    })
  })
}
const resizeStatus = ref(Date.now())
const changeResizeStatus = () => {
  resizeStatus.value = Date.now()
}
const debounceFn = debounce(changeResizeStatus)
onMounted(() => {
  window.addEventListener('resize', debounceFn)
})
onUnmounted(() => {
  window.removeEventListener('resize', debounceFn)
})
watch(
  () => props?.pageSize,
  (val: number | undefined) => {
    if (val) pageSize.value = val
  }
)
watch(
  () => props?.currentPage,
  (val: number | undefined) => {
    if (val) currentPage.value = val
  }
)
watch(
  () => props.data,
  () => {
    data.value = props.data
    total.value = props.total || 0

    if (props.employeeIdArr && props.employeeIdArr.length) {
      selectID(props.employeeIdArr)
    }
  },
  {
    deep: true,
    immediate: true
  }
)
const computedHeight = async () => {
  if (!props.height) {
    await nextTick()
    const tableEl = tablePagination.value.$el
    const parentEl = tableEl && tableEl.offsetParent

    if (!parentEl) return
    const parentHeight = parentEl.offsetHeight
    const tableTop = tableEl.offsetTop
    const paginationHeight = props.total ? 48 : 0
    const paddingBottomHeight =
      Number(
        window.getComputedStyle(parentEl)?.paddingBottom?.replace('px', '')
      ) || 0
    defaultHeight.value =
      parentHeight - tableTop - paginationHeight - paddingBottomHeight
  }
}

watch(
  [() => props.total, () => resizeStatus.value, () => searchFormStore.height],
  () => {
    computedHeight()
  },
  { deep: true, immediate: true }
)

const emits = defineEmits([
  'handleSizeChange',
  'handleCurrentChange',
  'update:pageSize',
  'update:currentPage',
  'handleSelectionChange',
  'update:multipleSelection',
  'array-item-click'
])
// 勾选
const handleSelectionChange = (val: any[]) => {
  if (props.isMultiple) {
    multipleSelection.value = val
  } else {
    nextTick(() => {
      let selectObj = val[val.length - 1]
      data.value.forEach((item: any) => {
        if (selectObj.id == item.id) {
          tablePagination.value.toggleRowSelection(item, true)
        } else {
          tablePagination.value.toggleRowSelection(item, false)
        }
      })
    })
  }
  emits('update:multipleSelection', val)
  emits('handleSelectionChange', val)
}
// 当前页展示条数改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  emits('update:pageSize', val)
  emits('handleSizeChange', {
    currentPage: currentPage.value,
    pageSize: val
  })
  props.employeeIdArr && selectID(props.employeeIdArr)
}
// 当前页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  emits('update:currentPage', val)
  emits('handleCurrentChange', {
    currentPage: val,
    pageSize: pageSize.value
  })
  props.employeeIdArr && selectID(props.employeeIdArr)
}
defineExpose({ tablePagination, computedHeight })
</script>
<style scoped lang="scss">
.vis-table-pagination {
  width: 100%;
}
.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #999;
  margin-right: 3px;
}
.tooltip-content {
  line-height: 1.3;
  a {
    color: #fff;
    display: block;
    padding: 2px 0;
    word-break: keep-all;
  }
  a:hover {
    color: #fff;
  }
}
.tooltip-wrapper {
  a {
    color: #29cca0;
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  a:hover {
    color: #29cca0;
  }
}
</style>
<style lang="scss">
.tooltip-style {
  max-width: 80%;
}
.image-cell .cell {
  white-space: nowrap;
  text-overflow: initial;
}
.el-pagination__editor.el-input {
  width: 64px;
}
</style>
