<template>
  <div>
    <div
      ref="cardRef"
      v-loading="loading"
      class="card"
      :style="{ height: height || defaultHeight }"
    >
      <div
        v-for="(item, ind) in data"
        :key="ind + (currentPage - 1) * pageSize + 1"
        class="card-item"
      >
        <el-card @click="clickCard(item)">
          <template v-if="titleKey" #header>
            <!-- 标题 -->
            <div class="card-header">
              <span class="card-title" :title="item[titleKey]">{{
                item[titleKey] || '--'
              }}</span>
              <!-- 自定义状态 -->
              <div v-if="showStatus" class="card-status">
                <slot name="status" :row="item"></slot>
              </div>
            </div>
          </template>
          <!-- 内容区域 -->
          <div class="card-body">
            <p
              v-for="column in columns"
              :key="column.id"
              class="card-column-item"
              :class="{ [column.className]: column.className }"
            >
              <template
                v-if="!column.notShowItemFn || !column.notShowItemFn(item)"
              >
                <span
                  class="card-label"
                  :style="`min-width:${cardLabelWidth}px`"
                  >{{ column.label || '--' }}</span
                >
                <span class="card-prop ellipsis" :title="item[column.prop]">
                  <!-- 自定义click -->
                  <template v-if="column.type === 'click'">
                    <span
                      class="cursor-pointer ellipsis"
                      :style="`color: ${item.color || '#29cca0'}`"
                      @click.stop="
                        () =>
                          typeof column.click === 'function'
                            ? column.click(item)
                            : undefined
                      "
                    >
                      <template v-if="column.slotName">
                        <slot
                          :name="column.slotName"
                          :row="item"
                          :column="column"
                        ></slot>
                      </template>
                      <template v-else>
                        {{ item[column.prop] || '--' }}
                      </template>
                    </span>
                  </template>
                  <!-- 自定义插槽 -->
                  <template v-else-if="column.slotName">
                    <slot
                      :name="column.slotName"
                      :row="item"
                      :column="column"
                    ></slot>
                  </template>
                  <template v-else> {{ item[column.prop] || '--' }} </template>
                </span>
              </template>
            </p>
          </div>
          <!-- footer/操作 -->
          <div class="card-footer">
            <slot name="footer" :row="item"></slot>
          </div>
        </el-card>
      </div>
      <!-- 无数据 -->
      <div
        v-if="data && data.length === 0 && finished"
        class="el-table__empty-block"
      >
        <span class="el-table__empty-text">暂无数据</span>
      </div>
    </div>

    <el-pagination
      v-if="total"
      ref="paginationRef"
      m-t-16px
      justify-end
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      v-bind="$attrs"
      :total="total"
      :pager-count="5"
      :page-count="pageCount"
      :prev-text="
        !($attrs.layout as string).includes('pager') ? '上一页' : undefined
      "
      :next-text="
        !($attrs.layout as string).includes('pager') ? '下一页' : undefined
      "
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script lang="ts" setup>
import { debounce } from '@/utils'
import useSearchFormStore from '@/store/searchForm'

const searchFormStore = useSearchFormStore()

const cardRef = ref<any>()
const paginationRef = ref<any>()
const defaultHeight = ref<number | string>('auto')

type PropsType = {
  data: Record<string, any>[]
  columns: any[]
  total?: number
  pageSize?: number
  currentPage?: number
  loading?: boolean
  finished?: boolean
  pageCount?: number
  height?: number | string
  showStatus?: boolean // 是否显示状态
  titleKey?: string // 标题key
  cardLabelWidth?: string // label的宽度 审批和工作票JSA票不一样
}
const props = withDefaults(defineProps<PropsType>(), {
  total: 0,
  pageSize: 0,
  currentPage: 1,
  loading: false,
  finished: false,
  pageCount: undefined,
  height: undefined,
  showStatus: true,
  titleKey: '',
  cardLabelWidth: '98'
})

const total = ref(props?.total || 0)
let data: any = ref(props.data)
const currentPage = ref(props.currentPage || 1)
const pageSize = ref(props.pageSize || 6)

const resizeStatus = ref(Date.now())
const changeResizeStatus = () => {
  resizeStatus.value = Date.now()
}
const debounceFn = debounce(changeResizeStatus)
onMounted(() => {
  window.addEventListener('resize', debounceFn)
})
onUnmounted(() => {
  window.removeEventListener('resize', debounceFn)
})

watch(
  () => props?.pageSize,
  (val: number | undefined) => {
    if (val) pageSize.value = val
  }
)
watch(
  () => props?.currentPage,
  (val: number | undefined) => {
    if (val) currentPage.value = val
  }
)
watch(
  () => props.data,
  () => {
    data.value = props.data
    total.value = props.total || 0
  },
  {
    deep: true,
    immediate: true
  }
)

const computedHeight = async () => {
  if (!props.height) {
    await nextTick()
    const cardEl = cardRef.value
    const parentEl = cardEl && cardEl.offsetParent

    if (!parentEl) return
    const parentHeight = parentEl.offsetHeight
    const tableTop = cardEl.offsetTop
    const paginationHeight = props.total ? 48 : 0
    const paddingBottomHeight =
      Number(
        window.getComputedStyle(parentEl)?.paddingBottom?.replace('px', '')
      ) || 0
    defaultHeight.value =
      parentHeight - tableTop - paginationHeight - paddingBottomHeight + 'px'
  }
}
watch(
  [() => props.total, () => resizeStatus.value, () => searchFormStore.height],
  () => {
    computedHeight()
  },
  { deep: true, immediate: true }
)
const emits = defineEmits([
  'handleSizeChange',
  'handleCurrentChange',
  'update:pageSize',
  'update:currentPage',
  'clickCard'
])
// 点击卡片 工作票有这个需求
const clickCard = (item: any) => {
  emits('clickCard', item)
}
// 当前页展示条数改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  emits('update:pageSize', val)
  emits('handleSizeChange', {
    currentPage: currentPage.value,
    pageSize: val
  })
}
// 当前页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  emits('update:currentPage', val)
  emits('handleCurrentChange', {
    currentPage: val,
    pageSize: pageSize.value
  })
}
</script>
<style scoped lang="scss">
$card-padding: 20px 16px;

.card {
  padding-top: 16px;
  margin-left: -12px;
  margin-right: -12px;
  display: flex;
  flex-wrap: wrap;
  overflow: auto;

  :deep(.el-card) {
    border-radius: 8px;
    border: 0;
  }

  :deep(.el-card__body),
  :deep(.el-card__header) {
    border-bottom: 0;
    padding: 0;
  }

  .card-item {
    padding-right: 12px;
    padding-left: 12px;
    max-width: 33.33%;
    flex: 0 0 33.33%;
    margin-bottom: 24px;
  }

  .card-header {
    position: relative;
    padding: $card-padding;
    padding-bottom: 0;

    .card-title {
      font-weight: bold;
      font-size: 16px;
      line-height: 24px;
      padding-right: 80px;
      height: 48px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .card-status {
      position: absolute;
      top: 20px;
      right: 16px;

      :deep(.el-tag.el-tag--info) {
        background-color: #ededed;
        border-color: #ededed;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }
  .card-body {
    padding: $card-padding;
    padding-bottom: 0;
    min-height: 143px;
  }

  .card-column-item {
    margin-bottom: 20px;
    display: flex;
    .card-label {
      display: inline-block;
      color: rgba(0, 0, 0, 0.45);
      min-width: 98px;
      margin-right: 16px;
    }
    .card-prop {
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .card-footer {
    padding: $card-padding;
    border-top: 1px solid #f4f4f4;
    text-align: center;
    position: relative;
  }
}
.el-table__empty-block {
  height: calc(100vh - 300px - 32px);
}
</style>
