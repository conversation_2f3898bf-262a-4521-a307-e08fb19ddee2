<template>
  <el-select
    :value="props.selectVal"
    :placeholder="props.placeholderVal"
    style="width: 100%"
    clearable
    remote
    :remote-method="remoteMethod"
    :filterable="props.queryData.val_ ? true : false"
    :loading="filterableLoading"
    remote-show-suffix
    @visible-change="filterableVisibleChange"
  >
    <div class="LabelSelectCpmBox">
      <el-option
        v-for="(item, keys) in filterableOptions"
        :key="keys"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </div>
  </el-select>
</template>
<script setup lang="ts">
import { rolling as rollingApi } from '@/api'

const props = defineProps({
  selectVal: {
    type: [String, Array],
    default: () => {
      return ''
    }
  },
  placeholderVal: {
    type: String,
    default: '请选择内容'
  },
  queryData: {
    type: Object,
    default: () => {
      return {
        val_: 'companyName',
        api_: 'getCompanyListPage',
        label_: 'companyName',
        value_: 'companyCode'
      }
    }
  },
  queryData2: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const strVal = ref('')
onMounted(() => {
  remoteMethod(strVal.value)
})
const filterableOptions = ref([])
const filterableLoading = ref(false)
const requsetObj = ref({
  pageNum: 1,
  pageSize: 100
})
const dom = ref()
const emit = defineEmits(['filterableOptions'])
const remoteMethod = async (query) => {
  strVal.value = query

  let params_ = { ...requsetObj.value }
  if (props.queryData.val_) {
    params_[props.queryData.val_] = query || ''
  }
  try {
    const { data } = await rollingApi[props.queryData.api_]({
      ...params_,
      ...props.queryData2
    })
    data?.data.records.forEach((item) => {
      item.label = item[props.queryData.label_]
      item.value = item[props.queryData.value_]
    })
    filterableOptions.value = data?.data.records || []
    emit('filterableOptions', filterableOptions.value)
  } catch (e) {
    filterableOptions.value = []
    console.log(e)
  }
}
/* 滚动监听函数 */
const scrollAddEventFn = (e) => {
  const self = e.target
  if (self.scrollHeight - self.scrollTop <= self.clientHeight) {
    requsetObj.value.pageSize += 100
    remoteMethod(strVal.value)
  }
}
const filterableVisibleChange = (isShow) => {
  if (isShow) {
    const parentDom = document.querySelectorAll(
      '.el-select-dropdown__wrap.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default'
    )
    setTimeout(() => {
      parentDom.forEach((e, idx) => {
        if (
          e.querySelector('.LabelSelectCpmBox') &&
          e.querySelector('.LabelSelectCpmBox').children &&
          e.querySelector('.LabelSelectCpmBox').children.length > 0
        ) {
          dom.value = parentDom[idx]
          dom.value.addEventListener('scroll', scrollAddEventFn, false)
        }
      })
    }, 0)
  }
}
</script>
<style lang="css" scoped></style>
