<script lang="ts" setup>
import visTablePagination from '@/components/table-pagination.vue'
import type { FormInstance } from 'element-plus'
import useProjectData from '@/hooks/useProjectData'
import usePaginationLocal from '@/hooks/usePaginationLocal'

// tab切换
const activeTabName = ref('list')
const tableRef = ref()
const selectedTableRef = ref()

const tableLoading = ref(false)
const {
  projectColumns,
  projectTotal,
  projectData,
  getProjectData,
  projectSearchData,
  projectPage,
  projectSizeChange,
  projectCurrentChange
} = useProjectData([tableLoading])
projectColumns.unshift({
  prop: 'selection',
  label: '选择',
  fixed: 'left',
  reserve: true,
  selectable: (row: { flag: boolean }) => {
    return !row.flag
  }
})

// 搜索
const searchFormRef = ref<FormInstance>()
const handleReset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  projectPage.pageSize = 10
  handleSearch(formEl)
}
const handleSearch = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  projectPage.pageNum = 1
  getProjectData()
}

// 已选择列表
const selectedColumns = projectColumns
const selectedData = ref<Record<string, any>[]>([])
const selectedTotal = computed(() => {
  return selectedData.value.length
})
const selectedDataCopy = ref<Record<string, any>[]>([])
const selectedTotalCopy = computed(() => {
  return selectedDataCopy.value.length
})
const {
  localData,
  handleSliceData,
  selectedSizeChange,
  selectedCurrentChange,
  pageObject: selectedPageObj
} = usePaginationLocal({ pageSize: 10 })

const handleSelectionChange = (rows: Record<string, any>[]) => {
  selectedData.value = [...rows]
  selectedDataCopy.value = [...rows]
  localHandleSearch(true)
  const _copy = [...selectedData.value]
  if (activeTabName.value === 'list') {
    selectedTableRef.value.tablePagination.clearSelection()
    _copy.forEach((row) => {
      selectedTableRef.value.tablePagination.toggleRowSelection(row, true)
    })
  }
}
const localSelectionChange = (rows: Record<string, any>[]) => {
  selectedData.value = [...rows]
  selectedDataCopy.value = [...rows]
  localHandleSearch(true)
  const _copy = [...selectedData.value]
  if (activeTabName.value === 'selected') {
    tableRef.value.tablePagination.clearSelection()
    _copy.forEach((row) => {
      tableRef.value.tablePagination.toggleRowSelection(row, true)
    })
  }
}

// 已选列表搜索
const localSearchFormRef = ref<FormInstance>()
const localSearchData = reactive<Record<string, any>>({
  assetSaleCompanyName: '',
  operationCompanyName: '',
  operationProjectCode: '',
  operationProjectName: '',
  serviceStatus: ''
})
const eqFields: string[] = ['stationType']
const localHandleReset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  selectedPageObj.pageSize = 10
  localHandleSearch(formEl)
}
const localHandleSearch = async (
  formEl: FormInstance | undefined | boolean
) => {
  if (!formEl) return
  selectedPageObj.pageNum = 1
  let notEmptyData: Record<string, any> = {}
  for (let key of Object.keys(localSearchData)) {
    if (localSearchData[key]) {
      notEmptyData[key] = localSearchData[key]
    }
  }
  selectedDataCopy.value = [...selectedData.value]
  if (notEmptyData.area) {
    notEmptyData.area = notEmptyData.area.join('')
  }
  for (let key of Object.keys(notEmptyData)) {
    selectedDataCopy.value = selectedDataCopy.value.filter((e) => {
      if (eqFields.includes(key)) {
        if (e[key] === notEmptyData[key]) {
          return true
        } else {
          return false
        }
      } else {
        if (e[key]?.includes(notEmptyData[key])) {
          return true
        } else {
          return false
        }
      }
    })
  }
  handleSliceData(selectedDataCopy.value)
}

defineExpose({
  selectedData
})
</script>
<template>
  <el-tabs v-model="activeTabName">
    <el-tab-pane label="运维项目列表" name="list">
      <div class="search-container">
        <el-form
          ref="searchFormRef"
          :inline="true"
          :model="projectSearchData"
          label-suffix=""
        >
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="运维项目编码" prop="operationProjectCode">
                <el-input
                  v-model="projectSearchData.operationProjectCode"
                  placeholder="请输入运维项目编码"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="运维项目名称" prop="operationProjectName">
                <el-input
                  v-model="projectSearchData.operationProjectName"
                  placeholder="请输入运维项目名称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" class="search-buttons text-right">
              <el-form-item style="width: auto; margin-right: 0">
                <el-button plain @click="handleReset(searchFormRef)"
                  >重置</el-button
                >
                <el-button type="primary" @click="handleSearch(searchFormRef)"
                  >查询</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <vis-table-pagination
        ref="tableRef"
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="projectPage.pageSize"
        :current-page="projectPage.pageNum"
        :columns="projectColumns"
        :total="projectTotal"
        :data="projectData"
        :show-overflow-tooltip="true"
        row-key="operationProjectCode"
        background
        class="vis-table-pagination"
        height="auto"
        @handle-selection-change="handleSelectionChange"
        @handle-size-change="projectSizeChange"
        @handle-current-change="projectCurrentChange"
      >
      </vis-table-pagination>
    </el-tab-pane>
    <el-tab-pane :label="`已选运维项目列表(${selectedTotal})`" name="selected">
      <div class="search-container">
        <el-form
          ref="localSearchFormRef"
          :inline="true"
          :model="localSearchData"
          label-suffix=""
        >
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="运维项目编码" prop="operationProjectCode">
                <el-input
                  v-model="localSearchData.operationProjectCode"
                  placeholder="请输入运维项目编码"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="运维项目名称" prop="operationProjectName">
                <el-input
                  v-model="localSearchData.operationProjectName"
                  placeholder="请输入运维项目名称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" class="search-buttons text-right">
              <el-form-item style="width: auto; margin-right: 0">
                <el-button plain @click="localHandleReset(localSearchFormRef)"
                  >重置</el-button
                >
                <el-button
                  type="primary"
                  @click="localHandleSearch(localSearchFormRef)"
                  >查询</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <vis-table-pagination
        ref="selectedTableRef"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="selectedPageObj.pageSize"
        :current-page="selectedPageObj.pageNum"
        :columns="selectedColumns"
        :total="selectedTotalCopy"
        :data="localData"
        :show-overflow-tooltip="true"
        row-key="operationProjectCode"
        background
        class="vis-table-pagination"
        height="auto"
        @handle-selection-change="localSelectionChange"
        @handle-size-change="selectedSizeChange"
        @handle-current-change="selectedCurrentChange"
      >
      </vis-table-pagination>
    </el-tab-pane>
  </el-tabs>
</template>
<style lang="scss" scoped>
.el-tabs :deep(.el-tabs__item) {
  padding: 0;
  padding-left: 20px;
}
</style>
