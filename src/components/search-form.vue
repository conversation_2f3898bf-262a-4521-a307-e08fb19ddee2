<!-- eslint-disable vue/no-mutating-props -->
<template>
  <el-form
    ref="formRef"
    :model="searchData"
    label-position="right"
    @submit.prevent="onSubmit"
  >
    <el-row :gutter="10">
      <template v-for="(item, i) in searchProps" :key="i">
        <el-col v-if="i < showSearchNum" :span="item.span || 8">
          <el-form-item
            :label="item.label"
            :label-width="item.width || labelWidth"
            :prop="item.prop"
          >
            <el-input
              v-if="!item.type || item.type === 'input'"
              v-model="searchData[item.prop]"
              :placeholder="item.placeholder || `请输入${item.label}`"
              :maxlength="item.maxlength || null"
              clearable
            />
            <el-input
              v-if="item.type === 'input2'"
              v-model="searchData[item.prop]"
              :placeholder="item.placeholder || `请输入${item.label}`"
              :maxlength="item.maxlength || null"
              clearable
              @keyup.enter="onSubmit"
            />

            <el-select
              v-if="item.type === 'select'"
              v-model="searchData[item.prop]"
              :placeholder="item.placeholder || `请选择${item.label}`"
              style="width: 100%"
              clearable
              :filterable="item.filterable ? true : false"
            >
              <el-option
                v-for="(item2, i2) in item.options"
                :key="i2"
                :label="item2.label"
                :value="item2.value"
              />
            </el-select>
            <!-- 虚拟下拉组件 -->
            <el-select-v2
              v-if="item.type === 'selectV2'"
              v-model="searchData[item.prop]"
              :options="item.options"
              :props="item.props || { label: 'label', value: 'value' }"
              :placeholder="item.placeholder || `请选择${item.label}`"
              clearable
              :filterable="item.filterable ? true : false"
            />
            <!-- 远程服务器调-下拉数据 -->
            <el-select
              v-if="item.type === 'filterableSelect'"
              v-model="searchData[item.prop]"
              :placeholder="item.placeholder || `请选择${item.label}`"
              style="width: 100%"
              clearable
              remote
              :remote-method="remoteMethod"
              :filterable="true"
              :loading="filterableLoading"
              remote-show-suffix
              @visible-change="filterableVisibleChange"
            >
              <div class="LabelSelectCpmBox">
                <el-option
                  v-for="(item2, i2) in filterableOptions"
                  :key="i2"
                  :label="item2.label"
                  :value="item2.value"
                >
                </el-option>
              </div>
            </el-select>
            <el-cascader
              v-if="item.type === 'cascader'"
              v-model="searchData[item.prop]"
              :options="item.options"
              :props="item.props || cascaderprops"
              :placeholder="item.placeholder || `请选择${item.label}`"
              :popper-append-to-body="false"
              :popper-class="item.class || null"
              style="width: 100%"
              clearable
            />
            <el-date-picker
              v-if="item.type === 'date'"
              v-model="searchData[item.prop]"
              :type="item.pickerType || 'date'"
              :value-format="item.format || 'YYYY-MM-DD'"
              :placeholder="item.placeholder || `请选择${item.label}`"
              :start-placeholder="item.startPlaceholder || `开始时间`"
              :end-placeholder="item.endPlaceholder || `结束时间`"
              style="width: 100%"
              :clearable="item.clearable === false ? false : true"
              :disabled-date="
                item.disabledDate ||
                (() => {
                  return false
                })
              "
            ></el-date-picker>
            <el-date-picker
              v-if="item.type === 'monthrange'"
              v-model="searchData[item.prop]"
              type="monthrange"
              :value-format="item.format || 'YYYY-MM'"
              :start-placeholder="item.startPlaceholder || `开始时间`"
              :end-placeholder="item.endPlaceholder || `结束时间`"
              style="width: 100%"
              :clearable="item.clearable"
              :disabled-date="item.disabledDate"
              @focus="item.focus"
              @blur="item.blur"
              @change="item.change"
              @calendar-change="item.calendarChange"
            ></el-date-picker>
            <StationSelect
              v-if="item.type === 'stationSelect'"
              ref="stationSelectRef"
              v-model="searchData[item.prop]"
            />
            <CompanySelect
              v-if="item.type === 'companySelect'"
              ref="companySelectRef"
              v-model="searchData[item.prop]"
              :item="item"
            />
            <ProjectSelect
              v-if="item.type === 'projectSelect'"
              ref="projectSelectRef"
              v-model="searchData[item.prop]"
              :item="item"
            />
          </el-form-item>
        </el-col>
      </template>
      <el-col
        v-if="showSearchNum != 3"
        :span="btnInfo.span || 24 - (Object.keys(searchProps).length % 3) * 8"
      >
        <el-row justify="end" align="middle" style="margin-bottom: 18px">
          <el-button
            :type="btnInfo.reset || 'default'"
            size="large"
            @click="onSubmit('reset')"
            >重置</el-button
          >
          <el-button
            :type="btnInfo.query || 'primary'"
            size="large"
            @click="onSubmit"
            >查询</el-button
          >
          <span
            v-if="Object.keys(searchProps).length > 3"
            class="show-more"
            @click="handleSearchNum"
            >收起</span
          >
        </el-row>
      </el-col>
    </el-row>
    <div v-if="showSearchNum == 3" class="extend">
      <el-button
        :type="btnInfo.reset || 'default'"
        size="large"
        @click="onSubmit('reset')"
      >
        重置
      </el-button>
      <el-button
        :type="btnInfo.query || 'primary'"
        size="large"
        @click="onSubmit"
      >
        查询
      </el-button>
      <span
        v-if="Object.keys(searchProps).length > 3"
        class="show-more"
        @click="handleSearchNum"
      >
        展开
      </span>
    </div>
  </el-form>
</template>

<script setup>
import StationSelect from '@/components/spic-station'
import useSearchFormStore from '@/store/searchForm'
import { stationLow as stationLowApi } from '@/api'
import { CompanySelect } from '@/components/spic-company'
import { ProjectSelect } from '@/components/spic-project'
const searchFormStore = useSearchFormStore()
const props = defineProps({
  //搜索信息
  searchData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  //表单数组
  searchProps: {
    type: Array,
    default: () => {
      return []
    }
  },
  //按钮信息
  btnInfo: {
    type: Object,
    default: () => {
      return {}
    }
  },
  labelWidth: {
    type: String,
    default: () => {
      return '80px'
    }
  }
})
const strVal = ref('')
let searchDataInitial
onMounted(() => {
  let isFilterableSelect = props.searchProps.some((item) => {
    return item.type == 'filterableSelect'
  })
  isFilterableSelect && remoteMethod(strVal.value)
  searchDataInitial = JSON.parse(JSON.stringify(props.searchData))
  Object.freeze(searchDataInitial)
})
const formRef = ref()
const stationSelectRef = ref()

const showSearchNum = ref(3)
const handleSearchNum = () => {
  if (showSearchNum.value == 3) {
    showSearchNum.value = 100
  } else {
    showSearchNum.value = 3
  }
  nextTick(() => {
    const height = formRef.value?.$el?.clientHeight || 0
    searchFormStore.updateHeight(height)
  })
}
const emit = defineEmits(['submitEmits'])
const onSubmit = (data) => {
  if (data == 'reset') {
    emit('submitEmits', { ...searchDataInitial })
    stationSelectRef.value?.[0] && stationSelectRef.value[0].resetFields()
  } else {
    emit('submitEmits', props.searchData)
  }
}
const cascaderprops = {
  checkStrictly: true
}
// ========= 远程服务器调-下拉数据 =========
const filterableOptions = ref([])
const filterableLoading = ref(false)
const requsetObj = ref({
  page: 1,
  size: 100
})
const dom = ref()
const remoteMethod = async (query) => {
  strVal.value = query
  try {
    const { data } = await stationLowApi.getCompanyListPage({
      pageNum: requsetObj.value.page,
      pageSize: requsetObj.value.size,
      companyName: query || ''
    })
    data?.data.forEach((item) => {
      item.label = item.companyName
      item.value = item.companyCode
    })
    filterableOptions.value = data?.data || []
  } catch (e) {
    filterableOptions.value = []
    console.log(e)
  }
}
/* 滚动监听函数 */
const scrollAddEventFn = (e) => {
  const self = e.target
  if (self.scrollHeight - self.scrollTop <= self.clientHeight) {
    requsetObj.value.size += 100
    remoteMethod(strVal.value)
  }
}
const filterableVisibleChange = (isShow) => {
  if (isShow) {
    const parentDom = document.querySelectorAll(
      '.el-select-dropdown__wrap.el-scrollbar__wrap.el-scrollbar__wrap--hidden-default'
    )
    setTimeout(() => {
      parentDom.forEach((e, idx) => {
        if (
          e.querySelector('.LabelSelectCpmBox') &&
          e.querySelector('.LabelSelectCpmBox').children &&
          e.querySelector('.LabelSelectCpmBox').children.length > 0
        ) {
          dom.value = parentDom[idx]
          dom.value.addEventListener('scroll', scrollAddEventFn, false)
        }
      })
    }, 0)
  }
}
</script>
<style lang="scss" scoped>
.show-more {
  cursor: pointer;
  font-size: 16px;
  color: #53d0ad;
  margin-left: 10px;
}
.el-form {
  display: flex;
  .el-row {
    flex: auto;
  }
  .extend {
    flex: none;
    margin-left: 15px;
    .el-button {
      width: 72px !important;
    }
    .el-button--default {
      border-color: rgba(174, 175, 180, 1) !important;
      color: rgba(0, 0, 0, 0.85) !important;
    }
  }
}
.el-button + .el-button {
  margin-left: 10px;
}

:deep(.el-form-item--default) {
  margin-bottom: 24px;
}
</style>
