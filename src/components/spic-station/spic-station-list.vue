<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import type { FormInstance } from 'element-plus'
import * as api from '@/api/index.ts'
import address from '@/api/data/area.json'

const props = withDefaults(
  defineProps<{
    value?: string | Record<string, any>
    label?: string
    valueName?: string
    labelName?: string
  }>(),
  {
    value: '',
    label: '',
    valueName: 'stationCode',
    labelName: 'stationName'
  }
)
const searchFormRef = ref<FormInstance>()
const tableRef = ref<any>()
const searchData = reactive({
  stationName: '',
  address: '',
  pageNum: 1,
  pageSize: 50
})
const handleReset = () => {
  if (!searchFormRef.value) return
  searchFormRef.value.resetFields()
  searchData.pageNum = 1
  searchData.pageSize = 50
  handleSearch()
}
const handleSearch = async () => {
  if (!searchFormRef.value) return
  searchData.pageNum = 1
  searchData.pageSize = 50
  await getTableData()
}

const columns = [
  {
    prop: 'radio-selection',
    label: ''
  },
  {
    prop: 'stationCode',
    label: '电站编号',
    minWidth: '120'
  },
  {
    prop: 'stationName',
    label: '电站名称',
    minWidth: '120'
  },
  {
    prop: 'stationType',
    label: '电站类型',
    formatter: (row: any) => {
      if (row.stationType === 0) {
        return '户用'
      }
      if (row.stationType >= 1) {
        return '工商业'
      }
      return row.stationType || '--'
    }
  },
  {
    prop: 'capins',
    label: '装机容量(kW)',
    minWidth: '120'
  },
  {
    prop: 'area',
    label: '行政区划',
    minWidth: '160'
  }
]
const tableData = reactive<{ total: number; data: any[] }>({
  data: [],
  total: 0
})
const tableLoading = ref(false)
const getTableData = async () => {
  const [provinceCode, cityCode, areaCode] = searchData.address || []
  const requestData: any = {
    ...searchData,
    code: localStorage.getItem('PVOM_COMPANY_CODE'),
    provinceCode: provinceCode || '',
    cityCode: cityCode || '',
    areaCode: areaCode || ''
  }
  delete requestData.address

  const {
    response: { data }
  } = await api.post({
    url: '/operate/station-group-user/stationListByUser',
    data: requestData,
    loading: [tableLoading]
  })
  tableData.total = data.total || 0
  tableData.data = data.data || []
  selectedData.value = selectedData.value[props.valueName]
    ? selectedData.value
    : typeof props.value === 'object'
    ? props.value
    : {
        [props.valueName]: props.value,
        [props.labelName]: props.label
      }
  tableRef.value &&
    tableRef.value.tablePagination.setCurrentRow(selectedData.value)
}
const handleSizeChange = async (params: any) => {
  searchData.pageNum = 1
  searchData.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.pageNum = params.currentPage
  searchData.pageSize = params.pageSize
  getTableData()
}
const selectedData = ref<Record<string, any>>({})
const selectTableCell = async (row: Record<string, any>) => {
  selectedData.value = { ...row }
}

onMounted(async () => {
  getTableData()
})

defineExpose({
  selectedData
})
</script>

<template>
  <div class="wrapper">
    <el-form
      ref="searchFormRef"
      :inline="true"
      :model="searchData"
      label-suffix=""
    >
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="电站名称" prop="stationName">
            <el-input
              v-model="searchData.stationName"
              placeholder="请输入电站名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="行政区划" prop="address">
            <el-cascader
              v-model="searchData.address"
              placeholder="请选择行政区划"
              :options="address as any[]"
              clearable
              filterable
              :props="{
                value: 'value',
                label: 'label',
                children: 'children',
                expandTrigger: 'hover',
                checkStrictly: true,
                emitPath: true
              }"
              popper-class="popper-width-540px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button plain size="large" @click="handleReset()"
              >重置</el-button
            >
            <el-button type="primary" size="large" @click="handleSearch()"
              >查询</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <vis-table-pagination
      ref="tableRef"
      :loading="tableLoading"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchData.pageSize"
      :current-page="searchData.pageNum"
      :columns="columns"
      :total="tableData.total"
      :data="tableData.data"
      :show-overflow-tooltip="true"
      :row-key="valueName"
      highlight-current-row
      background
      height="calc(100vh - 400px)"
      class="vis-table-pagination"
      @row-click="selectTableCell"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    >
    </vis-table-pagination>
  </div>
</template>
<style lang="scss" scoped>
.wrapper {
  padding: 0 15px;
  padding-bottom: 15px;
  height: 100%;
  overflow: hidden;
}
:deep(.vis-table-pagination) {
  .el-table__row .radio-selection {
    .cell {
      display: none;
    }
    &::before {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      border: solid 1px rgba(0, 0, 0, 0.15);
      border-radius: 50%;
      cursor: pointer;
    }
  }
  .el-table__row.current-row .radio-selection::before {
    border: solid 3px rgba(41, 204, 160, 1);
  }
}
.file-wrapper {
  display: inline-flex;
  align-items: center;
  background: #f6f8fa;
  padding: 3px 10px;
  vertical-align: middle;
  border-radius: 4px;
  .link {
    display: inline-block;
    margin-right: 5px;
    width: 16px;
    height: 16px;
  }
  span {
    display: inline-block;
    max-width: 355px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #2acba0;
    align-items: center;
  }
  .download {
    display: inline-block;
    margin-left: 5px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
}
</style>
