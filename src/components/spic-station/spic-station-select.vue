<script lang="ts" setup>
import StationDialog from './spic-station-dialog.vue'

interface Props {
  valueName?: string
  labelName?: string
}
const value = defineModel<string | Record<string, any>>({ required: true })
const label = defineModel<string>('label')
const props = withDefaults(defineProps<Props>(), {
  valueName: 'stationCode',
  labelName: 'stationName'
})

const dialogVisible = ref(false)

const emit = defineEmits(['change'])

const selectedData = ref<Record<string, any>>({})
const handleChange = (row: any) => {
  selectedData.value = { ...row }
  if (typeof value.value === 'object') {
    value.value = selectedData.value
  }
  if (typeof value.value === 'string') {
    value.value = selectedData.value?.[props.valueName] || null
    label.value = selectedData.value?.[props.labelName] || null
    emit('change', selectedData.value)
  }
}
const resetFields = () => {
  selectedData.value = {}
  value.value = typeof value.value === 'object' ? {} : ''
  label.value = ''
}
const inputRef = ref()
onMounted(() => {
  inputRef.value && (inputRef.value.input.readOnly = true)
})
defineExpose({
  selectedData,
  resetFields
})
</script>
<template>
  <el-input
    ref="inputRef"
    v-model="label"
    clearable
    placeholder="请选择电站"
    class="station-input5468"
    @clear="resetFields"
  >
    <template #append>
      <el-button style="color: #29cca0" @click="() => (dialogVisible = true)">
        电站选择
      </el-button>
    </template>
  </el-input>
  <StationDialog
    v-model="dialogVisible"
    :value="value"
    :label="label"
    :value-name="props.valueName"
    :label-name="props.labelName"
    @change="handleChange"
  />
</template>
