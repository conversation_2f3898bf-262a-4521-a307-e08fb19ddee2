<script lang="ts" setup>
import StationList from './spic-station-list.vue'

const dialogVisible = defineModel<boolean>({ required: true })
const props = withDefaults(
  defineProps<{
    value?: string | Record<string, any>
    label?: string
    valueName?: string
    labelName?: string
  }>(),
  {
    value: '',
    label: '',
    valueName: 'stationCode',
    labelName: 'stationName'
  }
)

const emit = defineEmits(['change'])

const stationListRef = ref()
const closeDialog = () => {
  dialogVisible.value = false
}
const onSubmit = () => {
  const selectedData = stationListRef.value.selectedData
  if (Object.keys(selectedData).length == 0) {
    ElMessage({
      type: 'warning',
      message: '请选择电站'
    })
    return
  }
  emit('change', selectedData)
  closeDialog()
}
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    title="电站选择"
    align-center
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="800px"
    class="vis-dialog record-dialog"
  >
    <StationList
      v-if="dialogVisible"
      ref="stationListRef"
      :value="props.value"
      :label="props.label"
      :value-name="props.valueName"
      :label-name="props.labelName"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<style lang="scss" scope></style>
