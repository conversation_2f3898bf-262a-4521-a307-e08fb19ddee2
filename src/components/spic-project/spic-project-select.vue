<script lang="ts" setup>
import useProjectData from './useProjectData.ts'

const { projectData } = useProjectData()
const model = defineModel<string>({ required: true })
defineProps({
  item: {
    type: Object,
    default: () => {}
  }
})
</script>
<template>
  <el-select-v2
    v-model="model"
    :options="
      projectData.map((item: any) => ({
        label: item.projectName,
        value: item.projectUniqueId
      }))
    "
    :placeholder="item?.placeholder || `请选择${item.label}`"
    clearable
    :filterable="true"
  />
</template>
