<script lang="ts" setup>
import type { UploadRawFile, UploadUserFile } from 'element-plus'
import uploadSvg from '@/assets/images/upload/upload.svg'
import viewSvg from '@/assets/images/upload/view.svg'
import deleteSvg from '@/assets/images/upload/delete.svg'
import downloadSvg from '@/assets/images/upload/download.svg'
import fileBgSvg from '@/assets/images/upload/fileBg.svg'
import { baseApi } from '@/api'
import axios from 'axios'

interface Props {
  // 类型 TODO 'auto'
  type?: 'image' | 'file' | 'auto'
  // 文件大小，单位 M
  fileSize?: number
  // 文件数量
  limit?: number // 最多文件个数
  // 文件类型
  fileExt?: string[] | null
  // 禁用
  disabled?: boolean
  uploadDisplay?: boolean
  handleRemoveFn?: any
}
const files = defineModel<string[]>()
const props = withDefaults(defineProps<Props>(), {
  type: 'image',
  fileSize: 5,
  limit: 5,
  fileExt: () => ['jpg', 'jpeg', 'png'],
  disabled: false,
  handleRemoveFn: undefined,
  uploadDisplay: false
})
const fileList = ref<UploadUserFile[]>([])
const getFilesUrl = () => {
  axios
    .all(
      fileList.value.map((e: any) => {
        return baseApi.downloadFile(e.fileName + '@' + e.originalFileName)
      })
    )
    .then(
      axios.spread(function (...data) {
        fileList.value = fileList.value.map((e: any, i: number) => {
          return {
            ...e,
            fileUrl: window.URL.createObjectURL(data[i].response.data)
          }
        })
      })
    )
}

const beforeUpload = (rawFile: UploadRawFile) => {
  unWatch()
  const fileExt = rawFile.name.replace(/.+\./, '')
  const fileSize = rawFile.size / 1024 / 1024 < props.fileSize
  if (Array.isArray(props.fileExt) && props.fileExt.length) {
    const extension = props.fileExt.includes(fileExt.toLowerCase())
    if (!extension) {
      ElMessage({
        message: '不支持的文件类型',
        type: 'error'
      })
      return false
    }
  }
  if (!fileSize) {
    ElMessage({
      message: `文件大小不能超过${props.fileSize}MB`,
      type: 'error'
    })
    return false
  }
}
const handleUpload = async (options: any): Promise<any> => {
  const { file } = options
  let formData = new FormData()
  formData.append('file', file)
  fileList.value.pop()
  const { data } = await baseApi.uploadFile(formData)
  if (data) {
    if (props.type === 'image') {
      const { response } = await baseApi.downloadFile(
        data.fileName + '@' + data.originalFileName
      )
      data.fileUrl = window.URL.createObjectURL(response.data)
    }
    fileList.value.push(data)
    ElMessage({
      message: `上传成功!`,
      type: 'success'
    })
  }
}

const handleRemove = async (file: { id: number; fileName: string }) => {
  unWatch()
  const index = fileList.value.findIndex(
    (e: any) => e.fileName === file.fileName
  )
  fileList.value.splice(index, 1)
  ElMessage({
    message: `删除成功!`,
    type: 'success'
  })
  props.handleRemoveFn && props.handleRemoveFn(fileList.value)
}

const showViewElement = ref(false)
const initialIndex = ref(0)
const handleView = (file: any) => {
  initialIndex.value = fileList.value.findIndex(
    (e: any) => e.fileName === file.fileName
  )
  showViewElement.value = true
}

const handleDownload = async (file: any) => {
  const {
    response: { data }
  } = await baseApi.downloadFile(file.fileName + '@' + file.originalFileName)
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(data)
  link.download = file.originalFileName
  link.click()
  data &&
    ElMessage({
      message: `下载成功!`,
      type: 'success'
    })
}
const uploadDisplay = computed(() => {
  if (props.disabled) return 'none'
  if (fileList.value.length >= props.limit) return 'none'
  if (props.uploadDisplay) return 'none'
  return 'inline-flex'
})

const unWatch = watch(
  () => files.value?.length,
  (val) => {
    fileList.value = val
      ? (files.value?.map((e: any) => {
          if (typeof e === 'string') {
            return {
              fileName: e.split('@')?.[0] || '',
              originalFileName: e.split('@')?.[1] || ''
            }
          }
          return e
        }) as any[])
      : []
    props.type === 'image' && getFilesUrl()
  },
  {
    immediate: true
  }
)

watch(
  () => fileList.value.length,
  () => {
    files.value = fileList.value.map((e: any) => {
      return e.fileName + '@' + e.originalFileName
    })
  }
)
</script>
<template>
  <div class="spic-upload">
    <el-upload
      v-model:file-list="fileList"
      list-type="picture-card"
      :limit="limit"
      :disabled="fileList.length >= limit"
      :auto-upload="true"
      :before-upload="beforeUpload"
      :http-request="handleUpload"
      :accept="
        Array.isArray(props.fileExt) && props.fileExt.length
          ? props.fileExt.map((e) => `.${e}`).join(',')
          : undefined
      "
    >
      <el-image :src="uploadSvg" class="upload-icon" />
      <template #file="{ file }">
        <div class="el-upload-list__item-thumbnail">
          <el-image
            v-if="props.type === 'image'"
            :src="file.fileUrl"
            fit="cover"
          >
            <template #error>
              <div class="el-image__error">加载中...</div>
            </template>
            <template #placeholder>
              <img :src="file.fileUrl" class="image" />
            </template>
          </el-image>
          <div v-if="props.type === 'file'" class="file">
            <div class="icon" :style="`background-image: url(${fileBgSvg})`">
              {{
                file.fileName?.split('.')?.at(-1)?.slice(0, 3)?.toUpperCase() ||
                ''
              }}
            </div>
            <div class="title">{{ file.originalFileName }}</div>
          </div>
        </div>
        <span class="el-upload-list__item-actions">
          <span
            v-if="props.type === 'image'"
            class="el-upload-list__item-preview"
            @click="handleView(file)"
          >
            <el-image :src="viewSvg" class="operate-icon" />
          </span>
          <span
            v-if="props.type === 'file'"
            class="el-upload-list__item-download"
            @click="handleDownload(file)"
          >
            <el-image :src="downloadSvg" class="operate-icon" />
          </span>
          <el-popconfirm
            v-if="!disabled"
            title="确认删除？"
            @confirm="handleRemove(file)"
          >
            <template #reference>
              <span class="el-upload-list__item-delete">
                <el-image :src="deleteSvg" class="operate-icon" />
              </span>
            </template>
          </el-popconfirm>
        </span>
      </template>
    </el-upload>

    <el-image-viewer
      v-if="showViewElement"
      :url-list="fileList.map((e: any) => e.fileUrl)"
      :initial-index="initialIndex"
      infinite
      teleported
      :z-index="3000"
      @close="() => (showViewElement = false)"
    />
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-upload-list--picture-card) {
  --el-upload-list-picture-card-size: 80px;
  .el-image {
    width: 100%;
    height: 100%;
  }
  .image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .file {
    height: 80px;
    .icon {
      background-repeat: no-repeat;
      background-size: contain;
      width: 50px;
      height: 50px;
      margin: 5px auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 600;
    }
    .title {
      font-size: 12px;
      width: 80px;
      padding: 0 3px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 20px;
      text-align: center;
    }
  }
  .upload-icon {
    width: 16px;
    height: 16px;
  }
  .operate-icon {
    width: 20px;
    height: 20px;
  }
  .el-upload-list__item-actions {
    span + span {
      margin-left: 8px;
    }
  }
}
:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 80px;
  display: v-bind(uploadDisplay);
}
</style>
