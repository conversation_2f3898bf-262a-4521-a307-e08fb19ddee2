<template>
  <div class="markdown-renderer" v-html="renderedContent"></div>
</template>

<script setup lang="ts">
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

// 注册常用语言 - 使用动态导入
import javascript from 'highlight.js/lib/languages/javascript'
import typescript from 'highlight.js/lib/languages/typescript'
import python from 'highlight.js/lib/languages/python'
import java from 'highlight.js/lib/languages/java'
import json from 'highlight.js/lib/languages/json'
import xml from 'highlight.js/lib/languages/xml'
import sql from 'highlight.js/lib/languages/sql'
import bash from 'highlight.js/lib/languages/bash'

hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('typescript', typescript)
hljs.registerLanguage('python', python)
hljs.registerLanguage('java', java)
hljs.registerLanguage('json', json)
hljs.registerLanguage('xml', xml)
hljs.registerLanguage('sql', sql)
hljs.registerLanguage('bash', bash)

// Props定义
interface Props {
  content: string
  options?: any
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  options: () => ({})
})

// 配置markdown-it
const md = new MarkdownIt({
  html: true,        // 启用HTML标签
  xhtmlOut: true,    // 使用XHTML输出
  breaks: true,      // 转换换行符为<br>
  linkify: true,     // 自动转换URL为链接
  typographer: true, // 启用一些语言中性的替换和引号美化
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return '<pre class="hljs"><code>' +
               hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
               '</code></pre>'
      } catch (__) {}
    }
    return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>'
  }
})

// 计算渲染后的内容
const renderedContent = computed(() => {
  if (!props.content) return ''
  return md.render(props.content)
})
</script>

<style lang="scss" scoped>
.markdown-renderer {
  line-height: 1.6;
  color: #303133;

  // 标题样式
  :deep(h1) {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #e4e7ed;
  }

  :deep(h2) {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 18px 0 14px 0;
    padding-bottom: 6px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(h3) {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin: 16px 0 12px 0;
  }

  :deep(h4) {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 14px 0 10px 0;
  }

  :deep(h5), :deep(h6) {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin: 12px 0 8px 0;
  }

  // 段落样式
  :deep(p) {
    margin: 12px 0;
    line-height: 1.6;
  }

  // 列表样式
  :deep(ul), :deep(ol) {
    margin: 12px 0;
    padding-left: 20px;

    li {
      margin: 4px 0;
      line-height: 1.5;
    }
  }

  :deep(ul) {
    list-style-type: disc;

    ul {
      list-style-type: circle;

      ul {
        list-style-type: square;
      }
    }
  }

  // 代码样式
  :deep(code) {
    background-color: #f5f7fa;
    color: #e6a23c;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
  }

  :deep(pre) {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    margin: 16px 0;
    overflow-x: auto;

    code {
      background: none;
      color: inherit;
      padding: 0;
      border-radius: 0;
      font-size: 14px;
    }
  }

  // 引用样式
  :deep(blockquote) {
    border-left: 4px solid #409eff;
    background-color: #f4f4f5;
    margin: 16px 0;
    padding: 12px 16px;
    color: #606266;

    p {
      margin: 0;
    }
  }

  // 表格样式
  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;

    th, td {
      border: 1px solid #ebeef5;
      padding: 8px 12px;
      text-align: left;
    }

    th {
      background-color: #f5f7fa;
      font-weight: 600;
      color: #303133;
    }

    tr:nth-child(even) {
      background-color: #fafafa;
    }
  }

  // 分割线样式
  :deep(hr) {
    border: none;
    border-top: 1px solid #ebeef5;
    margin: 20px 0;
  }

  // 链接样式
  :deep(a) {
    color: #409eff;
    text-decoration: none;

    &:hover {
      color: #66b1ff;
      text-decoration: underline;
    }
  }

  // 强调样式
  :deep(strong) {
    font-weight: 600;
    color: #303133;
  }

  :deep(em) {
    font-style: italic;
    color: #606266;
  }

  // 删除线
  :deep(del) {
    text-decoration: line-through;
    color: #909399;
  }
}

// 代码高亮主题适配 - 与Element Plus主题保持一致
:deep(.hljs) {
  background: #f8f9fa !important;
  color: #383a42;
  border-radius: 6px;
}

// 针对问题分析报告的特殊样式优化
.markdown-renderer {
  // 确保在灰色背景容器中的可读性
  &.in-gray-container {
    :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
      color: #303133;
    }

    :deep(p) {
      color: #495057;
    }

    :deep(code) {
      background-color: #ffffff;
      border: 1px solid #e4e7ed;
    }

    :deep(pre) {
      background-color: #ffffff;
      border: 1px solid #e4e7ed;
    }

    :deep(blockquote) {
      background-color: #ffffff;
      border-left: 4px solid #409eff;
    }

    :deep(table) {
      th {
        background-color: #ffffff;
      }

      tr:nth-child(even) {
        background-color: #f9f9f9;
      }
    }
  }
}
</style>
