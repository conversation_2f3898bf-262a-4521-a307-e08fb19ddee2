/**
 * 点击区域外部
 * @param app
 */
export default function (app: any) {
  app.directive('click-outside', {
    mounted(el: any, binding: any) {
      function eventHandler(e: any) {
        if (window.getComputedStyle(el).display !== 'none') {
          if (el.contains(e.target)) {
            return false
          }
          if (binding.value && typeof binding.value === 'function') {
            binding.value(e)
          }
        }
      }
      el.clickOutside = eventHandler
      document.addEventListener('click', eventHandler)
    },
    unmounted: (el: any) => {
      document.removeEventListener('click', el.clickOutside)
      delete el.clickOutside
    }
  })
}
