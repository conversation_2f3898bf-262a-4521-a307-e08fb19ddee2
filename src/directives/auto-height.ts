import { debounce } from '@/utils'
import useSearchFormStore from '@/store/searchForm'

/**
 * 自动计算高度指令
 * @param app
 */
export default function (app: any) {
  const searchFormStore = useSearchFormStore()

  app.directive('auto-height', {
    mounted: (el: any, binding: any) => {
      setHeight(el, binding)
      el.resizeListener = debounce(() => setHeight(el, binding), 100)
      window.addEventListener('resize', el.resizeListener)

      el.unWatch = watch(
        () => searchFormStore.height,
        () => {
          setHeight(el, binding)
        }
      )
    },
    unmounted: (el: any) => {
      window.removeEventListener('resize', el.resizeListener)
      el.unWatch()
      delete el.resizeListener
      delete el.unWatch
    }
  })

  const setHeight = (
    el: any,
    binding?: { value: { top: number; bottom: number } }
  ) => {
    const parentEl = el.offsetParent
    let parentHeight = parentEl?.offsetHeight
    const offsetTop = el.offsetTop

    if (Array.from(parentEl.classList).includes('page-container')) {
      parentHeight = window.innerHeight
    }

    const parentBottomHeight =
      Number(
        parentEl &&
          window.getComputedStyle(parentEl).paddingBottom.replace('px', '')
      ) || 0

    const elBottomHeight =
      Number(window.getComputedStyle(el).marginBottom.replace('px', '')) || 0

    const bottom = binding?.value?.bottom || 0
    const top = binding?.value?.top || 0

    el.style.height = `${
      parentHeight -
      offsetTop -
      parentBottomHeight -
      elBottomHeight -
      top -
      bottom
    }px`
  }
}
