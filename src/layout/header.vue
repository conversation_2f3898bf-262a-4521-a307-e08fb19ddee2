<script setup lang="ts">
import request from '@/utils/request'
import { getParents } from '@/utils/tree'
import useCompanyCodeStore from '@/store/companyCode'

const route = useRoute()
const router = useRouter()

const breadcrumbData = ref<Obj[]>([])

const titleCompile = (title: Obj | string) => {
  if (typeof title !== 'object') return title

  const type: 'params' | 'query' = title.type || 'params'
  const prop = title.prop || ''
  const titleKey = (route[type]?.[prop] as string) || ''
  return title?.data?.[titleKey] || ''
}

const pathCompile = (_path: string, index: number) => {
  const n = index - breadcrumbData.value.length + 1
  router.go(n)
}

watchEffect(() => {
  breadcrumbData.value = route.matched.filter((e) => e.meta?.title) || []
  if (breadcrumbData.value?.length > 1 && route.query.parentPageName) {
    breadcrumbData.value[breadcrumbData.value.length - 2].meta.title =
      route.query.parentPageName
  }
})

const stSelectStatus = ref(false)
const hideBack = ref(false)
const treeData = ref<any>([])
const treeRef = ref()
const treeProps = ref({
  children: 'treeChild',
  label: 'name'
})
const selectedId = ref<any>(0)
const selectedCode = ref<string>('')
const selectedName = ref<string>('')
const defaultExpandedKeys = ref<number[]>([0])
const getTreeData = async () => {
  const { data } =
    (await request({
      url: '/operate/operation-project/getOperationCompanyTreeV3',
      method: 'get'
    })) || []
  if (route.path === '/examine-management') {
    treeData.value = data?.data[0].treeChild || []
  } else {
    treeData.value = data?.data || []
  }
  if (treeData.value.length === 0) {
    router.replace('/no-auth?path=' + route.path)
  }
  if (
    route.path === '/score/operatorRating' ||
    route.path === '/warning/index' ||
    route.path === '/warning-record/index' ||
    route.path === '/examine-management' ||
    route.path === '/location/index'
  ) {
    treeData.value = filterTree(treeData.value)
  }
  if (localStorage.getItem('PVOM_COMPANY_ID')) {
    if (
      route.path !== '/score/operatorRating' &&
      route.path !== '/warning/index' &&
      route.path !== '/warning-record/index' &&
      route.path !== '/examine-management' &&
      route.path !== '/location/index'
    ) {
      changeTreeNode({
        id: localStorage.getItem('PVOM_COMPANY_ID'),
        code: localStorage.getItem('PVOM_COMPANY_CODE'),
        name: localStorage.getItem('PVOM_COMPANY_NAME')
      })
    } else {
      if (!localStorage.getItem('PVOM_COMPANY_CODE')?.startsWith('YWDZ-')) {
        changeTreeNode({
          id: localStorage.getItem('PVOM_COMPANY_ID'),
          code: localStorage.getItem('PVOM_COMPANY_CODE'),
          name: localStorage.getItem('PVOM_COMPANY_NAME')
        })
      } else {
        changeTreeNode(treeData.value?.[0] || {})
      }
    }
  } else {
    changeTreeNode(treeData.value?.[0] || {})
  }
}

function filterTree(data: any = []) {
  var newTree = data.filter((e: any) => !e.code.startsWith('YWDZ-'))
  newTree.forEach(
    (e: any) => e.treeChild && (e.treeChild = filterTree(e.treeChild))
  )
  return newTree
}

const paths = [
  '/station-low',
  '/station/all',
  '/station/okay',
  '/station/await',
  '/score/operatorRating',
  '/score/plantRating',
  '/problem/accident',
  '/problem/trouble',
  '/tools',
  '/store/enter',
  '/store/inventory',
  '/store/out',
  '/store/num',
  '/device',
  '/inspection',
  '/inspection/',
  '/overhaul',
  '/ticket/JSA',
  '/ticket/work',
  '/workorder/listIndex',
  '/deeect/listIndex',
  '/ticket/two',
  '/fault-list',
  '/ticket/handle',
  '/examine-management',
  '/templates/xj',
  '/templates/dx',
  '/location/index'
]
const showTree = computed(() => {
  return paths.includes(route.path)
})

const companyCode = useCompanyCodeStore()
onMounted(() => {
  if (route.query && route.query.hideBack === 'true') {
    hideBack.value = true
  }
  showTree.value && getTreeData()
})

const changeTreeNode = async (data: any = {}) => {
  try {
    selectedId.value = data.id ?? 0
    selectedCode.value = data.code ?? ''
    selectedName.value = data.name ?? ''
    // await getExistNode(data.code)
    // web 端共享参数
    localStorage.setItem('PVOM_COMPANY_ID', selectedId.value)
    localStorage.setItem('PVOM_COMPANY_CODE', selectedCode.value)
    localStorage.setItem('PVOM_COMPANY_NAME', selectedName.value)
    companyCode.update(selectedCode.value)
    nextTick(() => {
      treeRef.value?.setCurrentKey(selectedId.value, false)
      const node = treeRef.value?.getNode(selectedId.value)
      defaultExpandedKeys.value = getParents(node)
      stSelectStatus.value = false
    })
  } catch (e) {}
}
</script>

<template>
  <div v-if="!hideBack" class="page-header">
    <div class="header-title">
      <template v-if="breadcrumbData.length > 1">
        <el-icon class="backIcon"><ArrowLeft /></el-icon>
        <span class="goback" @click="() => router.go(-1)">返回上一级</span>
        <span class="detailLine">|</span>
      </template>
      <template
        v-for="(item, index) in breadcrumbData"
        :key="item.name + '_' + index"
      >
        <template v-if="index < breadcrumbData.length - 1">
          <a @click="pathCompile(item.path, index)">
            {{ titleCompile(item.meta.title) }}
          </a>
          <span>></span>
        </template>
        <span v-else>{{ titleCompile(item.meta.title) }}</span>
      </template>
    </div>
    <div class="operate-container">
      <div v-if="showTree" class="stqh">
        <span
          class="label"
          :title="selectedName"
          @click.stop="stSelectStatus = !stSelectStatus"
        >
          {{ selectedName }}
        </span>
        <div
          v-show="stSelectStatus"
          v-click-outside="() => (stSelectStatus = false)"
          class="st-select-container"
        >
          <el-scrollbar max-height="calc(100vh - 100px)">
            <el-tree
              ref="treeRef"
              :data="treeData"
              :props="treeProps"
              node-key="id"
              :accordion="true"
              :current-node-key="selectedId"
              :default-expanded-keys="defaultExpandedKeys"
              @node-click="changeTreeNode"
            >
              <template #default="{ node }">
                <div class="custom-tree-node">
                  <div class="label" :title="node.label">{{ node.label }}</div>
                </div>
              </template>
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-header {
  height: 60px;
  display: flex;
  align-items: center;
  background: #fff;
  box-shadow: 0 0 8px 0 rgb(21 102 80 / 10%);
  .header-title {
    padding-left: 24px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    .backIcon {
      position: relative;
      top: 2px;
    }
    .goback {
      color: #333;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;
    }
    .detailLine {
      color: #e4e4e4;
      margin: 0 6px 0 12px;
    }
    a {
      color: #999;
      margin-left: 6px;
    }
    span {
      margin-left: 6px;
    }
  }
  .operate-container {
    margin-left: 20px;
  }
}
.stqh {
  display: flex;
  position: relative;
  & > .label {
    display: inline-block;
    flex: none;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    background: url('@/assets/svgs/st-arrow.svg') no-repeat right center;
    padding-right: 20px;
    cursor: pointer;
  }
}
.st-select-container {
  width: 290px;
  box-shadow: 0px 8px 16px 0px rgba(7, 49, 69, 1);
  position: absolute;
  top: 30px;
  left: 0;
  z-index: 10001;
  overflow: hidden;
  padding: 8px 0 8px 8px;
  background: #fff;
  .el-scrollbar {
    padding-right: 8px;
  }
}
.el-tree {
  background: #fff;
  :deep(.el-tree-node__content) {
    background: #fff;
  }
  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #ebeff2;
  }
}
.custom-tree-node {
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .label {
    flex: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
