<script setup lang="ts">
import Header from './header.vue'
const route = useRoute()
const key = computed(() => route.path)
</script>

<template>
  <div class="page-container">
    <Header :key="key" />
    <RouterView :key="key"></RouterView>
  </div>
</template>
<style lang="scss" scoped>
.page-container {
  background: #f6f8fa;
  position: relative;
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
</style>
