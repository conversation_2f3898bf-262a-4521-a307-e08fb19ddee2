import axios from 'axios'
import { ElLoading } from 'element-plus'
import { service } from './encryptRequest'
interface LoadingClose {
  (): void
}
let loadingInstance: { close: LoadingClose } | null
const LoadingSvg = `<svg t="1699584736881" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27199" width="48" height="48"><path d="M224 224m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#e6e6e6" p-id="27200"></path><path d="M800 224m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#cdcdcd" p-id="27201"></path><path d="M800 800m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#8a8a8a" p-id="27202"></path><path d="M224 800m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#515151" p-id="27203"></path></svg>`
const loadingOptions = {
  fullscreen: true,
  text: '玩命请求中...',
  spinner: LoadingSvg,
  svgViewBox: '0 0 48 48'
}
let requestNum: number = 0
// Request 拦截
service.interceptors.request.use(
  (config: any) => {
    if (config.loading === true || typeof config.loading === 'undefined') {
      if (loadingInstance) {
        requestNum++
      }
      loadingInstance = ElLoading.service(loadingOptions)
    }
    if (Array.isArray(config.loading) && isRef(config.loading[0])) {
      config.loading[0].value = true
    }
    return config
  },
  (error: any) => {
    return Promise.reject(error)
  }
)

// Response 拦截
service.interceptors.response.use(
  (response: any) => {
    if (
      Array.isArray(response.config.loading) &&
      isRef(response.config.loading[0])
    ) {
      response.config.loading[0].value = false
    }
    if (
      response.config.loading === true ||
      typeof response.config.loading === 'undefined'
    ) {
      if (requestNum === 0) {
        loadingInstance && loadingInstance.close()
        loadingInstance = null
      } else {
        requestNum--
      }
    }
    return response
  },
  (error: any) => {
    if (Array.isArray(error.config.loading) && isRef(error.config.loading[0])) {
      error.config.loading[0].value = false
    }
    if (
      error.config.loading === true ||
      typeof error.config.loading === 'undefined'
    ) {
      if (requestNum === 0) {
        loadingInstance && loadingInstance.close()
        loadingInstance = null
      } else {
        requestNum--
      }
    }
    if (error.code === 'ERR_NETWORK') {
      error.message = '网络错误！'
    }
    if (error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED') {
      error.message = '请求超时！'
    }
    return Promise.reject(error)
  }
)

export default service
