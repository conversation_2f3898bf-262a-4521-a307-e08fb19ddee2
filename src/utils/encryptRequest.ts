import axios from 'axios'
import { handleEncryption } from "@/utils/util"
import { appInstance } from '@/main'

const handleNoAuth = () => {
	appInstance.config.globalProperties.$router.push('/no-auth')
}

/**
 * 创建一个配置好的axios实例
 * @param baseURL - API的基础URL，默认使用环境变量中的VITE_BASE_API_URL
 * @param config - 额外的axios配置
 * @returns 配置好的axios实例
 */

export const service = axios.create({
    baseURL: import.meta.env.VITE_APP_BASE_API,
    timeout: 60000,
    headers: {
      samesite: 'none',
      secure: true,
      withCredentials: true
    }
  })

// 请求拦截
service.interceptors.request.use((config: any) => {
    console.log("config=",config)
    config.headers = config.headers || {}
    const userInfo:any = localStorage.getItem("userInfo")||{}
    const menuCode = localStorage.getItem("menuCode")
    let par = { userId:userInfo?.id, menuCode,clientType:'web' }
    console.log("basic加密前参数=",par)
    let parstr = JSON.stringify(par)
    let basicEncrypt = JSON.parse(import.meta.env.VITE_BASIC_ENCRYPT)
    if (basicEncrypt) {
        const encPar= handleEncryption(parstr,true)
        config.headers["basic"] = encPar?.encryptData
        config.headers['cipherText'] = encPar.sm4SecretKeyEncrypted
    } else {
        config.headers["basic"] = parstr
    }
    return config
})

service.interceptors.response.use(
    (response: any) => {
        return response
    },
    (error: any) => {
        if (error?.response?.status === 403) {
            //无访问权限
            handleNoAuth()
        }
        return Promise.reject(error)
    }
)