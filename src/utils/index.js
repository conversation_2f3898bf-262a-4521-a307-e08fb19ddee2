/*
 * @Author: 赵鹏鹏
 * @Date: 2024-03-21 17:22:37
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @Description: 页面名称
 */

import dayjs from 'dayjs'

// 获取cookie
export const getCookieValue = function (name) {
  let cookies = document.cookie.split(';')
  for (let cookie of cookies) {
    let [key, value] = cookie.split('=')
    key = key.trim()
    if (key === name) return value
  }
  return undefined
}

// 禁用未来时间-时
export const disabledHours = (val) => {
  console.log('val1', val)
  const list = []
  if (val) {
    let a = new Date(val).getTime()
    let b = dayjs(a).format('YYYY-MM-DD')
    let c = dayjs().format('YYYY-MM-DD')
    if (b === c) {
      for (let i = 0; i < 24; i++) {
        // 限制之前 < 之后 > ，下面同理
        if (new Date().getHours() >= i) continue
        list.push(i)
      }
    }
  }
  return list
}
// 禁用未来时间-分
export const disabledMinutes = (hour, val) => {
  console.log('val2', hour, val)
  const list = []
  if (val) {
    let a = new Date(val).getTime()
    let b = dayjs(a).format('YYYY-MM-DD')
    let c = dayjs().format('YYYY-MM-DD')
    let d = dayjs().format('H')
    // 限制之前 < 之后 > ，下面同理
    if (b === c && hour <= d) {
      for (let i = 0; i < 60; i++) {
        if (new Date().getMinutes() >= i) continue
        list.push(i)
      }
    }
  }
  return list
}
// 禁用未来时间-秒
export const disabledSeconds = (hour, minute, val) => {
  console.log('val3', hour, minute, val)
  const list = []
  if (val) {
    let a = new Date(val).getTime()
    let b = dayjs(a).format('YYYY-MM-DD')
    let c = dayjs().format('YYYY-MM-DD')
    let d = dayjs().format('H')
    let m = dayjs().format('m')
    // 限制之前 < 之后 > ，下面同理
    if (b === c && hour <= d && minute <= m) {
      for (let i = 0; i < 60; i++) {
        if (new Date().getSeconds() >= i) continue
        list.push(i)
      }
    }
  }
  return list
}

/**
 * 格式化时间
 * @param {Date} now
 * @returns YYYY-MM-DD HH:mm:ss  TODO
 */
export const getDateTime = (now) => {
  now = now || new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  const hours = now.getHours()
  const minutes = now.getMinutes()
  const seconds = now.getSeconds()

  return `${year}-${month.toString().padStart(2, '0')}-${day
    .toString()
    .padStart(2, '0')} ${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

/**
 * 防抖函数
 * @param {Function} fn 原始函数
 * @param {Number} delay 延迟
 * @param {Boolean} immediate 是否立即执行
 * @returns Function
 */
export function debounce(fn, delay = 300, immediate = false) {
  let timer = null
  let isInvoke = false
  return function (...args) {
    if (timer) clearTimeout(timer)
    if (immediate && !isInvoke) {
      fn.apply(this, args)
      isInvoke = true
    } else {
      timer = setTimeout(() => {
        fn.apply(this, args)
        isInvoke = false
      }, delay)
    }
  }
}

export const dealTime = (time) => {
  if (!time) return '--'
  time = time.slice(0, 16)
  return time
}
