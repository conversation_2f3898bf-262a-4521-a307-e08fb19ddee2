import { ElLoading } from 'element-plus'
const LoadingSvg = `<svg t="1699584736881" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27199" width="48" height="48"><path d="M224 224m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#e6e6e6" p-id="27200"></path><path d="M800 224m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#cdcdcd" p-id="27201"></path><path d="M800 800m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#8a8a8a" p-id="27202"></path><path d="M224 800m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#515151" p-id="27203"></path></svg>`
const loadingOptions = {
  fullscreen: true,
  text: '11111',
  spinner: LoadingSvg,
  svgViewBox: '0 0 48 48'
}
interface LoadingClose {
  (): void
}
let loadingInstance: { close: LoadingClose }
export const loadingOpen = () => {
  loadingInstance = ElLoading.service(loadingOptions)
}
export const loadingClose = () => {
  loadingInstance && loadingInstance.close()
}
