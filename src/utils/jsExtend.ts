type ObjectKey = string | number | symbol

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface Object {
  isEqual(a: Record<ObjectKey, any>, b: Record<ObjectKey, any>): boolean
  deepClone(obj: Record<ObjectKey, any>): Record<ObjectKey, any>
  jsonClone(obj: Record<ObjectKey, any>): Record<ObjectKey, any>
}

/**
 * 深度比较两个对象是否相等
 * @param a object
 * @param b object
 * @returns boolean
 */
Object.isEqual = (a, b) => {
  if (a === b) return true

  if (a instanceof Date && b instanceof Date) return a.getTime() === b.getTime()

  if (!a || !b || (typeof a !== 'object' && typeof b !== 'object'))
    return a === b

  if (a.prototype !== b.prototype) return false

  const keys = Object.keys(a)
  if (keys.length !== Object.keys(b).length) return false

  return keys.every((k) => Object.isEqual(a[k], b[k]))
}

/**
 * 深度拷贝对象
 * @param obj object
 * @returns object
 */
Object.deepClone = (obj) => {
  if (!obj) return obj
  const clone = Object.assign({}, obj)
  Object.keys(clone).forEach(
    (key) =>
      (clone[key] =
        typeof obj[key] === 'object' ? Object.deepClone(obj[key]) : obj[key])
  )
  if (Array.isArray(obj)) {
    clone.length = obj.length
    return Array.from(clone as { length: number })
  }
  return clone
}

/**
 * Json方式深度拷贝
 * @param obj object
 * @returns object
 */
Object.jsonClone = (obj) => {
  if (!obj) return obj
  return JSON.parse(JSON.stringify(obj))
}
