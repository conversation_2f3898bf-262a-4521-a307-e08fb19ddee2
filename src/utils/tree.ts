export const getParents = (node: any) => {
  const parents: any[] = []
  if (node) {
    let parentNode = node.parent
    for (let j = 0, lv = node.level; j < lv; j++) {
      if (parentNode.level > 0) {
        parents.push(parentNode)
      }
      parentNode = parentNode.parent
    }
    if (parents.length > 1) {
      parents.reverse()
    }
    parents.push(node)
  }
  return parents.map((e) => e.data.id)
}

export const getChildren = (data: any = []) => {
  const result: any[] = []
  function traverse(node: any) {
    const newNode = { ...node }
    delete newNode.treeChild
    result.push(newNode)
    if (Array.isArray(node.treeChild)) {
      node.treeChild.forEach(traverse)
    }
  }
  data.forEach(traverse)
  return result
}
