export default {
  // 工单状态转换
  workStateFilter: (value) => {
    switch (value) {
      case 1:
        return '草稿'
      case 2:
        return '待处理'
      case 3:
        return '处理中'
      case 4:
        return '等待中'
      case 5:
        return '已取消'
      case 6:
        return '已完成'
    }
  },
  // 优先级转换
  priorityFilter: (value) => {
    switch (value) {
      case 1:
        return '低'
      case 2:
        return '中'
      case 3:
        return '高'
    }
  },
  // 工单类型
  workTypeFilter: (value) => {
    switch (value) {
      case 0:
        return '巡检工单'
      case 1:
        return '检修工单'
    }
  },
  // sceneOrRemote
  sceneOrRemoteFilter: (value) => {
    switch (value) {
      case 0:
        return '现场'
      case 1:
        return '远程'
    }
  },
  repairSchemeIdFilter: (value) => {
    switch (value) {
      case 1:
        return '更换配件'
      case 2:
        return '安装整改'
      case 3:
        return '重新调试'
      case 4:
        return '返厂调换'
      case 5:
        return '其他'
    }
  },
  taskStateIdFilter: (value) => {
    switch (value) {
      case 1:
        return '待执行'
      case 2:
        return '进行中'
      case 3:
        return '已结束'
    }
  },
  // 统计分类
  typeFilter: (value) => {
    switch (value) {
      case 1:
        return '远程处理时间'
      case 2:
        return '现场服务处理时间'
    }
  },
  // 统计状态
  stateFilter: (value) => {
    switch (value) {
      case 1:
        return '进行中'
      case 2:
        return '已完成'
    }
  },
  // 统计状态
  checkStateFilter: (value) => {
    switch (value) {
      case 1:
        return '制单'
      case 2:
        return '审核中'
      case 3:
        return '审批通过'
      case 4:
        return '审批驳回'
      case 5:
        return '审批撤回'
    }
  },
  // 时间转换器
  datetimeFilter: (date, format) => {
    if (!(date instanceof Date)) {
      return ''
    }
    const map = {
      'Y+': date.getFullYear(), // 年
      'M+': date.getMonth() + 1, // 月份
      'D+': date.getDate(), // 日
      'h+': date.getHours(), // 小时
      'm+': date.getMinutes(), // 分
      's+': date.getSeconds() // 秒
    }

    for (let key in map) {
      let reg = new RegExp(`(${key})`)
      if (reg.test(format)) {
        let str = map[key].toString()
        format = format.replace(RegExp.$1, str.padStart(RegExp.$1.length, '0'))
      }
    }
    return format
  },
  // 巡检项
  inspectionItemFilter: (value) => {
    switch (value) {
      case 1:
        return '光伏组件及固定支架检验'
      case 2:
        return '线路检验'
      case 3:
        return '交直流配电箱检验'
      case 4:
        return '逆变器检验'
      case 5:
        return '并网检查'
    }
  },
  // 巡检记录
  inspectionRecordFilter: (value) => {
    switch (value) {
      case 1:
        return '已完成'
      case 2:
        return '未完成'
    }
  },
  // 申请类型
  applyTypeFilter: (value) => {
    switch (value) {
      case 1:
        return '备件储存'
      case 2:
        return '耗材申请'
    }
  },
  // 故障原因类型（待确认）
  faultTypeFilter: (value) => {
    switch (value) {
      case 1:
        return ''
      case 2:
        return ''
    }
  },
  // 故障原因类型（待确认）
  settlementRulesFilter: (value) => {
    switch (value) {
      case '1':
        return '一级运维工工时'
      case '2':
        return '二级运维工工时'
      case '3':
        return '材料费'
      case '4':
        return '本单总价'
    }
  },
  // 工器具状态
  toolsStatus: {
    NORMAL: '正常',
    DISABLED: '停用'
  },
  // 工器具登记类型
  toolsCheckInType: {
    LENDING: '借出',
    RETURN: '归还',
    INSPECTION: '检验'
  },
  // 培训类型
  trainType: (value) => {
    switch (value) {
      case 1:
        return '工作许可人培训'
      case 2:
        return '工作负责人培训'
      case 3:
        return '工作票签发人培训'
      case 4:
        return '安全教育培训'
      case 5:
        return '应急演练培训'
      case 6:
        return '技术培训'
    }
  },
  // 培训级别
  trainLevel: (value) => {
    switch (value) {
      case 1:
        return '全员级'
      case 2:
        return '项目级'
      case 3:
        return '班组级'
    }
  },
  // 考试类型
  examType: (value) => {
    switch (value) {
      case 1:
        return '工作许可人考试'
      case 2:
        return '工作负责人考试'
      case 3:
        return '工作票签发人考试'
    }
  },
  // 考试结果
  examResult: (value) => {
    switch (value) {
      case 1:
        return '通过'
      case 2:
        return '未通过'
    }
  },
  // 审批流程类型
  instanceType: (value) => {
    const approvalMap = {
      // JSA_ISSUED: 'JSA票签发审批',
      JSA_SUMMARY: 'JSA票终结审批',
      JSA_OBSOLETE: 'JSA票作废审批',
      WORK_ISSUANCE_APPROVA_KEY: '电气一种工作票签发审批',
      // WORK_LICENSE_APPROVAL_KEY: '电气一种工作票许可审批',
      WORK_LICENSE_APPROVAL_KEY_NO_TEAM: '电气一种工作票许可审批',
      WORK_TERMINATION_OF_APPROVAL_KEY: '电气一种工作票终结审批',
      WORK_VOID_APPROVAL_KEY: '电气一种工作票作废审批',
      WORK_DELAYED_APPROVAL_KEY: '电气一种工作票延期审批',
      WORK_RESPONSIBLE_PERSON_CHANGE_APPROVAL_KEY:
        '电气一种工作票负责人变更审批',
      WORK_EQUIPMENT_TRIAL_OPERATION_PPROVAL_KEY:
        '电气一种工作票设备试运行审批',
      WORK_RESTORE_WORK_APPROVAL_KEY: '电气一种工作票恢复工作审批',
      EXPERIENCE_FEEDBACK_APPROVAL_KEY: '经验反馈单审批',
      EMERGENCY_DRILL_APPROVAL_KEY: '应急演练审批',
      TWO_KINDS_OF_ISSUANCE_APPROVA_KEY: '电气二种工作票签发审批',
      TWO_KINDS_OF_PERMISSION_APPROVA_KEY: '电气二种工作票许可审批',
      TWO_KINDS_OF_FINISH_OFF_APPROVA_KEY: '电气二种工作票终结审批',
      TWO_KINDS_OF_INVALID_APPROVA_KEY: '电气二种工作票作废审批',
      TWO_KINDS_OF_DELAYED_APPROVA_KEY: '电气二种工作票延期审批',
      OPERATION_WORK_ORDER_VERIFY_APPROVAL_KEY: '运维工单验证审批',
      CLEANSE_WORK_ORDER_VERIFY_APPROVAL_KEY: '清洗工单验证审批',
      INSPECTION_WORK_ORDER_VERIFY_APPROVAL_KEY: '巡检工单验证审批',
      CANCEL_WORK_ORDER_VERIFY_APPROVAL_KEY: '取消工单审批',
      OPERATION_COME_INTO_FORCE_APPROVAL_KEY: '操作票生效审批',
      OPERATION_FINISH_OFF_APPROVAL_KEY: '操作票终结审批',
      OPERATION_VOID_APPROVAL_KEY: '操作票作废审批',
      POSITION_CALIBRATE_APPROVAL_KEY: '位置校准审批'
    }
    if (value) {
      return approvalMap[value]
    } else {
      return approvalMap
    }
  },
  // 经验反馈单 审核状态
  feedbackStatus: (value) => {
    switch (value) {
      case 1:
        return '审核通过'
      case 2:
        return '待提交'
      case 3:
        return '审核中'
      case 4:
        return '审核驳回'
    }
  },
  // 经验反馈单 反馈类型
  feedbackType: (value) => {
    switch (value) {
      case 1:
        return '经验教训'
      case 2:
        return '良好实践'
    }
  },
  // 经验反馈单 来源
  feedbackOrigin: (value) => {
    switch (value) {
      case 1:
        return '内部'
      case 2:
        return '外部'
    }
  },
  // 经验反馈单 产业类别
  industryType: (value) => {
    switch (value) {
      case 1:
        return '核电'
      case 2:
        return '火电'
      case 3:
        return '水电'
      case 4:
        return '新能源'
      case 5:
        return '煤炭'
      case 6:
        return '铝业'
      case 7:
        return '环保'
      case 8:
        return '路港'
      case 9:
        return '海外'
      case 10:
        return '其他'
    }
  },
  // 经验反馈单 领域类别
  feedbackDomainType: (value) => {
    switch (value) {
      case 1:
        return '生产运营'
      case 2:
        return '工程建设'
      case 3:
        return '产品制造'
      case 4:
        return '服务支持'
    }
  }
}
