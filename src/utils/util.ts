import CryptoJS from 'crypto-js'
import { SM2, SM4 } from 'gm-crypto'

export const getUrlParams = () => {
    let url: string = window.location.href
    // 通过 ? 分割获取后面的参数字符串
    let urlArr = url.split('?')
    let urlStr = urlArr?.length>0?urlArr[1]:""
    // 创建空对象存储参数
    let obj: any = {}
    // 再通过 & 将每一个参数单独分割出来
    let paramsArr = urlStr?.split('&')||[]
    for (let i = 0, len = paramsArr.length; i < len; i++) {
      // 再通过 = 将每一个参数分割为 key:value 的形式
      let arr = paramsArr[i].split('=')
      obj[arr[0]] = arr[1]
    }
    return obj
}
// str: 需要加密的数据
export const handleEncryption = (str: string,isEncrypt?:any) => {
    const viteEncrypt = import.meta.env.VITE_ENCRYPT
    const flag = isEncrypt!==undefined ?isEncrypt: JSON.parse(viteEncrypt)
    if (flag) {
      // 生成sm4密钥（32位，16进制，和后端保持一致）
      const sm4SecretKey = CryptoJS.MD5('34324328943243').toString(CryptoJS.enc.Hex)
      // sm2公钥 加密sm4密钥
      const pubkey: any = "04477692aaa653668053c14d22132051becce0072755f2029df5b6ebc23ffea77326cd69425925adfa0e8527c22df0ccd543bcce28e5da2e10086f7be5ddc3589b"
      let sm4SecretKeyEncrypted = SM2.encrypt(sm4SecretKey, pubkey, {
        inputEncoding: 'utf8',
        outputEncoding: 'hex',
        mode: SM2.constants.C1C3C2
      })
      // 加密入参数据
      const encryptData = SM4.encrypt(str, sm4SecretKey, {
        iv: sm4SecretKey,
        inputEncoding: 'utf8',
        outputEncoding: 'hex',
        mode: SM4.constants.ECB
      })
      if (!sm4SecretKeyEncrypted.startsWith("04")) {
        sm4SecretKeyEncrypted="04"+sm4SecretKeyEncrypted
      }
      return { sm4SecretKeyEncrypted, encryptData }
    } else {
      let data = {}
      try {
        data = JSON.parse(str)
      } catch (error) {
        data = str
      }
      return {
        sm4SecretKeyEncrypted: '',
        encryptData: data
      }
    }
  }