import useRequest from '@/hooks/useRequest.ts'

export const { get, post, del } = useRequest()
export * as baseApi from './module/base.ts'
export * as overhaul from './module/overhaul.ts'
export * as fault from './module/fault.ts'
export * as opsCompany from './module/opsCompany.ts'
export * as opsPersonnel from './module/opsPersonnel.ts'
export * as storeManage from './module/storeManage.ts'
export * as educate from './module/educate.ts'
export * as team from './module/team.ts'
export * as approval from './module/approval.ts'
export * as ticket from './module/ticket.ts'
export * as stationLow from './module/stationLow.ts'
export * as allStation from './module/allStation.ts'
export * as operation from './module/operation.ts'
export * as score from './module/score.ts'
export * as reportForms from './module/reportForms.ts'
export * as planManagement from './module/planManagement.ts'
export * as safeRange from './module/safeRange.ts'
export * as deeect from './module/deeect.ts'
export * as workorder from './module/workorder.ts'
export * as rolling from './module/rolling.ts'
export * as handle from './module/handle.ts'
export * as examineManagement from './module/examineManagement.ts'
