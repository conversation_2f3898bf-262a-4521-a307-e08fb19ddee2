import request from '@/utils/request'

// ========= 培训管理 =========
// 获取运维人员
export const getOperationUserAllList = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/operationUser/getOperationUserAllList',
    method: 'post',
    data,
    loading
  })
}
// 获取培训列表
export const getList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/training-manage/getList',
    method: 'post',
    data: {
      ...data
      // code: localStorage.getItem('PVOM_COMPANY_CODE')
    },
    loading
  })
}
// 新增培训
export const trainingAdd = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/training-manage/add',
    method: 'post',
    data,
    loading
  })
}
// 获取培训中的项目列表
export const getOperationProjectList = (
  params: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/operation-project/getOperationProjectList',
    method: 'get',
    params,
    loading
  })
}

// 修改培训记录
export const update = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/training-manage/update',
    method: 'post',
    data,
    loading
  })
}

// 班组级列表
export const getOperationTeamsGroup_ = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/teamsGroups/getOperationTeamsGroup',
    method: 'post',
    data,
    loading
  })
}

// 考试管理相关
// 考试列表
export const getExamList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/examRecords/queryByExamRecordsPage',
    method: 'get',
    params,
    loading
  })
}
// 考试详情
export const getExamDetail = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/examRecords/queryDetailById',
    method: 'get',
    params,
    loading
  })
}
// 考试详情的人员列表
export const getExamUserList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/examRecords/queryByExamUserPage',
    method: 'get',
    params,
    loading
  })
}
// 考试管理 培训列表
export const getExamTrainList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/training-manage/getExamTrainingList',
    method: 'post',
    data,
    loading
  })
}
// 新增考试
export const examRecordsAdd = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/examRecords/examRecordsAdd',
    method: 'post',
    data,
    loading
  })
}
// 编辑考试
export const examRecordsEdit = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/examRecords/editExamRecords',
    method: 'post',
    data,
    loading
  })
}
