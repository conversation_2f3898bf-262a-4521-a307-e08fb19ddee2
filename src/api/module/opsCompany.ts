import request from '@/utils/request'

// 公司资质管理列表
export const getOperatorsList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operatorsManage/getOperatorsList',
    method: 'post',
    data,
    loading
  })
}

// 新增运维商
export const addOperatorsInfo = (data: any, loading: Loading = true) => {
  return request({
    url: '/operate/operatorsManage/addUpdateOperatorsInfo',
    method: 'post',
    data,
    loading
  })
}
// 新建运维商公司列表查询
export const getCompanyList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operatorsManage/getCompanyList',
    method: 'post',
    data,
    loading
  })
}
// 是否可以删除运维商接口
export const delOperatorsInfoCheck = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operatorsManage/delOperatorsInfoCheck?id=' + data.id,
    method: 'post',
    data: {},
    loading
  })
}
// 删除运维商
export const delFaultKnowledgeById = (data: any, loading: Loading = true) => {
  return request({
    url: '/operate/operatorsManage/delOperatorsInfoById?id=' + data.id,
    method: 'post',
    data: {},
    loading
  })
}
// 获取运维商详情信息查询
export const getOperatorsInfoById = (data: any, loading: Loading = true) => {
  return request({
    url: '/operate/operatorsManage/getOperatorsInfoById?id=' + data.id,
    method: 'post',
    data: {},
    loading
  })
}
// 运维商-核查记录列表信息查询
export const getVerifyRecordList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operatorsManage/getVerifyRecordList',
    method: 'post',
    data,
    loading
  })
}
// 添加编辑核查记录
export const addUpdateVerifyRecord = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operatorsManage/addUpdateVerifyRecord',
    method: 'post',
    data,
    loading
  })
}
// 删除核查记录
export const delVerifyRecord = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operatorsManage/delVerifyRecord?id=' + data.id,
    method: 'post',
    data: {},
    loading
  })
}
// 联系人列表
export const getOperatorsContactUser = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operatorsManage/getOperatorsContactUser',
    method: 'post',
    data,
    loading
  })
}
// 添加联系人
export const addOperatorsContactUser = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operatorsManage/addOperatorsContactUser',
    method: 'post',
    data,
    loading
  })
}
// 更新联系人
export const updateOperatorsContactUser = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operatorsManage/updateOperatorsContactUser',
    method: 'post',
    data,
    loading
  })
}
// 运维项目列表
export const getMaintainProjectVOList = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/operatorsManage/getMaintainProjectVOList',
    method: 'post',
    data,
    loading
  })
}
// 运维电站列表
export const getMaintainStationtVOList = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/operatorsManage/getMaintainStationtVOList',
    method: 'post',
    data,
    loading
  })
}
