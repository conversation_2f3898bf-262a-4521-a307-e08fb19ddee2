import useRequest from '@/hooks/useRequest.ts'

const { get, post } = useRequest()
export const uploadFile = async (data: FormData) => {
  return post({
    url: '/operate/operatorsManage/uploadContractInfoFile',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data,
    loading: true
  })
}

export const downloadFile = async (data: any) => {
  return post({
    url: '/operate/operatorsManage/downloadFaultKnowledgeFile',
    headers: { 'Content-Type': 'text/plan' },
    responseType: 'blob',
    data,
    loading: false
  })
}

// 获取当前登录人
export const getUser = (data: any = {}) => {
  return get({
    url: '/operate/uc/userNameId',
    data,
    loading: false
  })
}
