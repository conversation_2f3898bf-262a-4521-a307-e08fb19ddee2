/*  全部运维电站 接口 */
import request from '@/utils/request'

// 全部运维电站导出
export const downloadAllStation = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/stationLedger/allOperationPowerStationDownLoad',
    method: 'get',
    responseType: 'blob',
    params: {
      ...params,
      code: localStorage.getItem('PVOM_COMPANY_CODE')
    },
    loading
  })
}

// 项目名称列表  运维项目列表
export const getProjectList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/operation-project/getOperationProjectList',
    method: 'get',
    params,
    loading
  })
}

// 资产所属公司
export const getPropertyCompanyList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/operation-project/getCompanyList',
    method: 'post',
    data,
    loading
  })
}
