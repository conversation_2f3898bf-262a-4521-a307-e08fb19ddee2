import request from '@/utils/request'

// 缺陷-列表
export const getDefectMsgList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/defectMsgController/getDefectMsgList',
    method: 'post',
    data,
    loading
  })
}

// 获取大类-小类
export const getDefectClassToSubClass = (params: any, loading: Loading) => {
  return request({
    url: '/operate/defectMsgController/getDefectClassToSubClass',
    method: 'get',
    params,
    loading
  })
}

// 根据缺陷ID获取到对应的缺陷信息
export const getDefectMsgByDefectId = (
  params: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/defectMsgController/getDefectMsgByDefectId',
    method: 'get',
    params,
    loading
  })
}
