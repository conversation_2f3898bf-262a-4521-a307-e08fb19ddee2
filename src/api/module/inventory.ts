/*
 * @Description: 库存管理接口
 * @Author: zwcong
 * @Date: 2024-04-07 14:25:57
 * @LastEditors: zwcong
 * @LastEditTime: 2024-04-15 11:03:02
 */
import request from '@/utils/request'

// 库存管理-列表
export const queryByPage = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/inventoryManage/queryByPage',
    method: 'get',
    params: {
      ...params,
      code: localStorage.getItem('PVOM_COMPANY_CODE')
    },
    loading
  })
}

// 库存管理-类型下拉框
export const getToolsManageStateList = () => {
  return request({
    url: '/operate/toolsManage/getToolsManageStateList',
    method: 'get'
  })
}
