import request from '@/utils/request'

// 设备故障列表
export const getFaultList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/deviceFaultRecord/getDeviceFaultStationVOList',
    method: 'post',
    data: { ...data, code: localStorage.getItem('PVOM_COMPANY_CODE') },
    loading
  })
}
// 登记故障所属电站
export const getStationList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/deviceFaultRecord/getUserOperationStationVO',
    method: 'post',
    data,
    loading
  })
}

// 登记故障所属设备
export const getBelongDeviceList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/deviceFaultRecord/getBelongDeviceVO',
    method: 'get',
    params: params,
    loading
  })
}
// 故障类型信息查询
export const getFaultTypeList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/deviceFaultRecord/getFaultDatabaseList',
    method: 'get',
    params: params,
    loading
  })
}

// 关联工单列表
export const getRelatedOrder = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/remote-access/getRelatedOrder',
    method: 'get',
    params: params,
    loading
  })
}

// 新增 | 编辑 设备故障
export const saveDeviceFault = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/deviceFaultRecord/saveUpdateDeviceFaultRecord',
    method: 'post',
    data,
    loading
  })
}
// ==== 故障库 ===
// 获取设备类型列表
export const getDeviceTypeList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/fault-database/getDeviceTypeList',
    method: 'get',
    params,
    loading: loading
  })
}
// 添加故障记录
export const addDevice = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/fault-database/addDevice',
    method: 'post',
    data,
    loading: loading
  })
}
// 查看故障记录-列表
export const getDeviceList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/fault-database/getDeviceList',
    method: 'post',
    data,
    loading: loading
  })
}
// 删除故障库记录
export const deleteDevice = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/fault-database/deleteDevice',
    method: 'get',
    params,
    loading: loading
  })
}
// 修改故障记录
export const updateDevice = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/fault-database/updateDevice',
    method: 'post',
    data,
    loading: loading
  })
}
// 获取设备故障知识列表接口
export const getFaultKnowledgeList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/fault-database/getFaultKnowledgeList',
    method: 'post',
    data,
    loading: loading
  })
}
// 获取故障知识详情
export const getFaultKnowledgeInfo = (
  params: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/fault-database/getFaultKnowledgeInfo',
    method: 'get',
    params,
    loading: loading
  })
}
// 下载
export const downloadFile = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/operatorsManage/downloadFile',
    method: 'post',
    data: data.fileName,
    loading: loading,
    responseType: 'blob',
    headers: { 'Content-Type': 'text/plan' }
  })
}
// 获取设备故障列表接口
export const getDeviceFaultRecordList = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/fault-database/getDeviceFaultRecordList',
    method: 'post',
    data,
    loading: loading
  })
}

// ==== 故障知识 ====
// 故障知识列表查询
export const getFaultKnowledgeVOList = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/faultKnowledge/getFaultKnowledgeVOList',
    method: 'post',
    data,
    loading: loading
  })
}
// 故障类型信息查询
export const getFaultDatabaseList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/deviceFaultRecord/getFaultDatabaseList',
    method: 'get',
    params,
    loading: loading
  })
}
// 设备品牌信息查询
export const getDeviceBrandList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/faultKnowledge/getDeviceBrandList',
    method: 'get',
    params,
    loading: loading
  })
}
// 设备型号
export const getDeviceModelList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/faultKnowledge/getDeviceModelList',
    method: 'get',
    params,
    loading: loading
  })
}
// 添加修改故障知识
export const saveOrUpdateFaultKnowledge = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/faultKnowledge/saveOrUpdateFaultKnowledge',
    method: 'post',
    data,
    loading: loading
  })
}
// 查询故障知识详情
export const getFaultKnowledgeById = (
  params: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/faultKnowledge/getFaultKnowledgeById',
    method: 'get',
    params,
    loading: loading
  })
}
// 删除故障知识
export const delFaultKnowledgeById = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/faultKnowledge/delFaultKnowledgeById?id=' + data.id,
    method: 'post',
    data: {},
    loading: loading
  })
}
