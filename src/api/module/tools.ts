/*
 * @Description: 工器具接口
 * @Author: zwcong
 * @Date: 2024-04-07 14:25:57
 * @LastEditors: zwcong
 * @LastEditTime: 2024-04-08 10:46:03
 */
import request from '@/utils/request'

// 工器具列表
export const queryByPage = (params: any, loading: Loading) => {
  return request({
    url: '/operate/toolsManage/queryByPage',
    method: 'get',
    params: {
      ...params,
      code: localStorage.getItem('PVOM_COMPANY_CODE')
    },
    loading
  })
}

// 工器具-状态下拉框
export const getToolsManageStateList = () => {
  return request({
    url: '/operate/toolsManage/getToolsManageStateList',
    method: 'get'
  })
}

// 工器具-登记状态下拉框
export const getToolsRegisterTypeList = () => {
  return request({
    url: '/operate/toolsManage/getToolsRegisterTypeList',
    method: 'get'
  })
}

// 工器具-删除
export const delToolsManage = (params: any) => {
  return request({
    url: '/operate/toolsManage/delToolsManage',
    method: 'get',
    params
  })
}

// 工器具-新增
export const addToolsManage = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/toolsManage/addToolsManage',
    method: 'post',
    data,
    loading
  })
}

// 工器具-编辑
export const editToolsManage = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/toolsManage/editToolsManage',
    method: 'post',
    data,
    loading
  })
}

// 工器具-登记
export const registerToolsManage = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/toolsManage/registerToolsManage',
    method: 'post',
    data,
    loading
  })
}

// 工器具-详情
export const queryDetailById = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/toolsManage/queryDetailById',
    method: 'get',
    params,
    loading
  })
}

// 工器具-记录
export const queryToolsRecordsByPage = (
  params: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/toolsManage/queryToolsRecordsByPage',
    method: 'get',
    params,
    loading: loading
  })
}
