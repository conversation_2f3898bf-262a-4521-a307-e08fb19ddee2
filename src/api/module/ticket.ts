/*
 * @Author: zhaopengpeng006 <EMAIL>
 * @Date: 2024-05-30 17:32:29
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-09-13 13:49:37
 * @FilePath: \pv-om-web\src\api\module\ticket.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*  工作票、JSA票 接口 */
import request from '@/utils/request'
// 获取所有角色对应的人员
export const getRoleUserList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/approvalUserRoleController/getRoleUserList',
    method: 'post',
    data,
    loading
  })
}
// 班组列表接口-查询班组信息根据运维商id
export const getTeamsGroupByOperatorsId = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url:
      '/operate/teamsGroups/getTeamsGroupByOperatorsId?operatorsId=' +
      data.operatorsId,
    method: 'post',
    data: {},
    loading
  })
}
// 签发流程发起
export const initIssuance = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/businessMappingActivity/initiateIssuance',
    method: 'post',
    data,
    loading
  })
}
// 记录是否可以编辑
export const canUpdate = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/businessMappingActivity/canUpdate',
    method: 'get',
    params,
    loading
  })
}

// 获取检修工单编码1
export const getJianXiuNo = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/remote-access/getJianXiuNo',
    method: 'get',
    params,
    loading
  })
}
// 获取检修工单编码2
export const getWorkOrdertitle = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/twoSeedTicket/getWorkOrdertitle',
    method: 'get',
    params,
    loading
  })
}
// 获取操作票编码
export const getCaoZuoTicketNo = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/remote-access/getCaoZuoTicketNo',
    method: 'get',
    params,
    loading
  })
}
// 获取jsa操作票编码
export const getJsaNoList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/jsaTicket/getJsaNoList',
    method: 'get',
    params,
    loading
  })
}

// ========== 工作票 ==========

// 工作票 工作票列表
export const getWorkTicketList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/ticket-work/list',
    method: 'post',
    data: {
      ...data,
      code: localStorage.getItem('PVOM_COMPANY_CODE')
    },
    loading
  })
}
// 工作票 工作票详情
export const getWorkTicketDetail = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/ticket-work/info',
    method: 'get',
    params,
    loading
  })
}
// 工作票 新建 和 编辑
export const addWorkTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/ticket-work/add',
    method: 'post',
    data,
    loading
  })
}

// 工作票 补充信息 查询
export const getWorkTicketSupply = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/ticket-work/getSupplementaryInformation',
    method: 'get',
    params,
    loading
  })
}
// 工作票 补充信息 新增和编辑
export const addWorkTicketSupply = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/ticket-work/addSupplementaryInformation',
    method: 'post',
    data,
    loading
  })
}
// 工作票终结
export const finishWorkTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/businessMappingActivity/termination',
    method: 'post',
    data,
    loading
  })
}
// 工作票 变更负责人
export const changePersonWorkTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/businessMappingActivity/change',
    method: 'post',
    data,
    loading
  })
}

// 工作票 延期
export const delayWorkTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/businessMappingActivity/delay',
    method: 'post',
    data,
    loading
  })
}
//
// 工作票 作废
export const revokeWorkTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/businessMappingActivity/cancel',
    method: 'post',
    data,
    loading
  })
}
// 工作票 设备试运行
export const equipOperateWorkTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/businessMappingActivity/equipmentTrialOperation',
    method: 'post',
    data,
    loading
  })
}
// 工作票 恢复工作
export const recoveWorkTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/businessMappingActivity/restoreWork',
    method: 'post',
    data,
    loading
  })
}

// 工作票导出
export const downloadWorkTicket = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/ticket-work/workTicketByDownload',
    method: 'get',
    responseType: 'blob',
    params,
    loading
  })
}

export const uploadWorkTicketFiles = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/ticket-work/addFileURL',
    method: 'post',
    data,
    loading
  })
}

// ========== JSA票 ==========
// jsa票列表查询
export const getjsaTicketList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/jsaTicket/getjsaTicketList',
    method: 'post',
    data: { ...data, code: localStorage.getItem('PVOM_COMPANY_CODE') },
    loading
  })
}
// 危险辨识及控制--作业环境
export const getHomeworkEnvironmentList = (
  params: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/jsaTicket/getHomeworkEnvironmentList',
    method: 'get',
    params,
    loading
  })
}
// 危险辨识及控制--作业风险
export const getHomeworkRiskList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/jsaTicket/getHomeworkRiskList',
    method: 'get',
    params,
    loading
  })
}
// 危险辨识及控制--作业类别
export const getHomeworkTyepList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/jsaTicket/getHomeworkTyepList',
    method: 'get',
    params,
    loading
  })
}
// 添加jsa票
export const addJsaTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/jsaTicket/addJsaTicket',
    method: 'post',
    data,
    loading
  })
}
// jsa票查看详情
export const getJsaTicketDetailVO = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/jsaTicket/getJsaTicketDetailVO',
    method: 'get',
    params,
    loading
  })
}
// jsa票添加补充信息
export const addReplenishInfo = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/jsaTicket/addReplenishInfo',
    method: 'post',
    data,
    loading
  })
}
// jsa提交签发,终结流程
export const jsaLaunchProcess = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/businessMappingActivity/jsaLaunchProcess',
    method: 'post',
    data,
    loading
  })
}
// jsa票导出
export const download = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/jsaTicket/download',
    method: 'get',
    responseType: 'blob',
    params,
    loading
  })
}
// jsa票上传原件
export const uploadOriginal = (data: any, loading: Loading = false) => {
  return request({
    url:
      '/operate/jsaTicket/uploadOriginal?id=' +
      data.id +
      '&stampFile=' +
      data.stampFile,
    method: 'post',
    data: {},
    loading
  })
}

// ========== 二种工作票 ==========
// 二种票 列表
export const getTwoSeedTicketList = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/twoSeedTicket/getTwoSeedTicketList',
    method: 'post',
    data,
    loading
  })
}
// 二种票 新建 & 编辑
export const addTwoSeedTicket = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/twoSeedTicket/addTwoSeedTicket',
    method: 'post',
    data,
    loading
  })
}

// 二种票 详情
export const getTwoSeedTicketDetailVO = (
  params: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/twoSeedTicket/getTwoSeedTicketDetailVO',
    method: 'get',
    params,
    loading
  })
}

// 二种票 复制
export const copyTwoSeedTicket = (
  params: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/twoSeedTicket/copyTwoSeedTicket',
    method: 'post',
    params,
    loading
  })
}

// 二种票 添加补充信息
export const addTwoSeddSupply = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/twoSeedTicket/addReplenishInfo',
    method: 'post',
    data,
    loading
  })
}
// 二种票 上传原件
export const uploadTwoSeedOriginal = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url:
      '/operate/twoSeedTicket/uploadOriginal?id=' +
      data.id +
      '&stampFile=' +
      data.stampFile,
    method: 'post',
    data: {},
    loading
  })
}

// 二种票 签发审批发起
export const twoIssuedWorkApprove = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/businessMappingActivity/twoKindsOfIssuedWorkApprove',
    method: 'post',
    data,
    loading
  })
}
// 二种票 延期审批发起
export const twoDelayWorkApprove = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/businessMappingActivity/twoKindsDelayedWorkApprove',
    method: 'post',
    data,
    loading
  })
}

// 二种票 终结审批发起
export const twoFinishWorkApprove = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/businessMappingActivity/twoKindsFinishOffWorkApprove',
    method: 'post',
    data,
    loading
  })
}

// 二种票 作废审批发起
export const twoInvalidWorkApprove = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/businessMappingActivity/twoKindsInvalidWorkApprove',
    method: 'post',
    data,
    loading
  })
}

// 二种票 导出
export const downloadTwo = (
  params: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/twoSeedTicket/download',
    method: 'get',
    responseType: 'blob',
    params,
    loading
  })
}
// 二种票 作业风险
export const getTwoTicketRiskList = (
  params: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/twoSeedTicket/getTwoTicketRiskList',
    method: 'get',
    params,
    loading
  })
}
// 二种票 作业类别
export const getTwoTicketTyepList = (
  params: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/twoSeedTicket/getTwoTicketTyepList',
    method: 'get',
    params,
    loading
  })
}

// 二种票 查看审批流程
export const getApprovalProcess = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/approvalTableController/getApprovalProcessById',
    method: 'post',
    data,
    loading
  })
}

// 一种票查操作票
export const getIdByNo = (params: any, loading: boolean | boolean = false) => {
  return request({
    url: '/operate/operateTicketController/getIdByNo',
    method: 'get',
    params,
    loading
  })
}

// 权限判断
export const checkGroup = (params: any, loading: boolean | boolean = false) => {
  return request({
    url: '/operate/station-group-user/check',
    method: 'get',
    params,
    loading
  })
}
