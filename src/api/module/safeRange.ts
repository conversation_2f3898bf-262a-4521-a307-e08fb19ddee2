/*  安全范围 接口 */
import request from '@/utils/request'

/* 经验反馈单 */
// 经验反馈单 列表
export const getFeedbackList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/expFeedbackController/getExpFeedbackList',
    method: 'post',
    data,
    loading
  })
}
// 经验反馈单 详情
export const getFeedbackDetail = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/expFeedbackController/getExpFeedbackById',
    method: 'get',
    params,
    loading
  })
}
// 经验反馈单 新增
export const addFeedback = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/expFeedbackController/addExpFeedback',
    method: 'post',
    data,
    loading
  })
}
// 经验反馈单 编辑
export const editFeedback = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/expFeedbackController/updateExpFeedback',
    method: 'post',
    data,
    loading
  })
}
// 经验反馈单 删除
export const deleteFeedback = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/expFeedbackController/deleteExpFeedbackById',
    method: 'post',
    params,
    loading
  })
}
// 经验反馈单 提交审批
export const approveFeedback = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/businessMappingActivity/expFeedbackApprove',
    method: 'post',
    data,
    loading
  })
}
