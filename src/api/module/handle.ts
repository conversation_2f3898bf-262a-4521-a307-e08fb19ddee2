import request from '@/utils/request'

// 列表
export const operateTicketList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/operateTicketController/operateTicketList',
    method: 'post',
    data: { ...data, code: localStorage.getItem('PVOM_COMPANY_CODE') },
    loading
  })
}
// 保存草稿接口
export const addOperateTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/operateTicketController/addOperateTicket',
    method: 'post',
    data,
    loading
  })
}
// 生效审批
export const operateTicketComeIntoApprove = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/businessMappingActivity/operateTicketComeIntoApprove',
    method: 'post',
    data,
    loading
  })
}
// 作废审批
export const operateTicketVoidApprove = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/businessMappingActivity/operateTicketVoidApprove',
    method: 'post',
    data,
    loading
  })
}
// 终结审批
export const operateTicketFinishOffApprove = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/businessMappingActivity/operateTicketFinishOffApprove',
    method: 'post',
    data,
    loading
  })
}

// 导出
export const download = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/operateTicketController/download',
    method: 'get',
    params: params,
    loading,
    responseType: 'blob'
  })
}

// 编辑操作票
export const updateOperateTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/operateTicketController/updateOperateTicket',
    method: 'post',
    data,
    loading
  })
}

// 查询操作票详情
export const getOperateTicketById = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/operateTicketController/getOperateTicketById',
    method: 'get',
    params: params,
    loading
  })
}

// 关联工作票1 关联工作票2
export const getWorkOneTwoTicketList = (
  params: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/operateTicketController/getWorkOneTwoTicketList',
    method: 'get',
    params: params,
    loading
  })
}

// 补充信息、原件接口
export const updateTicketById = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/operateTicketController/updateTicketById',
    method: 'post',
    data,
    loading
  })
}

// 获取单位接口
export const getOperationCompanyList = (
  params: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/operation-project/getOperationCompanyList',
    method: 'get',
    params: params,
    loading
  })
}
