import request from '@/utils/request'
import useRequest from '@/hooks/useRequest.ts'
const { post } = useRequest()

// 入库列表
export const getStoreInList = async (data: Obj, loading: Loading = false) => {
  return await post({
    url: '/operate/inventory-receipt/getList',
    data: { ...data, code: localStorage.getItem('PVOM_COMPANY_CODE') },
    loading
  })
}

// 入库详情
export const getStoreInDetailList = async (
  data: Obj,
  loading: Loading = false
) => {
  return await post({
    url: '/operate/inventory-receipt/getDetail',
    data,
    loading
  })
}

// 添加入库
export const saveStoreIn = async (data: Obj, loading: Loading = true) => {
  return await post({
    url: '/operate/inventory-receipt/add',
    data,
    loading,
    useMessage: false
  })
}

// 所有物资列表
export const getGoods = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/inventoryGoods/queryByGoodsPage',
    method: 'get',
    params,
    loading
  })
}

// ========= 出库管理 =========
// 出库列表
export const getStoreOutList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/inventoryOut/getOperatorsList',
    method: 'post',
    data: {
      ...data,
      code: localStorage.getItem('PVOM_COMPANY_CODE')
    },
    loading
  })
}
// 出库详情
export const getStoreOutDetail = async (
  data: Obj,
  loading: Loading = false
) => {
  return await post({
    url: 'operate/inventoryOut/getInventoryOutDetailVOList',
    data,
    loading
  })
}
// 添加出库
export const saveStoreOut = async (data: Obj, loading: Loading = true) => {
  return await post({
    url: '/operate/inventoryOut/addInventoryOut',
    data,
    loading
  })
}

// ========= 盘点管理 =========
// 盘点列表查询
export const getCheckInventoryList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/checkInventory/getCheckInventoryList',
    method: 'post',
    data: {
      ...data,
      code: localStorage.getItem('PVOM_COMPANY_CODE')
    },
    loading
  })
}
// 根据电站查询物资
export const getCheckInventoryBy = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/checkInventory/getCheckInventoryBy',
    method: 'get',
    params,
    loading
  })
}
// 查看详情
export const getCheckInventoryById = async (
  data: any,
  loading: Loading = false
) => {
  return await post({
    url: '/operate/checkInventory/getCheckInventoryById',
    data,
    loading
  })
}
// 提交盘点
export const saveCheckInventory = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/checkInventory/saveCheckInventory',
    method: 'post',
    data,
    loading
  })
}
// 核对库存数量
export const checkInventoryNum = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/checkInventory/checkInventoryNum',
    method: 'post',
    data,
    loading
  })
}
