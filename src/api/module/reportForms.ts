/*
 * @Description:
 * @Author: zwcong
 * @Date: 2024-05-27 10:47:04
 * @LastEditors: zwcong
 * @LastEditTime: 2024-06-28 17:03:07
 */
import request from '@/utils/request'

// 项目（资产）公司电站报表列表查询
export const getProjectStationReport = (data: any, loading: Loading) => {
  return request({
    url: '/operate/project-company-station-report/pages',
    method: 'post',
    data,
    loading
  })
}

// 项目（资产）公司电站报表导出
export const projectStationReportExport: any = (data: any) => {
  return request({
    url: '/operate/project-company-station-report/export',
    method: 'post',
    data,
    loading: false,
    responseType: 'blob'
  })
}

// 运维公司电站报表列表查询
export const getOperationStationReport = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operation-company-station-report/operationProjectStationList',
    method: 'post',
    data,
    loading
  })
}

// 运维公司电站报表导出
export const projectOperationReportExport: any = (data: any) => {
  return request({
    url: '/operate/operation-company-station-report/export',
    method: 'post',
    data,
    loading: false,
    responseType: 'blob'
  })
}

// 日月报表管理
export const queryReportRecord = (params: any, loading: Loading) => {
  return request({
    url: '/operate/operateCompanyReportRecord/queryReportRecord',
    method: 'get',
    params,
    loading
  })
}
// 项目公司日报
export const queryProjectCompanyDayReport = (params: any, loading: Loading) => {
  return request({
    url: 'operate/operatorsDayEnergyReport/queryProjectCompanyDayReport',
    method: 'get',
    params,
    loading
  })
}
// 项目公司日报导出
export const queryProjectCompanyDayReportExport: any = (params: any) => {
  return request({
    url: '/operate/operatorsDayEnergyReport/queryProjectCompanyDayReportExport',
    method: 'get',
    params,
    loading: false,
    responseType: 'blob'
  })
}

// 运维公司列表查询
export const getOperationCompany = (params: any, loading: Loading) => {
  return request({
    url: '/operate/operation-company-station-report/query',
    method: 'get',
    params,
    loading
  })
}

// 项目公司列表查询
export const getProjectCompany = (params: any, loading: Loading) => {
  return request({
    url: '/operate/project-company-station-report/query',
    method: 'get',
    params,
    loading
  })
}
