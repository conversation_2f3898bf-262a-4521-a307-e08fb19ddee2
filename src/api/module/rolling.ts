import request from '@/utils/request'

// 资产公司2
export const getCompanyListPage = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/operation-project/getCompanyListPage',
    method: 'post',
    data,
    loading
  })
}

// 关联其他工单下拉列表
export const allWorkOrderList = (
  params: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/workOrder/allWorkOrderList',
    method: 'get',
    params,
    loading
  })
}

export const getTwoSeedTicketList = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/twoSeedTicket/getTwoSeedTicketList',
    method: 'post',
    data,
    loading
  })
}

export const getTickeFiltrationtList = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/ticket-work/getTickeFiltrationtList',
    method: 'post',
    data: {
      ...data,
      code: 'root'
    },
    loading
  })
}

export const getTwoSeedTickeFiltrationtList = (
  data: any,
  loading: boolean | boolean = false
) => {
  return request({
    url: '/operate/twoSeedTicket/getTwoSeedTickeFiltrationtList',
    method: 'post',
    data,
    loading
  })
}
