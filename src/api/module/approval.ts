/*
 * @Description: 审批
 * @Author: zwcong
 * @Date: 2024-04-24 14:25:57
 * @LastEditors: zwcong
 * @LastEditTime: 2024-05-13 15:01:57
 */
import request from '@/utils/request'

// 审批权限-列表
export const getApprovalUserRoleList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/approvalUserRoleController/getApprovalUserRoleList',
    method: 'post',
    data,
    loading
  })
}

// 审批权限-新建审批角色
export const addApprovalRole = (data: any) => {
  return request({
    url: '/operate/approvalUserRoleController/addApprovalRole',
    method: 'post',
    data,
    loading: false
  })
}

// 审批权限-添加人员角色
export const addApprovalUserRole: any = (data: Obj, loading: Loading) => {
  return request({
    url: '/operate/approvalUserRoleController/addApprovalUserRole',
    method: 'post',
    data,
    loading
  })
}

// 审批权限-删除
export const deleteApprovalUserRole: any = (params: Obj, loading: Loading) => {
  return request({
    url: '/operate/approvalUserRoleController/deleteApprovalUserRole',
    method: 'post',
    params,
    loading
  })
}

// 审批权限-获取所有角色
export const getRoleList: any = () => {
  return request({
    url: '/operate/approvalUserRoleController/getRoleList',
    method: 'get',
    loading: false
  })
}

// 审批权限-修改人员角色
export const updateApprovalUserRole: any = (data: Obj, loading: Loading) => {
  return request({
    url: '/operate/approvalUserRoleController/updateApprovalUserRole',
    method: 'post',
    data,
    loading
  })
}

// 获取UC用户列表
export const getUserInfoList: any = (params: Obj) => {
  return request({
    url: '/operate/uc/getUserInfoList',
    method: 'get',
    params,
    loading: false
  })
}

// 审批工作台-我的待办
export const getMyToDoList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/approvalTableController/getMyToDoList',
    method: 'post',
    data,
    loading
  })
}

// 审批工作台-我的已办
export const getMyHaveDoneList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/approvalTableController/getMyHaveDoneList',
    method: 'post',
    data,
    loading
  })
}

// 审批工作台-我发起的
export const getMyInitiateList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/approvalTableController/getMyInitiateList',
    method: 'post',
    data,
    loading
  })
}

// 审批工作台-查看审批流程
export const getApprovalProcess = (data: any) => {
  return request({
    url: '/operate/approvalTableController/getApprovalProcess',
    method: 'post',
    data,
    loading: true
  })
}

// 审批工作台-通过
export const passInstance = (data: any) => {
  return request({
    url: '/operate/approvalTableController/passInstance',
    method: 'post',
    data,
    loading: false
  })
}

// 审批工作台-驳回
export const turnDown = (data: any) => {
  return request({
    url: '/operate/approvalTableController/turnDown',
    method: 'post',
    data,
    loading: false
  })
}
