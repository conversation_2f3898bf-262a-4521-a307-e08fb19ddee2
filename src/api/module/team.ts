/*
 * @Description: 班组管理接口
 * @Author: zwcong
 * @Date: 2024-04-24 14:25:57
 * @LastEditors: zwcong
 * @LastEditTime: 2024-04-24 15:54:32
 */
import request from '@/utils/request'
import useRequest from '@/hooks/useRequest.ts'

const { post } = useRequest()

// 班组管理-列表
export const queryByPage = (data: any, loading: Loading) => {
  return request({
    url: '/operate/teamsGroups/getTeamsGroupsList',
    method: 'post',
    data,
    loading
  })
}

// 班组管理-添加修改
export const addUpdateTeamsGroups = (data: any) => {
  return request({
    url: '/operate/teamsGroups/addUpdateTeamsGroups',
    method: 'post',
    data,
    loading: true
  })
}

// 班组管理-班组成员查询
export const getTeamsMemberList: any = (data: Obj, loading: Loading) => {
  return post({
    url: '/operate/teamsGroups/getTeamsMemberList',
    data,
    loading
  })
}

// 班组管理-停用启用
export const updateTeamsGroupsStatus = (
  params: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/teamsGroups/updateTeamsGroupsStatus',
    method: 'post',
    params,
    loading
  })
}
