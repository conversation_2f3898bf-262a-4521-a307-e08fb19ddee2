/*
 * @Description:
 * @Author: zwcong
 * @Date: 2024-05-27 10:47:04
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-10-28 09:42:28
 */
import request from '@/utils/request'

// 检修及时率统计报表-列表
export const getOverhaulReportVOList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/dimensionReport/getOverhaulReportVOList',
    method: 'post',
    data,
    loading
  })
}

// 检修及时率统计报表-导出
export const download: any = (data: any) => {
  return request({
    url: '/operate/dimensionReport/download',
    method: 'post',
    data,
    loading: false,
    responseType: 'blob'
  })
}

// 运维商列表
export const queryOperateCompanyScore: any = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operatorsManage/queryOperateCompanyScore',
    method: 'post',
    data: {
      ...data,
      code: localStorage.getItem('PVOM_COMPANY_CODE')
    },
    loading
  })
}

// 运维报表 列表
export const getOperationReportList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operation-report/list',
    method: 'post',
    data,
    loading
  })
}
// 运维报表 获取资产公司/运维公司 type 1是资产公司 2是运维公司
export const getCompanyList = (params: any, loading: Loading) => {
  return request({
    url: '/operate/operation-report/getCompanyList',
    method: 'get',
    params,
    loading
  })
}
// 运维报表 生成运维报表 重新生成
export const addOperationReport = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operation-report/add',
    method: 'post',
    data,
    loading
  })
}
// 运维报表 删除
export const operateDelete = (params: any, loading: Loading) => {
  return request({
    url: '/operate/operation-report/delete',
    method: 'get',
    params,
    loading
  })
}
