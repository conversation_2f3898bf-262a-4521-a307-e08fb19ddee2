/*
 * @Description: 物资管理接口
 * @Author: zwcong
 * @Date: 2024-04-07 14:25:57
 * @LastEditors: zwcong
 * @LastEditTime: 2024-04-15 09:49:15
 */
import request from '@/utils/request'

// 物资管理-列表
export const queryByPage = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/inventoryGoods/queryByGoodsPage',
    method: 'get',
    params,
    loading
  })
}

// 物资管理-类型下拉框
export const allGoodsTypeList = (params: any) => {
  return request({
    url: '/operate/inventoryGoodsType/allGoodsTypeList',
    method: 'get',
    params,
    loading: false
  })
}
// 物资管理-状态下拉框
export const getGoodsStateList = () => {
  return request({
    url: '/operate/inventoryGoods/getGoodsStateList',
    method: 'get'
  })
}

// 物资管理-类别下拉框
export const getGoodsCateList = () => {
  return request({
    url: '/operate/inventoryGoods/getGoodsCateList',
    method: 'get'
  })
}

// 物资管理-删除
export const deleteById = (params: any) => {
  return request({
    url: '/operate/inventoryGoods/deleteById',
    method: 'get',
    params
  })
}

// 物资管理-新增
export const addGoods = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/inventoryGoods/addGoods',
    method: 'post',
    data,
    loading
  })
}

// 物资管理-物资类型新增
export const goodsTypeAdd = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/inventoryGoodsType/goodsTypeAdd',
    method: 'post',
    data,
    loading
  })
}
