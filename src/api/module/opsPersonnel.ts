import request from '@/utils/request'

// 运维人员列表
export const getOperationUserList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operationUser/getOperationUserList',
    method: 'post',
    data,
    loading
  })
}
// 运维人员详情
export const getOperationUser = (params: any, loading: Loading) => {
  return request({
    url: '/operate/operationUser/getOperationUserById',
    method: 'get',
    params,
    loading: loading
  })
}
// 所属运维公司
export const getOperatorsList = (params: any, loading: Loading) => {
  return request({
    url: '/operate/operationUser/getOperatorsList',
    method: 'get',
    params: params,
    loading
  })
}

// 新增运维人员
export const saveOperationUser = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operationUser/saveOperationUser',
    method: 'post',
    data,
    loading
  })
}
// 编辑运维人员
export const updateOperationUser = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operationUser/updateOperationUser',
    method: 'post',
    data,
    loading
  })
}
// 删除运维人员
export const deleteOperationUser = (params: any, loading: Loading) => {
  return request({
    url: '/operate/operationUser/deleteOperationUser',
    method: 'post',
    params,
    loading
  })
}
// 运维人员培训记录
export const getTrainRecord = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operationUser/getTrainingRecordsById',
    method: 'post',
    data,
    loading
  })
}
// 运维人员考试记录
export const getExamRecord = (data: any, loading: Loading) => {
  return request({
    url: '/operate/operationUser/getExamRecordsById',
    method: 'post',
    data,
    loading
  })
}
