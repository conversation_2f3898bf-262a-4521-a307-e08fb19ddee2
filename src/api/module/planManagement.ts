import request from '@/utils/request'

// 计划管理列表查询
export const getPlanManageList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/planManage/getPlanManageList',
    method: 'post',
    data: data,
    loading: loading
  })
}
// 添加修改计划管理
export const saveOrUpdatePlanManage = (data: any, loading: Loading) => {
  return request({
    url: '/operate/planManage/saveOrUpdatePlanManage',
    method: 'post',
    data: data,
    loading: loading
  })
}
// 计划管理启用禁用
export const statusPlanManage = (data: any, loading: Loading) => {
  return request({
    url: `/operate/planManage/statusPlanManage?id=${data.id}&status=${data.status}`,
    method: 'post',
    data: {},
    loading: loading
  })
}
// 查看详情计划管理详情
export const getPlanManageById = (params: any, loading: Loading) => {
  return request({
    url: '/operate/planManage/getPlanManageById',
    method: 'get',
    params,
    loading: loading
  })
}

// 应急演练管理列表
export const getEmergencies = (data: any, loading: Loading) => {
  return request({
    url: '/operate/planManage/getPlanManageList',
    method: 'post',
    data: data,
    loading: loading
  })
}
// 应急演练管理修改
export const updateEmergency = (data: any, loading: Loading) => {
  return request({
    url: '/operate/planManage/saveOrUpdatePlanManage',
    method: 'post',
    data: data,
    loading: loading
  })
}
// 应急演练管理详情
export const getEmergencyById = (params: any, loading: Loading) => {
  return request({
    url: '/operate/planManage/getPlanManageById',
    method: 'get',
    params,
    loading: loading
  })
}
// 应急演练管理删除
export const deleteEmergency = (data: any, loading: Loading) => {
  return request({
    url: `/operate/planManage/statusPlanManage?id=${data.id}&status=${data.status}`,
    method: 'post',
    data: {},
    loading: loading
  })
}
