/*
 * @Author: zhaopengpeng006 <EMAIL>
 * @Date: 2024-10-15 10:10:09
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-10-16 16:30:45
 * @FilePath: \pv-om-web\src\api\module\examineManagement.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 考核列表
export const attendanceManagementControllerList = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/attendanceManagementController/list',
    method: 'post',
    data: { ...data, code: localStorage.getItem('PVOM_COMPANY_CODE') },
    loading
  })
}
// 列表导出
export const downloadWorkTicket = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/attendanceManagementController/download',
    method: 'post',
    responseType: 'blob',
    data,
    loading
  })
}
