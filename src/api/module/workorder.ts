/*
 * @Description: 工单列表
 * @Author: zhao<PERSON>
 * @Date: 2024-04-07 14:25:57
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-08-23 21:15:26
 */
import request from '@/utils/request'

// 工单列表
export const getWorkOrderList = (data: any, loading: Loading) => {
  return request({
    url: '/operate/workOrder/getWorkOrderList',
    method: 'post',
    data,
    loading
  })
}

// 工单列表导出
export const downloadOrder = (params: any = {}, loading: Loading = false) => {
  return request({
    url: '/operate/workOrder/download',
    method: 'get',
    responseType: 'blob',
    params,
    loading
  })
}

// 根据电站编码获取设备信息
export const getDeviceInfoListByStationCode = (
  params: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/deviceInfoMasterController/getDeviceInfoListByStationCode',
    method: 'get',
    params,
    loading
  })
}
// 根据电站编码获取运维商接口
export const getOperatorsInfo = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/workOrder/getOperatorsInfo',
    method: 'get',
    params,
    loading
  })
}
// 根据设备编码获取设备信息
export const getDeviceInfoByDeviceCode = (params: any, loading: Loading) => {
  return request({
    url: '/operate/deviceInfoMasterController/getDeviceInfoByDeviceCode',
    method: 'get',
    params,
    loading
  })
}

// 添加工单
export const addWorkOrder = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/workOrder/addWorkOrder',
    method: 'post',
    data,
    loading
  })
}

// 工单进度查询接口
export const getOrderProgressTime = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/workOrder/getOrderProgressTime',
    method: 'get',
    params,
    loading
  })
}

// 获取工单详情接口
export const getWorkOrderDetailVO = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/workOrder/getWorkOrderDetailVO',
    method: 'get',
    params,
    loading
  })
}

// 关联其他工单下拉列表
export const allWorkOrderList = (
  params: any = {},
  loading: Loading = false
) => {
  return request({
    url: '/operate/workOrder/allWorkOrderList',
    method: 'get',
    params,
    loading
  })
}
// 判断工程师状态
export const engineerShow = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/workOrder/engineerShow',
    method: 'get',
    params,
    loading
  })
}

// 修改工单
export const updateWorkOrder = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/workOrder/updateWorkOrder',
    method: 'post',
    data,
    loading
  })
}

// 获取工单打卡记录接口
export const getSginRecords = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/workOrder/getSginRecords',
    method: 'get',
    params,
    loading
  })
}
// 取消工单1
export const cancelWorkOrder = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/workOrder/cancelWorkOrder',
    method: 'post',
    data,
    loading
  })
}
// 取消工单2
export const cancelWorkOrderVerifyApprove = (
  data: any,
  loading: Loading = false
) => {
  return request({
    url: '/operate/businessMappingActivity/cancelWorkOrderVerifyApprove',
    method: 'post',
    data,
    loading
  })
}
// 指派工单
export const assignWorkOrder = (data: any, loading: Loading = false) => {
  return request({
    url:
      '/operate/workOrder/assignWorkOrder?workOrderId=' +
      data.workOrderId +
      '&engineer=' +
      data.engineer,
    method: 'post',
    data: {},
    loading
  })
}

// 根据巡检模板
export const getInspectionTree = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/inspection-template-tree/getInspectionTemplateBYId',
    method: 'get',
    params,
    loading
  })
}

// 获取巡检记录列表
export const getInspectionRecode = (data: any, loading: Loading = false) => {
  return request({
    url:
      '/operate/inspection-recode/getInspectionRecodeByOrderNo?orderNo=' +
      data.orderNo,
    method: 'post',
    data: {},
    loading
  })
}

// 获取计划管理列表
export const getAllPlanManageList = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/planManage/getAllPlanManageList',
    method: 'get',
    params,
    loading
  })
}

// 登录人不是工单的区域监盘人
export const isAllocated = (params: any, loading: Loading = false) => {
  return request({
    url: '/operate/uc/isAllocated',
    method: 'get',
    params,
    loading
  })
}

// 是否有工单强制取消按钮的权限
export const getCancelButtonPermission = (loading: Loading = false) => {
  return request({
    url: '/operate/uc/isForeCancelButton',
    method: 'get',
    loading
  })
}

// 强制取消工单接口(包括正在进行的审批流程)
export const forceCancelWorkOrder = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/workOrder/forceCancelWorkOrder',
    method: 'post',
    data,
    loading
  })
}
