import request from '@/utils/request'

// 巡检-检修工单列表查询
export const inspectionList = (data: any, loading: Loading = false) => {
  return request({
    url: '/operate/work-inspection-order/list',
    method: 'post',
    data: { ...data, code: localStorage.getItem('PVOM_COMPANY_CODE') },
    loading: loading
  })
}
// 巡检-检修记录查询
export const getInspectionRecord = (params: any) => {
  return request({
    url: '/operate/work-inspection-order/getInspectionRecord',
    method: 'get',
    params: params
  })
}
// 巡检-检修记录查询-项目表
export const inspectionDetail = (params: any) => {
  return request({
    url: '/operate/work-inspection-order/inspectionDetail',
    method: 'get',
    params: params
  })
}
// 巡检-维保任务
export const getMaintenanceTask = (params: any) => {
  return request({
    url: '/operate/work-inspection-order/getMaintenanceTask',
    method: 'get',
    params: params
  })
}
// 巡检-财务结算
export const getFinancialSettlementInfo = (params: any) => {
  return request({
    url: '/operate/work-inspection-order/getFinancialSettlementInfo',
    method: 'get',
    params: params
  })
}
// 备注信息明细、单据
export const getSparePartsInfo = (params: any) => {
  return request({
    url: '/operate/work-inspection-order/getSparePartsInfo',
    method: 'get',
    params: params
  })
}
export const getSparePartsLibrary = (params: any) => {
  return request({
    url: '/operate/work-inspection-order/getSparePartsLibrary',
    method: 'get',
    params: params
  })
}
// 现场维修
export const getSignHandleInfo = (params: any) => {
  return request({
    url: '/operate/work-inspection-order/getSignHandleInfo',
    method: 'get',
    params: params
  })
}
// SLA
export const getSlaStatisticsInfo = (params: any) => {
  return request({
    url: '/operate/work-inspection-order/getSlaStatisticsInfo',
    method: 'get',
    params: params
  })
}
// 用户反馈
export const getUserFeedbackInfo = (params: any) => {
  return request({
    url: '/operate/work-inspection-order/getUserFeedbackInfo',
    method: 'get',
    params: params
  })
}
