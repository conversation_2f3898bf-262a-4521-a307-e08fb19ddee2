import request from '@/utils/request'

// 获取故障类型列表
export const getFaultTypeList = (data: any) => {
  return request({
    url: '/opAi/faultType',
    method: 'post',
    data
  })
}

// 设置维护人
export const setUpdateUser = (data: any) => {
  return request({
    url: '/opAi/updateUser',
    method: 'post',
    data
  })
}

// 发送短信
export const setSendSms = (data: any) => {
  return request({
    url: '/opAi/sendSms',
    method: 'post',
    data
  })
}

// 结果修正
export const setRevised = (data: any) => {
  return request({
    url: '/opAi/revised',
    method: 'post',
    data
  })
}

// 派单
export const setDispatch = (data: any) => {
  return request({
    url: '/opAi/dispatch',
    method: 'post',
    data
  })
}

// 排查完整记录
export const getInvestigationRecord = (data: any) => {
  return request({
    url: '/opAi/investigationRecord',
    method: 'post',
    data
  })
}

// 更新预警状态
export const updateWarnStatus = (data: any) => {
  return request({
    url: '/opAi/updateWarnStatus',
    method: 'post',
    data
  })
}

// 获取排查完整记录
export const getInvestigationRecordDetail = (params: any) => {
  return request({
    url: '/opAi/getInvestigationRecord',
    method: 'get',
    params
  })
}
