<template>
  <div class="add">
    <div class="page-operate">
      <div class="operate-title">
        {{ route.params.pageStatus == 'add' ? '新建' : '编辑' }}经验反馈单
      </div>
    </div>
    <div class="info-base">
      <addBase
        ref="baseRef"
        :detail-data="detailData"
        @get-base-data="getBaseData"
      >
      </addBase>
      <addImprove
        ref="improveRef"
        :detail-data="detailData"
        @get-improve-data="getImproveData"
      >
      </addImprove>
      <addFiles
        ref="filesRef"
        :detail-data="detailData"
        @get-files-data="getFilesData"
      >
      </addFiles>
    </div>
    <div class="page-footer">
      <el-button plain @click="onCancel">返回</el-button>
      <el-button v-preventReClick="1000" type="primary" @click="onSumbit(1)">
        保存草稿
      </el-button>
      <el-button v-preventReClick="1000" type="primary" @click="onSumbit(2)">
        提交审批
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import addBase from './components/addBase.vue'
import addImprove from './components/addImprove.vue'
import addFiles from './components/addFiles.vue'
import { safeRange as safeRangeAPI } from '@/api/index.ts'

const route = useRoute()
const router = useRouter()

onMounted(async () => {
  // 编辑草稿
  if (route.params.id) {
    getDetail()
  }
})

let detailData: any = ref()
// 获取详情
const getDetail = async () => {
  try {
    let { data } = await safeRangeAPI.getFeedbackDetail(
      { id: route.params.id },
      true
    )
    detailData.value = data.data
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

// 第一部分
const baseData = ref({})
const baseValid = ref(false)
const baseRef = ref<any>(null)
const getBaseData = (val: any) => {
  baseValid.value = val.valid
  baseData.value = val.data
}
// 第二部分
const improveData = ref({})
const improveRef = ref<any>(null)
const improveVaild = ref(false)
const getImproveData = (val: any) => {
  improveVaild.value = val.valid
  improveData.value = val.data
}
// 第三部分
const filesData = ref({})
const filesRef = ref<any>(null)
const filesVaild = ref(false)
const getFilesData = (val: any) => {
  filesVaild.value = val.valid
  filesData.value = val.data
}

// 保存草稿1 提交审批2
const onSumbit = async (type: number) => {
  await baseRef.value.submitForm(type)
  await improveRef.value.submitForm(type)
  await filesRef.value.submitForm(type)

  if (type === 2) {
    if (!baseValid.value || !improveVaild.value || !filesVaild.value) {
      return
    }
  }
  try {
    const params = {
      id: route.params.id || '',
      ...baseData.value,
      ...improveData.value,
      ...filesData.value
    }

    // console.log(params)
    // return;
    const apiName = route.params.id
      ? safeRangeAPI.editFeedback
      : safeRangeAPI.addFeedback
    const { data } = await apiName(params, true)
    if (data.code === '200') {
      if (type === 2) {
        const res = await safeRangeAPI.approveFeedback(
          {
            businessId: route.params.id ? route.params.id : data.data
          },
          true
        )
        if (res.data.code === '200') {
          ElMessage({
            message: `操作成功!`,
            type: 'success'
          })
          router.push('/safe-range/feedback')
        } else {
          ElMessage({
            message: res.data.message,
            type: 'error'
          })
        }
      } else if (type === 1) {
        ElMessage({
          message: `操作成功!`,
          type: 'success'
        })
        router.push('/safe-range/feedback')
      }
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {}
}

const onCancel = () => {
  router.go(-1)
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
