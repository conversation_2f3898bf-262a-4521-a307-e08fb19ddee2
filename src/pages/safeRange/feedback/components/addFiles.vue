<template>
  <el-form
    ref="formRef"
    :model="formData"
    label-suffix=""
    :rules="formRules"
    class="mt24"
  >
    <el-row :gutter="24">
      <el-col :span="10">
        <el-form-item label="支持材料" prop="supportingMaterial">
          <el-input
            v-model.trim="formData.supportingMaterial"
            placeholder="请输入支持材料"
            maxlength="1024"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="10" :offset="3">
        <el-form-item label="验证关闭说明" prop="verifyOffDesc">
          <el-input
            v-model.trim="formData.verifyOffDesc"
            placeholder="请输入验证关闭说明"
            maxlength="1024"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="审核人" prop="auditor">
          <el-select
            v-model="formData.auditor"
            placeholder="请选择审核人"
            filterable
            clearable
          >
            <el-option
              v-for="item in roleList6"
              :key="item.ucUserId"
              :label="item.ucUserName"
              :value="`${item.ucUserName}_${item.ucUserId}`"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10" :offset="3">
        <el-form-item label="批准人" prop="approver">
          <el-select
            v-model="formData.approver"
            placeholder="请选择批准人"
            filterable
            clearable
          >
            <el-option
              v-for="item in roleList7"
              :key="item.ucUserId"
              :label="item.ucUserName"
              :value="`${item.ucUserName}_${item.ucUserId}`"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="附件" :class="files?.length ? 'mb16' : 'mb24'">
          <SpicUpload
            v-model="files"
            :file-size="50"
            :file-ext="fileExt"
            type="file"
            :limit="20"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import SpicUpload from '@/components/spic-upload'
import useRoles from '@/hooks/useRoles'
import type { FormInstance, FormRules } from 'element-plus'

let files: any = ref([]) // 附件
const fileExt = ref(['jpg', 'png', 'pdf'])
const { roleList6, roleList7 } = useRoles()

type PropsType = {
  detailData: any
}
onMounted(async () => {})
const props = withDefaults(defineProps<PropsType>(), {})

watch(
  () => props?.detailData,
  (val: any) => {
    if (val) {
      formRef.value?.resetFields()
      for (const key in formData.value) {
        formData.value[key] = val[key]
      }
      if (val.attachments) {
        files.value = val.attachments.split(',')
      }
    }
  }
)

const formData = ref<any>({
  supportingMaterial: '', // 支持材料
  verifyOffDesc: '', // 验证关闭说明
  auditor: '', // 审核人
  approver: '', // 批准人
  attachments: '' // 附件
})
const formRules = reactive<FormRules>({
  supportingMaterial: [
    { required: true, message: '请输入支持材料', trigger: 'blur' }
  ],
  verifyOffDesc: [
    { required: true, message: '请输入验证关闭说明', trigger: 'blur' }
  ],
  auditor: [{ required: true, message: '请选择审核人', trigger: 'change' }],
  approver: [{ required: true, message: '请选择批准人', trigger: 'change' }]
})

watch(
  () => files.value,
  (val) => {
    formData.value.attachments = val?.length ? val.join(',') : ''
  },
  {
    deep: true
  }
)

// 提交 保存草稿1 签发审批2
const formRef = ref<FormInstance>()
const emit = defineEmits(['getFilesData'])
const submitForm = async (type: number) => {
  if (!formRef.value) return
  if (type === 1) {
    emit('getFilesData', {
      data: { ...formData.value }
    })
  } else {
    await formRef.value.validate((valid) => {
      emit('getFilesData', {
        valid,
        data: { ...formData.value }
      })
      return valid
    })
  }
}

// 重置
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})
</script>

<style scoped src="../assets/index.scss"></style>
