<template>
  <el-form ref="formRef" :model="formData" label-suffix="" :rules="formRules">
    <el-row :gutter="24">
      <template v-if="route.params.id">
        <el-col :span="10">
          <el-form-item label="反馈单编号" prop="expNo">
            <el-input
              v-model="formData.expNo"
              placeholder="创建后自动生成"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="3">
          <el-form-item label="审核状态" prop="expStatus">
            <el-input
              v-model="formData.expStatusVal"
              placeholder="创建后自动生成"
              disabled
            />
          </el-form-item>
        </el-col>
      </template>
      <el-col :span="10">
        <el-form-item label="标题" prop="expName">
          <el-input
            v-model.trim="formData.expName"
            placeholder="请输入标题"
            maxlength="64"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="10" :offset="3">
        <el-form-item label="单位" prop="operationCompanyId">
          <el-select
            v-model="formData.operationCompanyId"
            placeholder="请选择单位"
            filterable
            clearable
            @change="changeDepart"
          >
            <el-option
              v-for="item in companyList"
              :key="item.companyCode"
              :label="item.companyName"
              :value="item.companyCode"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="反馈类别" prop="expType">
          <el-select
            v-model="formData.expType"
            placeholder="请选择反馈类别"
            clearable
          >
            <el-option
              v-for="item in feedbackList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10" :offset="3">
        <el-form-item label="来源" prop="expSource">
          <el-select
            v-model="formData.expSource"
            placeholder="请选择来源"
            clearable
          >
            <el-option
              v-for="item in originList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="报告级别" prop="expLevel">
          <el-select
            v-model="formData.expLevel"
            placeholder="请选择报告级别"
            clearable
          >
            <el-option
              v-for="item in reportList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10" :offset="3">
        <el-form-item label="产业类别" prop="estateTypeList">
          <el-select
            v-model="formData.estateTypeList"
            placeholder="请选择产业类别"
            multiple
            clearable
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
          >
            <el-option
              v-for="item in industryList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="领域类别" prop="domainType">
          <el-select
            v-model="formData.domainType"
            placeholder="请选择领域类别"
            clearable
          >
            <el-option
              v-for="item in fieldList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10" :offset="3">
        <el-form-item label="信息描述" prop="msgDesc">
          <el-input
            v-model.trim="formData.msgDesc"
            placeholder="请输入信息描述"
            maxlength="1024"
          />
        </el-form-item>
      </el-col>
      <el-col :span="23">
        <el-form-item label="原因分析" prop="causeAnalysis">
          <el-input
            v-model.trim="formData.causeAnalysis"
            :rows="3"
            type="textarea"
            placeholder="请输入原因分析"
            maxlength="1024"
            autocomplete="off"
            show-word-limit
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'
import filters from '@/utils/filter.js'

const route = useRoute()
const router = useRouter()
type PropsType = {
  detailData: any
}
const props = withDefaults(defineProps<PropsType>(), {})

watch(
  () => props?.detailData,
  (val: any) => {
    formRef.value?.resetFields()
    if (val) {
      if (val.estateType) {
        val.estateTypeList = val.estateType.split(',').map(Number)
      }
      if (val.expStatus) {
        val.expStatusVal = filters.feedbackStatus(val.expStatus)
      }
      for (const key in formData.value) {
        formData.value[key] = val[key]
      }
    }
  }
)

let formData = ref<any>({
  expNo: '', // 反馈单编号
  expStatus: '', // 审核状态
  expStatusVal: '', // 审核状态 name
  expName: '', // 标题
  operationCompanyId: '', // 单位 id
  operationCompanyName: '', // 单位name
  expType: '', //  反馈类别
  expSource: '', // 来源
  expLevel: '', // 报告级别
  estateTypeList: [], // 产业类别 多选
  estateType: '', // 产业类别 字符串 格式'1,2,3'
  domainType: '', // 领域类别
  msgDesc: '', // 信息描述
  causeAnalysis: '' // 原因分析
})

const formRules = reactive<FormRules>({
  expNo: [{ required: true, message: '请输入反馈单编号', trigger: 'blur' }],
  expStatus: [{ required: true, message: '请输入审核状态', trigger: 'blur' }],
  expName: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  operationCompanyId: [
    { required: true, message: '请选择单位', trigger: 'change' }
  ],
  expType: [{ required: true, message: '请选择反馈类别', trigger: 'change' }],
  expSource: [{ required: true, message: '请选择来源', trigger: 'change' }],
  expLevel: [{ required: true, message: '请选择报告级别', trigger: 'change' }],
  estateTypeList: [
    { required: true, message: '请选择产业类别', trigger: 'change' }
  ],
  domainType: [
    { required: true, message: '请选择领域类别', trigger: 'change' }
  ],
  msgDesc: [{ required: true, message: '请输入信息描述', trigger: 'blur' }],
  causeAnalysis: [
    { required: true, message: '请输入原因分析', trigger: 'blur' }
  ]
})

// 提交 保存草稿1 签发审批2
const formRef = ref<FormInstance>()
const emit = defineEmits(['getBaseData'])
const submitForm = async (type: number) => {
  if (!formRef.value) return
  if (formData.value?.estateTypeList?.length) {
    formData.value.estateType = formData.value.estateTypeList.join(',')
  }
  if (type === 1) {
    emit('getBaseData', {
      data: { ...formData.value }
    })
  } else {
    await formRef.value.validate((valid) => {
      emit('getBaseData', {
        valid,
        data: { ...formData.value }
      })
      return valid
    })
  }
}

onMounted(() => {
  getCompanyList()
})

let companyList = ref<Record<string, any>[]>([])
// 获取单位list
const getCompanyList = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    companyList.value = data.data
  } catch (e: any) {}
}

// 修改部门
const changeDepart = (val: any) => {
  companyList.value.forEach((item: any) => {
    if (val == item.companyCode) {
      formData.value.operationCompanyName = item.companyName
    }
  })
}

// 下拉框的options
// 反馈类别
const feedbackList = [
  { value: 1, label: '经验教训' },
  { value: 2, label: '良好实践' }
]
// 来源
const originList = [
  { value: 1, label: '内部' },
  { value: 2, label: '外部' }
]
// 报告类别
const reportList = [
  { value: 'A', label: 'A' },
  { value: 'B', label: 'B' },
  { value: 'C', label: 'C' }
]
// 产业类别
const industryList = [
  { value: 1, label: '核电' },
  { value: 2, label: '火电' },
  { value: 3, label: '水电' },
  { value: 4, label: '新能源' },
  { value: 5, label: '煤炭' },
  { value: 6, label: '铝业' },
  { value: 7, label: '环保' },
  { value: 8, label: '路港' },
  { value: 9, label: '海外' },
  { value: 10, label: '其他' }
]
// 领域类别
const fieldList = [
  { value: 1, label: '生产运营' },
  { value: 2, label: '工程建设' },
  { value: 3, label: '产品制造' },
  { value: 4, label: '服务支持' }
]

// 重置
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})
</script>

<style scoped src="../assets/index.scss"></style>
