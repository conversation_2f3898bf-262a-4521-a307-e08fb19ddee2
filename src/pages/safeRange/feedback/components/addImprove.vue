<template>
  <el-form ref="formRef" :model="formData">
    <div class="mbn8">改进行动</div>
    <table-info
      ref="tableRef"
      :columns="columns"
      :table-data="tableList"
      :maxlength="maxlength"
      @get-tabel-data="getTData"
    ></table-info>
  </el-form>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import tableInfo from './tableRow.vue'
const route = useRoute()
// const ticketWorkId = route.query.id ? Number(route.query.id) : ''
const props = defineProps({
  detailData: {
    type: Object,
    require: true,
    default: null
  }
})
// 表格
const columns = ref([
  { prop: 'index', label: '' },
  {
    prop: 'actionItem',
    label: '行动项',
    slotName: 'actionItem',
    minWidth: 200,
    maxlength: 20,
    placeholder: '行动项'
  },
  {
    prop: 'dutySectionUser',
    label: '责任部门/人员',
    slotName: 'dutySectionUser',
    minWidth: 116,
    maxlength: 10,
    placeholder: '责任部门/人员'
  },
  {
    prop: 'completionTime',
    label: '完成时间',
    slotName: 'completionTime',
    minWidth: 100,
    placeholder: '完成时间',
    type: 'datetime',
    format: 'YYYY-MM-DD HH:mm:ss',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 66,
    fixed: 'right'
  }
])
const tableList = ref<any[]>([])

watch(
  () => props?.detailData,
  (val: any) => {
    if (val.improveActionList) {
      val.improveActionList.forEach((item: any) => {
        tableList.value.push(item)
      })
    }
  }
)
const maxlength = ref(1024)
const tableRef = ref<any>(null)
const tData = ref<any>([])
const tValid = ref(false)
const getTData = (val: any) => {
  tValid.value = val.valid
  if (val.data?.length) {
    tData.value = val.data.map((item: any) => {
      return {
        actionItem: item.actionItem || '',
        completionTime: item.completionTime || '',
        dutySectionUser: item.dutySectionUser || '',
        expId: route.params.id || ''
      }
    })
  }
}

// 提交 保存草稿1 提交审批2
let formData = ref()
const formRef = ref<FormInstance>()
const emit = defineEmits(['submitTable', 'getImproveData'])
const submitForm = async (type: number) => {
  if (!formRef.value) return
  await tableRef.value.submitTable(type)
  formData.value = [...tData.value]
  let valid = tValid.value
  emit('getImproveData', {
    valid,
    data: {
      improveActionList: formData.value
    }
  })
}
defineExpose({
  submitForm
})
// defineExpose({ boxPagination, computedHeight })
</script>
<style scoped src="../assets/index.scss"></style>
