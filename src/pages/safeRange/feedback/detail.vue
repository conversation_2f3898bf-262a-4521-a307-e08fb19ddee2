<template>
  <div class="detail">
    <div class="page-operate">
      <div class="operate-title">查看详情</div>
    </div>
    <div class="info-base">
      <el-form>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="反馈单编号">
              {{ detailData?.expNo || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核状态">
              {{
                detailData?.expStatus
                  ? filters.feedbackStatus(detailData?.expStatus)
                  : '--'
              }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标题">
              {{ detailData?.expName || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位">
              {{ detailData?.operationCompanyName || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="反馈类别">
              {{
                detailData?.expType
                  ? filters.feedbackType(detailData?.expType)
                  : '--'
              }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="来源">
              {{
                detailData?.expSource
                  ? filters.feedbackOrigin(detailData?.expSource)
                  : '--'
              }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报告级别">
              {{ detailData?.expLevel || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产业类别">
              {{ detailData?.estateVal || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="领域类别">
              {{
                detailData?.domainType
                  ? filters.feedbackDomainType(detailData?.domainType)
                  : '--'
              }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="信息描述">
              {{ detailData?.msgDesc || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="原因分析">
              {{ detailData?.causeAnalysis || '--' }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-form>
        <div class="mb16 mt16">改进行动</div>
        <vis-table-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :columns="columns"
          :data="tableList"
          background
          border
          :height="'auto'"
          :show-overflow-tooltip="true"
          class="vis-table-pagination safe-table1"
          highlight-current-row
        >
        </vis-table-pagination>
      </el-form>
      <el-form>
        <el-row :gutter="24" class="mt24">
          <el-col :span="12">
            <el-form-item label="支持材料">
              {{ detailData?.supportingMaterial || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="验证关闭说明">
              {{ detailData?.verifyOffDesc || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核人">
              {{
                detailData?.auditor ? detailData?.auditor.split('_')[0] : '--'
              }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批准人">
              {{
                detailData?.approver ? detailData?.approver.split('_')[0] : '--'
              }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件" :class="files?.length ? 'mb16' : 'mb24'">
              <SpicUpload
                v-if="files?.length"
                v-model="files"
                :file-size="50"
                :file-ext="fileExt"
                type="file"
                :limit="20"
                :upload-display="true"
                disabled
              />
              <span v-else> -- </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import SpicUpload from '@/components/spic-upload'
import visTablePagination from '@/components/table-pagination.vue'
import { safeRange as safeRangeAPI } from '@/api/index.ts'
import filters from '@/utils/filter.js'

const route = useRoute()
onMounted(async () => {
  await getFeedbackDetail()
})

let detailData: any = ref()
const tableList = ref<any[]>([]) // 改进行动
let files: any = ref([]) // 附件
const fileExt = ref(['jpg', 'png', 'pdf'])

// 获取详情
const getFeedbackDetail = async () => {
  try {
    let { data } = await safeRangeAPI.getFeedbackDetail(
      { id: route.params.id },
      true
    )
    files.value = data.data?.attachments
      ? data.data?.attachments.split(',')
      : []
    tableList.value = data.data?.improveActionList?.length
      ? data.data.improveActionList
      : []
    // data.data.estateType = '1,2,4,5,10'
    if (data.data?.estateType) {
      let estateTypeList = data.data.estateType.split(',')
      let formatEstateTypeList = estateTypeList.map((item: any) => {
        return filters.industryType(Number(item))
      })
      data.data.estateVal = formatEstateTypeList.join(',')
    }
    detailData.value = data.data
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

const columns = [
  { prop: 'index', label: '序号', minWidth: 100 },
  { prop: 'actionItem', label: '行动项', minWidth: 200 },
  { prop: 'dutySectionUser', label: '责任部门/人员', minWidth: 116 },
  { prop: 'completionTime', label: '完成时间', minWidth: 100 }
]
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
