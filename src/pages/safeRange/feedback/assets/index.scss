.info-base {
  padding-bottom: 0;
  .operate {
    margin-bottom: 24px;
    margin-bottom: 0;
    padding-left: 0;
  }
  .info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 24px;
    .info-item {
      padding-bottom: 24px;
    }
    .label {
      color: rgba(0, 0, 0, 0.45);
    }
  }
}

.page-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
  line-height: 64px;
  margin: 0 24px 24px;
  height: 64px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  & > .el-button + .el-button {
    margin-left: 16px !important;
  }
  & > .el-button {
    min-width: 72px !important;
    height: 40px !important;
  }
}

// 详情
.detail {
  word-wrap: break-word;
  word-break: break-all;
  :deep(.el-form-item--default .el-form-item__label) {
    color: rgba(0, 0, 0, 0.45) !important;
    height: 22px !important;
    line-height: 22px !important;
    padding-right: 8px;
    width: 92px !important;
  }

  :deep(.el-form-item--default .el-form-item__content) {
    color: rgba(0, 0, 0, 0.85) !important;
    line-height: 22px !important;
  }
}

// 新建
.add {
  word-wrap: break-word;
  word-break: break-all;

  .el-row .el-col {
    :deep(.el-form-item__label) {
      width: 108px !important;
    }
  }
}

.mb16 {
  margin-bottom: 16px !important;
}
.mb24 {
  margin-bottom: 24px !important;
}
.mt16 {
  margin-top: 16px !important;
}
.mt24 {
  margin-top: 24px !important;
}
.mbn8 {
  margin-bottom: -8px;
}
