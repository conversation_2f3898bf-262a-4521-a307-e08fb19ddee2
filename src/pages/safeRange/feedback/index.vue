<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="40px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>经验反馈单管理列表</p>
        <el-button type="primary" @click="handleAdd(null)">
          <el-icon><Plus /></el-icon>
          新建经验反馈单
        </el-button>
      </div>
      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="tableColumns"
        :total="tableData.total"
        :data="tableData.data"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #expType="{ row }">
          {{ row.expType ? filters.feedbackType(row.expType) : '--' }}
        </template>
        <template #expStatus="{ row }">
          <el-tag v-if="row.expStatus == 1" type="success" color="#F0F5FA">
            {{ filters.feedbackStatus(row.expStatus) }}
          </el-tag>
          <el-tag v-if="row.expStatus == 2" type="info">
            {{ filters.feedbackStatus(row.expStatus) }}
          </el-tag>
          <el-tag v-if="row.expStatus == 3" type="warning">
            {{ filters.feedbackStatus(row.expStatus) }}
          </el-tag>
          <el-tag v-if="row.expStatus == 4" type="danger">
            {{ filters.feedbackStatus(row.expStatus) }}
          </el-tag>
        </template>
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button
              v-if="row.expStatus == 1 || row.expStatus == 3"
              link
              @click="handleDetail(row)"
            >
              查看详情
            </el-button>
            <el-button
              v-if="row.expStatus == 2 || row.expStatus == 4"
              link
              @click="handleAdd(row)"
            >
              编辑
            </el-button>
            <el-button v-if="row.expStatus == 2" link @click="deleteItem(row)">
              删除
            </el-button>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import { safeRange as safeRangeAPI } from '@/api/index.ts'
import filters from '@/utils/filter.js'

const router = useRouter()
let searchData = ref({
  expName: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([{ label: '标题', prop: 'expName' }])
const tableColumns = [
  {
    prop: 'expNo',
    label: '经验反馈单编号',
    minWidth: 100
  },
  {
    prop: 'expName',
    label: '标题',
    minWidth: 140
  },
  {
    prop: 'expType',
    label: '反馈类别',
    minWidth: 100,
    slotName: 'expType'
  },
  {
    prop: 'expStatus',
    label: '审核状态',
    minWidth: 100,
    slotName: 'expStatus'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 100,
    fixed: 'right'
  }
]
const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageNum = 1
  searchData.value.pageSize = 10
  getTableData()
}

const getTableData = async () => {
  try {
    let { data } = await safeRangeAPI.getFeedbackList(searchData.value, true)
    tableData.data = data?.data?.records || []
    tableData.total = data?.data?.total || 0
  } catch (e: any) {
    tableData.data = []
    tableData.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

onMounted(async () => {
  getTableData()
})

const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

// 删除
const deleteItem = async (val: any) => {
  ElMessageBox.confirm('确定删除?', '删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const { data } = await safeRangeAPI.deleteFeedback({ id: val.id }, true)
        if (data.code === '200') {
          ElMessage({
            message: `删除成功!`,
            type: 'success'
          })
          getTableData()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    })
    .catch(() => {})
}
const handleAdd = async (val: any) => {
  if (val) {
    router.push(`/safe-range/feedback/add/edit/${val.id}`)
  } else {
    router.push(`/safe-range/feedback/add/add`)
  }
}

const handleDetail = async (item: any) => {
  router.push(`/safe-range/feedback/detail/${item.id}`)
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped></style>
