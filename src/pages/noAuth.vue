<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const path = route.query.path as string
const handleRefresh = () => {
  path && router.replace(path)
}
</script>
<template>
  <div class="wrapper">
    <el-result
      icon="error"
      title="权限不足"
      sub-title="您未配置任何数据权限，请联系管理员进行配置。"
    >
      <template #extra>
        <el-button type="primary" @click="handleRefresh">刷新</el-button>
      </template>
    </el-result>
  </div>
</template>
<style lang="scss" scoped>
.wrapper {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
