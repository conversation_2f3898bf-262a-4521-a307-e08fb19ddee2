<!--
 * @Author: 赵鹏鹏
 * @Date: 2024-03-25 13:33:43
 * @LastEditors: 赵鹏鹏
 * @Description: 设备故障列表
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="设备故障列表"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="800px"
    class="vis-dialog record-dialog"
  >
    <vis-table-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchData.pageSize"
      :columns="columns"
      :total="list.total"
      :data="list.records"
      :height="469"
      :show-overflow-tooltip="true"
      :current-page="searchData.pageNum"
      background
      class="vis-table-pagination"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    >
      <!-- 故障状态 -->
      <template #faultLevelName="{ row }">
        <div
          v-if="row.faultLevelName"
          :class="[
            row.faultLevelName == '已处理'
              ? 'table-faultLevelName__ noClass'
              : 'table-faultLevelName__ yeClass'
          ]"
        >
          {{ row.faultLevelName }}
        </div>
        <div v-else>--</div>
      </template>
    </vis-table-pagination>
  </el-dialog>
</template>
<script lang="ts" setup>
import { getDeviceFaultRecordList } from '@/api/module/fault.ts'
import visTablePagination from '@/components/table-pagination.vue'

let dialogVisible = ref(false)
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {
        faultTypeName: '', // 故障类型名称
        faultCode: '', // 故障类型代码
        id: ''
      }
    }
  }
})
const searchData = ref({
  pageNum: 1, // 当前页
  pageSize: 10, // 每页条数
  id: ''
})

watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      searchData.value.id = props.rows.id || ''
      if (props.rows.faultTypeName) {
        searchData.value.faultTypeName = props.rows.faultTypeName
      }
      if (props.rows.faultCode) {
        searchData.value.faultCode = props.rows.faultCode
      }
      getListFun()
    }
  },
  {
    deep: true
  }
)
const columns = reactive([
  {
    prop: 'stationName',
    label: '电站名称'
  },
  {
    prop: 'deviceName',
    label: '设备名称'
  },
  {
    prop: 'faultTypeName',
    label: '故障名称'
  },
  {
    prop: 'foundTime',
    label: '发现时间'
  },
  {
    prop: 'faultLevelName',
    label: '故障状态',
    slotName: 'faultLevelName'
  }
])
const list = reactive({
  records: [],
  total: 0
})
const handleSizeChange = (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getListFun()
}
const handleCurrentChange = (params: any) => {
  searchData.value.pageNum = params.currentPage
  getListFun()
}
const getListFun = async () => {
  await getDeviceFaultRecordList({
    ...searchData.value
  })
    .then(({ data }) => {
      if (data.code == 200) {
        list.records = data.data.records || []
        list.total = data.data.total
      } else {
        list.records = []
        list.total = 0
      }
    })
    .catch((err) => {
      list.records = []
      list.total = 0
      console.log(err)
    })
}
// 关闭弹窗
const emit = defineEmits(['closeDialog'])
const dialogClose = () => {
  emit('closeDialog', false)
}
</script>
<style lang="scss">
.el-dialog.record-dialog .el-dialog__body {
  padding-bottom: 15px !important;
}
.table-faultLevelName__ {
  width: 58px !important;
  text-align: center;
}
.table-faultLevelName__.noClass {
  background: rgba(41, 204, 160, 0.1);
  color: rgba(41, 204, 160, 1);
}
.table-faultLevelName__.yeClass {
  color: #ff0000;
  background-color: rgba(255, 0, 0, 0.1);
}
</style>
