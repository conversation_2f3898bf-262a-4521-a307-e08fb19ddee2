<!--
 * @Author: 赵鹏鹏
 * @Date: 2024-03-25 13:33:43
 * @LastEditors: 赵鹏鹏
 * @Description: 故障知识弹窗详情
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="故障知识详情"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="520px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="infoDetaile"
        label-suffix=""
        label-width="130px"
        class="elFrom"
      >
        <!-- 故障类型 -->
        <el-form-item label="故障类型" prop="faultTypeName">
          <span>{{ infoDetaile.faultTypeName || '--' }}</span>
        </el-form-item>

        <!-- 故障特征 -->
        <el-form-item label="故障特征" prop="faultTraits">
          <span>{{ infoDetaile.faultTraits || '--' }}</span>
        </el-form-item>

        <!-- 故障根本原因 -->
        <el-form-item label="故障根本原因" prop="faultCause">
          <span>{{ infoDetaile.faultCause || '--' }}</span>
        </el-form-item>

        <!-- 处理措施 -->
        <el-form-item label="处理措施" prop="measures">
          <span>{{ infoDetaile.measures || '--' }}</span>
        </el-form-item>

        <!-- 设备品牌 -->
        <el-form-item label="设备品牌" prop="brandName">
          <span>{{ infoDetaile.brandName || '--' }}</span>
        </el-form-item>

        <!-- 设备型号 -->
        <el-form-item label="设备型号" prop="deviceModelsName">
          <span>{{ infoDetaile.deviceModelsName || '--' }}</span>
        </el-form-item>

        <!-- 关键词 -->
        <el-form-item label="关键词" prop="keyword">
          <span>{{ infoDetaile.keyword || '--' }}</span>
        </el-form-item>

        <!-- 附件 -->
        <el-form-item label="附件" prop="fileName" class="fileForm">
          <div class="fileBox__" v-if="infoDetaile.fileName2">
            <el-icon size="16" color="#29CCA0"><Link /></el-icon>
            <span class="fileText">
              {{
                infoDetaile.fileName2 && infoDetaile.fileName2.length > 15
                  ? infoDetaile.fileName2.slice(0, 16) +
                    '...' +
                    getFileExtension(infoDetaile.fileName2)
                  : infoDetaile.fileName2
              }}</span
            >
            <el-icon
              size="16"
              color="#29CCA0"
              @click="handleDownload(infoDetaile)"
              ><Download
            /></el-icon>
          </div>
          <div v-else>--</div>
        </el-form-item>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="dialogClose">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import request from '@/utils/request'
import { getFaultKnowledgeInfo } from '@/api/module/fault.ts'
let dialogVisible = ref(false)
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  detaileId: {
    type: String,
    default: ''
  }
})
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      getDetaileFun()
    }
  }
)
const infoDetaile = ref({})
const getDetaileFun = async () => {
  await getFaultKnowledgeInfo({
    id: props.detaileId || ''
  })
    .then(({ data }) => {
      if (data.code == 200) {
        infoDetaile.value = data.data
        let fileName1 = '',
          fileName2 = ''
        if (data.data.fileName) {
          fileName1 = data.data.fileName.split('@')[0]
          fileName2 = data.data.fileName.split('@')[1]
        }
        infoDetaile.value.fileName1 = fileName1
        infoDetaile.value.fileName2 = fileName2
      } else {
        infoDetaile.value = {}
      }
    })
    .catch((err) => {
      infoDetaile.value = {}
      console.log(err)
    })
}
const getFileExtension = (filename: string) => {
  return /[.]/.exec(filename) ? /[^.]+$/.exec(filename)[0] : undefined
}
// 下载
const handleDownload = async (file: any) => {
  try {
    const { data } = await request({
      url: '/operate/operatorsManage/downloadFaultKnowledgeFile',
      headers: { 'Content-Type': 'text/plan' },
      responseType: 'blob',
      method: 'post',
      data: `${file.fileName1}@${file.fileName2}`,
      loading: true
    })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(data)
    link.download = file.fileName2
    link.click()
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
// 关闭弹窗
const emit = defineEmits(['closeDetaileDialog'])
const dialogClose = () => {
  emit('closeDetaileDialog', false)
}
</script>
<style lang="scss">
.elFrom {
  .el-form-item__content {
    .fileBox__ {
      width: 280px;
      display: flex;
      align-items: center;
      background: rgba(246, 248, 250, 1);
      padding: 0 16px;
      box-sizing: border-box;
      .fileText {
        margin: 0 30px 0 8px;
      }
    }
  }
}
</style>
