<!--
 * @Author: 赵鹏鹏
 * @Date: 2024-03-25 13:33:43
 * @LastEditors: 赵鹏鹏
 * @Description: 故障知识弹窗
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="故障知识"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="800px"
    class="vis-dialog knowledge-dialog"
  >
    <vis-table-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchData.pageSize"
      :columns="columns"
      :total="list.total"
      :data="list.records"
      :height="469"
      :show-overflow-tooltip="true"
      :current-page="searchData.pageNum"
      background
      class="vis-table-pagination"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    >
      <!-- 操作 -->
      <template #operate="{ row }">
        <div class="table-operate">
          <el-button link @click.stop="lookDetaile(row)">查看详情</el-button>
        </div>
      </template>
    </vis-table-pagination>

    <!-- 详情弹窗 -->
    <faultDetail
      :isShow="isShowfaultDetail"
      :detaileId="detaileId"
      @closeDetaileDialog="closeDetaileDialog"
    ></faultDetail>
  </el-dialog>
</template>
<script lang="ts" setup>
import { getFaultKnowledgeList } from '@/api/module/fault.ts'
import faultDetail from './fault-detail.vue'
import visTablePagination from '@/components/table-pagination.vue'

let dialogVisible = ref(false)
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {
        faultTypeName: '', // 故障类型名称
        faultCode: '' // 故障类型代码
      }
    }
  }
})
const searchData = ref({
  pageNum: 1, // 当前页
  pageSize: 10, // 每页条数
  id: ''
})

watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      searchData.value.id = props.rows.id || ''
      getListFun()
    }
  }
)
const columns = reactive([
  {
    prop: 'faultTypeName',
    label: '关联故障类型'
  },
  {
    prop: 'faultCode',
    label: '故障类型编码'
  },
  {
    prop: 'deviceModelsNameAnd',
    label: '所属设备型号'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 80,
    fixed: 'right'
  }
])
const list = reactive({
  records: [],
  total: 0
})
const handleSizeChange = (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getListFun()
}
const handleCurrentChange = (params: any) => {
  searchData.value.pageNum = params.currentPage
  getListFun()
}
const getListFun = async () => {
  await getFaultKnowledgeList({
    ...searchData.value
  })
    .then(({ data }) => {
      if (data.code == 200) {
        let arr = []
        data.data.records.length &&
          data.data.records.forEach((item: any) => {
            arr.push({
              faultTypeName: props.rows.faultTypeName,
              faultCode: props.rows.faultCode,
              id: item.id,
              deviceModelsNameAnd: item.deviceModelsCode
                ? item.brandName + item.deviceModelsName
                : '--'
            })
          })
        list.records = arr || []
        list.total = data.data.total
      } else {
        list.records = []
        list.total = 0
      }
    })
    .catch((err) => {
      list.records = []
      list.total = 0
      console.log(err)
    })
}
// 关闭弹窗
const emit = defineEmits(['closeDialog'])
const dialogClose = () => {
  emit('closeDialog', false)
}
// 查看详情
let isShowfaultDetail = ref(false)
let detaileId = ref('')
const lookDetaile = async (row: any) => {
  isShowfaultDetail.value = true
  detaileId.value = row.id || ''
}
const closeDetaileDialog = () => {
  isShowfaultDetail.value = false
  detaileId.value = ''
}
</script>
<style lang="scss">
.el-dialog.knowledge-dialog .el-dialog__body {
  padding-bottom: 15px !important;
}
</style>
