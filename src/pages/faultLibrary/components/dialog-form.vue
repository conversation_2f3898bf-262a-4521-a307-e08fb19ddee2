<!--
 * @Author: 赵鹏鹏
 * @Date: 2024-03-20 15:14:02
 * @LastEditors: 赵鹏鹏
 * @Description: 页面名称
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="titleVal"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="500px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-suffix=""
        label-width="140px"
        :rules="formRules"
      >
        <el-form-item label="故障类型名称" prop="faultTypeName">
          <el-input
            v-model="formData.faultTypeName"
            :maxlength="32"
            autocomplete="off"
            placeholder="请输入故障类型名称"
          />
        </el-form-item>
        <el-form-item label="故障类型代码" prop="faultCode">
          <el-input
            v-model="formData.faultCode"
            :maxlength="6"
            autocomplete="off"
            placeholder="请输入数字1-6位"
          />
        </el-form-item>
        <el-form-item label="故障类型等级" prop="faultLevelName">
          <el-select
            v-model="formData.faultLevelName"
            placeholder="请选择故障类型等级"
          >
            <el-option label="一般" :value="'一般'"></el-option>
            <el-option label="严重" :value="'严重'"></el-option>
            <el-option label="重大" :value="'重大'"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属设备类型" prop="deviceType">
          <el-select
            v-model="formData.deviceType"
            placeholder="请选择所属设备类型"
            :disabled="typeVal == 'edit' || false"
          >
            <el-option
              v-for="(item, index) in deviceTypeArr"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="submitForm()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import {
  getDeviceTypeList,
  addDevice,
  updateDevice
} from '@/api/module/fault.ts'
import type { FormInstance, FormRules } from 'element-plus'
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  faultTypeName: '', // 故障类型名称
  faultCode: '', // 故障类型代码
  faultLevelName: '一般', // 故障类型等级
  deviceType: '' // 设备类型
})
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  typeVal: {
    type: String,
    default: '',
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {
        faultTypeName: '', // 故障类型名称
        faultCode: '', // 故障类型代码
        faultLevelName: '一般', // 故障类型等级
        deviceType: '' // 设备类型
      }
    }
  }
})

let dialogVisible = ref(false)
let id_ = ref('')
const deviceTypeArr = ref<Record<string, any>[]>([])
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      await getDeviceTypeList_()
    }
  },
  {
    deep: true
  }
)

let titleVal = ref('标题')
watch(
  () => props.typeVal,
  (val: string) => {
    if (val === 'add') {
      titleVal.value = '新建故障类型'
    } else if (val === 'edit') {
      titleVal.value = '编辑故障类型'
    }
  }
)
// 表单信息
const getDeviceTypeList_ = async () => {
  await getDeviceTypeList()
    .then(({ data }) => {
      if (data.code == 200) {
        let data_ = data.data || [],
          arr = []
        for (let i in data_) {
          arr.push({
            label: data_[i],
            value: `${data_[i]}_${i}`
          })
        }
        deviceTypeArr.value = arr

        setFormData()
      } else {
        formData.value = {}
        deviceTypeArr.value = []
      }
    })
    .catch((err) => {
      deviceTypeArr.value = []
      console.log(err)
    })
}
const setFormData = () => {
  if (props.typeVal == 'add') {
    formData.value.faultTypeName = '' // 故障类型名称
    formData.value.faultCode = '' // 故障类型代码
    formData.value.faultLevelName = '一般' // 故障类型等级
    formData.value.deviceType = '' // 设备类型
  } else {
    formData.value.faultTypeName = props.rows.faultTypeName
    formData.value.faultCode = props.rows.faultCode
    formData.value.faultLevelName = props.rows.faultLevelName
    formData.value.deviceType =
      (props.rows.deviceType && props.rows.deviceType.split('_')[0]) || ''
    id_.value = props.rows.id || ''
  }
}
const numValidate = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入数字1-6位'))
  } else {
    if (!/^\d{1,6}$/.test(value)) {
      callback(new Error('请输入数字1-6位'))
    } else {
      callback()
    }
  }
}
const formRules = reactive<FormRules<Record<string, any>>>({
  faultTypeName: [
    { required: true, message: '请输入故障类型名称', trigger: 'blur' }
  ],
  faultCode: [
    {
      required: true,
      message: '请输入数字1-6位',
      validator: numValidate,
      trigger: 'change'
    }
  ],
  faultLevelName: [
    { required: true, message: '请选择故障类型等级', trigger: 'change' }
  ],
  deviceType: [
    { required: true, message: '请选择所属设备类型', trigger: 'change' }
  ]
})
// 点击确定
const emit = defineEmits(['closeDialog', 'updateList'])
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      if (props.typeVal == 'add') {
        await addDevice(
          {
            ...formData.value
          },
          true
        )
          .then(({ data }) => {
            if (data.code == 200) {
              ElMessage({
                message: '新建成功',
                type: 'success'
              })
              dialogClose()
              emit('updateList')
            } else {
              ElMessage({
                message: data.message,
                type: 'error'
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
      } else {
        await updateDevice(
          {
            ...formData.value,
            id: id_.value
          },
          true
        )
          .then(({ data }) => {
            if (data.code == 200) {
              ElMessage({
                message: '编辑成功',
                type: 'success'
              })
              dialogClose()
              emit('updateList')
            } else {
              ElMessage({
                message: data.message,
                type: 'error'
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
      }
    }
  })
}
// 关闭弹窗
const dialogClose = () => {
  formRef.value && formRef.value.resetFields()
  emit('closeDialog', false)
}
</script>
<style lang="scss" scoped></style>
