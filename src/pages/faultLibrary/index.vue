<!--
 * @Author: 赵鹏鹏
 * @Date: 2024-03-20 09:19:35
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @Description: 页面名称
-->
<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-title">{{ $route.meta.title }}</div>
    </div>
    <div class="page-main">
      <div class="page-search">
        <searchForm
          :search-props="searchProps"
          :search-data="searchData"
          label-width="60px"
          @submit-emits="handleSearch"
        ></searchForm>
      </div>
      <div v-auto-height class="main">
        <div class="operate">
          <p>故障库列表</p>
          <el-button type="primary" @click="editDetail(row, 'add')"
            ><el-icon><Plus /></el-icon>新建故障类型</el-button
          >
        </div>

        <vis-table-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :loading="loading"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchData.pageSize"
          :columns="columns"
          :total="list.total"
          :data="list.records"
          :show-overflow-tooltip="true"
          :current-page="searchData.pageNum"
          background
          class="vis-table-pagination"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
        >
          <!-- 故障等级 -->
          <template #faultLevelName="{ row }">
            <el-tag v-if="row.faultLevel === 1" type="success"> 一般 </el-tag>
            <el-tag v-if="row.faultLevel === 2" type="warning"> 严重 </el-tag>
            <el-tag v-if="row.faultLevel === 3" type="danger"> 重大 </el-tag>
          </template>
          <!--  所属设备类型 -->
          <template #deviceType="{ row }">
            {{ row.deviceType && row.deviceType.split('_')[0] }}
          </template>
          <!-- 关联故障知识 -->
          <template #gls="{ row }">
            <div class="table-operate">
              <sapn v-if="row.faultKnowledgeRecord == 0">--</sapn>
              <el-button link @click="editDetail(row, 'look')" v-else
                >查看</el-button
              >
            </div>
          </template>
          <!-- 关联故障设备记录 -->
          <template #deviceFaultRecord="{ row }">
            <div class="table-operate">
              <el-button link @click="editDetail(row, 'num')">{{
                row.deviceFaultRecord
              }}</el-button>
            </div>
          </template>
          <!-- 操作 -->
          <template #operate="{ row }">
            <div class="table-operate">
              <el-button link @click.stop="editDetail(row, 'edit')"
                >编辑</el-button
              >
              <el-popconfirm
                title="确认删除？"
                @confirm="handleDelete(row)"
                v-if="row.isShowE"
              >
                <template #reference>
                  <el-button link @click.stop>删除</el-button>
                </template>
              </el-popconfirm>
              <el-button link v-else @click="deleteType()">删除</el-button>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
    <!-- 弹窗 -->
    <dialogForm
      :isShow="isShow"
      :typeVal="typeVal"
      :rows="rows"
      @closeDialog="closeDialog"
      @updateList="getListFun"
    ></dialogForm>
    <!-- 故障知识 -->
    <dialogknowledge
      :isShow="isShowLedge"
      :rows="rows"
      @closeDialog="() => (isShowLedge = false)"
    />
    <!-- 设备故障列表 -->
    <dialogrecord
      :isShow="isShowrecord"
      :rows="rows"
      @closeDialog="() => (isShowrecord = false)"
    />
  </div>
</template>
<script setup lang="ts">
import { getDeviceList, deleteDevice } from '@/api/module/fault.ts'
import visTablePagination from '@/components/table-pagination.vue'
import dialogForm from './components/dialog-form.vue'
import dialogknowledge from './components/dialog-knowledge.vue'
import dialogrecord from './components/dialog-record.vue'

// 搜索
const searchProps = ref([
  {
    prop: 'faultTypeName',
    label: '故障类型名称',
    placeholder: '请输入故障类型名称',
    span: 8,
    width: '110px',
    maxlength: 32
  },
  {
    prop: 'faultCode',
    label: '故障类型编码',
    placeholder: '请输入故障类型编码',
    span: 8,
    width: '110px'
  }
])
const searchData = ref({
  faultTypeName: '', // 名称
  faultCode: '', // 编码
  pageNum: 1, // 当前页
  pageSize: 10 // 每页条数
})
// 表格
let loading = ref(false)
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1, // 当前页
    pageSize: 10 // 每页条数
  }
  getListFun()
}
const columns = reactive([
  {
    prop: 'faultTypeName',
    label: '故障类型名称'
  },
  {
    prop: 'faultLevelName',
    label: '故障等级',
    slotName: 'faultLevelName'
  },
  {
    prop: 'faultCode',
    label: '故障类型编码'
  },
  {
    prop: 'deviceType',
    label: '所属设备类型',
    slotName: 'deviceType'
  },
  {
    prop: 'gls',
    label: '关联故障知识',
    slotName: 'gls'
  },
  {
    prop: 'deviceFaultRecord',
    label: '关联设备故障记录',
    slotName: 'deviceFaultRecord'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 120,
    fixed: 'right'
  }
])
const list = reactive({
  records: [],
  total: 0
})
const handleSizeChange = (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getListFun()
}
const handleCurrentChange = (params: any) => {
  searchData.value.pageNum = params.currentPage
  getListFun()
}
const handleDelete = async (row: any) => {
  await deleteDevice({
    id: row.id
  })
    .then(({ data }) => {
      if (data.code == '200') {
        if (data.data) {
          ElMessage({
            message: '删除成功',
            type: 'success'
          })
          getListFun()
        } else {
          ElMessage({
            message: data.message,
            type: 'warning'
          })
        }
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
const deleteType = () => {
  ElMessage({
    message: '当前故障类型已使用，不可删除！',
    type: 'warning'
  })
}
onMounted(() => {
  getListFun()
})
const getListFun = async () => {
  await getDeviceList({
    ...searchData.value
  })
    .then(({ data }) => {
      if (data.code == 200) {
        data.data.records.length &&
          data.data.records.forEach((item: any) => {
            if (item.deviceFaultRecord == 0 && item.faultKnowledgeRecord == 0) {
              item.isShowE = true
            } else {
              item.isShowE = false
            }
          })
        list.records = data.data.records || []
        list.total = data.data.total
      } else {
        list.records = []
        list.total = 0
      }
    })
    .catch((err) => {
      list.records = []
      list.total = 0
      console.log(err)
    })
}

let isShowLedge = ref(false) // 故障知识弹窗
let isShowrecord = ref(false) // 故障列表弹窗
// 弹窗
let isShow = ref(false)
let typeVal = ref('')
const rows = ref({})
const editDetail = (ag1: any, ag2: string) => {
  switch (ag2) {
    case 'add': // 新增
      isShow.value = true
      rows.value = {}
      typeVal.value = 'add'
      break
    case 'edit': // 编辑
      isShow.value = true
      rows.value = ag1
      typeVal.value = 'edit'
      break
    case 'look': // 故障知识
      rows.value = ag1
      isShowLedge.value = true
      break
    case 'num': // 故障记录
      if (ag1.deviceFaultRecord > 0) {
        rows.value = ag1
        isShowrecord.value = true
      }
      break
    default:
      return
  }
}
const closeDialog = () => {
  isShow.value = false
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.main {
  :deep(.el-table) {
    margin-top: 10px;
  }
  &-btn {
    display: flex;
    justify-content: space-between;
  }
}
.page-container {
  :deep(.vis-dialog) {
    padding: 0 !important;
  }
}
</style>
