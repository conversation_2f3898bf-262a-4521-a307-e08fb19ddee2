<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>考核列表</p>
      </div>
      <div class="tables">
        <vis-table-pagination
          :loading="tableLoading"
          :columns="columns"
          :total="listTotal"
          :data="listData"
          :show-overflow-tooltip="true"
          background
          class="vis-table-pagination"
        >
          <template #operate="{ row }">
            <div class="table-operate">
              <el-button link @click="handleDowload(row)">
                下载明细表
              </el-button>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { examineManagement as examineManagementAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import useCompanyCodeStore from '@/store/companyCode'
import dayjs from 'dayjs'

const companyCode = useCompanyCodeStore()

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  companyCode.data && (await getTableData())
  startWatch = true
})

// 搜索
const searchData = ref({
  time: [
    dayjs().add(-1, 'month').startOf('month').format('YYYY-MM-DD'),
    dayjs(new Date()).format('YYYY-MM-DD')
  ]
})
const chooseDay: any = ref(null)
const searchProps = ref([
  {
    prop: 'time',
    label: '选择时间',
    type: 'monthrange',
    format: 'YYYY-MM-DD',
    span: 8,
    width: 100,
    clearable: false,
    calendarChange: (val: Date[]) => {
      const [pointDay] = val
      chooseDay.value = pointDay
    },
    change: () => {
      chooseDay.value = null
    },
    disabledDate: (time: any) => {
      // return time.getTime() >= Date.now() - 2592000000
      return time.getTime() >= Date.now()
    }
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val
  }
  await getTableData()
}

const columns = [
  {
    prop: 'companyName',
    label: '运维商名称'
  },
  {
    prop: 'completeCount',
    label: '完成工单数'
  },
  {
    prop: 'overCount',
    label: '超时工单数'
  },
  {
    prop: 'total',
    label: '工单总数'
  },
  {
    prop: 'rate',
    label: '工单闭环率'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 100,
    fixed: 'right'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } =
      await examineManagementAPI.attendanceManagementControllerList(
        {
          startTime: searchData.value.time[0],
          endTime: searchData.value.time[1]
        },
        true
      )
    listData.value = data.data
  } catch (e) {
    listData.value = []
  }
}
// 操作
const handleDowload = async (row: any) => {
  try {
    let res = await examineManagementAPI.downloadWorkTicket({
      code: row.companyCode,
      companyName: row.companyName,
      name: row.name,
      total: row.total,
      overCount: row.overCount,
      completeCount: row.completeCount,
      rate: row.rate,
      startTime: searchData.value.time[0],
      endTime: searchData.value.time[1]
    })
    let str = res.headers['content-disposition']
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(res.data)
    link.download = decodeURIComponent(str.split('=')[1])
    link.click()
  } catch (e) {
    console.log('导出失败！')
  }
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
