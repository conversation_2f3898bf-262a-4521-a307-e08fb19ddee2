<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="100px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>故障列表</p>
        <el-button type="primary" @click="handleAdd(null)">
          <el-icon><Plus /></el-icon>
          新建故障
        </el-button>
      </div>
      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="tableColumns"
        :total="tableData.total"
        :data="tableData.data"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #faultLevel="{ row }">
          <el-tag v-if="row.faultLevel === 1" type="success"> 一般 </el-tag>
          <el-tag v-if="row.faultLevel === 2" type="warning"> 重大 </el-tag>
          <el-tag v-if="row.faultLevel === 3" type="danger"> 紧急 </el-tag>
        </template>
        <template #faultState="{ row }">
          <el-tag
            v-if="row.faultState === 1"
            type="success"
            class="tag-info"
            color="#F0F5FA"
          >
            已处理
          </el-tag>
          <el-tag v-if="row.faultState === 2" type="info"> 未处理 </el-tag>
        </template>
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="dialogEditFault(row)">编辑</el-button>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible"
    :title="(dialogStatus === 'edit' ? '编辑' : '新建') + '故障'"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    class="vis-dialog"
    width="650px"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="100px"
      :rules="formRules"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="所属电站" prop="stationCode">
            <StationSelect
              v-model="formData.stationCode"
              v-model:label="formData.stationName"
              @change="
                (data: any) => {
                  changeSelectStation(data.stationCode)
                }
              "
            />
            <!-- <el-select
              v-model="formData.stationCode"
              placeholder="请选择所属电站"
              filterable
              @change="changeSelectStation"
            >
              <el-option
                v-for="item in stations"
                :key="item.stationCode"
                :label="item.stationName"
                :value="item.stationCode"
              />
            </el-select> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属设备" prop="deviceUniqueId">
            <el-select
              ref="getSelectDeviceNameRef"
              v-model="formData.deviceUniqueId"
              placeholder="请选择所属设备"
              filterable
              @change="changeSelectDevice"
            >
              <el-option
                v-for="item in devices"
                :key="item.deviceUniqueId"
                :label="item.deviceName"
                :value="item.deviceUniqueId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="故障类型" prop="faultDatabaseId">
            <el-select
              v-model="formData.faultDatabaseId"
              placeholder="请选择故障类型"
              filterable
            >
              <el-option
                v-for="item in faultTypes"
                :key="item.id"
                :label="item.faultTypeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发现时间" prop="foundTime">
            <el-date-picker
              v-model="formData.foundTime"
              type="datetime"
              placeholder="请选择发现时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="故障状态" prop="faultState">
            <el-select
              v-model="formData.faultState"
              placeholder="请选择故障状态"
            >
              <el-option label="已处理" :value="1"></el-option>
              <el-option label="未处理" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="故障等级" prop="faultLevel">
            <el-select
              v-model="formData.faultLevel"
              placeholder="请选择故障等级"
            >
              <el-option label="一般" :value="1"></el-option>
              <el-option label="重大" :value="2"></el-option>
              <el-option label="紧急" :value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联工单" prop="workOrderVOList">
            <el-select
              v-model="formData.workOrdeValue"
              placeholder="请选择关联工单"
              multiple
              clearable
              filterable
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="1"
              remote
              :remote-method="getOrders"
              remote-show-suffix
              @change="changeWorkOrder"
            >
              <template v-for="item in orders" :key="item.value">
                <el-option
                  :label="item.orderName"
                  :title="item.orderName"
                  :value="item.orderId"
                />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="故障描述" prop="faultDesc">
            <el-input
              v-model="formData.faultDesc"
              :rows="5"
              type="textarea"
              placeholder="请输入故障描述"
              maxlength="300"
              autocomplete="off"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import { fault as faultAPI } from '@/api/index.ts'
import { loadingOpen, loadingClose } from '@/utils/common.ts'
import useCompanyCodeStore from '@/store/companyCode'
import StationSelect from '@/components/spic-station'

const companyCode = useCompanyCodeStore()

let searchData = ref({
  deviceUniqueId: '',
  faultTypeName: '',
  stationName: '',
  deviceName: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  { label: '故障名称', prop: 'faultTypeName' },
  { label: '电站名称', prop: 'stationName' },
  { label: '设备名称', prop: 'deviceName' }
])
const router = useRouter()
const tableColumns = [
  { prop: 'stationName', label: '电站名称' },
  { prop: 'deviceName', label: '设备名称' },
  { prop: 'faultTypeName', label: '故障名称' },
  { prop: 'foundTime', label: '发现时间', minWidth: '120px' },
  { prop: 'faultLevel', label: '故障等级', slotName: 'faultLevel' },
  { prop: 'faultState', label: '故障状态', slotName: 'faultState' },
  {
    prop: 'workOrderVOList',
    label: '关联工单',
    type: 'array',
    emit: 'click',
    href: (row: Record<string, any>) => {
      router.push(`/workorder/lookorder/${row.workId}?parentPageName=故障列表`)
    },
    minWidth: 200
  },
  { prop: 'createUserName', label: '创建人' },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 60,
    fixed: 'right'
  }
]
const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageNum = 1
  searchData.value.pageSize = 10
  getTableData()
}

const getTableData = async () => {
  try {
    let { data } = await faultAPI.getFaultList(searchData.value, true)
    tableData.data = data?.data?.records || []
    tableData.total = data?.data?.total || 0
  } catch (e: any) {
    tableData.data = []
    tableData.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  getStations()
  getFaultTypes()
  companyCode.data && (await getTableData())
  startWatch = true
})

const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

// 新增编辑弹框
let dialogVisible = ref(false)
let dialogStatus = ref('add')
const formRef = ref<FormInstance>()
const formRules = reactive<FormRules<Record<string, any>>>({
  stationCode: [
    { required: true, message: '请选择所属电站', trigger: 'change' }
  ],
  deviceUniqueId: [
    { required: true, message: '请选择所属设备', trigger: 'change' }
  ],
  faultDatabaseId: [
    { required: true, message: '请选择故障类型', trigger: 'change' }
  ],
  foundTime: [{ required: true, message: '请选择发现时间', trigger: 'change' }],
  faultState: [
    { required: true, message: '请选择故障状态', trigger: 'change' }
  ],
  faultLevel: [
    { required: true, message: '请选择故障等级', trigger: 'change' }
  ],
  faultDesc: [{ required: true, message: '请输入故障描述', trigger: 'blur' }]
})

let formData = ref<Record<string, any>>({
  id: null,
  stationCode: '',
  stationName: '',
  deviceUniqueId: '',
  deviceName: '',
  faultDatabaseId: '',
  foundTime: '',
  faultLevel: 1,
  faultState: 1,
  faultDesc: '',
  workOrderVOList: [],
  workOrdeValue: []
})
let stationId = ''
const dialogEditFault = async (node: any = null) => {
  stationId = node.stationCode
  // 没有关联工单，就不用等待getOrders了，这个接口跑的太慢了
  loadingOpen()
  if (node.workOrderVOList?.length) {
    await getOrders()
  } else {
    getOrders()
  }
  await getDevices(node.stationCode)
  loadingClose()
  handleAdd(node)
}
const handleAdd = async (node: any = null) => {
  dialogStatus.value = node ? 'edit' : 'add'
  // formRef.value?.resetFields()
  formData.value = {
    id: node?.id || null,
    stationCode: node?.stationCode || '',
    stationName: node?.stationName || '',
    deviceUniqueId: node?.deviceUniqueId || '',
    deviceName: node?.deviceName || '',
    faultDatabaseId: node?.faultDatabaseId || '',
    foundTime: node?.foundTime || '',
    faultLevel: node?.faultLevel || 1,
    faultState: node?.faultState || 1,
    faultDesc: node?.faultDesc || '',
    workOrderVOList: [],
    workOrdeValue: []
  }
  if (node?.workOrderVOList) {
    node.workOrderVOList.forEach((item: any) => {
      formData.value.workOrdeValue.push(Number(item.workId))
      formData.value.workOrderVOList.push({
        workId: Number(item.workId),
        workNo: item.workOrderNo,
        objectApiName: item.objectApiName
      })
    })
  } else {
    formData.value.workOrdeValue = []
  }
  dialogVisible.value = true
}
const stations = ref<Record<string, any>[]>([])
const getStations = async () => {
  try {
    const { data } = await faultAPI.getStationList({}, false)
    stations.value = data.data
  } catch (e: any) {
    stations.value = []
  }
}
// 修改电站，重新获取‘所属设备’和‘关联工单’,并清空
const changeSelectStation = (val: string = '') => {
  formData.value.deviceName = ''
  formData.value.deviceUniqueId = ''
  formData.value.workOrderVOList = []
  formData.value.workOrdeValue = []
  stationId = val
  val && (getDevices(val), getOrders())
}
const devices = ref<Record<string, any>[]>([])
const getDevices = async (stationCode: string = '') => {
  try {
    const { data } = await faultAPI.getBelongDeviceList({ stationCode }, false)
    devices.value = data.data
  } catch (e: any) {
    devices.value = []
  }
}
const changeSelectDevice = (val: string = '') => {
  const idx = devices.value.findIndex((option) => {
    return option.deviceUniqueId === val
  })
  formData.value.deviceName = idx > -1 ? devices.value[idx].deviceName : ''
}
const faultTypes = ref<Record<string, any>[]>([])
const getFaultTypes = async () => {
  try {
    const { data } = await faultAPI.getFaultTypeList({}, false)
    faultTypes.value = data.data
  } catch (e: any) {
    faultTypes.value = []
  }
}
const orders = ref<Record<string, any>[]>([])
const getOrders = async (orderName: string = '') => {
  try {
    const { data } = await faultAPI.getRelatedOrder(
      {
        // powerStation: 'GFHY01088305', // test 此电站code有关联工单列表
        powerStation: stationId,
        orderName
      },
      false
    )
    orders.value = data.data
  } catch (e: any) {
    orders.value = []
  }
}
const changeWorkOrder = (val: any) => {
  formData.value.workOrderVOList = []
  orders.value.forEach((item: any) => {
    if (val.includes(item.orderId)) {
      formData.value.workOrderVOList.push({
        workId: item.orderId,
        workNo: item.orderNo,
        objectApiName: item.orderType
      })
    }
  })
}

const handleSave = () => {
  formRef.value?.validate(async (valid: any) => {
    if (valid) {
      try {
        let params = {
          deviceName: formData.value.deviceName,
          deviceUniqueId: formData.value.deviceUniqueId,
          faultDatabaseId: formData.value.faultDatabaseId,
          faultDesc: formData.value.faultDesc,
          faultLevel: formData.value.faultLevel,
          faultState: formData.value.faultState,
          foundTime: formData.value.foundTime,
          id: formData.value.id,
          stationCode: formData.value.stationCode,
          stationName: formData.value.stationName,
          workOrderVOList: formData.value.workOrderVOList
        }
        const { data } = await faultAPI.saveDeviceFault(params, true)
        if (data.code === '200') {
          ElMessage({
            message: `${
              dialogStatus.value === 'edit' ? '修改' : '添加'
            }故障成功!`,
            type: 'success'
          })
          dialogClose()
          getTableData()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}
const dialogClose = () => {
  formRef.value?.resetFields()
  dialogVisible.value = false
  stationId = ''
  devices.value = []
  orders.value = []
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.tag-info {
  :deep(.el-tag__content) {
    color: #7a8a99;
  }
}
:deep(.el-select__tags-text) {
  max-width: 58px !important;
  display: block !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
</style>
