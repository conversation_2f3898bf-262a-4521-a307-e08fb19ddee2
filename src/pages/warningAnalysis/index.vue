<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>AI预警分析记录</p>
      </div>
      <TablePagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :show-overflow-tooltip="true"
        background
        :columns="columns"
        :data="tableData.data"
        :total="tableData.total"
        :loading="tableLoading"
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        class="table-pagination"
        @handle-size-change="changeSize"
        @handle-current-change="changeCurrent"
      >
        <template #stationName="{ row }">
          <span
            style="color: #29cca0; cursor: pointer"
            @click="toViewDetail(row)"
            >{{ row.stationName }}</span
          >
        </template>
        <template #troubleshootingSteps="{ row }">
          <span
            style="color: #29cca0; cursor: pointer"
            @click="toViewTroubleshootingSteps(row)"
            >查看</span
          >
        </template>
        <template #interact="{ row }">
          <div style="display: flex; align-items: center; gap: 8px">
            <span :class="getInteractClass(row.interact)">{{
              row.interact === 1
                ? '已触达'
                : row.interact === 2
                  ? '未触达'
                  : '--'
            }}</span>
            <el-button
              v-if="row.interact !== 1"
              link
              type="primary"
              size="small"
              @click="handleSendSms(row)"
            >
              发送短信
            </el-button>
            <el-button
              v-else
              link
              type="primary"
              size="small"
              @click="handleViewRecord(row)"
            >
              查看记录
            </el-button>
          </div>
        </template>
        <template #stationStatus="{ row }">
          <div style="display: flex; align-items: center; gap: 8px">
            <span :class="getStatusClass(row.stationStatus)">{{
              row.stationStatus === 1
                ? '已恢复'
                : row.stationStatus === 2
                  ? '未恢复'
                  : '--'
            }}</span>
            <el-button
              v-if="row.stationStatus === 2"
              link
              type="primary"
              size="small"
              @click="handleWarnConfirm(row)"
            >
              预警确认
            </el-button>
          </div>
        </template>
        <template #orderName="{ row }">
          <span
            style="color: #29cca0; cursor: pointer"
            @click="toViewWork(row)"
            >{{ row.orderName }}</span
          >
        </template>
      </TablePagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import useTableData from '@/hooks/useTableData'
import * as api from '@/api/index.ts'
import {
  getFaultTypeList,
  setSendSms,
  updateWarnStatus
} from '@/api/warningAnalysis'

const router = useRouter()

const searchProps = ref<Obj[]>([
  {
    prop: 'nameOrNumber',
    label: '电站名称/编码',
    width: '120px',
    type: 'input'
  },
  {
    prop: 'faultCode',
    label: '故障类型',
    width: '80px',
    type: 'select',
    options: []
  }
])
let searchData = reactive<Obj>({
  nameOrNumber: '',
  faultCode: '',
  pageSize: 10,
  pageNum: 1
})
const handleSearch = async (val: any) => {
  Object.keys(searchData).forEach((key) => {
    searchData[key] = val[key]
  })
  changeData(true)
}

const columns = [
  {
    prop: 'warnNumber',
    label: '预警编号'
  },
  {
    prop: 'stationName',
    label: '所属电站',
    slotName: 'stationName'
  },
  {
    prop: 'warnTypeName',
    label: '预警分析故障类型'
  },
  {
    prop: 'warnLevelName',
    label: '故障等级'
  },
  // {
  //   prop: 'troubleshootingSteps',
  //   label: '排查步骤',
  //   slotName: 'troubleshootingSteps'
  // },
  {
    prop: 'restart',
    label: '远程重启情况',
    formatter: (row: any) => {
      return row.restart === 0 ? '未重启' : row.restart === 1 ? '已重启' : '--'
    }
  },
  {
    prop: 'interact',
    label: '互动情况',
    slotName: 'interact'
  },
  {
    prop: 'orderName',
    label: '相关工单名称',
    slotName: 'orderName'
  },
  {
    prop: 'stationStatus',
    label: '预警状态',
    slotName: 'stationStatus'
  }
]

const getTableData = async (data: Obj, loading: Loading) => {
  return await api.post('/opAi/warnAnalysisRecordList', { ...data }, loading)
}
const {
  tableData,
  tablePage,
  tableLoading,
  changeCurrent,
  changeSize,
  changeData
} = useTableData(getTableData, searchData, { immediate: true })

const toViewDetail = (row: any) => {
  console.log(row)
  router.push({
    path: `/warningAnalysisRecord/detail`
  })
  // router.push({
  //   path: `/station/all/detail/17403888944461223832940680793586`,
  //   query: {
  //     hideBack: 'true'
  //   }
  // })
}

const toViewTroubleshootingSteps = (row: any) => {
  console.log('查看排查步骤:', row)
  // 这里可以添加查看排查步骤的逻辑，比如弹出弹窗或跳转页面
}

const toViewWork = (row: any) => {
  console.log(row)
  router.push({
    path: `/warningAnalysisRecord/work`
  })
  // router.push(`/workorder/lookorder/21790`)
}

// 发送短信确认对话框
const handleSendSms = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确认向手机号 ${row.phone || '未知号码'} 发送短信通知吗？`,
      '发送短信确认',
      {
        confirmButtonText: '确认发送',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用发送短信接口
    await setSendSms({
      id: row.id,
      phone: row.phone
    })

    ElMessage({
      message: '短信发送成功！',
      type: 'success'
    })

    // 刷新列表数据
    changeData(true)
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage({
        message: '短信发送失败，请重试',
        type: 'error'
      })
    }
  }
}

// 查看记录
const handleViewRecord = (row: any) => {
  console.log('查看互动记录:', row)
  // 在新的tab窗口中打开详情页面
  const routeData = router.resolve({
    path: `/feedbackLedger`
  })
  window.open(routeData.href, '_blank')
}

// 预警确认
const handleWarnConfirm = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确认向手机号 ${row.phone || '未知号码'} 发送预警确认通知吗？`,
      '预警确认',
      {
        confirmButtonText: '确认发送',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用预警确认接口
    await updateWarnStatus({
      phone: row.phone
    })

    ElMessage({
      message: '预警确认成功！',
      type: 'success'
    })

    // 刷新列表数据
    changeData(true)
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage({
        message: '预警确认失败，请重试',
        type: 'error'
      })
    }
  }
}

// 获取互动情况样式类
const getInteractClass = (interact: number) => {
  if (interact === 1) return 'status-success'
  if (interact === 2) return 'status-warning'
  return ''
}

// 获取状态样式类
const getStatusClass = (status: number) => {
  if (status === 1) return 'status-success'
  if (status === 2) return 'status-warning'
  return ''
}

const getFaultTypeList_ = async () => {
  try {
    const { data } = await getFaultTypeList({})
    searchProps.value[1].options = data.data.map((item: any) => ({
      label: item.faultTypeName,
      value: item.faultCode
    }))
  } catch (e: any) {
    faultTypeList.value = []
  }
}
onMounted(() => {
  getFaultTypeList_()
})
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>

<style scoped>
.status-success {
  color: #67c23a;
  font-weight: 500;
}

.status-warning {
  color: #e6a23c;
  font-weight: 500;
}
</style>
