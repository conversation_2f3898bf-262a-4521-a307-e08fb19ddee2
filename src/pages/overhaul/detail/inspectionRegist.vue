<template>
  <p class="detailTitle" mt-15px>签到信息</p>
  <vis-table-pagination
    :columns="inspectionColumns"
    :data="detail.inspectionData"
    background
    :border="true"
    class="vis-table-pagination"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="10"
    :total="detail.total"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
</template>
<script setup>
import visTablePagination from '@/components/table-pagination.vue'
import filters from '@/utils/filter.js'
import request from '@/utils/request'
let route = useRoute()
const inspectionColumns = ref([
  {
    prop: 'signAddress',
    label: '上门签到地址'
  },
  {
    prop: 'signTime',
    label: '签到时间'
  },
  {
    prop: 'signLocation',
    label: '签到地点'
  },
  {
    prop: 'lng_lat',
    label: '经纬度坐标'
  },
  {
    prop: 'signMark',
    label: '签到备注',
    type: 'html',
    minWidth: 80
  }
])
let detail = reactive({
  total: 0,
  inspectionData: [] // 维保任务
})
let params = ref({
  workOrderNo: route.params.id,
  pageNum: 1,
  pageSize: 10
})
const getData = async () => {
  try {
    let result = await request({
      url: '/operate/work-inspection-order/getSignInfo',
      method: 'get',
      params: params.value
    })
    if (result.data.code == 200) {
      if (result.data.data.length > 0) {
        result.data.data.forEach((value) => {
          value.lng_lat = value.lng + ', ' + value.lat
          value.type = filters.typeFilter(value.type)
          value.state = filters.stateFilter(value.state)
        })
      }
      detail.inspectionData = result.data.data
      detail.total = result.data.total
    }
  } catch (error) {
    console.info(error)
  }
}
const handleSizeChange = (pm) => {
  params.value.pageNum = 1
  params.value.pageSize = pm.pageSize
  getData()
}
const handleCurrentChange = (pm) => {
  params.value.pageNum = pm.currentPage
  getData()
}
onMounted(async () => {
  await getData()
})
</script>
