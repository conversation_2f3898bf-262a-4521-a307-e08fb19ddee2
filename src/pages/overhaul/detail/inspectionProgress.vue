<template>
  <el-descriptions :column="1">
    <el-descriptions-item label="安全措施项">
      <pre class="pre8978">{{
        detail.inspectionData?.[0]?.safetyMeasureItem || '--'
      }}</pre>
    </el-descriptions-item>
    <el-descriptions-item label="处理描述">
      <pre class="pre8978">{{
        detail.inspectionData?.[0]?.processDescription || '--'
      }}</pre>
    </el-descriptions-item>
    <el-descriptions-item label="图片">
      <template v-if="detail.inspectionData?.[0]?.imagesUrl">
        <el-image
          v-for="(item, index) in detail.inspectionData?.[0]?.imagesUrl.split(
            ','
          )"
          :key="'imagesUrl' + index"
          :src="`${WF_PATH}${item}`"
          :preview-src-list="
            detail.inspectionData?.[0]?.imagesUrl
              .split(',')
              .map((v) => WF_PATH + v)
          "
          fit="cover"
          class="w-80px h-80px mr-12px"
        >
        </el-image>
      </template>
      <template v-else>--</template>
    </el-descriptions-item>
    <el-descriptions-item label="附件">
      <a
        v-if="detail.inspectionData?.[0]?.attachment"
        :href="`${WF_PATH}${detail.inspectionData?.[0]?.attachment}`"
        target="_blank"
        title="下载文件"
      >
        <span
          class="download-file"
          :style="`background: url(${fileBgSvg}) no-repeat; background-size: 100%`"
        >
          {{ detail.inspectionData?.[0]?.attachment.split('.').at(-1) }}
        </span>
        <span class="file-name">
          {{ detail.inspectionData?.[0]?.attachment.split('&').at(-1) }}
        </span>
      </a>
      <template v-else>--</template>
    </el-descriptions-item>
  </el-descriptions>
  <!-- <vis-table-pagination
    :columns="inspectionColumns"
    :data="detail.inspectionData"
    background
    class="vis-table-pagination"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="10"
    :total="detail.total"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination> -->
</template>
<script setup>
// import visTablePagination from '@/components/table-pagination.vue'
import request from '@/utils/request'
import filters from '@/utils/filter.js'
import fileBgSvg from '@/assets/svgs/fileBg.svg'

const WF_PATH = import.meta.env.VITE_APP_WF_PATH
let route = useRoute()

// const inspectionColumns = ref([
//   {
//     prop: 'type',
//     label: '统计分类'
//   },
//   {
//     prop: 'state',
//     label: '统计状态'
//   },
//   {
//     prop: 'statisticsDuration',
//     label: '统计时长'
//   },
//   {
//     prop: 'effectiveDuration',
//     label: '有效时长'
//   }
// ])
let detail = reactive({
  total: 0,
  inspectionData: []
})
let params = ref({
  workOrderNo: route.params.id,
  pageNum: 1,
  pageSize: 10
})
const getData = async () => {
  try {
    let result = await request({
      url: '/operate/work-inspection-order/getServiceProgress',
      method: 'get',
      params: params.value
    })
    if (result.data.code == 200) {
      if (result.data.data.length > 0) {
        result.data.data.forEach((value) => {
          value.type = filters.typeFilter(value.type)
          value.state = filters.stateFilter(value.state)
        })
      }
      detail.inspectionData = result.data.data
      detail.inspectionData = detail.inspectionData.map((e) => {
        let imagesUrl = ''
        if (e.imageUrlOne) {
          imagesUrl = imagesUrl.length
            ? imagesUrl + ',' + e.imageUrlOne
            : e.imageUrlOne
        }
        if (e.imageUrlTwo) {
          imagesUrl = imagesUrl.length
            ? imagesUrl + ',' + e.imageUrlTwo
            : e.imageUrlTwo
        }
        if (e.imageUrlThree) {
          imagesUrl = imagesUrl.length
            ? imagesUrl + ',' + e.imageUrlThree
            : e.imageUrlThree
        }
        if (e.imageUrlFour) {
          imagesUrl = imagesUrl.length
            ? imagesUrl + ',' + e.imageUrlFour
            : e.imageUrlFour
        }
        return {
          ...e,
          imagesUrl
        }
      })
      detail.total = result.data.total
    }
  } catch (error) {
    console.info(error)
  }
}
onMounted(async () => {
  await getData()
})
</script>
<style lang="scss" scoped>
:deep(
    .el-descriptions__body
      .el-descriptions__table:not(.is-bordered)
      .el-descriptions__cell
  ) {
  padding-bottom: 12px !important;
  display: inline-flex;
}

:deep(.el-descriptions__label) {
  width: 90px !important;
  color: #666 !important;
  font-weight: 600 !important;
}

:deep(.el-descriptions__content) {
  width: calc(100% - 90px) !important;
}

pre.pre8978 {
  all: initial;
  width: auto;
  display: inline-block;
  white-space: pre-wrap;
  color: #333;
  line-height: 1.5;
}

.download-file {
  display: block;
  width: 80px;
  height: 80px;
  overflow: hidden;
  line-height: 80px;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  margin: 0 auto;
}

.file-name {
  line-height: 42px;
}
</style>
