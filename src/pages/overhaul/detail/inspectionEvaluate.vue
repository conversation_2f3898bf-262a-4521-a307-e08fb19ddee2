<template>
  <vis-table-pagination
    :columns="inspectionColumns"
    :data="detail.inspectionData"
    background
    :border="true"
    class="vis-table-pagination"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="10"
    :total="detail.total"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
</template>
<script setup>
import visTablePagination from '@/components/table-pagination.vue'
import filters from '@/utils/filter.js'
import request from '@/utils/request'
let route = useRoute()
// 巡检记录1
const inspectionColumns = ref([
  {
    prop: 'evaluateResultOne',
    label: '消缺效率'
  },
  {
    prop: 'evaluateResultTwo',
    label: '消缺质量'
  },
  {
    prop: 'evaluateResultThree',
    label: '安全措施布置情况'
  }
])
// 获取详情信息接口
let detail = reactive({
  total: 0,
  inspectionData: [] // 维保任务
})
// 巡检记录
let params = ref({
  workOrderNo: route.params.id,
  pageNum: 1,
  pageSize: 10
})
const getData = async () => {
  try {
    let result = await request({
      url: '/operate/work-inspection-order/getOrderEvaluate',
      method: 'get',
      params: params.value
    })
    if (result.data.code == 200) {
      if (result.data.data.length > 0) {
        result.data.data.forEach((value) => {
          value.settlementRules = filters.settlementRulesFilter(
            value.settlementRules
          )
        })
      }
      detail.inspectionData = result.data.data
    }
  } catch (e) {
    detail.inspectionData = []
    detail.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
const handleSizeChange = (pm) => {
  params.value.pageNum = 1
  params.value.pageSize = pm.pageSize
  getData()
}
const handleCurrentChange = (pm) => {
  params.value.pageNum = pm.currentPage
  getData()
}
onMounted(async () => {
  await getData()
})
</script>
