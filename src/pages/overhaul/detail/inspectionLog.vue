<template>
  <!-- <p class="detailTitle">财务结算列表</p> -->
  <vis-table-pagination
    :columns="inspectionColumns"
    :data="detail.inspectionData"
    background
    :border="true"
    class="vis-table-pagination"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="10"
    :total="detail.total"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
</template>
<script setup>
import visTablePagination from '@/components/table-pagination.vue'
import { overhaul as api } from '@/api/index.ts'
import filters from '@/utils/filter.js'
let route = useRoute()
// 巡检记录1
const inspectionColumns = ref([
  {
    prop: 'settlementRules',
    label: '结算规则'
  },
  {
    prop: 'unitPrice',
    label: '单价'
  },
  {
    prop: 'quantity',
    label: '数量'
  },
  {
    prop: 'unit',
    label: '单位'
  },
  {
    prop: 'price',
    label: '总价'
  }
])
// 获取详情信息接口
let detail = reactive({
  total: 0,
  inspectionData: [] // 维保任务
})
// 巡检记录
let params = ref({
  workOrderNo: route.params.id,
  pageNum: 1,
  pageSize: 10
})
const getFinancialSettlementInfoFun = async () => {
  try {
    let result = await api.getFinancialSettlementInfo(params.value)
    if (result.data.code == 200) {
      if (result.data.data.length > 0) {
        result.data.data.forEach((value) => {
          value.settlementRules = filters.settlementRulesFilter(
            value.settlementRules
          )
        })
      }
      detail.inspectionData = result.data.data
      detail.total = result.data.total
    }
  } catch (e) {
    detail.inspectionData = []
    detail.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
const handleSizeChange = (pm) => {
  params.value.pageNum = 1
  params.value.pageSize = pm.pageSize
  getFinancialSettlementInfoFun()
}
const handleCurrentChange = (pm) => {
  params.value.pageNum = pm.currentPage
  getFinancialSettlementInfoFun()
}
onMounted(async () => {
  await getFinancialSettlementInfoFun() // 维保任务
})
</script>
