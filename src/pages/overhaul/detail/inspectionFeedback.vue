<template>
  <vis-table-pagination
    :columns="inspectionColumns"
    :data="detail.inspectionData"
    background
    :border="true"
    class="vis-table-pagination"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="10"
    :total="detail.total"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
</template>
<script setup>
import visTablePagination from '@/components/table-pagination.vue'
import { overhaul as api } from '@/api/index.ts'
let route = useRoute()
// 巡检记录1
const inspectionColumns = ref([
  {
    prop: 'userName',
    label: '用户名称'
  },
  {
    prop: 'contact',
    label: '联系方式'
  },
  {
    prop: 'serviceAttitude',
    label: '服务态度'
  },
  {
    prop: 'serviceEfficiency',
    label: '服务效率'
  },
  {
    prop: 'suggestion',
    label: '用户建议'
  }
])
// 获取详情信息接口
let detail = reactive({
  total: 0,
  inspectionData: [] // 维保任务
})
// 巡检记录
let params = ref({
  workOrderNo: route.params.id,
  pageNum: 1,
  pageSize: 10
})
const getFinancialSettlementInfoFun = async () => {
  try {
    let result = await api.getUserFeedbackInfo(params.value)
    if (result.data.code == 200) {
      detail.inspectionData = result.data.data
      detail.total = result.data.total
    }
  } catch (error) {
    console.info(error)
  }
}
const handleSizeChange = (pm) => {
  params.value.pageNum = 1
  params.value.pageSize = pm.pageSize
  getFinancialSettlementInfoFun()
}
const handleCurrentChange = (pm) => {
  params.value.pageNum = pm.currentPage
  getFinancialSettlementInfoFun()
}
onMounted(async () => {
  await getFinancialSettlementInfoFun() // 维保任务
})
</script>
