<script setup lang="ts">
import { ticket as ticketAPI } from '@/api/index.ts'
import { post, get } from '@/api/index.ts'

const router = useRouter()
const route = useRoute()
const id = route.params.id
const type = route.params.type

const isReadonly = computed(() => type === 'detail')

const detailData = ref<Record<string, any>>({})

const getDetailData = async () => {
  const { data } = await get(
    '/operate/emergency-drill-plan-info/getEmergencyDrillPlanInfo',
    { id },
    true
  )
  detailData.value = data || {}
  detailData.value.drillPlanOssUrls =
    detailData.value?.drillPlanOssUrls?.split(',') || []
  detailData.value.drillRecordOssUrls =
    detailData.value?.drillRecordOssUrls?.split(',') || []

  detailData.value.approverUserId = detailData.value.approverUserId
    ? detailData.value.approverName + '_' + detailData.value.approverUserId
    : null
  formData.value = { ...detailData.value }
}

const roleList8 = ref<Obj[]>([])

const getRoleUserList = async () => {
  try {
    let { data } = await ticketAPI.getRoleUserList({})
    roleList8.value = data['JS-0008'] || []
  } catch (e) {}
}

const formRef = ref()
onMounted(async () => {
  getRoleUserList()
  getDetailData()
})

const formData = ref<any>({
  emergencyDrillPlanCode: '',
  emergencyDrillPlanName: '',
  planTime: '',
  practicalTime: '',
  organization: '',
  participatingUnit: '',
  craticNum: '',
  fundInvestment: '',
  drillPlace: '',
  embezzleGoods: '',
  drillPlanOssUrls: null,
  drillRecordOssUrls: null,
  drillRange: '',
  approverUserId: null
})
const rules = reactive<any>({
  emergencyDrillPlanCode: [
    { required: true, message: '请输入应急演练计划编号', trigger: 'blur' }
  ],
  emergencyDrillPlanName: [
    { required: true, message: '请输入应急演练计划名称', trigger: 'blur' }
  ],
  planTime: [
    { required: true, message: '请选择应急演练计划时间', trigger: 'change' }
  ],
  practicalTime: [
    { required: true, message: '请选择应急演练实际时间', trigger: 'change' }
  ],
  organization: [
    { required: true, message: '请输入组织部门', trigger: 'blur' }
  ],
  participatingUnit: [
    { required: true, message: '请输入参加单位', trigger: 'blur' }
  ],
  craticNum: [{ required: true, message: '请输入参加人数', trigger: 'blur' }],
  fundInvestment: [
    { required: true, message: '请输入经费投入', trigger: 'blur' }
  ],
  drillPlace: [{ required: true, message: '请输入演练地点', trigger: 'blur' }],
  embezzleGoods: [
    { required: true, message: '请输入动用物资', trigger: 'blur' }
  ],
  drillPlanOssUrls: [
    { required: true, message: '请上传应急演练预案', trigger: 'change' }
  ],
  drillRecordOssUrls: [
    { required: true, message: '请上传应急演练记录', trigger: 'change' }
  ],
  approverUserId: [
    { required: true, message: '请选择审批人', trigger: 'change' }
  ]
})
const formatNumber1 = (value: any) => {
  formData.value.craticNum = value
    .replace(/[^0-9]/g, '')
    .replace(/^0+(\d)/, '$1')
}
const formatNumber2 = (value: any) => {
  formData.value.fundInvestment = value
    .replace(/[^\d^\.]+/g, '')
    .replace(/^0+(\d)/, '$1')
    .replace(/^\./, '0.')
    .replace(/(\..*)\./g, '$1')
    .replace(/(\.\d{2}).*/g, '$1')
    .replace(/(^\d{7})\d/, '$1')
}
const onCancel = () => {
  router.back()
}
const onSumbit = async () => {
  await formRef.value.validate(async (valid: any) => {
    if (valid) {
      const { code } = await post(
        '/operate/emergency-drill-plan-info/updateEmergencyDrillPlanInfo',
        {
          ...formData.value,
          drillPlanOssUrls: formData.value.drillPlanOssUrls.join(','),
          drillRecordOssUrls: formData.value.drillRecordOssUrls.join(',')
        },
        true
      )
      if (code === '200') {
        ElMessage({
          message: '保存成功！',
          type: 'success'
        })
        router.back()
      }
    }
  })
}
</script>

<template>
  <div class="page-title">查看详情</div>
  <el-form ref="formRef" :model="formData" :rules="rules" label-width="150px">
    <div class="info-base pb-0px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="应急演练计划编号" prop="emergencyDrillPlanCode">
            <span>{{ formData.emergencyDrillPlanCode }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应急演练计划名称" prop="emergencyDrillPlanName">
            <span>{{ formData.emergencyDrillPlanName }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="应急演练计划时间" prop="planTime">
            <span v-if="isReadonly">{{ formData.planTime }}</span>
            <el-date-picker
              v-else
              v-model="formData.planTime"
              type="month"
              value-format="YYYY-MM"
              placeholder="请选择应急演练计划时间"
              style="width: 100%"
              clearable
              :disabled-date="
                (date: Date) => Date.now() - 86400000 > date.getTime()
              "
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应急演练实际时间" prop="practicalTime">
            <span v-if="isReadonly">{{ formData.practicalTime }}</span>
            <el-date-picker
              v-else
              v-model="formData.practicalTime"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择应急演练实际时间"
              style="width: 100%"
              clearable
              :disabled-date="
                (date: Date) => Date.now() - 86400000 > date.getTime()
              "
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="组织部门" prop="organization">
            <span v-if="isReadonly">{{ formData.organization }}</span>
            <el-input
              v-else
              v-model.trim="formData.organization"
              maxlength="64"
              placeholder="请输入组织部门"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="参加单位" prop="participatingUnit">
            <span v-if="isReadonly">{{ formData.participatingUnit }}</span>
            <el-input
              v-else
              v-model.trim="formData.participatingUnit"
              maxlength="64"
              placeholder="请输入参加单位"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="参加人数" prop="craticNum">
            <span v-if="isReadonly">{{ formData.craticNum }}</span>
            <el-input
              v-else
              v-model.number="formData.craticNum"
              placeholder="请输入参加人数"
              clearable
              maxlength="5"
              @input="formatNumber1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经费投入(元)" prop="fundInvestment">
            <span v-if="isReadonly">{{ formData.fundInvestment }}</span>
            <el-input
              v-else
              v-model.number="formData.fundInvestment"
              placeholder="请输入经费投入"
              clearable
              maxlength="10"
              @input="formatNumber2"
              @change="
                () =>
                  (formData.fundInvestment = Number(
                    formData.fundInvestment
                  ).toFixed(2))
              "
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="演练地点" prop="drillPlace">
            <span v-if="isReadonly">{{ formData.drillPlace }}</span>
            <el-input
              v-else
              v-model.trim="formData.drillPlace"
              maxlength="64"
              placeholder="请输入演练地点"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="动用物资" prop="embezzleGoods">
            <span v-if="isReadonly">{{ formData.embezzleGoods }}</span>
            <el-input
              v-else
              v-model.trim="formData.embezzleGoods"
              maxlength="64"
              placeholder="请输入动用物资"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <div class="info-base pb-0px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="应急演练预案" prop="drillPlanOssUrls">
            <SpicUpload
              v-model="formData.drillPlanOssUrls"
              :file-size="50"
              type="file"
              :file-ext="[
                'jpg',
                'jpeg',
                'png',
                'gif',
                'pdf',
                'doc',
                'docx',
                'xls',
                'xlsx'
              ]"
              :limit="1000"
              :disabled="isReadonly"
            />
            <div v-if="!isReadonly" class="tip tip-img">
              <el-icon color="#FF9900" size="16px"><WarningFilled /></el-icon>
              <div class="txt">提示：单个文件大小不超过50MB</div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应急演练记录" prop="drillRecordOssUrls">
            <SpicUpload
              v-model="formData.drillRecordOssUrls"
              :file-size="50"
              type="file"
              :file-ext="[
                'jpg',
                'jpeg',
                'png',
                'gif',
                'pdf',
                'doc',
                'docx',
                'xls',
                'xlsx'
              ]"
              :limit="1000"
              :disabled="isReadonly"
            />
            <div v-if="!isReadonly" class="tip tip-img">
              <el-icon color="#FF9900" size="16px"><WarningFilled /></el-icon>
              <div class="txt">提示：单个文件大小不超过50MB</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <div class="info-base pb-0px">
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="演练范围" prop="drillRange">
            <span>{{ formData.drillRange }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="审批人" prop="approverUserId">
            <span v-if="isReadonly">{{ formData.approverName }}</span>
            <el-select
              v-else
              v-model="formData.approverUserId"
              placeholder="请选择审核人"
              filterable
              clearable
            >
              <el-option
                v-for="item in roleList8"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <div class="page-footer">
      <el-button plain @click="onCancel">返回</el-button>
      <el-button v-if="!isReadonly" type="primary" @click="onSumbit"
        >提交</el-button
      >
    </div>
  </el-form>
</template>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.pb-0px {
  padding-bottom: 0;
}
.tip {
  font-size: 12px;
  font-weight: 400;
  color: #ff9900;
  line-height: 16px;
  display: flex;
  align-items: center;
  margin-left: 8px;
  &.tip-img {
    margin-top: 8px;
    margin-left: 0;
    flex-basis: 100%;
  }
  .txt {
    margin-left: 4px;
  }
}
</style>
