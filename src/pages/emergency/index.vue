<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="120px"
        @submit-emits="handleSearch"
      >
      </searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>应急演练列表</p>
      </div>
      <div class="tables">
        <vis-table-pagination
          :loading="tableLoading"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="tablePage.pageSize"
          :current-page="tablePage.pageNum"
          :columns="columns"
          :total="tableData.total"
          :data="tableData.data"
          :show-overflow-tooltip="true"
          background
          class="vis-table-pagination"
          @handle-size-change="changeSize"
          @handle-current-change="changeCurrent"
        >
          <template #operate="{ row }">
            <div class="table-operate">
              <el-button
                v-if="
                  row.emergencyDrillStatus === 1 ||
                  row.emergencyDrillStatus === 3
                "
                link
                @click="handleDetail(row)"
                >查看详情</el-button
              >
              <el-button
                v-if="
                  row.emergencyDrillStatus === 2 ||
                  row.emergencyDrillStatus === 4
                "
                link
                @click="handleEdit(row)"
                >编辑</el-button
              >
              <el-popconfirm
                v-if="row.emergencyDrillStatus === 2"
                :title="'确认删除？'"
                @confirm="handleDelete(row)"
              >
                <template #reference>
                  <el-button link @click.stop>删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import { post, get } from '@/api/index.ts'
import useTableData from '@/hooks/useTableData.ts'

const router = useRouter()

const searchProps = ref([
  {
    prop: 'emergencyDrillPlanName',
    label: '应急演练计划名称',
    placeholder: '请输入应急演练计划名称',
    span: 8,
    width: 130,
    maxlength: 32
  },
  {
    prop: 'emergencyDrillStatus',
    label: '应急演练状态',
    placeholder: '请选择应急演练状态',
    span: 8,
    width: 100,
    type: 'select',
    options: [
      { label: '处理中', value: '处理中' },
      { label: '未完成', value: '未完成' },
      { label: '已完成', value: '已完成' }
    ]
  }
])
const searchData = reactive<Obj>({
  emergencyDrillPlanName: '',
  emergencyDrillStatus: ''
})
const handleSearch = async (val: any) => {
  Object.keys(searchData).forEach((key) => {
    searchData[key] = val[key]
  })
  changeData(true)
}
const columns = [
  {
    prop: 'emergencyDrillPlanCode',
    label: '应急演练计划编号'
  },
  {
    prop: 'emergencyDrillPlanName',
    label: '应急演练计划名称'
  },
  {
    prop: 'planTime',
    label: '应急演练计划时间'
  },
  {
    prop: 'emergencyDrillStatusString',
    label: '应急演练状态',
    type: 'tag'
  },
  {
    prop: 'approverStatusString',
    label: '审核状态',
    type: 'tag'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 120,
    fixed: 'right'
  }
]
const getTableData = async (data: Obj, loading: Loading) => {
  return await post(
    '/operate/emergency-drill-plan-info/getEmergencyDrillPlanInfoList',
    data,
    loading
  )
}
const {
  tableData,
  tablePage,
  tableLoading,
  changeCurrent,
  changeSize,
  changeData
} = useTableData(getTableData, searchData)

const handleDetail = (row: Record<string, any>) => {
  router.push(`/emergency/detail/${row.id}`)
}
const handleEdit = (row: Record<string, any>) => {
  router.push(`/emergency/edit/${row.id}`)
}
const handleDelete = async (row: Record<string, any>) => {
  const { code } = await get(
    '/operate/emergency-drill-plan-info/deleteEmergencyDrillPlanInfo',
    { id: row.id },
    true
  )
  if (code === '200') {
    ElMessage({
      message: '删除成功！',
      type: 'success'
    })
    changeData('delete')
  }
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
// tag相关
.tag {
  font-size: 14px;
  font-weight: 400;
  padding: 1px 12px;
}

.tag1 {
  background: rgba(41, 204, 160, 0.1);
  color: rgba(41, 204, 160, 1);
}

.tag2 {
  background: rgba(230, 135, 46, 0.15);
  color: rgba(230, 135, 46, 1);
}
</style>
