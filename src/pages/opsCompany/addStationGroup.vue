<template>
  <div class="page-title">{{ id ? '电站组详情' : '新增电站组' }}</div>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="130px"
    label-position="right"
  >
    <div class="info-base" style="padding-bottom: 0px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="所属运维公司" prop="companyCode">
            <template v-if="id">{{ formData.companyName }}</template>
            <el-select
              v-else
              v-model="formData.companyCode"
              placeholder="请选择公司名称"
              clearable
              :disabled="!!companyCode"
              @change="changeCompany"
            >
              <el-option
                v-for="item in operatorsList"
                :key="item.companyCode"
                :label="item.companyName"
                :value="item.companyCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="运维电站组名称" prop="stationGroupName">
            <template v-if="id">{{ formData.stationGroupName }}</template>
            <el-input
              v-else
              v-model="formData.stationGroupName"
              :maxlength="20"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <div class="info-base">
      <div class="operate">
        <p>关联资产公司</p>
        <el-button type="primary" @click="showStaff('companys')">
          <el-icon m-r-5px><Link /></el-icon>关联
        </el-button>
      </div>
      <div class="selected-box">
        <div
          v-for="(item, index) in formData.companys"
          :key="index"
          class="spic-tag"
        >
          <span>{{ item.label }}</span>
          <div class="delete" @click="deleteTag(index, 'companys')"></div>
        </div>
      </div>
      <div class="explain-box">
        已关联 {{ formData.companys.length }} 个公司
        <span v-if="formData.companys.length" @click="showStaff('companys')">
          查看详情 &gt;
        </span>
      </div>
    </div>
    <div class="info-base">
      <div class="operate">
        <p>关联资产项目</p>
        <el-button type="primary" @click="showStaff('projects')">
          <el-icon m-r-5px><Link /></el-icon>关联
        </el-button>
      </div>
      <div class="selected-box">
        <div
          v-for="(item, index) in formData.projects"
          :key="index"
          class="spic-tag"
        >
          <span>{{ item.label }}</span>
          <div class="delete" @click="deleteTag(index, 'projects')"></div>
        </div>
      </div>
      <div class="explain-box">
        已关联 {{ formData.projects.length }} 个项目
        <span v-if="formData.projects.length" @click="showStaff('projects')">
          查看详情 &gt;
        </span>
      </div>
    </div>
    <div class="info-base">
      <div class="operate">
        <p>手动关联电站</p>
        <el-button type="primary" @click="relateStation">
          <el-icon m-r-5px><Link /></el-icon>关联
        </el-button>
      </div>
      <div class="selected-box">
        <div
          v-for="(item, index) in formData.stations.slice(0, 20)"
          :key="index"
          class="spic-tag"
        >
          <span>{{ item.stationName }}</span>
          <div class="delete" @click="deleteTag(index, 'stations')"></div>
        </div>
        <div v-if="formData.stations.length > 20" class="more2342">...</div>
      </div>
      <div class="explain-box">
        已关联 {{ formData.stations.length }} 个电站
        <span v-if="formData.stations.length" @click="relateStation">
          查看详情 &gt;
        </span>
      </div>
    </div>
    <div class="page-footer">
      <el-button plain @click="onCancel">返回</el-button>
      <el-button type="primary" @click="onSumbit">提交</el-button>
    </div>
  </el-form>
  <select-staff
    :arr-list="staffType === 'projects' ? formData.projects : formData.companys"
    :extra-data="formData"
    :is-show="isShow"
    :type="staffType"
    :dialog-title="staffType === 'projects' ? '选择资产项目' : '选择资产公司'"
    @uplate-data="getStaffData"
    @close-dialog="isShow = false"
  ></select-staff>
</template>

<script setup lang="ts">
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'
import selectStaff from './components/selectStaff.vue'
import useSelectedElectricStore from '@/store/selectedElectric'
import request from '@/utils/request.ts'
import { get } from '@/api/index.ts'

const route = useRoute()
const router = useRouter()

const id: any = route.params?.id || null
const companyCode = route.query?.companyCode || null
const formData = ref<Obj>({
  companyCode: companyCode || '',
  companyName: '',
  stationGroupName: '',
  companys: [],
  projects: [],
  stations: []
})
const operatorsList = ref<Obj[]>([])
const changeCompany = (val: any) => {
  formData.value.companyName =
    operatorsList.value.find((item: any) => item.companyCode === val)
      ?.companyName || null
}

const deleteTag = (index: number, prop: string) => {
  formData.value[prop].splice(index, 1)
}

const selectedElectric = useSelectedElectricStore()

const staffType = ref<string>('')
const showStaff = (str: string) => {
  staffType.value = str
  isShow.value = true
}

const relateStation = () => {
  selectedElectric.updateFormData(formData.value)
  selectedElectric.update(formData.value.stations)
  router.push(route.path + '/select-station')
}

const isShow = ref<boolean>(false)
const getStaffData = (arr: Obj[]) => {
  if (staffType.value === 'projects') {
    formData.value.projects = [...arr]
  } else {
    formData.value.companys = [...arr]
  }
}

const formRules = reactive<Obj>({
  companyCode: [
    { required: true, message: '请选择所属运维公司', trigger: 'change' }
  ],
  stationGroupName: [
    { required: true, message: '运维电站组名称', trigger: 'blur' }
  ]
})
const formRef = ref()

const onCancel = () => {
  router.push('/ops-company')
}

const onSumbit = async () => {
  if (!formRef.value) return
  if (
    !formData.value.companys.length &&
    !formData.value.projects.length &&
    !formData.value.stations.length
  ) {
    ElMessage({
      type: 'error',
      message: '请选择资产公司、资产项目或关联电站'
    })
    return
  }
  await formRef.value.validate(async (valid: any) => {
    if (valid) {
      try {
        let url = '/operate/stationGroupMange/add'
        if (id) {
          url = '/operate/stationGroupMange/stationGroupEdit'
        }
        const { data } = await request({
          url,
          method: 'post',
          data: {
            operateCompanyCode: formData.value.companyCode,
            operateCompanyName: formData.value.companyName,
            stationGroupName: formData.value.stationGroupName,
            assetList: formData.value.companys,
            assetProjectList: formData.value.projects,
            stationInfoList: formData.value.stations.map((e: any) => ({
              stationUniqueId: e.stationUniqueId,
              stationName: e.stationName
            })),
            id
          }
        })
        if (data.code !== '200') {
          ElMessage({
            message: data?.message,
            type: 'error'
          })
        } else {
          router.push('/ops-company')
        }
      } catch (e: any) {
        ElMessage({
          message: e?.message,
          type: 'error'
        })
      }
    }
  })
}

onMounted(async () => {
  if (id) {
    const { data } = await get(
      '/operate/stationGroupMange/queryDetailById',
      { id },
      true
    )

    formData.value.companyCode = data?.operateCompanyCode || ''
    formData.value.companyName = data?.operateCompanyName || ''
    formData.value.stationGroupName = data?.stationGroupName || ''
    formData.value.companys =
      data?.assetList?.map((e: any) => ({
        ...e,
        key: e.companyCode,
        label: e.companyName
      })) || []
    formData.value.projects =
      data?.assetProjectList?.map((e: any) => ({
        ...e,
        key: e.projectUniqueId,
        label: e.projectName
      })) || []
    formData.value.stations =
      data?.stationInfoList?.map((e: any) => ({
        ...e,
        key: e.stationUniqueId,
        label: e.stationName
      })) || []
  }
  formData.value = { ...formData.value, ...selectedElectric.formData }
  await getOperatorsList()
  if (!id) {
    changeCompany(formData.value.companyCode)
  }
})

const getOperatorsList = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    operatorsList.value = data.data || []
  } catch (e: any) {
    operatorsList.value = []
  }
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss">
.more2342 {
  color: #999;
  height: 22px;
  font-size: 22px;
  line-height: 42px;
  margin-left: 5px;
}
.selected-box {
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  display: flex;
  flex-wrap: wrap;
  padding: 5px;
  min-height: 52px;
  .spic-tag {
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    margin: 5px;
    background: rgba(246, 248, 250, 1);
    font-size: 14px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    .span {
      flex: auto;
    }
    .delete {
      flex: none;
      width: 16px;
      height: 16px;
      background: url('./assets/delete2.svg') no-repeat;
      cursor: pointer;
      margin-left: 6px;
    }
  }
}
.explain-box {
  padding: 16px 0 12px 0;
  span {
    color: rgba(41, 204, 160, 1);
    margin-left: 10px;
    cursor: pointer;
  }
}
.tabs-box__ {
  padding: 24px 24px 0;
  .consultingInfo {
    .mb0 {
      margin-bottom: 0 !important;
    }
  }
  .checkList {
    padding: 9px 24px 0 !important;
  }
  .el-tbas {
    height: 100% !important;
    .el-tbas__content {
      height: calc(100% - 55px);
    }
  }
}
</style>
