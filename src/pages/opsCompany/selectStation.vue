<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import type { FormInstance } from 'element-plus'
import useElectricData from './hooks/useElectricData'
import usePaginationLocal from './hooks/usePaginationLocal'
import useSelectedElectricStore from '@/store/selectedElectric'
import address from '@/api/data/area.json'
import request from '@/utils/request'

const electricAreas: any = address
const router = useRouter()
const route = useRoute()
const projectCode = route.params.projectCode as string

// tab切换
const activeTabName = ref('list')
const tableRef = ref()
const selectedTableRef = ref()

const tableLoading = ref(false)
// 运维电站列表
const {
  electricColumns,
  electricTotal,
  electricData,
  getElectricData,
  electricSearchData,
  electricPage,
  electricSizeChange,
  electricCurrentChange
} = useElectricData([tableLoading])
electricColumns.unshift({
  prop: 'selection',
  label: '选择',
  fixed: 'left',
  reserve: true,
  selectable: (row: Obj) => {
    if (companyCodes.includes(row.projectCompanyCode)) return false
    if (projectCodes.includes(row.projectInfoUniqueId)) return false
    if (row.isSelected) return false
    return true
  }
})

// 搜索
const searchFormRef = ref<FormInstance>()
const handleReset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  electricPage.pageSize = 10
  handleSearch(formEl)
}
const handleSearch = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  electricPage.pageNum = 1
  getElectricData()
}

// 已选择列表
const selectedColumns = electricColumns
const selectedData = ref<Record<string, any>[]>([])
const selectedTotal = computed(() => {
  return selectedData.value.length
})
const selectedDataCopy = ref<Record<string, any>[]>([])
const selectedTotalCopy = computed(() => {
  return selectedDataCopy.value.length
})
const {
  localData,
  handleSliceData,
  selectedSizeChange,
  selectedCurrentChange,
  pageObject: selectedPageObj
} = usePaginationLocal({ pageSize: 10 })

const handleSelectionChange = (rows: Record<string, any>[]) => {
  if (!startWatchChange.value) return

  selectedData.value = [...rows]
  selectedDataCopy.value = [...rows]
  localHandleSearch(true)
  const _copy = [...selectedData.value]
  if (activeTabName.value === 'list') {
    selectedTableRef.value.tablePagination.clearSelection()
    _copy.forEach((row) => {
      selectedTableRef.value.tablePagination.toggleRowSelection(row, true)
    })
  }
}
const localSelectionChange = (rows: Record<string, any>[]) => {
  if (!startWatchChange.value) return

  selectedData.value = [...rows]
  selectedDataCopy.value = [...rows]
  localHandleSearch(true)
  const _copy = [...selectedData.value]
  if (activeTabName.value === 'selected') {
    tableRef.value.tablePagination.clearSelection()
    _copy.forEach((row) => {
      tableRef.value.tablePagination.toggleRowSelection(row, true)
    })
  }
}

// 已选列表搜索
const localSearchFormRef = ref<FormInstance>()
const localSearchData = reactive<Record<string, any>>({
  projectName: '',
  projectCompanyName: '',
  stationCode: '',
  area: '',
  stationName: '',
  stationType: ''
})
const eqFields: string[] = ['stationType']
const localHandleReset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  selectedPageObj.pageSize = 10
  localHandleSearch(formEl)
}
const localHandleSearch = async (
  formEl: FormInstance | undefined | boolean
) => {
  if (!formEl) return
  selectedPageObj.pageNum = 1
  let notEmptyData: Record<string, any> = {}
  for (let key of Object.keys(localSearchData)) {
    if (localSearchData[key]) {
      notEmptyData[key] = localSearchData[key]
    }
  }
  selectedDataCopy.value = [...selectedData.value]
  if (notEmptyData.area) {
    notEmptyData.area = notEmptyData.area.join('')
  }
  for (let key of Object.keys(notEmptyData)) {
    selectedDataCopy.value = selectedDataCopy.value.filter((e) => {
      if (eqFields.includes(key)) {
        if (e[key] === notEmptyData[key]) {
          return true
        } else {
          return false
        }
      } else {
        if (e[key]?.includes(notEmptyData[key])) {
          return true
        } else {
          return false
        }
      }
    })
  }
  handleSliceData(selectedDataCopy.value)
}

const onCancel = () => {
  router.back()
}

const selectedElectric = useSelectedElectricStore()
let companyCodes: string[] = []
let projectCodes: string[] = []

const startWatchChange = ref(false)
onMounted(() => {
  companyCodes =
    selectedElectric.formData?.companys?.map((e: any) => e.companyCode) || []
  projectCodes =
    selectedElectric.formData?.projects?.map((e: any) => e.projectUniqueId) ||
    []

  startWatchChange.value = false
  selectedData.value = [...selectedElectric.data]
  selectedDataCopy.value = [...selectedElectric.data]
  localHandleSearch(true)
  tableRef.value.tablePagination.clearSelection()
  selectedTableRef.value.tablePagination.clearSelection()
  selectedDataCopy.value?.forEach((row: any) => {
    tableRef.value.tablePagination.toggleRowSelection(row, true)
    selectedTableRef.value.tablePagination.toggleRowSelection(row, true)
  })
  startWatchChange.value = true
})

const onRelation = async () => {
  if (projectCode) {
    try {
      const requestData = selectedData.value.map((e: any) => {
        return {
          ...e,
          operationProjectCode: projectCode
        }
      })
      await request({
        url: '/operate/operation-project/addOperationProjectStationList',
        method: 'post',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
          stationList: JSON.stringify(requestData),
          operationCompanyCode: route.query.operationCompanyCode || ''
        }
      })
      ElMessage({
        message: '关联电站成功！',
        type: 'success'
      })
      router.go(-1)
    } catch (e: any) {
      ElMessage({
        message: e.message,
        type: 'error'
      })
    }
  } else {
    selectedElectric.update([...selectedData.value])
    selectedElectric.updateFormData({
      ...selectedElectric.formData,
      stations: selectedElectric.data
    })
    onCancel()
    ElMessage({
      message: '关联电站成功！',
      type: 'success'
    })
  }
}
</script>

<template>
  <div class="page-main">
    <div v-auto-height class="main">
      <el-tabs
        v-model="activeTabName"
        v-auto-height
        @tab-change="
          (name: any) =>
            name === 'selected' && selectedTableRef.computedHeight()
        "
      >
        <el-tab-pane label="户用电站列表" name="list">
          <div class="search-container">
            <el-form
              ref="searchFormRef"
              :inline="true"
              :model="electricSearchData"
              label-suffix=""
            >
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="项目名称" prop="projectName">
                    <el-input
                      v-model="electricSearchData.projectName"
                      placeholder="请输入项目名称"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="资产所属公司" prop="projectCompanyName">
                    <el-input
                      v-model="electricSearchData.projectCompanyName"
                      placeholder="请输入资产所属公司"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="行政区划" prop="districtAddress">
                    <el-cascader
                      v-model="electricSearchData.districtAddress"
                      placeholder="请选择行政区划"
                      :options="electricAreas"
                      clearable
                      filterable
                      :props="{
                        value: 'value',
                        label: 'label',
                        children: 'children',
                        expandTrigger: 'hover',
                        checkStrictly: true,
                        emitPath: true
                      }"
                      popper-class="popper-width-540px"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="电站类型" prop="stationType">
                    <el-select
                      v-model="electricSearchData.stationType"
                      placeholder="请选择电站类型"
                      clearable
                    >
                      <el-option label="户用" :value="0"></el-option>
                      <el-option label="工商业" :value="1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="电站编号" prop="stationCode">
                    <el-input
                      v-model="electricSearchData.stationCode"
                      placeholder="请输入电站编号"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="电站名称" prop="stationName">
                    <el-input
                      v-model="electricSearchData.stationName"
                      placeholder="请输入电站名称"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24" class="search-buttons text-right">
                  <el-form-item style="width: auto; margin-right: 0">
                    <el-button plain @click="handleReset(searchFormRef)"
                      >重置</el-button
                    >
                    <el-button
                      type="primary"
                      @click="handleSearch(searchFormRef)"
                      >查询</el-button
                    >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <vis-table-pagination
            ref="tableRef"
            :loading="tableLoading"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="electricPage.pageSize"
            :current-page="electricPage.pageNum"
            :columns="electricColumns"
            :total="electricTotal"
            :data="electricData"
            :show-overflow-tooltip="true"
            row-key="stationUniqueId"
            background
            class="vis-table-pagination"
            @handle-selection-change="handleSelectionChange"
            @handle-size-change="electricSizeChange"
            @handle-current-change="electricCurrentChange"
          >
          </vis-table-pagination>
        </el-tab-pane>
        <el-tab-pane :label="`已选列表(${selectedTotal})`" name="selected">
          <div class="search-container">
            <el-form
              ref="localSearchFormRef"
              :inline="true"
              :model="localSearchData"
              label-suffix=""
            >
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="项目名称" prop="projectName">
                    <el-input
                      v-model="localSearchData.projectName"
                      placeholder="请输入项目名称"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="资产所属公司" prop="projectCompanyName">
                    <el-input
                      v-model="localSearchData.projectCompanyName"
                      placeholder="请输入资产所属公司"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="行政区划" prop="area">
                    <el-cascader
                      v-model="localSearchData.area"
                      placeholder="请选择行政区划"
                      :options="electricAreas"
                      clearable
                      filterable
                      :props="{
                        value: 'label',
                        label: 'label',
                        children: 'children',
                        expandTrigger: 'hover',
                        checkStrictly: true,
                        emitPath: true
                      }"
                      popper-class="popper-width-540px"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="电站类型" prop="stationType">
                    <el-select
                      v-model="localSearchData.stationType"
                      placeholder="请选择电站类型"
                      clearable
                    >
                      <el-option label="户用" :value="0"></el-option>
                      <el-option label="工商业" :value="1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="电站编号" prop="stationCode">
                    <el-input
                      v-model="localSearchData.stationCode"
                      placeholder="请输入电站编号"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="电站名称" prop="stationName">
                    <el-input
                      v-model="localSearchData.stationName"
                      placeholder="请输入电站名称"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24" class="search-buttons text-right">
                  <el-form-item style="width: auto; margin-right: 0">
                    <el-button
                      plain
                      @click="localHandleReset(localSearchFormRef)"
                      >重置</el-button
                    >
                    <el-button
                      type="primary"
                      @click="localHandleSearch(localSearchFormRef)"
                      >查询</el-button
                    >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <vis-table-pagination
            ref="selectedTableRef"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="selectedPageObj.pageSize"
            :current-page="selectedPageObj.pageNum"
            :columns="selectedColumns"
            :total="selectedTotalCopy"
            :data="localData"
            :show-overflow-tooltip="true"
            row-key="stationUniqueId"
            background
            class="vis-table-pagination"
            @handle-selection-change="localSelectionChange"
            @handle-size-change="selectedSizeChange"
            @handle-current-change="selectedCurrentChange"
          >
          </vis-table-pagination>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
  <div class="page-footer">
    <el-button plain @click="onCancel">取消</el-button>
    <el-button
      type="primary"
      :disabled="!selectedData.length"
      @click="onRelation"
    >
      确认关联
    </el-button>
  </div>
</template>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.page-main {
  height: calc(100vh - 166px);
  .main {
    padding-top: 5px;
  }
}
</style>
