<script setup lang="ts">
import request from '@/utils/request'
import BasicInfo from './components/basicInfo.vue'
import StationGroups from './components/stationGroups.vue'
import StationPage from './components/stationPage.vue'
import PersonnelList from './components/personnelList.vue'
import TeamList from './components/teamList.vue'
import ContactsPage from './components/contactsPage.vue'
import { getChildren, getParents } from '@/utils/tree'

const route = useRoute()
const router = useRouter()
const readonly = ref(true)

const selectedId = ref<any>(Number(route.query.selectedId) || 0)
const selectedCode = ref<any>(route.query.selectedCode || '')
const depth = ref<any>(Number(route.query.depth) || 1)
const selectedChildren = ref<Obj[]>([])
const tabValue = ref(Number(route.query.tabValue ?? 2))

try {
  const e: Obj = JSON.parse(
    localStorage.getItem('PVOM_STATION_GROUP_QUERY') || '{}'
  )
  selectedId.value = Number(e.selectedId || 0)
  selectedCode.value = e.selectedCode || ''
  depth.value = Number(e.depth || 1)
  tabValue.value = Number(e.tabValue || 2)
} catch {}

const treeProps = ref({
  children: 'treeChild',
  label: 'name'
})
const treeRef = ref()
const treeData = ref<any[]>([])
const defaultExpandedKeys = ref<number[]>(
  JSON.parse(localStorage.getItem('defaultExpandedKeys') || '[0]')
)

const getTreeData = async () => {
  try {
    const { data } = await request({
      url: '/operate/operation-project/getOperationCompanyTreeV2',
      method: 'get',
      loading: true
    })
    treeData.value = data?.data || []
    if (localStorage.getItem('PVOM_STATION_GROUP_QUERY')) {
      changeTreeNode({
        id: selectedId.value,
        code: selectedCode.value,
        depth: depth.value
      })
      localStorage.removeItem('PVOM_STATION_GROUP_QUERY')
    } else {
      changeTreeNode(treeData.value?.[0] || {})
    }
  } catch (e: any) {
    treeData.value = []
    changeTreeNode()
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

const noAuth = ref(true)
const changeTreeNode = async (data: any = {}) => {
  readonly.value = true

  // 有无基本信息的菜单切换
  if (!selectedId.value && data.id) tabValue.value = 1
  if (selectedId.value && !data.id && tabValue.value === 1) tabValue.value = 2

  selectedId.value = data.id ?? 0
  selectedCode.value = !data.code || data.code === 'root' ? '' : data.code
  depth.value = data.depth ?? 1

  try {
    await getExistNode()
    noAuth.value = false
    selectedChildren.value = getChildren(data.treeChild)
    localStorage.setItem(
      'PVOM-SELECTED-CHILDREN',
      JSON.stringify(selectedChildren.value.map((e: any) => e.code))
    )
    router.replace({
      path: route.path,
      query: {
        selectedId: selectedId.value,
        selectedCode: selectedCode.value,
        depth: depth.value,
        tabValue: tabValue.value
      }
    })
    nextTick(() => {
      treeRef.value.setCurrentKey(selectedId.value, false)
      const node = treeRef.value.getNode(selectedId.value)
      defaultExpandedKeys.value = getParents(node)
      localStorage.setItem(
        'defaultExpandedKeys',
        JSON.stringify(defaultExpandedKeys.value)
      )
    })
  } catch (e) {
    noAuth.value = true
  }
}

const changeTabValue = (val: any) => {
  tabValue.value = val
  router.replace({
    path: router.currentRoute.value.path,
    query: {
      selectedId: selectedId.value,
      selectedCode: selectedCode.value,
      depth: depth.value,
      tabValue: tabValue.value
    }
  })
}

onMounted(async () => {
  await getTreeData()
})

// const handleDelete = async (node: any) => {
//   try {
//     let { data } = await api.delFaultKnowledgeById({
//       id: node?.data?.id || null
//     })
//     if (data.code == '200') {
//       ElMessage({
//         message: '删除成功！',
//         type: 'success'
//       })
//       syncTreeData()
//     } else {
//       ElMessage({
//         message: data.message,
//         type: 'error'
//       })
//     }
//   } catch (e: any) {
//     ElMessage({
//       message: e,
//       type: 'error'
//     })
//   }
// }

const getExistNode = async (showMessage: boolean = false) => {
  const { data } = await request({
    url: '/operate/safeZoneController/getExistNode',
    method: 'post',
    data: {
      code: selectedCode.value,
      type: 3
    },
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    loading: false
  })
  if (!data.data) {
    showMessage &&
      ElMessage({
        message: '当前选择节点权限不足，请在左上方重新选择下级节点。！',
        type: 'warning'
      })
    return Promise.reject()
  }
  return true
}
</script>

<template>
  <div v-auto-height class="page-wrapper">
    <div class="sidebar">
      <div class="header">
        <div class="title">运维商列表</div>
        <el-button plain @click="$router.push('/ops-company/add')">
          <el-icon><Plus /></el-icon> &nbsp;新增
        </el-button>
      </div>
      <el-scrollbar max-height="calc(100% - 63px)">
        <el-tree
          ref="treeRef"
          :props="treeProps"
          :data="treeData"
          node-key="id"
          :accordion="true"
          :current-node-key="selectedId"
          :default-expanded-keys="defaultExpandedKeys"
          @node-click="changeTreeNode"
        >
          <template #default="{ node }">
            <div class="custom-tree-node">
              <div class="label" :title="node.label">{{ node.label }}</div>
              <!-- <div v-if="node.data.depth > 1" class="buttons">
                <el-popconfirm title="确认删除？" @confirm="handleDelete(node)">
                  <template #reference>
                    <div class="delete" @click.stop></div>
                  </template>
                </el-popconfirm>
              </div> -->
            </div>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
    <div class="info-tab">
      <div v-if="noAuth" class="no-auth">暂无数据</div>
      <el-tabs v-else v-model="tabValue" @tab-change="changeTabValue">
        <el-tab-pane v-if="selectedId" label="基础信息" :name="1">
          <el-scrollbar max-height="100%">
            <BasicInfo
              v-if="tabValue === 1 && selectedId"
              :key="selectedId"
              v-model="readonly"
              :company-id="selectedId"
            ></BasicInfo>
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="运维电站组" :name="2">
          <StationGroups
            v-if="tabValue === 2"
            :key="selectedId"
            :company-code="selectedCode"
          />
        </el-tab-pane>
        <el-tab-pane label="运维电站列表" :name="3">
          <StationPage
            v-if="tabValue === 3"
            :key="selectedId"
            :company-code="selectedCode"
            :depth="depth"
          ></StationPage>
        </el-tab-pane>
        <el-tab-pane label="运维人员列表" :name="4">
          <PersonnelList
            v-if="tabValue === 4"
            :key="selectedId"
            :company-id="selectedId"
          ></PersonnelList>
        </el-tab-pane>
        <el-tab-pane label="班组列表" :name="5">
          <TeamList
            v-if="tabValue === 5"
            :key="selectedId"
            :company-id="selectedId"
          ></TeamList>
        </el-tab-pane>
        <el-tab-pane label="联系人列表" :name="6">
          <ContactsPage
            v-if="tabValue === 6"
            :key="selectedId"
            :company-code="selectedCode"
          ></ContactsPage>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.custom-tree-node {
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .label {
    flex: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .buttons {
    display: none;
    flex: none;
    align-items: center;
    padding-right: 5px;
    .delete {
      background: url('./assets/delete.svg') no-repeat;
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }
}
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background: #ebeff2;
  .custom-tree-node .buttons {
    display: flex;
  }
}
.no-auth {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
