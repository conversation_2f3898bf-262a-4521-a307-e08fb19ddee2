<template>
  <div class="page-title">新增运维商</div>
  <div v-auto-height="{ bottom: 58 }" class="info-base">
    <el-scrollbar max-height="100%">
      <BasicInfo ref="basicInfoRef"></BasicInfo>
    </el-scrollbar>
  </div>
  <div class="page-footer">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button type="primary" @click="onSumbit">提交</el-button>
  </div>
</template>
<script setup lang="ts">
import BasicInfo from './components/basicInfo.vue'
import request from '@/utils/request'

const basicInfoRef = ref<any>(null)
const router = useRouter()

const onCancel = () => {
  router.push('/ops-company')
}

const onSumbit = async () => {
  await basicInfoRef.value.submitForm()
  await syncTreeData()
  onCancel()
}

const syncTreeData = async () => {
  return await request({
    url: '/operate/operatorsManage/addOperateCompanyTree',
    method: 'get',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    loading: true
  })
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss">
.tabs-box__ {
  padding: 24px 24px 0;
  .consultingInfo {
    .mb0 {
      margin-bottom: 0 !important;
    }
  }
  .checkList {
    padding: 9px 24px 0 !important;
  }
  .el-tbas {
    height: 100% !important;
    .el-tbas__content {
      height: calc(100% - 55px);
    }
  }
}
</style>
