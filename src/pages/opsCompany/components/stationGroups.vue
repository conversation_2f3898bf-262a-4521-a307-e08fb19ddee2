<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import request from '@/utils/request'
import { get } from '@/api/index.ts'
import useSelectedElectricStore from '@/store/selectedElectric'

const selectedElectric = useSelectedElectricStore()
const route = useRoute()
const router = useRouter()
const props = defineProps({
  companyCode: {
    type: String,
    default: () => ''
  }
})

onMounted(async () => {
  await getTableData()
})

const columns = [
  {
    prop: 'stationGroupNo',
    label: '运维电站组编码',
    minWidth: 120
  },
  {
    prop: 'stationGroupName',
    label: '运维电站组名称',
    minWidth: '120'
  },
  {
    prop: 'stationNum',
    label: '关联电站数',
    minWidth: '120'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: '100',
    fixed: 'right'
  }
]
const tableTotal = ref<number>(0)
const tableData = ref<Record<string, any>[]>([])
let pageObj = reactive({
  pageNum: 1,
  pageSize: 10
})
const tableLoading = ref(false)
const getTableData = async () => {
  let selectedChildren = []
  try {
    const data = localStorage.getItem('PVOM-SELECTED-CHILDREN')
    if (data) selectedChildren = JSON.parse(data) || []
  } catch {}
  props.companyCode && selectedChildren.push(props.companyCode)
  try {
    const { data } = await request({
      url: '/operate/stationGroupMange/queryByPage',
      method: 'get',
      params: {
        ...pageObj,
        operateCompanyCodeList: props.companyCode || ''
        // operateCompanyCodeList: props.companyCode
        //   ? selectedChildren.join(',')
        //   : null
      },
      loading: [tableLoading]
    })
    tableTotal.value = data?.data?.total || 0
    tableData.value = data?.data?.records || []
  } catch (e) {
    tableData.value = []
    tableTotal.value = 0
  }
}
const handleSizeChange = async (params: Record<string, any>) => {
  pageObj.pageNum = 1
  pageObj.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: Record<string, any>) => {
  pageObj.pageNum = params.currentPage
  pageObj.pageSize = params.pageSize
  await getTableData()
}

// 操作
const handleAdd = () => {
  selectedElectric.update()
  selectedElectric.updateFormData()
  localStorage.setItem('PVOM_STATION_GROUP_QUERY', JSON.stringify(route.query))
  router.push(`/ops-company/station-group/add?companyCode=${props.companyCode}`)
}
const handleEdit = (row: Obj) => {
  if (!row.id) return
  selectedElectric.update()
  selectedElectric.updateFormData()
  localStorage.setItem('PVOM_STATION_GROUP_QUERY', JSON.stringify(route.query))
  router.push('/ops-company/station-group/edit/' + row.id)
}

const handleDelete = async (row: Obj) => {
  const { code } = await get(
    '/operate/stationGroupMange/delStationGroup',
    { id: row.id },
    true
  )
  if (code === '200') {
    ElMessage({
      message: '删除成功！',
      type: 'success'
    })
    if (pageObj.pageSize === 0) return
    const totalPage = Math.ceil(tableTotal.value / pageObj.pageSize)
    pageObj.pageNum = pageObj.pageNum > totalPage ? totalPage : pageObj.pageNum
    getTableData()
  }
}
</script>

<template>
  <div class="operate end">
    <el-button type="primary" @click="handleAdd">
      <el-icon m-r-5px> <Plus /> </el-icon>新增
    </el-button>
  </div>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="pageObj.pageSize"
    :current-page="pageObj.pageNum"
    :columns="columns"
    :total="tableTotal"
    :data="tableData"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
    <template #operate="{ row }">
      <div class="table-operate">
        <el-button link @click="handleEdit(row)">编辑</el-button>
        <el-popconfirm :title="'确认删除？'" @confirm="handleDelete(row)">
          <template #reference>
            <el-button link @click.stop>删除</el-button>
          </template>
        </el-popconfirm>
      </div>
    </template>
  </vis-table-pagination>
</template>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
