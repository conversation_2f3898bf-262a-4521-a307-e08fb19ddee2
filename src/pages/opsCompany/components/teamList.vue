<template>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="searchData.pageSize"
    :current-page="searchData.pageNum"
    :columns="tableColumns"
    :total="tableData.total"
    :data="tableData.data"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import { queryByPage } from '@/api/module/team.ts'

const props = defineProps({
  companyId: {
    type: Number,
    default: () => 0
  }
})

let searchData = ref({
  operatorsId: props.companyId,
  pageNum: 1,
  pageSize: 10
})

const tableColumns = ref([
  {
    prop: 'teamsGroupsNo',
    label: '班组编号'
  },
  {
    prop: 'teamsGroupsName',
    label: '班组名称',
    minWidth: 140
  },
  {
    prop: 'teamGroupLeader',
    label: '班组组长'
  },
  {
    prop: 'teamGroupNum',
    label: '班组成员'
  }
])

let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})

let tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await queryByPage(searchData.value, [tableLoading])
    if (data.code === '200') {
      tableData.total = data?.data?.total || 0
      tableData.data = data?.data?.records || []
    }
  } catch (e) {
    tableData.total = 0
    tableData.data = []
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

onMounted(async () => {
  getTableData()
})
</script>
