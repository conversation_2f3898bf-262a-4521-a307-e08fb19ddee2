<template>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="searchData.pageSize"
    :current-page="searchData.pageNum"
    :columns="tableColumns"
    :total="tableData.total"
    :data="tableData.data"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
    <template #userSex="{ row }">
      <span v-if="row.userSex === 1">男</span>
      <span v-if="row.userSex === 2">女</span>
    </template>
    <template #roles="{ row }">
      {{ row.qualifications?.length ? row.qualifications.join('、') : '无' }}
    </template>
  </vis-table-pagination>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import { getOperationUserList } from '@/api/module/opsPersonnel.ts'

const props = defineProps({
  companyId: {
    type: Number,
    default: () => 0
  }
})

let searchData = ref({
  operatorsId: props.companyId,
  pageNum: 1,
  pageSize: 10
})

const tableColumns = ref([
  { prop: 'userName', label: '姓名' },
  { prop: 'userSex', label: '性别', slotName: 'userSex' },
  { prop: 'userAge', label: '年龄' },
  { prop: 'userPhone', label: '联系电话' },
  { prop: 'roles', label: '人员资质', slotName: 'roles', minWidth: 160 }
])

let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})

let tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await getOperationUserList(searchData.value, [tableLoading])
    if (data.code === '200') {
      tableData.total = data?.data?.total || 0
      data?.data?.records?.forEach((el: any) => {
        el.qualifications = []
        el.roles?.forEach((i: any) => {
          el.qualifications.push(i.roleName)
        })
        // if (el.isWorkPermit) {
        //   el.qualifications.push('工作许可人')
        // }
        // if (el.isWorkLeader) {
        //   el.qualifications.push('工作负责人')
        // }
        // if (el.isWorkTicketIssuer) {
        //   el.qualifications.push('作票签发人')
        // }
      })
      tableData.data = data?.data?.records || []
    }
  } catch (e) {
    tableData.total = 0
    tableData.data = []
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

onMounted(async () => {
  getTableData()
})
</script>
