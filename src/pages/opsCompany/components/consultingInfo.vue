<template>
  <div class="consultingInfo" :class="isTypeHandleShow ? 'cls-detail' : ''">
    <div class="title">
      <div class="left-title">
        资质信息
        <div v-if="!isTypeHandleShow" class="waringInfo">
          <el-icon color="#FF9900">
            <WarningFilled />
          </el-icon>
          提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件
        </div>
      </div>
    </div>
    <el-form
      ref="consultingInfoFormRef"
      :inline="true"
      :model="formData"
      :rules="isTypeHandleShow ? {} : consulRules"
      label-suffix=""
      label-width="124px"
      class="consulelForm__"
    >
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="营业执照" prop="license">
            <template v-if="!licenseArr?.length && readonly">--</template>
            <SpicUpload
              v-else
              :key="uploadKey"
              v-model="licenseArr"
              type="image"
              :handle-remove-fn="handleRemoveLicenseFn"
              :limit="5"
              :disabled="readonly && isTypeHandleShow"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="营业执照有效期" prop="licenseTimeRange">
            <span v-if="readonly">
              {{
                formData.licenseStartDate && formData.licenseEndDate
                  ? `${formData.licenseStartDate.slice(
                      0,
                      10
                    )}至${formData.licenseEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="licenseTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="企业资质" prop="enterpriseQualification">
            <template v-if="!enterpriseQualificationArr?.length && readonly"
              >--</template
            >
            <SpicUpload
              v-else
              :key="uploadKey"
              v-model="enterpriseQualificationArr"
              type="image"
              :handle-remove-fn="handleRemoveEnterpriseQualificationFn"
              :limit="5"
              :disabled="readonly && isTypeHandleShow"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item
            label="企业资质有效期"
            prop="enterpriseQualificationTimeRange"
          >
            <span v-if="readonly">
              {{
                formData.enterpriseQualificationStartDate &&
                formData.enterpriseQualificationEndDate
                  ? `${formData.enterpriseQualificationStartDate.slice(
                      0,
                      10
                    )}至${formData.enterpriseQualificationEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="enterpriseQualificationTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="安全许可证" prop="securityLicense">
            <template v-if="!securityLicenseArr?.length && readonly"
              >--</template
            >
            <SpicUpload
              v-else
              :key="uploadKey"
              v-model="securityLicenseArr"
              type="image"
              :handle-remove-fn="handleRemoveSecurityLicenseFn"
              :limit="5"
              :disabled="readonly && isTypeHandleShow"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item
            label="安全许可证有效期"
            prop="securityLicenseTimeRange"
          >
            <span v-if="readonly">
              {{
                formData.securityLicenseStartDate &&
                formData.securityLicenseEndDate
                  ? `${formData.securityLicenseStartDate.slice(
                      0,
                      10
                    )}至${formData.securityLicenseEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="securityLicenseTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="安全承诺书" prop="safePromise" class="mb0">
            <template v-if="!safePromiseArr?.length && readonly">--</template>
            <SpicUpload
              v-else
              :key="uploadKey"
              v-model="safePromiseArr"
              type="image"
              :handle-remove-fn="handleRemoveSafePromiseFn"
              :limit="5"
              :disabled="readonly && isTypeHandleShow"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item
            label="安全承诺书有效期"
            prop="safePromiseTimeRange"
            class="mb0"
          >
            <span v-if="readonly">
              {{
                formData.safePromiseStartDate && formData.safePromiseEndDate
                  ? `${formData.safePromiseStartDate.slice(
                      0,
                      10
                    )}至${formData.safePromiseEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="safePromiseTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { opsCompany as opsCompanyAPI } from '@/api/index.ts'
import SpicUpload from '@/components/spic-upload'
import type { FormInstance, FormRules } from 'element-plus'
interface RuleForm {
  license: string
  enterpriseQualification: string
  securityLicense: string
  safePromise: string
}
const consultingInfoFormRef = ref<FormInstance>()
const readonly = defineModel<boolean>({ default: false })
const props = defineProps({
  detailData: {
    type: Object,
    default: () => {
      return {
        license: '',
        licenseStartDate: '',
        licenseEndDate: '',
        enterpriseQualification: '',
        enterpriseQualificationStartDate: '',
        enterpriseQualificationEndDate: '',
        securityLicense: '',
        securityLicenseStartDate: '',
        securityLicenseEndDate: '',
        safePromise: '',
        safePromiseStartDate: '',
        safePromiseEndDate: ''
      }
    }
  }
})
const formData = ref<Record<string, any>>({
  license: '',
  licenseStartDate: '',
  licenseEndDate: '',
  enterpriseQualification: '',
  enterpriseQualificationStartDate: '',
  enterpriseQualificationEndDate: '',
  securityLicense: '',
  securityLicenseStartDate: '',
  securityLicenseEndDate: '',
  safePromise: '',
  safePromiseStartDate: '',
  safePromiseEndDate: ''
})

const consulRules = reactive<FormRules<RuleForm>>({
  license: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
  enterpriseQualification: [
    {
      required: true,
      message: '请上传企业资质',
      trigger: 'change'
    }
  ],
  securityLicense: [
    { required: true, message: '请上传安全许可证', trigger: 'change' }
  ],
  safePromise: [
    {
      required: true,
      message: '请上传安全承诺书',
      trigger: 'change'
    }
  ]
})
// 上传图片
const licenseArr = ref<any[]>([])

watch(
  () => licenseArr.value,
  (val) => {
    if (val?.length) {
      formData.value.license = val.join(',')
      consultingInfoFormRef.value?.validateField('license')
    } else {
      formData.value.license = ''
    }
  },
  {
    deep: true
  }
)

const enterpriseQualificationArr = ref<any[]>([])
watch(
  () => enterpriseQualificationArr.value,
  (val) => {
    if (val?.length) {
      formData.value.enterpriseQualification = val.join(',')
      consultingInfoFormRef.value?.validateField('enterpriseQualification')
    } else {
      formData.value.enterpriseQualification = ''
    }
  },
  {
    deep: true
  }
)

const securityLicenseArr = ref<any[]>([])

watch(
  () => securityLicenseArr.value,
  (val) => {
    if (val?.length) {
      formData.value.securityLicense = val.join(',')
      consultingInfoFormRef.value?.validateField('securityLicense')
    } else {
      formData.value.securityLicense = ''
    }
  },
  {
    deep: true
  }
)
const safePromiseArr = ref<any[]>([])

watch(
  () => safePromiseArr.value,
  (val) => {
    if (val?.length) {
      formData.value.safePromise = val.join(',')
      consultingInfoFormRef.value?.validateField('safePromise')
    } else {
      formData.value.safePromise = ''
    }
  },
  {
    deep: true
  }
)

const handleRemoveLicenseFn = () => {
  if (licenseArr.value.length <= 0) {
    formData.value.licenseStartDate = ''
    formData.value.licenseEndDate = ''
    licenseTimeRange.value = []
  }
}
const handleRemoveEnterpriseQualificationFn = () => {
  if (enterpriseQualificationArr.value.length <= 0) {
    formData.value.enterpriseQualificationStartDate = ''
    formData.value.enterpriseQualificationEndDate = ''
    enterpriseQualificationTimeRange.value = []
  }
}
const handleRemoveSecurityLicenseFn = () => {
  if (securityLicenseArr.value.length <= 0) {
    formData.value.securityLicenseStartDate = ''
    formData.value.securityLicenseEndDate = ''
    securityLicenseTimeRange.value = []
  }
}
const handleRemoveSafePromiseFn = () => {
  if (safePromiseArr.value.length <= 0) {
    formData.value.safePromiseStartDate = ''
    formData.value.safePromiseEndDate = ''
    safePromiseTimeRange.value = []
  }
}

// 提交
const emit = defineEmits(['setPicData'])
const isTypeHandleShow = ref(false) // 判断页面状态
const isDisable = ref(false) // 判断是否禁用
const id_ = ref('') // 页面id
const oldObjSearchData = ref({})
const submitForm = async () => {
  await consultingInfoFormRef.value?.validate(async (valid) => {
    if (readonly.value) {
      if (valid) {
        dealEffecteTime()
        try {
          let { data } = await opsCompanyAPI.addOperatorsInfo({
            ...formData.value,
            id: id_.value,
            menuType: 2
          })

          if (data.code == '200') {
            ElMessage({
              message: '保存成功！',
              type: 'success'
            })
            isTypeHandleShow.value = true
            oldObjSearchData.value = JSON.parse(JSON.stringify(formData.value))
          } else {
            isTypeHandleShow.value = false
            ElMessage({
              message: '保存失败！',
              type: 'error'
            })
          }
        } catch (e) {
          isTypeHandleShow.value = false
          isDisable.value = true
          ElMessage({
            message: '保存失败！',
            type: 'error'
          })
        }
      } else {
        isTypeHandleShow.value = false
        isDisable.value = true
      }
    } else {
      dealEffecteTime()
      emit('setPicData', {
        valid,
        data: { ...formData.value }
      })
    }
  })
}
// 处理有效期开始时间和结束时间
const dealEffecteTime = () => {
  if (licenseTimeRange.value?.length) {
    formData.value.licenseStartDate = licenseTimeRange.value[0] + ' 00:00:00'
    formData.value.licenseEndDate = licenseTimeRange.value[1] + ' 23:59:59'
  } else {
    formData.value.licenseStartDate = ''
    formData.value.licenseEndDate = ''
  }

  if (enterpriseQualificationTimeRange.value?.length) {
    formData.value.enterpriseQualificationStartDate =
      enterpriseQualificationTimeRange.value[0] + ' 00:00:00'
    formData.value.enterpriseQualificationEndDate =
      enterpriseQualificationTimeRange.value[1] + ' 23:59:59'
  } else {
    formData.value.enterpriseQualificationStartDate = ''
    formData.value.enterpriseQualificationEndDate = ''
  }

  if (securityLicenseTimeRange.value?.length) {
    formData.value.securityLicenseStartDate =
      securityLicenseTimeRange.value[0] + ' 00:00:00'
    formData.value.securityLicenseEndDate =
      securityLicenseTimeRange.value[1] + ' 23:59:59'
  } else {
    formData.value.securityLicenseStartDate = ''
    formData.value.securityLicenseEndDate = ''
  }

  if (safePromiseTimeRange.value?.length) {
    formData.value.safePromiseStartDate =
      safePromiseTimeRange.value[0] + ' 00:00:00'
    formData.value.safePromiseEndDate =
      safePromiseTimeRange.value[1] + ' 23:59:59'
  } else {
    formData.value.safePromiseStartDate = ''
    formData.value.safePromiseEndDate = ''
  }
}
// 重置
const resetForm = () => {
  if (!consultingInfoFormRef.value) return
  consultingInfoFormRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm,
  formData,
  dealEffecteTime
})

// 编辑按钮
onMounted(async () => {
  if (readonly.value) {
    isTypeHandleShow.value = true
  }
})
watch(
  () => props.detailData,
  async (val) => {
    setFormData(val)
  },
  {
    deep: true
  }
)
const licenseTimeRange = ref<any[]>([])
const enterpriseQualificationTimeRange = ref<any[]>([])
const securityLicenseTimeRange = ref<any[]>([])
const safePromiseTimeRange = ref<any[]>([])
const setFormData = (val: any) => {
  if (val) {
    formData.value.license = val.license
    formData.value.enterpriseQualification = val.enterpriseQualification
    formData.value.securityLicense = val.securityLicense
    formData.value.safePromise = val.safePromise
    id_.value = val.id

    // 营业执照
    licenseArr.value = val.license ? val.license?.split(',') : []

    // 企业执照
    enterpriseQualificationArr.value = val.enterpriseQualification
      ? val.enterpriseQualification?.split(',')
      : []

    // 安全许可证
    securityLicenseArr.value = val.securityLicense
      ? val.securityLicense?.split(',')
      : []

    // 安全承诺书
    safePromiseArr.value = val.safePromise ? val.safePromise?.split(',') : []

    // 4个证书的有效期
    if (val.licenseStartDate && val.licenseEndDate) {
      formData.value.licenseStartDate = val.licenseStartDate
      formData.value.licenseEndDate = val.licenseEndDate
      licenseTimeRange.value = [
        props.detailData.licenseStartDate.slice(0, 10),
        props.detailData.licenseEndDate.slice(0, 10)
      ]
    }
    if (
      val.enterpriseQualificationStartDate &&
      val.enterpriseQualificationEndDate
    ) {
      formData.value.enterpriseQualificationStartDate =
        val.enterpriseQualificationStartDate
      formData.value.enterpriseQualificationEndDate =
        val.enterpriseQualificationEndDate
      enterpriseQualificationTimeRange.value = [
        props.detailData.enterpriseQualificationStartDate.slice(0, 10),
        props.detailData.enterpriseQualificationEndDate.slice(0, 10)
      ]
    }
    if (val.securityLicenseStartDate && val.securityLicenseEndDate) {
      formData.value.securityLicenseStartDate = val.securityLicenseStartDate
      formData.value.securityLicenseEndDate = val.securityLicenseEndDate
      securityLicenseTimeRange.value = [
        props.detailData.securityLicenseStartDate.slice(0, 10),
        props.detailData.securityLicenseEndDate.slice(0, 10)
      ]
    }
    if (val.safePromiseStartDate && val.safePromiseEndDate) {
      formData.value.safePromiseStartDate = val.safePromiseStartDate
      formData.value.safePromiseEndDate = val.safePromiseEndDate
      safePromiseTimeRange.value = [
        props.detailData.safePromiseStartDate.slice(0, 10),
        props.detailData.safePromiseEndDate.slice(0, 10)
      ]
    }
  }
}
const uploadKey = ref<any>('')
</script>
<style lang="scss">
.consulelForm__ {
  .el-form-item--default {
    margin: 0 !important;
    margin-bottom: 24px !important;
  }
}
</style>
<style lang="scss" scoped>
.consultingInfo {
  .title {
    font-size: 16px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left-title {
      display: flex;
      align-items: center;
    }

    .waringInfo {
      display: flex;
      align-items: center;
      color: #ff9900;
      font-size: 12px;

      .el-icon {
        margin: 0 4px 0 8px;
        font-size: 16px;
      }
    }
  }
}

.cls-detail {
  word-wrap: break-word;
  word-break: break-all;
}
.cls-detail :deep(.el-form-item__label) {
  color: rgba(0, 0, 0, 0.45) !important;
}
.cls-detail :deep(.el-form-item__content) {
  line-height: 22px !important;
}
</style>
