<template>
  <el-dialog
    v-model="transferDialogVisible"
    :title="title"
    class="vis-dialog pb100"
    align-center
    :before-close="closeDialog"
    :close-on-click-modal="false"
    width="900"
  >
    <el-transfer
      ref="transferRef"
      v-model="selectedKeys"
      filterable
      :data="dataList"
      :titles="['全选', '全选']"
      target-order="unshift"
      :format="{
        noChecked: '（0/${total}）',
        hasChecked: '（${checked}/${total}）'
      }"
      style="text-align: left; display: inline-block"
      filter-placeholder="请输入姓名"
    >
      <template #default="{ option }">
        <div :title="option.userName">
          <span class="title1">{{ option.userName }}</span>
          <span class="title2">({{ option.userPhone }})</span>
        </div>
      </template>
    </el-transfer>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSave">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import request from '@/utils/request'

const selectedKeys = ref<any[]>([]) // 已选列表的数据

const transferDialogVisible = ref(false)
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  title: {
    type: String,
    default: '选择用户',
    required: false
  },
  selectedIds: {
    type: Array,
    default: () => []
  },
  selectOne: {
    type: Boolean,
    default: false
  },
  operatorsId: {
    type: Number,
    default: 0,
    required: true
  }
})
watch(
  () => props.isShow,
  async (val: boolean) => {
    transferDialogVisible.value = val
    if (val) {
      await getData()
      selectedKeys.value = [...props.selectedIds]
    }
  }
)
watch(
  () => selectedKeys.value?.length,
  (num: number) => {
    if (props.selectOne) {
      if (num > 1) {
        ElMessage.warning('只能选择一个用户')
        selectedKeys.value = [selectedKeys.value?.[0]]
      }
    }
  }
)
const dataList = ref<Obj[]>([])
const getData = async () => {
  try {
    const { data } = await request({
      url: '/operate/operationUser/getOperationUserList',
      method: 'post',
      data: {
        operatorsId: props.operatorsId,
        pageSize: 1000,
        pageNo: 1
      },
      loading: true
    })
    dataList.value = data?.data?.records || []
    dataList.value = dataList.value?.map((e: any) => ({
      ...e,
      label: e.userName,
      key: e.ucUserId
    }))
  } catch (e: any) {
    dataList.value = []
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
// 点击取消
const transferRef = ref()
const emit = defineEmits(['closeDialog', 'uplateData'])
const closeDialog = () => {
  dataList.value = []
  selectedKeys.value = []
  transferDialogVisible.value = false

  transferRef.value?.clearQuery('left')
  transferRef.value?.clearQuery('right')
  emit('closeDialog')
}
// 点击确认
const handleSave = () => {
  if (selectedKeys.value.length) {
    let arr: any = []
    selectedKeys.value.forEach((item: any) => {
      dataList.value.forEach((items: any) => {
        if (items.key == item) {
          arr.push(items)
        }
      })
    })
    emit('uplateData', arr)
    closeDialog()
  } else {
    emit('uplateData', [])
    closeDialog()
  }
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
:deep(.el-transfer) {
  margin-bottom: 100px !important;
}
:deep(.el-transfer-panel) {
  width: 366px !important;
}
:deep(.el-transfer__button) {
  padding: 0;
  margin: 0 0 16px 0;
  display: block !important;
  width: 36px;
  height: 36px;
}

:deep(.el-transfer__button.is-disabled) {
  background: rgba(238, 238, 238, 1);
  border-color: rgba(238, 238, 238, 1);
}

:deep(
  .el-transfer__button.is-disabled .el-icon,
  .el-transfer__button.is-disabled span
) {
  color: rgba(169, 169, 169, 1);
}
:deep(
  .el-transfer-panel
    .el-transfer-panel__header
    .el-checkbox
    .el-checkbox__label
    span
) {
  // position: static !important;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  font-weight: 400;
}
:deep(.el-transfer-panel .el-transfer-panel__header .el-checkbox__label) {
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 400;
}
:deep(.el-transfer-panel__item .el-checkbox__label) {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
:deep(.el-transfer__button > span > i) {
  // display: none;
  font-size: 20px;
}
:deep(.el-transfer-panel .el-transfer-panel__header) {
  background: #fff;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
  box-sizing: content-box;
  position: relative;
}

:deep(.el-transfer-panel:first-child) {
  .el-transfer-panel__header::after {
    content: '人员列表';
    position: absolute;
    left: 50%;
    margin-left: -28px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
}
:deep(.el-transfer-panel:last-child) {
  .el-transfer-panel__header::after {
    content: '已选列表';
    position: absolute;
    left: 50%;
    margin-left: -28px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
}
:deep(.el-transfer-panel__empty) {
  margin-top: 150px;
  color: rgba(0, 0, 0, 0.45);
}
:deep(.el-transfer-panel__empty::before) {
  content: '暂无数据';
  position: absolute;
  width: 100px;
  background: #fff;
  left: 148px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}
:deep(.el-transfer-panel__body) {
  height: 404px;
}
:deep(.el-transfer-panel .el-input__wrapper) {
  height: 40px;
}
:deep(.el-transfer-panel__filter .el-input__inner) {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85) !important;
}
:deep(.el-transfer-panel__filter .el-input__inner::placeholder) {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.25) !important;
}
.el-dialog__footer .el-button {
  width: 72px;
  height: 40px;
}
</style>
<style lang="scss">
.el-dialog.vis-dialog.pb100 .el-dialog__body {
  padding: 24px 0 24px 35px !important;
}
.title1 {
  font-size: 14px;
  margin-right: 12px;
}
.title2 {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
