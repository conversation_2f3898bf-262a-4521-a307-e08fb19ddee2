<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import useOpsElectricData from '../hooks/useOpsElectricData'

const props = defineProps({
  companyCode: {
    type: String,
    default: () => ''
  },
  depth: {
    type: Number,
    default: () => 0
  }
})

let searchData = ref({
  stationName: ''
})

const tableLoading = ref(false)
const {
  opsElectricColumns,
  opsElectricTotal,
  opsElectricData,
  opsElectricSizeChange,
  opsElectricCurrentChange,
  opsElectricPage,
  getOpsElectricData
} = useOpsElectricData(props.companyCode, props.depth, searchData)

const searchProps = ref([{ label: '电站名称', prop: 'stationName', span: 24 }])
const handleSearch = async (val: any) => {
  searchData.value = val
  opsElectricPage.pageNum = 1
  opsElectricPage.pageSize = 10
  getOpsElectricData()
}
</script>
<template>
  <div class="operate" style="width: 600px">
    <searchForm
      :search-props="searchProps"
      :search-data="searchData"
      label-width="80px"
      @submit-emits="handleSearch"
    ></searchForm>
  </div>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="opsElectricPage.pageSize"
    :current-page="opsElectricPage.pageNum"
    :columns="opsElectricColumns"
    :total="opsElectricTotal"
    :data="opsElectricData"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    @handle-size-change="opsElectricSizeChange"
    @handle-current-change="opsElectricCurrentChange"
  >
  </vis-table-pagination>
</template>
