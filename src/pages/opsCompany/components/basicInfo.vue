<template>
  <div
    class="basicInfo border-bottom"
    :class="isTypeHandleShow ? 'cls-detail' : ''"
  >
    <div class="title">
      基础信息
      <div>
        <el-button v-if="isTypeHandleShow" type="primary" @click="onSumbitEdit">
          编辑
        </el-button>
        <div v-if="companyId && !isTypeHandleShow">
          <el-button plain @click="cancelBtn">取消</el-button>
          <el-button type="primary" @click="submitForm">保存</el-button>
        </div>
      </div>
    </div>
    <el-form
      ref="formRef"
      :inline="true"
      :model="formData"
      :rules="isTypeHandleShow ? {} : rules"
      label-suffix=""
      label-width="110px"
      class="elForm__"
    >
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="运维单位类型" prop="operatorType">
            <span v-if="isTypeHandleShow">{{
              formData.operatorType == 2
                ? '运维实施单位'
                : formData.operatorType == 1
                  ? '运维管理单位'
                  : ''
            }}</span>
            <el-select
              v-else
              v-model="formData.operatorType"
              placeholder="请选择运维单位类型"
              :disabled="disabledParentCompany"
              clearable
            >
              <el-option label="运维管理单位" :value="1" />
              <el-option label="运维实施单位" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="formData.operatorType == '2'" :span="10" :offset="2">
          <el-form-item label="上级管理单位" prop="parentCompanyId">
            <span v-if="isTypeHandleShow">{{ formData.parentCompany }}</span>
            <el-select
              v-else
              v-model="formData.parentCompanyId"
              placeholder="请选择上级管理单位"
              clearable
              filterable
              @change="changeParentItem"
            >
              <template v-for="item in parentCompanys" :key="item.id">
                <el-option :label="item.companyName" :value="item.id" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="formData.operatorType != '2' ? 2 : 0">
          <el-form-item label="公司名称" prop="companyCode">
            <span v-if="isTypeHandleShow">{{ formData.companyName }}</span>
            <el-select
              v-else
              v-model="formData.companyCode"
              placeholder="请选择公司"
              clearable
              :disabled="isDisable"
              filterable
              @change="changeItem"
            >
              <template v-for="item in companyTypesArr" :key="item.companyCode">
                <el-option
                  :label="item.companyName"
                  :value="item.companyCode"
                  :disabled="item.isSelect == 1 ? true : false"
                />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="formData.operatorType == '2' ? 2 : 0">
          <el-form-item label="公司地址" prop="address">
            <span v-if="isTypeHandleShow">{{ formData.address }}</span>
            <el-input
              v-else
              v-model.trim="formData.address"
              placeholder="请输入公司地址"
              clearable
            />
          </el-form-item>
        </el-col>
        <!-- 公司负责人 -->
        <el-col :span="10" :offset="formData.operatorType != '2' ? 2 : 0">
          <el-form-item label="公司负责人" prop="companyLeader_">
            <span v-if="isTypeHandleShow">
              {{ formData.companyLeader_ || '--' }}
            </span>
            <el-input
              v-else
              v-model="formData.companyLeader_"
              placeholder="请选择公司负责人"
              clearable
              @click="focusFnCompanyLeader"
            >
              <template #suffix>
                <el-icon class="el-input__icon"><ArrowDown /></el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="10" :offset="formData.operatorType == '2' ? 2 : 0">
          <el-form-item label="区域监盘人" prop="headUser_">
            <span v-if="isTypeHandleShow">
              {{ formData.headUser_ }}
            </span>
            <el-input
              v-else
              v-model="formData.headUser_"
              placeholder="请选择区域监盘人"
              clearable
              @clear="() => ((formData.headUser = ''), (employeeIdArr = []))"
              @click="focusFn"
            >
              <template #suffix>
                <el-icon class="el-input__icon"><ArrowDown /></el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="formData.operatorType != '2' ? 2 : 0">
          <el-form-item label="联系电话" prop="phone">
            <span v-if="isTypeHandleShow">{{ formData.phone }}</span>
            <el-input
              v-else
              v-model.trim="formData.phone"
              placeholder="请输入联系电话"
              clearable
              maxlength="11"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="formData.operatorType == '2' ? 2 : 0">
          <el-form-item label="身份证号" prop="identityCard">
            <span v-if="isTypeHandleShow">{{ formData.identityCard }}</span>
            <el-input
              v-else
              v-model="formData.identityCard"
              placeholder="请输入身份证号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="formData.operatorType == '2'"
          :span="10"
          :offset="formData.operatorType != '2' ? 2 : 0"
        >
          <el-form-item label="身份证有效期" prop="identityCardTimeRange">
            <span v-if="isTypeHandleShow">
              {{
                formData.identityCardStartDate && formData.identityCardEndDate
                  ? `${formData.identityCardStartDate.slice(
                      0,
                      10
                    )}至${formData.identityCardEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="identityCardTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="formData.operatorType != '2'"
          :span="10"
          :offset="formData.operatorType != '2' ? 2 : 0"
        >
          <el-form-item label="身份证有效期" prop="identityCardTimeRange">
            <span v-if="isTypeHandleShow">
              {{
                formData.identityCardStartDate && formData.identityCardEndDate
                  ? `${formData.identityCardStartDate.slice(
                      0,
                      10
                    )}至${formData.identityCardEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="identityCardTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="formData.operatorType == '2' ? 2 : 0">
          <el-form-item
            label="身份证照片"
            prop="cardPhoto"
            class="cardPhotoClass"
          >
            <SpicUpload
              :key="uploadKey"
              v-model="files"
              type="image"
              :handle-remove-fn="handleRemoveFn"
              :limit="5"
              :disabled="readonly && isTypeHandleShow"
            />
          </el-form-item>
          <div v-if="!isTypeHandleShow" class="waringInfoBas__">
            <el-icon color="#FF9900">
              <WarningFilled />
            </el-icon>
            提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <ConsultingInfo
    ref="consultingInfoRef"
    v-model="readonly"
    :detail-data="detailData"
  ></ConsultingInfo>

  <SelectUser
    :is-show="isShowCompanyLeader"
    :operators-id="detailData.id"
    :selected-ids="employeeIdArrCompanyLeader"
    title="公司负责人"
    :select-one="true"
    @uplate-data="uplateDataCompanyLeader"
    @close-dialog="() => (isShowCompanyLeader = false)"
  ></SelectUser>
  <SelectUser
    :is-show="isShow"
    :operators-id="detailData.id"
    :selected-ids="employeeIdArr"
    title="区域监盘人"
    @uplate-data="uplateData"
    @close-dialog="() => (isShow = false)"
  ></SelectUser>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import SpicUpload from '@/components/spic-upload'
import { opsCompany as api } from '@/api/index.ts'
import request from '@/utils/request.ts'
import ConsultingInfo from './consultingInfo.vue'
import SelectUser from './selectUser.vue'

const consultingInfoRef = ref<any>()

const formRulesheadUser = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入区域监盘人'))
  } else {
    callback()
  }
}
const formRulesphone = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入联系电话'))
  } else {
    if (value.trim().length < 11) {
      callback(new Error('请输入11位联系电话'))
    } else {
      callback()
    }
  }
}
const formRulesidentityCard = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入身份证号'))
  } else {
    // !/^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[Xx\d]$/
    if (!/^[0-9A-Za-z]+$/.test(value)) {
      callback(new Error('请输入正确身份证号'))
    } else {
      callback()
    }
  }
}
const readonly = defineModel<boolean>({ default: false })

const props = defineProps({
  companyId: {
    type: Number,
    default: () => 0
  }
})

const detailData = ref<Obj>({
  operatorType: '',
  parentCompany: '',
  parentCompanyId: null,
  companyCode: '',
  companyName: '',
  address: '',
  headUser: '',
  phone: '',
  cardPhoto: '',
  identityCard: ''
})
const getDetailData = async () => {
  if (!props.companyId) return
  try {
    let { data } = await api.getOperatorsInfoById({
      id: props.companyId
    })
    detailData.value = data?.data || {}
    setformData(detailData.value)
  } catch (e: any) {
    detailData.value = {}
    setformData(detailData.value)
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

const formRef = ref<FormInstance>()
const formData = ref<any>({
  operatorType: null, // 运维单位类型
  parentCompanyId: null,
  parentCompany: '', // 公司名称
  companyCode: '',
  companyName: '',
  address: '',
  headUser: '',
  headUser_: '',
  companyLeader_: '', // 公司负责人
  companyLeader: '', // 公司负责人
  phone: '',
  cardPhoto: '',
  identityCard: '',
  identityCardStartDate: '',
  identityCardEndDate: ''
})
const isShow = ref(false)
const isShowCompanyLeader = ref(false)
const employeeIdArr = ref<any[]>([])
const employeeIdArrCompanyLeader = ref<any[]>([])
const employeeArr = ref<any[]>([])
const uplateData = (arr: Obj[]) => {
  arr = arrayRd(arr)
  formData.value.headUser_ = arr.map((e: any) => e.userName).join(',')
  formData.value.headUser = arr
    .map((e: any) => e.userName + '_' + e.ucUserId)
    .join(',')
  employeeIdArr.value = arr.map((e: any) => e.ucUserId)
  employeeArr.value = arr
}
// 按ID去重
const arrayRd = (arr: any[]) => {
  let find: any = []
  arr.forEach((e: any) => {
    if (!find.find((i: any) => i.ucUserId == e.ucUserId)) {
      find.push(e)
    }
  })
  return find
}
const uplateDataCompanyLeader = (arr: Obj[]) => {
  formData.value.companyLeader_ = arr.map((e: any) => e.userName).join(',')
  formData.value.companyLeader = arr
    .map((e: any) => e.userName + '_' + e.ucUserId)
    .join(',')
  employeeIdArrCompanyLeader.value = arr.map((e: any) => e.ucUserId)
}
const focusFn = () => {
  isShow.value = true
}
const focusFnCompanyLeader = () => {
  isShowCompanyLeader.value = true
}
const companyTypesArr = ref<any[]>([])
const parentCompanys = ref<any[]>([])
const rules = reactive<any>({
  operatorType: [
    { required: true, message: '请选择运维单位类型', trigger: 'change' }
  ],
  parentCompanyId: [
    { required: true, message: '请选择上级管理单位', trigger: 'blur' }
  ],
  companyCode: [
    { required: false, message: '请选择公司名称', trigger: 'change' }
  ],
  address: [
    {
      required: true,
      message: '请输入公司地址',
      trigger: 'blur'
    }
  ],
  headUser_: [
    {
      required: true,
      trigger: 'blur',
      validator: formRulesheadUser
    }
  ],
  companyLeader_: [
    {
      required: true,
      trigger: 'blur',
      message: '请选择公司负责人'
    }
  ],
  phone: [
    {
      required: true,
      trigger: 'blur',
      validator: formRulesphone
    }
  ],
  cardPhoto: [
    { required: true, message: '请上传身份证照片', trigger: 'change' }
  ],
  identityCard: [
    {
      required: true,
      trigger: 'blur',
      validator: formRulesidentityCard
    }
  ]
})
const identityCardTimeRange = ref<any[]>([])
// 上传图片
let files = ref<any[]>([])

watch(
  () => files.value,
  (val) => {
    if (val.length) {
      formData.value.cardPhoto = val.join(',')
      formRef.value?.validateField('cardPhoto')
    } else {
      formData.value.cardPhoto = ''
    }
  },
  {
    deep: true
  }
)

const handleRemoveFn = () => {
  if (files.value.length <= 0) {
    formData.value.identityCardStartDate = ''
    formData.value.identityCardEndDate = ''
    identityCardTimeRange.value = []
  }
}

// 提交
const isTypeHandleShow = ref(false) // 判断页面状态
const isDisable = ref(true) // 判断是否禁用
const id_ = ref('') // 页面id
const oldObjformData = ref<any>({})

const submitForm = async () => {
  await formRef.value?.validate(async (valid) => {
    if (valid) {
      dealEffecteTime()
      consultingInfoRef.value.dealEffecteTime()
      try {
        let { data } = await api.addOperatorsInfo({
          ...formData.value,
          ...consultingInfoRef.value.formData,
          id: id_.value || null
        })
        if (data.code == '200') {
          ElMessage({
            message: '保存成功！',
            type: 'success'
          })
          readonly.value = true
          isTypeHandleShow.value = true
          oldObjformData.value = JSON.parse(JSON.stringify(formData.value))
        } else {
          isTypeHandleShow.value = false
          ElMessage({
            message: '保存失败！',
            type: 'error'
          })
        }
      } catch (e) {
        readonly.value = false
        isTypeHandleShow.value = false
        isDisable.value = true
        ElMessage({
          message: '保存失败！',
          type: 'error'
        })
      }
    } else {
      readonly.value = false
      isTypeHandleShow.value = false
      isDisable.value = true
    }
  })
}
// 处理有效期开始时间和结束时间
const dealEffecteTime = () => {
  if (identityCardTimeRange.value?.length) {
    formData.value.identityCardStartDate =
      identityCardTimeRange.value[0] + ' 00:00:00'
    formData.value.identityCardEndDate =
      identityCardTimeRange.value[1] + ' 23:59:59'
  } else {
    formData.value.identityCardStartDate = ''
    formData.value.identityCardEndDate = ''
  }
}
// 重置
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})

onMounted(async () => {
  getDetailData()
  if (readonly.value) {
    isTypeHandleShow.value = true
  } else {
    isDisable.value = false
    getApiCompanyList()
    getparentCompanys()
  }
})

const disabledParentCompany = ref(false)
const setformData = (val: any) => {
  if (val) {
    formData.value.operatorType = val.operatorType
    if (formData.value.operatorType == '1') {
      disabledParentCompany.value = true
    }
    formData.value.parentCompanyId = val.parentCompanyId
    formData.value.parentCompany = val.parentCompany

    formData.value.companyCode = val.companyCode
    formData.value.companyName = val.companyName
    formData.value.address = val.address
    formData.value.headUser = val.headUser
    formData.value.companyLeader = val.companyLeader

    formData.value.headUser_ =
      val.headUser &&
      val.headUser
        .split(',')
        .map((item: any) =>
          item
            ?.split('_')
            ?.slice(0, item?.split('_')?.length - 1)
            ?.join('_')
        )
        .join(',')
    formData.value.companyLeader_ =
      val.companyLeader &&
      val.companyLeader
        .split(',')
        .map((item: any) =>
          item
            ?.split('_')
            ?.slice(0, item?.split('_')?.length - 1)
            ?.join('_')
        )
        .join(',')
    employeeIdArr.value =
      val.headUser &&
      val.headUser.split(',').map((item: any) => item.split('_')?.at(-1))

    employeeIdArrCompanyLeader.value =
      val.companyLeader &&
      val.companyLeader.split(',').map((item: any) => item.split('_')?.at(-1))

    employeeArr.value =
      formData.value.headUser_ &&
      formData.value.headUser_?.split(',')?.map((e: any, i: number) => ({
        employeeName: e,
        id: employeeIdArr.value[i]
      }))

    formData.value.phone = val.phone
    formData.value.cardPhoto = val.cardPhoto
    formData.value.identityCard = val.identityCard
    id_.value = val.id
    if (val.identityCardStartDate && val.identityCardEndDate) {
      formData.value.identityCardStartDate = val.identityCardStartDate
      formData.value.identityCardEndDate = val.identityCardEndDate
      identityCardTimeRange.value = [
        detailData.value.identityCardStartDate.slice(0, 10),
        detailData.value.identityCardEndDate.slice(0, 10)
      ]
    }
    // 身份证照片
    files.value = val.cardPhoto ? val.cardPhoto?.split(',') : []
  }
}
const getApiCompanyList = async () => {
  try {
    let { data } = await api.getCompanyList(
      {
        companyCode: '',
        companyName: '',
        companyTypeId: '',
        socialUniformCreditCode: '',
        pageNum: 1,
        pageSize: 20000
      },
      false
    )
    if (data.code == '200') {
      companyTypesArr.value = data.data || []
    } else {
      companyTypesArr.value = []
    }
  } catch (e) {
    companyTypesArr.value = []
  }
}
const getparentCompanys = async () => {
  try {
    let { data } = await request({
      url: '/operate/operatorsManage/queryOperateCompanyByManage',
      method: 'get'
    })
    if (data.code == '200') {
      parentCompanys.value = data.data || []
    } else {
      parentCompanys.value = []
    }
  } catch (e) {
    parentCompanys.value = []
  }
}
const changeItem = (val: string) => {
  companyTypesArr.value.length &&
    companyTypesArr.value.forEach((item: any) => {
      if (item.companyCode == val) {
        formData.value.companyName = item.companyName
      }
    })
}
const changeParentItem = (val: string) => {
  parentCompanys.value.length &&
    parentCompanys.value.forEach((item: any) => {
      if (item.id == val) {
        formData.value.parentCompany = item.companyName
      }
    })
}

const onSumbitEdit = async () => {
  readonly.value = false
  isTypeHandleShow.value = !isTypeHandleShow.value
  if (!isTypeHandleShow.value) {
    isDisable.value = true
    oldObjformData.value = JSON.parse(JSON.stringify({ ...formData.value }))
  }
  getApiCompanyList()
  getparentCompanys()
}
const uploadKey = ref<any>('')
const cancelBtn = () => {
  formData.value = JSON.parse(JSON.stringify(oldObjformData.value))
  readonly.value = true
  isTypeHandleShow.value = true

  for (let i in rules) {
    rules[i][0].required = false
  }
  setTimeout(() => {
    for (let i in rules) {
      rules[i][0].required = true
    }
  }, 0)
  files.value = oldObjformData.value.cardPhoto.split(',')
  uploadKey.value = Date.now()
}
</script>

<style lang="scss" scoped>
.basicInfo {
  margin-bottom: 24px;
  .title {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    margin-bottom: 24px;
  }
}
.border-bottom {
  border-bottom: solid 1px rgba(0, 0, 0, 0.1);
}
.cls-detail {
  word-wrap: break-word;
  word-break: break-all;
}
.cls-detail :deep(.el-form-item__label) {
  color: rgba(0, 0, 0, 0.45) !important;
  line-height: 22px !important;
  height: 22px !important;
}
.cls-detail :deep(.el-form-item__content) {
  line-height: 22px !important;
}
</style>
<style lang="scss">
.elForm__ {
  .el-form-item--default.cardPhotoClass {
    margin-bottom: 20px !important;
  }
}

.rowRig__ {
  padding-left: 10px !important;
  box-sizing: border-box;
}

.waringInfoBas__ {
  padding-left: 100px;
  display: flex;
  align-items: center;
  color: #ff9900;
  font-size: 12px;
  margin-bottom: 24px;
  .el-icon {
    margin: 0 4px 0 8px;
    font-size: 16px;
  }
}
</style>
