<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import useContactsData from '../hooks/useContactsData'
import { opsCompany as opsCompanyAPI } from '@/api/index.ts'
import type { FormInstance, FormRules } from 'element-plus'
import request from '@/utils/request'

const props = defineProps({
  companyCode: {
    type: String,
    default: () => ''
  }
})

const tableLoading = ref(false)
const {
  contactsColumns,
  contactsTotal,
  contactsData,
  contactsSizeChange,
  contactsCurrentChange,
  getContactsData
} = useContactsData(props.companyCode)
contactsColumns.push({
  prop: 'operate',
  slotName: 'operate',
  label: '操作',
  minWidth: '100',
  fixed: 'right'
})

const dialogFormVisible = ref(false)
let formData = ref<Record<string, any>>({
  name: '',
  phone: '',
  remark: ''
})
const formStatus = ref<string>('')
const formRef = ref<FormInstance>()
const formRules = reactive<FormRules<Record<string, any>>>({
  name: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系人电话', trigger: 'blur' }]
})
const dialogTitle = computed(() => {
  if (formStatus.value === 'add') return '新增联系人'
  if (formStatus.value === 'edit') return '更新联系人'
  return ''
})
const dialogOpen = (flag: 'add' | 'edit', row: Record<string, any> = {}) => {
  formStatus.value = flag
  dialogFormVisible.value = true
  formData.value = { ...row }
}
const dialogClose = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  formStatus.value = ''
  dialogFormVisible.value = false
}
const onSaveForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      delete formData.value.createTime
      delete formData.value.updateTime
      let API: string = ''
      if (formStatus.value === 'add') {
        API = 'addOperatorsContactUser'
      }
      if (formStatus.value === 'edit') {
        API = 'updateOperatorsContactUser'
      }
      try {
        let { data } = await (opsCompanyAPI as any)[API]({
          companyCode: props.companyCode,
          ...formData.value
        })
        if (data.code === '200') {
          ElMessage({
            message: data.data,
            type: 'success'
          })
          dialogClose()
          getContactsData()
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}
const handleDelete = async (row: Record<string, any>) => {
  try {
    const { data } = await request({
      url: '/operate/operatorsManage/deleteOperatorsContactUser',
      method: 'post',
      data: {
        id: row.id
      },
      loading: true
    })
    if (data.code === '200') {
      ElMessage({
        message: data.data,
        type: 'success'
      })
      getContactsData()
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
</script>
<template>
  <div mb-12px text-right>
    <el-button
      v-if="contactsData.length < 10"
      type="primary"
      @click="dialogOpen('add')"
      >新增</el-button
    >
  </div>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="10"
    :columns="contactsColumns"
    :total="contactsTotal"
    :data="contactsData"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    @handle-size-change="contactsSizeChange"
    @handle-current-change="contactsCurrentChange"
  >
    <template #operate="{ row }">
      <div class="table-operate">
        <el-button link @click="dialogOpen('edit', row)">修改</el-button>
        <el-popconfirm title="确认删除？" @confirm="handleDelete(row)">
          <template #reference>
            <el-button link type="danger">删除</el-button>
          </template>
        </el-popconfirm>
      </div>
    </template>
  </vis-table-pagination>
  <el-dialog
    v-model="dialogFormVisible"
    :title="dialogTitle"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="500px"
    class="vis-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="110px"
      :rules="formRules"
    >
      <el-form-item label="联系人姓名" prop="name">
        <el-input v-model="formData.name" :maxlength="50" autocomplete="off" />
      </el-form-item>
      <el-form-item label="联系人电话" prop="phone">
        <el-input v-model="formData.phone" autocomplete="off" :maxlength="50" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          :rows="5"
          :maxlength="200"
          type="textarea"
          autocomplete="off"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="onSaveForm">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
