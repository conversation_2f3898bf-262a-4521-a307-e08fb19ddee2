import type { PageObject } from '../types'
import { opsCompany as opsCompanyAPI } from '@/api/index.ts'

export default function (companyCode: string) {
  const contactsColumns: Record<string, any>[] = [
    {
      prop: 'name',
      label: '联系人姓名',
      minWidth: '100'
    },
    {
      prop: 'phone',
      label: '联系电话'
    },
    {
      prop: 'remark',
      label: '备注'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      minWidth: '170'
    },
    {
      prop: 'updateTime',
      label: '更新时间',
      minWidth: '170'
    }
  ]
  // 联系人信息
  const contactsTotal = ref<number>(0)
  const contactsData = ref<Record<string, any>[]>([])
  onMounted(async () => {
    getContactsData()
  })
  const getContactsData = async ({ current = 1, size = 10 } = {}) => {
    try {
      let { data } = await opsCompanyAPI.getOperatorsContactUser({
        companyCode,
        current,
        size
      })
      if (data.code === '200') {
        contactsData.value = [...(data.data?.records || [])]
      } else {
        ElMessage({
          message: data.message,
          type: 'error'
        })
      }
    } catch (e) {
      contactsData.value = []
    }
  }
  const contactsSizeChange = (params: PageObject) => {
    getContactsData({
      current: params.currentPage,
      size: params.pageSize
    })
  }
  const contactsCurrentChange = (params: PageObject) => {
    getContactsData({
      current: params.currentPage,
      size: params.pageSize
    })
  }
  return {
    contactsColumns,
    contactsTotal,
    contactsData,
    contactsSizeChange,
    contactsCurrentChange,
    getContactsData
  }
}
