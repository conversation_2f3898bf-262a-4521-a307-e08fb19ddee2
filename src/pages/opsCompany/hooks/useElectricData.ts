import request from '@/utils/request'

export default function (loading?: any) {
  // 电站列表
  const electricColumns: Record<string, any>[] = [
    {
      prop: 'tyPower',
      label: '电站类型',
      formatter: (row: any) => {
        if (row.tyPower === 0) {
          return '户用'
        }
        if (row.tyPower >= 1) {
          return '工商业'
        }
        return row.tyPower || '--'
      }
    },
    {
      prop: 'stationUniqueId',
      label: '电站编号',
      minWidth: '120'
    },
    {
      prop: 'stationName',
      label: '电站名称',
      minWidth: '120'
    },
    {
      prop: 'areaName',
      label: '行政区划',
      minWidth: '160'
    },
    {
      prop: 'capins',
      label: '装机容量(kW)',
      minWidth: '120'
    },
    {
      prop: 'projectName',
      label: '项目名称'
    },
    {
      prop: 'projectCompanyName',
      label: '资产所属公司',
      minWidth: '120'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      minWidth: '120'
    }
  ]
  const electricTotal = ref<number>(0)
  const electricData = ref<Record<string, any>[]>([])
  const electricSearchData = reactive({
    projectName: '',
    projectCompanyName: '',
    stationCode: '',
    districtAddress: '',
    stationName: '',
    tyPower: '',
    stationType: ''
  })
  const electricPage = reactive({
    pageNum: 1,
    pageSize: 10
  })
  onMounted(async () => {
    getElectricData()
  })
  const getElectricData = async () => {
    try {
      const [provinceCode, cityCode, areaCode] =
        electricSearchData.districtAddress || []
      const { data } = await request({
        url: '/operate/operation-project/getProjectStations',
        method: 'post',
        data: {
          ...electricSearchData,
          ...electricPage,
          provinceCode: provinceCode || '',
          cityCode: cityCode || '',
          areaCode: areaCode || ''
        },
        loading: loading
      })
      electricTotal.value = data?.data?.total || 0
      electricData.value = [...(data?.data?.records || [])]
      electricData.value = electricData.value.map((e: any) => ({
        ...e,
        stationCode: e.stationUniqueId || e.stationCode
      }))
    } catch (e) {
      electricTotal.value = 0
      electricData.value = []
    }
  }
  const electricSizeChange = (params: Record<string, any>) => {
    electricPage.pageNum = 1
    electricPage.pageSize = params.pageSize
    getElectricData()
  }
  const electricCurrentChange = (params: Record<string, any>) => {
    electricPage.pageNum = params.currentPage
    electricPage.pageSize = params.pageSize
    getElectricData()
  }
  return {
    electricColumns,
    electricTotal,
    electricData,
    electricPage,
    electricSearchData,
    getElectricData,
    electricSizeChange,
    electricCurrentChange
  }
}
