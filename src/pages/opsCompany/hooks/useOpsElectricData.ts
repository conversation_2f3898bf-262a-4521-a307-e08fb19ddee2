// import { opsCompany as opsCompanyAPI } from '@/api/index.ts'
import request from '@/utils/request'

export default function (companyCode: string, depth: number, searchData: any) {
  const opsElectricColumns: Record<string, any>[] = [
    {
      prop: 'stationName',
      label: '电站名称'
    },
    {
      prop: 'stationUniqueId',
      label: '电站编号'
    },
    {
      prop: 'location',
      label: '行政区域'
    },
    {
      prop: 'capins',
      label: '装机容量(kW)'
    },
    {
      prop: 'createTime',
      label: '创建时间'
    }
  ]
  const opsElectricTotal = ref<number>(0)
  const opsElectricData = ref<Record<string, any>[]>([])
  const opsElectricPage = reactive({
    pageNum: 1,
    pageSize: 10
  })
  onMounted(async () => {
    getOpsElectricData()
  })
  const getOpsElectricData = async () => {
    try {
      await getExistNode()
      const { data } = await request({
        url: '/operate/operation-project/getOperationCompanyTree/stationsV2',
        method: 'get',
        loading: true,
        params: {
          ...opsElectricPage,
          ...searchData.value,
          code: companyCode,
          depth
        }
      })
      opsElectricData.value = data?.data || []
      opsElectricTotal.value = data?.total || 0
    } catch (e) {
      opsElectricData.value = []
      opsElectricTotal.value = 0
    }
    // try {
    //   const { data } = await opsCompanyAPI.getMaintainStationtVOList({
    //     companyCode,
    //     ...searchData.value,
    //     ...opsElectricPage
    //   })
    //   opsElectricTotal.value = data?.data?.total || 0
    //   opsElectricData.value = [...(data?.data?.records || [])]
    // } catch (e) {
    //   opsElectricTotal.value = 0
    //   opsElectricData.value = []
    // }
  }
  const opsElectricSizeChange = (params: Record<string, any>) => {
    opsElectricPage.pageNum = 1
    opsElectricPage.pageSize = params.pageSize
    getOpsElectricData()
  }
  const opsElectricCurrentChange = (params: Record<string, any>) => {
    opsElectricPage.pageNum = params.currentPage
    opsElectricPage.pageSize = params.pageSize
    getOpsElectricData()
  }
  const getExistNode = async (showMessage: boolean = false) => {
    const { data } = await request({
      url: '/operate/safeZoneController/getExistNode',
      method: 'post',
      data: {
        code: companyCode,
        type: 3
      },
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      loading: false
    })
    if (!data.data) {
      showMessage &&
        ElMessage({
          message: '当前选择节点权限不足，请在左上方重新选择下级节点。！',
          type: 'warning'
        })
      return Promise.reject()
    }
    return true
  }
  return {
    opsElectricColumns,
    opsElectricTotal,
    opsElectricData,
    getOpsElectricData,
    opsElectricPage,
    opsElectricSizeChange,
    opsElectricCurrentChange
  }
}
