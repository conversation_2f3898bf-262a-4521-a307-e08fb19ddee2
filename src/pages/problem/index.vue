<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import ProblemForm from './components/problemForm.vue'
import { post, get, baseApi } from '@/api/index.ts'
import type { AppType, FormType } from './types'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const route = useRoute()

const appType = ref<AppType>((route.params.type as AppType) || 'accident')
const appTypeName = computed(() =>
  appType.value === 'trouble' ? '隐患' : '事故'
)
const appTypeProp = computed(() =>
  appType.value === 'trouble' ? 'hiddenTrouble' : 'trouble'
)
const appTypeApi = computed(() =>
  appType.value === 'trouble' ? 'hidden-trouble' : 'trouble'
)

const searchProps = ref([
  {
    label: `${appTypeName.value}名称`,
    prop: `${appTypeProp.value}Name`
  },
  {
    label: '所属电站',
    prop: 'stationCode',
    type: 'stationSelect'
  },
  {
    type: 'select',
    label: '状态',
    prop: 'state',
    options: [
      {
        label: '待处理',
        value: '待处理'
      },
      {
        label: '已处理',
        value: '已处理'
      }
    ]
  }
])
let searchData = ref({
  [`${appTypeProp.value}Name`]: '',
  stationCode: '',
  state: '',
  pageSize: 10,
  pageNum: 1
})

const tableColumns = [
  {
    prop: `${appTypeProp.value}Name`,
    label: `${appTypeName.value}名称`
  },
  {
    prop: `${appTypeProp.value}TypeName`,
    label: `${appTypeName.value}类型`,
    slotName: 'typeName'
  },
  {
    prop: 'stationName',
    label: '所属电站'
  },
  {
    prop: `${appTypeProp.value}Desc`,
    label: `${appTypeName.value}描述`
  },
  {
    prop: 'foundTime',
    label: '发现时间'
  },
  {
    prop: 'stateName',
    label: '状态',
    slotName: 'stateName'
  },
  {
    prop: 'handleDesc',
    label: '处理情况'
  },
  {
    prop: 'attachment',
    label: '附件',
    slotName: 'attachment',
    minWidth: 200,
    formDefault: []
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 100,
    fixed: 'right'
  }
]
const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})

const handleSearch = async (val: any) => {
  searchData.value = { ...val }
  searchData.value.pageNum = 1
  searchData.value.pageSize = 10
  getTableData()
}

let tableLoading = ref(false)
const getTableData = async () => {
  let { data } = await post({
    url: `/operate/${appTypeApi.value}-manage/getList`,
    data: {
      ...searchData.value,
      code: localStorage.getItem('PVOM_COMPANY_CODE')
    },
    loading: [tableLoading]
  })
  tableData.data = data.records || []
  tableData.total = data?.total || 0
  tableData.data = tableData.data.map((e: any) => {
    return {
      ...e,
      attachment: e.attachment ? e.attachment.split(',') : []
    }
  })
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  companyCode.data && (await getTableData())
  startWatch = true
})

const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const rowData = ref({})
const handleAdd = () => {
  rowData.value = {}
  formType.value = 'registe'
  dialogVisible.value = true
}
const handleUpdate = (row: Record<string, any>) => {
  rowData.value = { ...row }
  formType.value = 'handle'
  dialogVisible.value = true
}
const handleDelete = async (row: Record<string, any>) => {
  const { code } = await get({
    url: `/operate/${appTypeApi.value}-manage/delete`,
    data: { id: row.id },
    loading: true
  })
  if (code == 200) {
    ElMessage({
      type: 'success',
      message: `删除${appTypeName.value}成功`
    })
    getTableData()
  }
}

const dialogVisible = ref<boolean>(false)

const formType = ref<FormType>('registe')
const formTypeName = computed(() =>
  formType.value === 'handle' ? '处理' : '登记'
)
const closeDialog = () => {
  dialogVisible.value = false
}
const problemFormRef = ref()
const onSubmit = async () => {
  if (await problemFormRef.value?.onSubmit()) {
    closeDialog()
    getTableData()
  }
}

const handleDownload = async (fileName: string) => {
  const {
    response: { data }
  } = await baseApi.downloadFile(fileName)
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(data)
  link.download = fileName.split('@').at(-1) as string
  link.click()
  data &&
    ElMessage({
      message: `下载成功!`,
      type: 'success'
    })
}

const tooltipDisabled = ref(true)
const handleMouseEnter = (event: Event) => {
  const element: any = event.currentTarget
  if (element.scrollWidth > element.clientWidth) {
    tooltipDisabled.value = false
  } else {
    tooltipDisabled.value = true
  }
}
const handleMouseLeave = () => {
  tooltipDisabled.value = false
}
</script>

<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="80px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>{{ `${appTypeName}列表` }}</p>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          {{ `登记${appTypeName}` }}
        </el-button>
      </div>
      <vis-table-pagination
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="tableColumns"
        :total="tableData.total"
        :data="tableData.data"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #typeName="{ row }">
          <el-tag
            v-if="row[`${appTypeProp}TypeName`] === `一般${appTypeName}`"
            type="info"
            >{{ row[`${appTypeProp}TypeName`] }}</el-tag
          >
          <el-tag
            v-else-if="row[`${appTypeProp}TypeName`] === `严重${appTypeName}`"
            type="warning"
            >{{ row[`${appTypeProp}TypeName`] }}</el-tag
          >
          <el-tag
            v-else-if="row[`${appTypeProp}TypeName`] === `重大${appTypeName}`"
            type="danger"
            >{{ row[`${appTypeProp}TypeName`] }}</el-tag
          >
          <template v-else>--</template>
        </template>
        <template #stateName="{ row }">
          <el-tag v-if="row.stateName === '待处理'" type="danger">{{
            row.stateName
          }}</el-tag>
          <el-tag v-else-if="row.stateName === '已处理'" type="info">{{
            row.stateName
          }}</el-tag>
          <template v-else>--</template>
        </template>
        <template #attachment="{ row }">
          <template v-if="row.attachment.length">
            <el-tooltip placement="top" :disabled="tooltipDisabled">
              <template #content>
                <p
                  v-for="item in row.attachment"
                  :key="item"
                  class="download-text"
                  @click="handleDownload(item)"
                >
                  {{ item.split('@').at(-1) }}
                </p>
              </template>
              <div
                class="file-list"
                @mouseenter="handleMouseEnter($event)"
                @mouseleave="handleMouseLeave()"
              >
                <el-tag
                  v-for="item in row.attachment"
                  :key="item"
                  effect="plain"
                  @click="handleDownload(item)"
                >
                  {{ item.split('@').at(-1) }}
                </el-tag>
              </div>
            </el-tooltip>
          </template>
          <template v-else>--</template>
        </template>
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button
              v-if="row.stateName === '待处理'"
              link
              @click="handleUpdate(row)"
              >处理</el-button
            >
            <el-popconfirm title="确认删除？" @confirm="handleDelete(row)">
              <template #reference>
                <el-button link type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible"
    :title="`${formTypeName}${appTypeName}`"
    align-center
    :before-close="closeDialog"
    :close-on-click-modal="false"
    width="620px"
    class="vis-dialog record-dialog"
  >
    <el-scrollbar max-height="calc(100vh - 180px)">
      <ProblemForm
        v-if="dialogVisible"
        ref="problemFormRef"
        :data="rowData"
        :app-type="appType"
        :app-type-name="appTypeName"
        :app-type-prop="appTypeProp"
        :app-type-api="appTypeApi"
        :form-type="formType"
        :form-type-name="formTypeName"
        :columns="tableColumns"
      />
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
:deep(.el-upload-list__item) {
  margin-bottom: 0;
}
.file-list {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  .el-tag,
  p {
    cursor: pointer;
    &:hover {
      background: #eee;
      color: #688161;
    }
  }
  .el-tag + .el-tag {
    margin-left: 4px;
  }
}
p.download-text {
  cursor: pointer;
  color: #ccc;
  line-height: 22px;
  &:hover {
    color: #fff;
  }
}
</style>
