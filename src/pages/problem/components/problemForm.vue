<script setup lang="ts">
import type { FormRules } from 'element-plus'
import type { AppType, FormType } from '../types'
import StationSelect from '@/components/spic-station'
import { getDateTime } from '@/utils'
import SpicUpload from '@/components/spic-upload'
import { post } from '@/api/index.ts'

const props = defineProps<{
  data: Record<string, any>
  appType: AppType
  appTypeName: string
  appTypeProp: string
  appTypeApi: string
  formType: FormType
  formTypeName: string
  columns: Record<string, any>[]
}>()

const formData = ref<Record<string, any>>(
  (function () {
    return Object.fromEntries(
      props.columns
        .map(({ prop, formDefault }) => {
          return [prop, formDefault || '']
        })
        .concat([['stationCode', '']])
    )
  })()
)

const now = new Date()
onMounted(() => {
  formData.value = {
    ...formData.value,
    ...props.data
  }
  formData.value.foundTime = getDateTime(now)
})
const formRef = ref()

const formRules = reactive<FormRules<Record<string, any>>>({
  [`${props.appTypeProp}Name`]: [
    {
      required: true,
      message: `请输入${props.appTypeName}名称`,
      trigger: 'blur'
    }
  ],
  [`${props.appTypeProp}TypeName`]: [
    {
      required: true,
      message: `请选择${props.appTypeName}类型`,
      trigger: 'change'
    }
  ],
  stationCode: [
    { required: true, message: '请选择所属电站', trigger: 'change' }
  ],
  foundTime: [{ required: true, message: '请选择发现时间', trigger: 'change' }],
  [`${props.appTypeProp}Desc`]: [
    {
      required: true,
      message: `请选输入${props.appTypeName}描述`,
      trigger: 'change',
      validator: (_rule: any, value: string, callback: any) => {
        value.trim() === '' ? callback(new Error()) : callback()
      }
    }
  ],
  stateName: [
    {
      required: true,
      message: `请选择${props.appTypeName}状态`,
      trigger: 'change'
    }
  ],
  handleDesc: [
    {
      required: true,
      message: '请输入处理情况',
      trigger: 'change',
      validator: (_rule: any, value: string, callback: any) => {
        value.trim() === '' ? callback(new Error()) : callback()
      }
    }
  ]
})

const hoursArray = [...new Array(24).keys()]
const minutesArray = [...new Array(60).keys()]

const disabledTime = (type: string) => {
  const hours = now.getHours()
  const minutes = now.getMinutes()
  const seconds = now.getSeconds()
  const todayMinTime = new Date(new Date().toLocaleDateString()).getTime()

  if (new Date(formData.value.foundTime).getTime() < todayMinTime) {
    return []
  }

  const selectedTime = formData.value.foundTime.split(' ')[1]
  const timesArray = selectedTime && selectedTime.split(':')

  if (type === 'hours') {
    return hoursArray.filter((e: number) => e > hours)
  }
  if (type === 'minutes') {
    return hours <= timesArray[0]
      ? minutesArray.filter((e: number) => e > minutes)
      : []
  }
  if (type === 'seconds') {
    return hours <= timesArray[0]
      ? minutes <= timesArray[1]
        ? minutesArray.filter((e: number) => e > seconds)
        : []
      : []
  }
  return []
}
const timeEditable = async () => {
  await nextTick()
  let element = document.querySelectorAll('.el-date-picker__editor-wrap input')
  for (var i = 0; i <= element.length - 1; i++) {
    element[i].setAttribute('readonly', 'readonly')
  }
}
const onSubmit = async () => {
  if (!formRef.value) return
  const requestData: any = {
    ...formData.value,
    attachment: formData.value.attachment.join(',')
  }
  delete requestData.updateTime
  delete requestData.createTime
  if (await formRef.value.validate(() => {})) {
    const { code } = await post({
      url: `/operate/${props.appTypeApi}-manage/${
        props.formType === 'handle' ? 'update' : 'add'
      }`,
      data: requestData,
      loading: true
    })
    if (code == 200) {
      ElMessage({
        type: 'success',
        message: `${props.formTypeName}${props.appTypeName}成功`
      })
      return true
    }
  }
  return false
}
watch(
  () => formData.value.foundTime,
  () => {
    if (new Date(formData.value.foundTime).getTime() > now.getTime()) {
      formData.value.foundTime = getDateTime(now)
    }
  }
)
defineExpose({
  onSubmit
})
</script>
<template>
  <el-form
    ref="formRef"
    :rules="formRules"
    :model="formData"
    label-suffix=""
    label-position="right"
    label-width="120px"
    style="padding: 0 15px 0 0"
  >
    <el-form-item
      v-if="formType === 'registe'"
      :label="`${appTypeName}名称`"
      :prop="`${appTypeProp}Name`"
    >
      <el-input
        v-model.trim="formData[`${appTypeProp}Name`]"
        :placeholder="`请输入${appTypeName}名称`"
        autocomplete="off"
        maxlength="64"
      ></el-input>
    </el-form-item>
    <el-form-item
      v-if="formType === 'registe'"
      :prop="`${appTypeProp}TypeName`"
    >
      <template #label>
        <span>{{ `${appTypeName}类型` }}</span>
        <span v-if="appType === 'accident'" class="question-filled">
          <el-tooltip placement="top">
            <template #content>
              <span style="color: #bbb">
                一般事故包括：一般误操作事故、一般火灾事故<br />
                严重事故包括：集体中毒事故<br />
                重大事故包括：人身重伤以上事故、严重危害电网安全事故
              </span>
            </template>
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </span>
        <span>：</span>
      </template>
      <el-select
        v-model="formData[`${appTypeProp}TypeName`]"
        :placeholder="`请选择${appTypeName}类型`"
        clearable
      >
        <el-option label="一般" :value="`一般${appTypeName}`" />
        <el-option
          v-if="appType === 'accident'"
          label="严重"
          :value="`严重${appTypeName}`"
        />
        <el-option label="重大" :value="`重大${appTypeName}`" />
      </el-select>
    </el-form-item>
    <el-form-item
      v-if="formType === 'registe'"
      :label="`所属电站`"
      prop="stationCode"
    >
      <StationSelect
        v-model="formData.stationCode"
        @change="(data: any) => (formData.stationName = data.stationName)"
      />
    </el-form-item>
    <el-form-item
      v-if="formType === 'registe'"
      label="发现时间"
      prop="foundTime"
    >
      <el-date-picker
        v-model="formData.foundTime"
        type="datetime"
        placeholder="请选择发现时间"
        :editable="false"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :disabled-date="(date: Date) => now.getTime() < date.getTime()"
        :disabled-hours="() => disabledTime('hours')"
        :disabled-minutes="() => disabledTime('minutes')"
        :disabled-seconds="() => disabledTime('seconds')"
        popper-class="hide-time8794"
        @focus="timeEditable"
      >
      </el-date-picker>
    </el-form-item>
    <el-form-item
      v-if="formType === 'registe'"
      :label="`${appTypeName}描述`"
      :prop="`${appTypeProp}Desc`"
    >
      <el-input
        v-model="formData[`${appTypeProp}Desc`]"
        type="textarea"
        :placeholder="`请输入${appTypeName}描述`"
        autocomplete="off"
        :rows="3"
        :maxlength="1000"
      ></el-input>
    </el-form-item>
    <el-form-item
      v-if="formType === 'registe'"
      :label="`${appTypeName}状态`"
      prop="stateName"
    >
      <el-select
        v-model="formData.stateName"
        :placeholder="`请选择${appTypeName}状态`"
        clearable
        @change="(val: any) => val === '待处理' && (formData.handleDesc = '')"
      >
        <el-option label="待处理" value="待处理" />
        <el-option label="已处理" value="已处理" />
      </el-select>
    </el-form-item>
    <el-form-item
      v-if="formType === 'handle' || formData.stateName === '已处理'"
      :label="`处理情况`"
      prop="handleDesc"
    >
      <el-input
        v-model="formData.handleDesc"
        type="textarea"
        :placeholder="`请输入处理情况`"
        autocomplete="off"
        :rows="3"
        :maxlength="1000"
      ></el-input>
    </el-form-item>
    <el-form-item :label="`附件`" prop="attachment">
      <SpicUpload
        v-model="formData.attachment"
        :file-size="50"
        :file-ext="null"
        type="file"
      />
    </el-form-item>
  </el-form>
</template>
<style lang="scss" scoped>
.question-filled {
  padding: 2px 2px 0 2px;
  .el-icon {
    color: #29cca0;
    cursor: pointer;
  }
}
</style>
<style lang="scss">
.el-picker__popper.hide-time8794 {
  .el-picker-panel__link-btn.is-text {
    display: none;
  }
}
</style>
