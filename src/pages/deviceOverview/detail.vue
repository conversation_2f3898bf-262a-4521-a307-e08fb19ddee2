<script setup lang="ts">
import DeviceQx from './components/deviceQx.vue'
import DeviceJx from './components/deviceJx.vue'
import DeviceGz from './components/deviceGz.vue'
const router = useRouter()
const route = useRoute()
const path = route.query.path as string
const pathName = route.query.pathName as string
// const deviceId = route.params.deviceId

const activeTabName = ref<any>(route.query.tabName || 'tab1')

const detailData = ref<Record<string, any>>({})

onMounted(() => {
  const itemInfo = sessionStorage.getItem('itemInfo')
  if (itemInfo) {
    detailData.value = JSON.parse(itemInfo) || {}
  }
})

const tabChange = (tabName: string) => {
  router.replace({
    path: route.path,
    query: {
      ...route.query,
      tabName
    }
  })
}
</script>

<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-title">
        <el-icon class="backIcon"><ArrowLeft /></el-icon>
        <span class="goback" @click="() => router.go(-1)">返回上一级</span>
        <span class="detailLine">|</span>
        <router-link :to="pathName ? path : `/device`">
          {{ pathName ? pathName : '设备台账' }}
        </router-link>
        <span>></span>
        <span>设备台账详情</span>
      </div>
    </div>
    <div class="info-base">
      <el-descriptions>
        <el-descriptions-item label="设备编码：">{{
          detailData.deviceUniqueId || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="设备名称：">{{
          detailData.deviceName || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="SN码：">{{
          detailData.deviceSn || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="设备类型：">{{
          detailData.deviceTypesName || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="设备品牌：">{{
          detailData.deviceBrandName || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="设备型号：">{{
          detailData.deviceModeName || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="所属电站：">{{
          detailData.stationName || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="所属项目：">{{
          detailData.projectName || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="所属运维商：">{{
          detailData.operatorName || '--'
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div v-auto-height class="info-tab">
      <el-tabs v-model="activeTabName" @tab-change="tabChange">
        <el-tab-pane label="设备缺陷记录" name="tab1">
          <DeviceQx v-if="activeTabName === 'tab1'"></DeviceQx>
        </el-tab-pane>
        <el-tab-pane label="设备检修记录" name="tab2">
          <DeviceJx v-if="activeTabName === 'tab2'"></DeviceJx>
        </el-tab-pane>
        <el-tab-pane label="设备故障记录" name="tab3">
          <DeviceGz v-if="activeTabName === 'tab3'" :detail-data="detailData">
          </DeviceGz>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scope lang="scss">
iframe {
  width: 100%;
  height: calc(100vh - 360px);
  border: none;
  z-index: 2;
}
</style>
