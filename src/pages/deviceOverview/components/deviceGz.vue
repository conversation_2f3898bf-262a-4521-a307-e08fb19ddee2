<template>
  <div class="operate end">
    <el-button type="primary" @click="handleAdd(null)">
      <el-icon><Plus /></el-icon>
      新建故障
    </el-button>
  </div>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="searchData.pageSize"
    :current-page="searchData.pageNum"
    :columns="tableColumns"
    :total="tableData.total"
    :data="tableData.data"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
    <template #faultLevel="{ row }">
      <el-tag v-if="row.faultLevel === 1" type="success"> 一般 </el-tag>
      <el-tag v-if="row.faultLevel === 2" type="warning"> 重大 </el-tag>
      <el-tag v-if="row.faultLevel === 3" type="danger"> 紧急 </el-tag>
    </template>
    <template #faultState="{ row }">
      <el-tag
        v-if="row.faultState === 1"
        type="success"
        class="tag-info"
        color="#F0F5FA"
      >
        已处理
      </el-tag>
      <el-tag v-if="row.faultState === 2" type="info"> 未处理 </el-tag>
    </template>
    <template #operate="{ row }">
      <div class="table-operate">
        <el-button link @click="dialogEditFault(row)">编辑</el-button>
      </div>
    </template>
  </vis-table-pagination>
  <el-dialog
    v-model="dialogVisible"
    :title="(dialogStatus === 'edit' ? '编辑' : '登记') + '故障'"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="650px"
    class="vis-dialog"
  >
    <!-- <el-scrollbar max-height="calc(100vh - 180px)"> -->
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="110px"
      :rules="formRules"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="所属电站" prop="stationCode">
            <el-input
              v-model="stationName"
              placeholder="请选择所属电站"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属设备" prop="deviceUniqueId">
            <el-input
              v-model="formData.deviceName"
              placeholder="请选择所属电站"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="故障类型" prop="faultDatabaseId">
            <el-select
              v-model="formData.faultDatabaseId"
              placeholder="请选择故障类型"
              filterable
            >
              <el-option
                v-for="item in faultTypes"
                :key="item.id"
                :label="item.faultTypeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发现时间" prop="foundTime">
            <el-date-picker
              v-model="formData.foundTime"
              type="datetime"
              placeholder="请选择发现时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="故障等级" prop="faultLevel">
            <el-select
              v-model="formData.faultLevel"
              placeholder="请选择故障等级"
            >
              <el-option label="一般" :value="1"></el-option>
              <el-option label="重大" :value="2"></el-option>
              <el-option label="紧急" :value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="故障状态" prop="faultState">
            <el-select
              v-model="formData.faultState"
              placeholder="请选择故障状态"
            >
              <el-option label="已处理" :value="1"></el-option>
              <el-option label="未处理" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="关联工单" prop="workOrderVOList">
            <el-select
              v-model="formData.workOrdeValue"
              placeholder="请选择关联工单"
              multiple
              clearable
              filterable
              :collapse-tags="true"
              :max-collapse-tags="5"
              :collapse-tags-tooltip="true"
              popper-class="select4786"
              remote
              :remote-method="getOrders"
              remote-show-suffix
              @change="changeWorkOrder"
            >
              <template v-for="item in orders" :key="item.value">
                <el-option
                  :label="item.orderName"
                  :title="item.orderName"
                  :value="item.orderId"
                />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="故障描述" prop="faultDesc">
            <el-input
              v-model="formData.faultDesc"
              :rows="5"
              type="textarea"
              placeholder="请输入故障描述"
              maxlength="300"
              autocomplete="off"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- </el-scrollbar> -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import { fault as faultAPI } from '@/api/index.ts'
import type { FormInstance, FormRules } from 'element-plus'
import { loadingOpen, loadingClose } from '@/utils/common.ts'

let route = useRoute()
const WF_PATH = import.meta.env.VITE_APP_WF_PATH

const props = defineProps({
  detailData: {
    type: Object,
    required: false,
    default: () => {
      return {
        stationName: '',
        deviceName: ''
      }
    }
  }
})
const router = useRouter()
const tableColumns = [
  { prop: 'stationName', label: '电站名称' },
  { prop: 'deviceName', label: '设备名称' },
  { prop: 'faultTypeName', label: '故障名称' },
  { prop: 'foundTime', label: '发现时间', minWidth: '120px' },
  { prop: 'faultLevel', label: '故障等级', slotName: 'faultLevel' },
  { prop: 'faultState', label: '故障状态', slotName: 'faultState' },
  {
    prop: 'workOrderVOList',
    label: '关联工单',
    type: 'array',
    emit: 'click',
    href: (row: Record<string, any>) => {
      router.push(
        `/workorder/lookorder/${row.workId}?parentPageName=设备台账详情`
      )
    },
    minWidth: 200
  },
  { prop: 'createUserName', label: '创建人' },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 60,
    fixed: 'right'
  }
]
let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})

let searchData = ref({
  deviceUniqueId: route.params.deviceId,
  pageNum: 1,
  pageSize: 10
})

let tableLoading = ref(false)
const getTableData = async () => {
  try {
    const { data } = await faultAPI.getFaultList(searchData.value, [
      tableLoading
    ])
    tableData.total = data?.data?.total || 0
    tableData.data = data?.data?.records || []
  } catch (e: any) {
    tableData.total = 0
    tableData.data = []
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

onMounted(async () => {
  getTableData()
  getFaultTypes()
  getOrders()
})

// 新增编辑弹框
let dialogVisible = ref(false)
let dialogStatus = ref('add')
const stationName: any = ref(props.detailData.stationName)
const formRef = ref<FormInstance>()
const formRules = reactive<FormRules<Record<string, any>>>({
  stationCode: [
    { required: true, message: '请选择所属电站', trigger: 'change' }
  ],
  deviceUniqueId: [
    { required: true, message: '请选择所属设备', trigger: 'change' }
  ],
  faultDatabaseId: [
    { required: true, message: '请选择故障类型', trigger: 'change' }
  ],
  foundTime: [{ required: true, message: '请选择发现时间', trigger: 'change' }],
  faultState: [
    { required: true, message: '请选择故障状态', trigger: 'change' }
  ],
  faultLevel: [
    { required: true, message: '请选择故障等级', trigger: 'change' }
  ],
  faultDesc: [{ required: true, message: '请输入故障描述', trigger: 'blur' }]
})

let formData = ref<Record<string, any>>({})
const stationId = route.query.stationId
const dialogEditFault = async (node: any = null) => {
  loadingOpen()
  await getDevices(node.stationCode)
  loadingClose()
  handleAdd(node)
}
const handleAdd = async (node: any = null) => {
  dialogStatus.value = node ? 'edit' : 'add'
  formRef?.value?.resetFields()
  formData.value = {
    stationCode: stationId,
    deviceUniqueId: route.params.deviceId,
    deviceName: props.detailData.deviceName,
    id: node?.id || null,
    faultDatabaseId: node?.faultDatabaseId || '',
    foundTime: node?.foundTime || '',
    faultLevel: node?.faultLevel || 1,
    faultState: node?.faultState || 1,
    faultDesc: node?.faultDesc || '',
    workOrderVOList: [],
    workOrdeValue: []
  }
  if (node?.workOrderVOList) {
    node.workOrderVOList.forEach((item: any) => {
      formData.value.workOrdeValue.push(Number(item.workId))
      formData.value.workOrderVOList.push({
        workId: Number(item.workId),
        workNo: item.workOrderNo,
        objectApiName: item.objectApiName
      })
    })
  } else {
    formData.value.workOrdeValue = []
  }
  dialogVisible.value = true
}

const devices = ref<Record<string, any>[]>([])
const getDevices = async (stationCode: string = '') => {
  try {
    const { data } = await faultAPI.getBelongDeviceList({ stationCode }, false)
    devices.value = data.data
  } catch (e: any) {
    devices.value = []
  }
}

const faultTypes = ref<Record<string, any>[]>([])
const getFaultTypes = async () => {
  try {
    const { data } = await faultAPI.getFaultTypeList({}, false)
    faultTypes.value = data.data
  } catch (e: any) {
    faultTypes.value = []
  }
}
const orders = ref<Record<string, any>[]>([])
const getOrders = async (orderName: string = '') => {
  try {
    const { data } = await faultAPI.getRelatedOrder(
      {
        // powerStation: 'GFHY01088305', // test 此电站code有关联工单列表
        powerStation: stationId,
        orderName
      },
      false
    )
    orders.value = data.data
  } catch (e: any) {
    orders.value = []
  }
}
const changeWorkOrder = (val: any) => {
  formData.value.workOrderVOList = []
  orders.value.forEach((item: any) => {
    if (val.includes(item.orderId)) {
      formData.value.workOrderVOList.push({
        workId: item.orderId,
        workNo: item.orderNo,
        objectApiName: item.orderType
      })
    }
  })
}

const handleSave = () => {
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      try {
        let params = {
          deviceName: formData.value.deviceName,
          deviceUniqueId: formData.value.deviceUniqueId,
          faultDatabaseId: formData.value.faultDatabaseId,
          faultDesc: formData.value.faultDesc,
          faultLevel: formData.value.faultLevel,
          faultState: formData.value.faultState,
          foundTime: formData.value.foundTime,
          id: formData.value.id,
          stationCode: formData.value.stationCode,
          workOrderVOList: formData.value.workOrderVOList
        }
        const { data } = await faultAPI.saveDeviceFault(params, true)
        if (data.code === '200') {
          ElMessage({
            message: `${
              dialogStatus.value === 'edit' ? '修改' : '添加'
            }故障成功!`,
            type: 'success'
          })
          dialogClose()
          getTableData()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}
const dialogClose = () => {
  formRef.value.resetFields()
  dialogVisible.value = false
}
</script>
<style scoped lang="scss">
.operate {
  margin-bottom: 12px;
  text-align: right;
}
.tag-info {
  :deep(.el-tag__content) {
    color: #7a8a99;
  }
}
</style>
<style lang="scss">
.select4786 {
  width: 430px;
}
</style>
