<template>
  <!-- <div class="operate end">
    <el-button type="primary" @click="handleAdd">登记缺陷</el-button>
  </div> -->
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="searchData.pageSize"
    :current-page="searchData.pageNum"
    :columns="tableColumns"
    :total="tableData.total"
    :data="tableData.data"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
  <el-dialog
    v-model="dialogVisible"
    title="登记缺陷"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="600px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 180px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-suffix=""
        label-width="110px"
        :rules="formRules"
      >
        <el-form-item label="缺陷名称" prop="name">
          <el-input
            v-model="formData.name"
            :maxlength="100"
            autocomplete="off"
            placeholder="请输入缺陷名称"
          />
        </el-form-item>
        <el-form-item label="发现时间" prop="foundTime">
          <el-date-picker
            v-model="formData.foundTime"
            type="datetime"
            placeholder="请选择发现时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="缺陷等级" prop="defectLevel">
          <el-select
            v-model="formData.defectLevel"
            placeholder="请选择缺陷等级"
          >
            <el-option label="一般" :value="1"></el-option>
            <el-option label="重大" :value="2"></el-option>
            <el-option label="紧急" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="缺陷状态" prop="defectState">
          <el-select
            v-model="formData.defectState"
            placeholder="请选择缺陷状态"
          >
            <el-option label="已处理" :value="1"></el-option>
            <el-option label="未处理" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="缺陷描述" prop="defectDesc">
          <el-input
            v-model="formData.defectDesc"
            :rows="5"
            :maxlength="200"
            type="textarea"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="关联工单" prop="workOrderVOList">
          <el-select
            v-model="formData.workOrderVOList"
            placeholder="请选择关联工单"
            multiple
            clearable
            filterable
            remote
            :remote-method="getOrders"
            :collapse-tags="true"
            :max-collapse-tags="5"
            :collapse-tags-tooltip="true"
            popper-class="select4786"
          >
            <template v-for="item in orders" :key="item.value">
              <el-option
                :label="item.orderName"
                :title="item.orderName"
                :value="`${item.orderId},${item.orderNo},${item.orderType}`"
              />
            </template>
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import request from '@/utils/request'
import { type FormInstance, type FormRules } from 'element-plus'

let route = useRoute()
const router = useRouter()

const tableColumns = ref([
  {
    prop: 'defectClassName',
    label: '缺陷大类'
  },
  {
    prop: 'defectSubclassName',
    label: '缺陷小类'
  },
  {
    prop: 'defectType',
    label: '缺陷类型',
    formatter: (e: any) => {
      return {
        1: '部件损坏',
        2: '通讯中断',
        3: '数据错误',
        4: '其他',
        5: '原因不明'
      }[e.defectType as string]
    }
  },
  {
    prop: 'defectLevel',
    label: '缺陷级别'
  },
  {
    prop: 'defectState',
    label: '缺陷状态',
    formatter: (e: any) => {
      return {
        1: '待处理',
        2: '处理中',
        3: '已处理',
        4: '已取消'
      }[e.defectState as string]
    }
  },
  {
    prop: 'defectFinder',
    label: '发现人',
    formatter: (e: any) => {
      return e?.defectFinder?.split('_')[0]
    }
  },
  {
    prop: 'createTime',
    label: '发现时间',
    minWidth: '120px'
  },
  {
    prop: 'workTitle',
    label: '关联工单',
    type: 'click',
    click: (row: Record<string, any>) => {
      router.push(
        `/workorder/lookorder/${row.workOrderId}?parentPageName=设备台账详情`
      )
    },
    minWidth: 200
  }
])
let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})

let searchData = ref({
  deviceUniqueId: route.params.deviceId,
  pageNum: 1,
  pageSize: 10
})

let tableLoading = ref(false)
const getTableData = async () => {
  try {
    const { data } = await request({
      url: '/operate/deviceLedger/getDeviceDefectRecordVOList',
      method: 'post',
      data: searchData.value,
      loading: [tableLoading]
    })
    tableData.total = data?.data?.total || 0
    tableData.data = data?.data?.records || []
  } catch (e: any) {
    tableData.total = 0
    tableData.data = []
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

onMounted(async () => {
  getTableData()
})

let dialogVisible = ref(false)
const formRef = ref<FormInstance>()
let formData = ref<Record<string, any>>({
  deviceUniqueId: route.params.deviceId,
  name: '',
  foundTime: '',
  defectLevel: 1,
  defectState: 1,
  defectDesc: '',
  workOrderVOList: []
})
const formRules = reactive<FormRules<Record<string, any>>>({
  name: [{ required: true, message: '请输入缺陷名称', trigger: 'blur' }],
  foundTime: [{ required: true, message: '请选择发现时间', trigger: 'change' }],
  defectLevel: [
    { required: true, message: '请选择权限等级', trigger: 'change' }
  ],
  defectState: [
    { required: true, message: '请选择缺陷状态', trigger: 'change' }
  ],
  defectDesc: [{ required: true, message: '请输入缺陷描述', trigger: 'blur' }]
})

const orders = ref<Record<string, any>[]>([])
const stationId = route.query.stationId
const getOrders = async (orderName: string = '') => {
  try {
    const { data } = await request({
      url: '/operate/remote-access/getRelatedOrder',
      method: 'get',
      loading: false,
      params: {
        powerStation: stationId,
        orderName
      }
    })
    orders.value = data.data
  } catch (e: any) {
    orders.value = []
  }
}
const handleAdd = () => {
  getOrders()
  dialogVisible.value = true
}
const dialogClose = () => {
  formRef.value && formRef.value.resetFields()
  dialogVisible.value = false
}
const handleSave = async () => {
  if (!formRef) return
  await formRef.value!.validate(async (valid) => {
    if (valid) {
      try {
        let data = JSON.parse(JSON.stringify(formData.value))
        data.workOrderVOList = data.workOrderVOList.map((e: any) => {
          const [workId, workNo, objectApiName] = e.split(',')
          return {
            workId,
            workNo,
            objectApiName
          }
        })
        await request({
          url: '/operate/deviceLedger/saveDeviceDefectRecord',
          method: 'post',
          data,
          loading: true
        })
        ElMessage({
          message: '登记缺陷成功！',
          type: 'success'
        })
        dialogClose()
        getTableData()
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}
</script>
<style scoped lang="scss">
.operate {
  margin-bottom: 12px;
  text-align: right;
}
</style>
<style lang="scss">
.select4786 {
  width: 430px;
}
</style>
