<template>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next"
    :page-sizes="[10, 20, 50, 100]"
    :page-count="
      Math.min(
        Math.ceil(tableData.total / searchData.pageSize),
        scrollIds.length
      )
    "
    :page-size="searchData.pageSize"
    :current-page="searchData.pageNum"
    :columns="tableColumns"
    :total="tableData.total"
    :data="tableData.data"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination scroll_table"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
    <template #workState="{ row }">
      <el-tag v-if="row.workState == 1" class="tag__ tag__1">待指派</el-tag>
      <el-tag v-if="row.workState == 2" class="tag__ tag__2">待接单</el-tag>
      <el-tag v-if="row.workState == 3" class="tag__ tag__3">待开始</el-tag>
      <el-tag v-if="row.workState == 4" class="tag__ tag__4">处理中</el-tag>
      <el-tag v-if="row.workState == 5" class="tag__ tag__5">待验证</el-tag>
      <el-tag v-if="row.workState == 6" class="tag__ tag__6">已完成</el-tag>
      <el-tag v-if="row.workState == 7" class="tag__ tag__7">已取消</el-tag>
    </template>
  </vis-table-pagination>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import request from '@/utils/request'

let route = useRoute()
const router = useRouter()
const tableColumns = ref([
  {
    prop: 'workOrderName',
    label: '关联检修工单',
    type: 'click',
    click: (row: Record<string, any>) => {
      router.push(
        `/workorder/lookorder/${row.dataId}?parentPageName=设备台账详情`
      )
    },
    minWidth: 200
  },
  {
    prop: 'createTime',
    label: '工单创建时间',
    minWidth: '120px'
  },
  {
    prop: 'endTime',
    label: '工单完成时间',
    minWidth: '120px'
  },
  {
    prop: 'workState',
    label: '工单状态',
    slotName: 'workState',
    width: 108
  },
  {
    prop: 'processDescription',
    label: '处理描述'
  },
  {
    prop: 'imageUrlOne',
    label: '检修前图片',
    type: 'image-new'
  },
  {
    prop: 'imageUrlTwo',
    label: '处理完成图片',
    type: 'image-new'
  },
  {
    prop: 'createUser',
    label: '创建人',
    formatter: (e: any) => {
      return e?.createUser?.split('_')[0]
    }
  },
  {
    prop: 'handlers',
    label: '处理人',
    formatter: (e: any) => {
      return e?.handlers?.split('_')[0]
    }
  },
  {
    prop: 'acceptancePerson',
    label: '验收人',
    formatter: (e: any) => {
      return e?.acceptancePerson?.split('_')[0]
    }
  }
])

let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})
let searchData = ref({
  powerStation: route.query.stationId,
  sn: route.params.deviceId,
  pageSize: 10,
  pageNum: 1
})
let scrollIds = [0]
let oldPageNum = 1
let tableLoading = ref(false)
const getTableData = async () => {
  try {
    let scrollId = 0
    if (searchData.value.pageNum >= oldPageNum) {
      scrollId = scrollIds.at(-1) || 0
    } else {
      scrollIds.splice(searchData.value.pageNum)
      scrollId = scrollIds.at(-1) || 0
    }
    let params: Record<string, any> = {
      sn: searchData.value.sn,
      scrollId,
      pageSize: searchData.value.pageSize
    }
    const { data } = await request({
      url: '/operate/remote-access/getEquipmentRelatedOrder',
      method: 'get',
      loading: [tableLoading],
      params
    })
    tableData.data = data?.data?.equipmentRelatedOrderVOS || []
    tableData.total = data?.data?.page?.total || 0

    tableData.data = tableData.data.map((item: Record<string, any>) => {
      return {
        ...item,
        imageUrlOne: item?.imageUrlOne?.split(',') || '',
        imageUrlTwo: item?.imageUrlTwo?.split(',') || ''
      }
    })
    tableData.data?.at(-1)?.dataId &&
      scrollIds.push(tableData.data.at(-1).dataId)
    oldPageNum = searchData.value.pageNum
  } catch (e: any) {
    tableData.data = []
    tableData.total = 0
    scrollIds = [0]
    oldPageNum = 1
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  scrollIds = [0]
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}
onMounted(async () => {
  getTableData()
})
</script>

<style lang="scss" scoped>
.tag__ {
  font-size: 14px;
  font-weight: 400;
  width: 66px;
  height: 22px;
  line-height: 22px;
  text-align: center;
}
.tag__1 {
  background: rgba(255, 0, 0, 0.1);
  color: rgba(255, 0, 0, 1);
}

.tag__2 {
  background: rgba(16, 140, 255, 0.1);
  color: rgba(16, 140, 255, 1);
}

.tag__3 {
  background: rgba(41, 204, 160, 0.1);
  color: rgba(41, 204, 160, 1);
}
.tag__4 {
  background: rgba(92, 66, 255, 0.1);
  color: rgba(92, 66, 255, 1);
}
.tag__5 {
  background: rgba(251, 110, 30, 0.1);
  color: rgba(251, 110, 30, 1);
}
.tag__6 {
  background: rgba(248, 183, 16, 0.1);
  color: rgba(248, 183, 16, 1);
}
.tag__7 {
  background: rgba(122, 138, 153, 0.1);
  color: rgba(122, 138, 153, 1);
}
</style>
