<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import request from '@/utils/request'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const router = useRouter()

let searchData = ref({
  deviceUniqueId: '',
  deviceName: '',
  deviceSn: '',
  deviceTypesName: '',
  deviceBrandName: '',
  deviceModeName: '',
  stationName: '',
  projectName: '',
  operatorName: '',
  pageSize: 10,
  pageNum: 1
})

const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})

const searchProps = ref([
  {
    label: '设备编码',
    prop: 'deviceUniqueId'
  },
  {
    label: '设备名称',
    prop: 'deviceName'
  },
  {
    label: 'SN码',
    prop: 'deviceSn'
  },
  {
    label: '设备类型',
    prop: 'deviceTypesName'
  },
  {
    label: '设备品牌',
    prop: 'deviceBrandName'
  },
  {
    label: '设备型号',
    prop: 'deviceModeName'
  },
  {
    label: '所属电站',
    prop: 'stationName'
  },
  {
    label: '所属项目',
    prop: 'projectName'
  }
  // {
  //   label: '所属运维商',
  //   prop: 'operatorName'
  // }
])

const tableColumns = [
  {
    prop: 'deviceUniqueId',
    label: '设备编码'
  },
  {
    prop: 'deviceName',
    label: '设备名称'
  },
  {
    prop: 'deviceSn',
    label: 'SN码'
  },
  {
    prop: 'deviceTypesName',
    label: '设备类型'
  },
  {
    prop: 'deviceBrandName',
    label: '设备品牌'
  },
  {
    prop: 'deviceModeName',
    label: '设备型号'
  },
  {
    prop: 'stationName',
    label: '所属电站'
  },
  {
    prop: 'projectName',
    label: '所属项目'
  },
  {
    prop: 'operatorName',
    label: '所属运维商',
    minWidth: '110px'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 60,
    fixed: 'right'
  }
]

const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageNum = 1
  searchData.value.pageSize = 10
  getTableData()
}

let tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await request({
      url: '/operate/deviceLedger/getDeviceLedgerVOListV2',
      method: 'post',
      data: {
        ...searchData.value,
        code: localStorage.getItem('PVOM_COMPANY_CODE')
      },
      loading: true
    })
    tableData.data = data?.data || []
    tableData.total = data?.total || 0
  } catch (e: any) {
    tableData.data = []
    tableData.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  companyCode.data && (await getTableData())
  startWatch = true
})

const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const handleView = (row: Record<string, any>) => {
  sessionStorage.setItem('itemInfo', JSON.stringify(row))
  router.push({
    path: `/device/detail/${row.deviceUniqueId}`,
    query: {
      stationId: row.stationInfoUniqueId || '',
      deviceSn: row.deviceSn || ''
    }
  })
}
</script>

<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="100px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <vis-table-pagination
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="tableColumns"
        :total="tableData.total"
        :data="tableData.data"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="handleView(row)">查看</el-button>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
