<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import GroupUsers from './components/groupUsers.vue'
import GroupStations from './components/groupStations.vue'
import request from '@/utils/request'
import { getParents } from '@/utils/tree'

const searchProps = reactive<any>([
  {
    prop: 'name',
    label: '查询',
    placeholder: '请输入组织、项目名称',
    span: 16,
    width: '50px'
  }
])

let searchData = ref<Partial<Record<string, any>>>({
  name: ''
})

const first = ref(true)
const num = ref(0)
const handleSearch = async (val: any) => {
  first.value = true
  num.value = 0
  searchData.value = { ...val }
  treeRef.value!.filter(searchData.value.name)
  if (num.value === 0) {
    if (searchData.value.name) {
      changeTreeNode()
    } else {
      changeTreeNode(treeData.value?.[0] || {})
    }
  }
  const node = treeRef.value.getNode(selectedId.value)
  for (var i = 0; i < node.store._getAllNodes().length; i++) {
    node.store._getAllNodes()[i].expanded =
      node.store._getAllNodes()[i].data.id === selectedId.value
  }
}

const filterNode = (value: any, data: any, node: any) => {
  if (!value) return true
  let parentNode = node.parent
  let labels = [node.label]
  let level = 1
  while (level < node.level) {
    labels = [...labels, parentNode.label]
    parentNode = parentNode.parent
    level++
  }
  const res = labels.some((label) => label.indexOf(value) !== -1)
  if (res) {
    num.value = num.value + 1
    if (first.value && value) {
      changeTreeNode(data)
      first.value = false
    }
    return true
  }
  return false
}

const selectedId = ref<any>(0)
const selectedCode = ref<string>('root')
const depth = ref<number>(1)
const isLeaf = ref<number>(0)
const defaultExpandedKeys = ref<number[]>([0])

const groupUsersRef = ref()
const groupStationsRef = ref()

const treeProps = ref({
  children: 'treeChild',
  label: 'name'
})
const treeRef = ref()
const treeData = ref<any[]>([])

const syncTreeData = async () => {
  await request({
    url: '/operate/operatorsManage/addOperateCompanyTree',
    method: 'get',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    loading: true
  })
  getTreeData()
}

const getTreeData = async () => {
  try {
    const { data } = await request({
      url: '/operate/operation-project/getOperationCompanyTreeV3',
      method: 'get',
      loading: true
    })
    treeData.value = data?.data || []
    changeTreeNode({ id: selectedId.value, code: selectedCode.value })
  } catch (e: any) {
    treeData.value = []
    changeTreeNode()
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

const changeTreeNode = async (data: any = {}) => {
  selectedId.value = data.id ?? 0
  selectedCode.value = data.code || 'root'
  depth.value = data.depth || 1
  isLeaf.value = data.isLeaf || 0

  nextTick(() => {
    treeRef.value.setCurrentKey(selectedId.value, false)
    const node = treeRef.value.getNode(selectedId.value)
    defaultExpandedKeys.value = getParents(node)
  })
}

const tabValue = ref(1)
const changeTabValue = (val: any) => {
  tabValue.value = val
}

onMounted(async () => {
  getTreeData()
})

const getExistNode = async (showMessage: boolean = false) => {
  const { data } = await request({
    url: '/operate/safeZoneController/getExistNode',
    method: 'post',
    data: {
      code: selectedCode.value,
      type: 3
    },
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    loading: false
  })
  if (!data.data) {
    showMessage &&
      ElMessage({
        message: '当前选择节点权限不足，请在左上方重新选择下级节点。！',
        type: 'warning'
      })
    return Promise.reject()
  }
  return true
}

provide('getExistNode', getExistNode)
</script>

<template>
  <div class="info-base" style="margin-bottom: 0; padding-bottom: 0">
    <searchForm
      :search-props="searchProps"
      :search-data="searchData"
      label-width="60px"
      @submit-emits="handleSearch"
    ></searchForm>
  </div>
  <div v-auto-height class="page-wrapper">
    <div class="sidebar">
      <div class="header">
        <div class="title">运维权限树</div>
        <div class="refresh-btn" @click="syncTreeData">
          <el-icon><Refresh /></el-icon> &nbsp;同步
        </div>
      </div>
      <el-scrollbar max-height="calc(100% - 63px)">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          node-key="id"
          :accordion="true"
          :current-node-key="selectedId"
          :default-expanded-keys="defaultExpandedKeys"
          :filter-node-method="filterNode"
          @node-click="changeTreeNode"
        >
          <template #default="{ node }">
            <div class="custom-tree-node">
              <div class="label">{{ node.label }}</div>
            </div>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
    <div class="info-tab">
      <el-tabs v-model="tabValue" @tab-change="changeTabValue">
        <el-tab-pane label="人员" :name="1">
          <GroupUsers
            v-if="tabValue === 1"
            ref="groupUsersRef"
            :key="selectedId"
            :radio-value="1"
            :selected-id="selectedId"
            :selected-code="selectedCode"
            :depth="depth"
          ></GroupUsers>
        </el-tab-pane>
        <el-tab-pane label="电站" :name="2">
          <GroupStations
            v-if="tabValue === 2"
            :key="selectedId"
            ref="groupStationsRef"
            :radio-value="1"
            :selected-id="selectedId"
            :selected-code="selectedCode"
            :depth="depth"
            :is-leaf="isLeaf"
          ></GroupStations>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.refresh-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  color: rgba(41, 204, 160, 1);
}
.custom-tree-node {
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .label {
    flex: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .buttons {
    display: none;
    flex: none;
    align-items: center;
    padding-right: 5px;
    .delete {
      background: url('./assets/delete.svg') no-repeat;
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }
}
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background: #ebeff2;
  .custom-tree-node .buttons {
    display: flex;
  }
}
</style>
