<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import { post, get } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import { Search } from '@element-plus/icons-vue'
import request from '@/utils/request'

const searchProps = reactive<any>([
  {
    prop: 'nickName',
    label: '姓名'
  },
  {
    prop: 'phone',
    label: '手机号'
  }
])
let searchData = ref<Partial<Record<string, any>>>({
  nickName: '',
  phone: '',
  pageSize: 10,
  pageNum: 1
})

const handleSearch = async (val: any) => {
  searchData.value = { ...val }
  searchData.value.pageNum = 1
  searchData.value.pageSize = 10
  getTableData()
}

const tableColumns = ref<any[]>([
  {
    prop: 'nickName',
    label: '用户姓名'
  },
  {
    prop: 'userName',
    label: '用户名'
  },
  {
    prop: 'phone',
    label: '手机号'
  },
  {
    prop: 'updateTime',
    label: '更新时间'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: '100',
    fixed: 'right'
  }
])
let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})
let tableLoading = ref(false)
const getTableData = async () => {
  const { response } = await post({
    url: '/operate/user/dataAuth/list',
    data: searchData.value,
    loading: [tableLoading]
  })
  tableData.data = response?.data?.data || []
  tableData.total = response?.data?.total || 0
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}
onMounted(async () => {
  getTableData()
})

const searchCheckList = ref({
  label: ''
})
const rowData = ref<Obj>([])
const treeData = ref<Obj[]>([])
const checkList = ref<Obj[][]>([])
const checkListClone = ref<Obj[][]>([])
const checkedData = ref<string[][]>([])
let dialogVisible = ref(false)
const dialogOpen = async (row: Obj) => {
  rowData.value = { ...row }
  dialogVisible.value = true
  getCheckList()
  getCheckedData(row)
}
const dialogClose = () => {
  checkList.value = []
  checkListClone.value = []
  checkedData.value = []
  rowData.value = {}
  dialogVisible.value = false
}
const getCheckList = async () => {
  const { data } = await get({
    url: '/operate/user/dataAuth/rootTrees',
    loading: true
  })
  treeData.value = data
  checkListClone.value = tree2List(data || [])
  checkList.value = JSON.parse(JSON.stringify(checkListClone.value))
}
const getCheckedData = async (row: Obj) => {
  const { data } = await get({
    url: '/operate/user/dataAuth/treesByUser',
    data: {
      ucUserId: row.ucUserId
    },
    loading: true
  })
  const arr = tree2List(data || [])
  arr.forEach((item: any[], index) => {
    checkedData.value[index] = item
      .filter((e: any) => e.selected)
      .map((e: any) => e.code)
  })
  checkedDataOld = JSON.parse(JSON.stringify(checkedData.value))
}
const tree2List = (treeData: Obj[]) => {
  let res: Obj[][] = []
  if (!Array.isArray(treeData)) return res
  let arr = JSON.parse(JSON.stringify(treeData))
  if (Array.isArray(arr)) {
    while (arr.length) {
      const item: Obj = arr.shift()
      if (Array.isArray(item.treeChild)) {
        arr = arr.concat(item.treeChild)
      }
      delete item.treeChild
      if (Array.isArray(res[item.depth - 1])) {
        res[item.depth - 1].push(item)
      } else {
        res[item.depth - 1] = [item]
      }
    }
  }
  return res
}

const handleSave = async () => {
  eachTree(treeData.value, checkedData.value.flat())
  const { code } = await post({
    url: '/operate/user/dataAuth/updateUserDataAuth',
    data: {
      authTrees: treeData.value,
      id: rowData.value.id,
      ucUserId: rowData.value.ucUserId
    },
    loading: true
  })
  if (code === '200') {
    ElMessage({
      message: '修改权限成功！',
      type: 'success'
    })
    dialogClose()
  }
}

const eachTree = (treeData: Obj[], codes: string[]) => {
  treeData.forEach((e: any) => {
    if (codes.includes(e.code)) {
      e.selected = 1
    }
    if (Array.isArray(e.treeChild)) {
      eachTree(e.treeChild, codes)
    }
  })
}

watchEffect(() => {
  checkList.value = JSON.parse(JSON.stringify(checkListClone.value))
  if (searchCheckList.value.label) {
    checkList.value = checkList.value.map((item: any) => {
      return item.filter((e: any) =>
        e.name.includes(searchCheckList.value.label)
      )
    })
  }
})

let checkedDataOld: any = []
const handleCheck = async ({ code }: Obj) => {
  try {
    await getExistNode(code)
    checkedDataOld = JSON.parse(JSON.stringify(checkedData.value))
  } catch {
    checkedData.value = JSON.parse(JSON.stringify(checkedDataOld))
  }
}

const getExistNode = async (code: string) => {
  const { data } = await request({
    url: '/operate/safeZoneController/getExistNode',
    method: 'post',
    data: {
      code,
      type: 3
    },
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    loading: false
  })
  if (!data.data) {
    ElMessage({
      message: '当前选择节点权限不足，请在左上方重新选择下级节点。！',
      type: 'warning'
    })
    return Promise.reject()
  }
  return true
}
</script>

<template>
  <div class="info-base" style="padding-bottom: 0">
    <searchForm
      :search-props="searchProps"
      :search-data="searchData"
      label-width="60px"
      @submit-emits="handleSearch"
    ></searchForm>
  </div>
  <div v-auto-height class="info-tab">
    <vis-table-pagination
      :loading="tableLoading"
      layout="total, sizes, prev, pager, next"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchData.pageSize"
      :current-page="searchData.pageNum"
      :columns="tableColumns"
      :total="tableData.total"
      :data="tableData.data"
      :show-overflow-tooltip="true"
      background
      class="vis-table-pagination"
      row-key="userName"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    >
      <template #operate="{ row }">
        <el-button link type="primary" @click="dialogOpen(row)">
          修改权限
        </el-button>
      </template>
    </vis-table-pagination>
  </div>
  <el-dialog
    v-model="dialogVisible"
    title="运维可见选择"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="1200px"
    class="vis-dialog"
  >
    <div v-if="dialogVisible" class="mb-24px">
      <el-input
        v-model="searchCheckList.label"
        placeholder="请输入组织、项目名称"
        clearable
        :suffix-icon="Search"
        class="mb-24px"
        style="width: 292px"
      >
      </el-input>
      <div class="checkbox-wrapper">
        <div v-for="(item, index) in checkList" :key="index" class="item">
          <el-scrollbar height="400px">
            <el-checkbox-group v-model="checkedData[index]">
              <template v-for="obj in item" :key="obj.code">
                <el-checkbox
                  :label="obj.name"
                  :value="obj.code"
                  :title="obj.name"
                  @change="() => handleCheck(obj)"
                />
              </template>
            </el-checkbox-group>
          </el-scrollbar>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="handleSave">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.checkbox-wrapper {
  display: flex;
  margin-left: -24px;
  .item {
    flex: none;
    width: 25%;
    border-right: 1px solid rgba(230, 233, 236, 1);
    padding: 0 24px;
    &:last-child {
      border-right: none;
    }
  }
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
  }
  .el-checkbox {
    margin-right: 0;
    :deep(.el-checkbox__label) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
