<template>
  <searchForm
    v-if="type === 'select'"
    :search-props="searchProps"
    :search-data="searchData"
    label-width="60px"
    @submit-emits="handleSearch"
  ></searchForm>
  <div v-else class="operate">
    <span>人员列表</span>
    <el-button type="primary" @click="dialogOpen">
      <el-icon><Plus /></el-icon> &nbsp; 添加人员
    </el-button>
  </div>

  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="searchData.pageSize"
    :current-page="searchData.pageNum"
    :columns="tableColumns"
    :total="tableData.total"
    :data="tableData.data"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    row-key="username"
    @handle-selection-change="handleSelectionChange"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
    <template #operate="{ row }">
      <div class="table-operate">
        <el-popconfirm title="确认删除？" @confirm="handleDelete(row)">
          <template #reference>
            <el-button link type="danger">删除</el-button>
          </template>
        </el-popconfirm>
      </div>
    </template>
  </vis-table-pagination>
  <el-dialog
    v-model="dialogVisible"
    title="添加人员"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="800px"
    class="vis-dialog"
  >
    <div mb-15px>
      <GroupUsers
        v-if="dialogVisible"
        ref="selectRef"
        type="select"
      ></GroupUsers>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="handleSave">关联</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import request from '@/utils/request'

const selectRef = ref()
const dialogVisible = ref(false)
const dialogOpen = async () => {
  try {
    await getExistNode(true)
    dialogVisible.value = true
  } catch (e) {}
}
const dialogClose = () => {
  dialogVisible.value = false
}
const handleSave = async () => {
  if (selectRef.value.selectedData.length === 0) {
    ElMessage({
      message: '请选择成员',
      type: 'error'
    })
    return
  }
  const stationGroupUser = selectRef.value.selectedData.map((e: any) => {
    return {
      ucUserId: e.id,
      userName: e.username,
      nickName: e.employeeName,
      phone: e.employeePhone,
      stationGroupUser: {
        userId: e.id,
        code: props.selectedCode,
        depth: props.depth,
        type: 3
      }
    }
  })
  try {
    const { data } = await request({
      url: '/operate/user/dataAuth/save',
      method: 'post',
      data: stationGroupUser,
      loading: true
    })
    if (data.code == 200) {
      if (data.data) {
        ElMessage({
          message: data.data,
          type: 'error'
        })
      } else {
        ElMessage({
          message: '添加成员成功！',
          type: 'success'
        })
        getTableData()
        dialogClose()
      }
    } else {
      ElMessage({
        message: data.message || data.msg,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

const props = withDefaults(
  defineProps<{
    type?: string
    radioValue?: number
    selectedId?: number | string | null
    selectedCode?: string
    depth?: number | null
  }>(),
  {
    type: undefined,
    radioValue: 1,
    selectedId: null,
    selectedCode: '',
    depth: null
  }
)

const tableColumns = ref<any[]>([])
if (props.type === 'select') {
  tableColumns.value = [
    {
      prop: 'selection',
      label: '选择',
      fixed: 'left',
      reserve: true,
      selectable: (row: { isSelect: string }) => {
        return row.isSelect !== '1'
      }
    },
    {
      prop: 'employeeName',
      label: '姓名'
    },
    {
      prop: 'username',
      label: '用户名'
    },
    {
      prop: 'employeePhone',
      label: '手机号'
    }
  ]
} else {
  tableColumns.value = [
    {
      prop: 'nickname',
      label: '姓名'
    },
    {
      prop: 'username',
      label: '用户名'
    },
    {
      prop: 'mobile',
      label: '手机号'
    },
    {
      prop: 'operate',
      slotName: 'operate',
      label: '操作',
      width: 80,
      fixed: 'right'
    }
  ]
}
const selectedData = ref<Record<string, any>[]>([])
const handleSelectionChange = (rows: Record<string, any>[]) => {
  selectedData.value = [...rows]
}

let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})

const searchProps = ref([
  {
    prop: 'keyWord',
    label: '关键字',
    placeholder: '请输入姓名/用户名/手机号',
    span: 16
  }
])
let searchData = ref({
  employeeName: '',
  username: '',
  employeePhone: '',
  keyWord: '',
  pageSize: 10,
  pageNum: 1
})
const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageSize = 10
  searchData.value.pageNum = 1
  getTableData()
}

const getExistNode: any = inject('getExistNode')
let tableLoading = ref(false)
const getTableData = async () => {
  if (props.type === 'select') {
    try {
      const { data } = await request({
        url: '/operate/uc/getUserInfoList',
        method: 'get',
        loading: [tableLoading],
        params: {
          pageSize: searchData.value.pageSize,
          pageNo: searchData.value.pageNum,
          keyWord: searchData.value.keyWord
        }
      })
      tableData.data = data?.data?.list || []
      tableData.total = data?.data?.total || 0
    } catch (e: any) {
      tableData.data = []
      tableData.total = 0
    }
  } else {
    try {
      await getExistNode()
      let url = '/operate/org-project/getOrgProjectTree/peoples'
      const { data } = await request({
        url,
        method: 'get',
        loading: [tableLoading],
        params: {
          pageSize: searchData.value.pageSize,
          pageNum: searchData.value.pageNum,
          code: props.selectedCode,
          type: 3
        },
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      })
      if (typeof data.data === 'string') {
        ElMessage({
          message: data.data,
          type: 'error'
        })
        tableData.data = []
        tableData.total = 0
      } else {
        tableData.data = data?.data?.data || []
        tableData.total = data?.data?.total || 0
      }
    } catch (e: any) {
      tableData.data = []
      tableData.total = 0
    }
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

onMounted(async () => {
  getTableData()
})

const handleDelete = async (row: Record<string, any>) => {
  try {
    const { data } = await request({
      url: '/operate/operation-project/deleteOperationCompanyRelation',
      method: 'post',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      data: {
        idsList: row.id,
        type: 3,
        depth: props.depth || null,
        code: props.selectedCode
      },
      loading: true
    })
    if (data.code === '200') {
      ElMessage({
        message: '删除成功！',
        type: 'success'
      })
      searchData.value.pageSize = 10
      searchData.value.pageNum = 1
      getTableData()
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

defineExpose({ selectedData })
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
