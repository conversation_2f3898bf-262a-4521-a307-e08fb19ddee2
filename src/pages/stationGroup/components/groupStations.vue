<template>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="searchData.pageSize"
    :current-page="searchData.pageNum"
    :columns="tableColumns"
    :total="tableData.total"
    :data="tableData.data"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    row-key="id"
    @handle-selection-change="handleSelectionChange"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import request from '@/utils/request'

const getExistNode: any = inject('getExistNode')

const props = withDefaults(
  defineProps<{
    radioValue?: number
    selectedId?: number | string | null
    selectedCode?: string
    depth?: number | null
    isLeaf?: number
  }>(),
  {
    radioValue: 1,
    selectedId: null,
    selectedCode: '',
    depth: null,
    isLeaf: 0
  }
)

const tableColumns = ref<any[]>([
  {
    prop: 'stationName',
    label: '电站名称'
  },
  {
    prop: 'stationUniqueId',
    label: '电站编号'
  },
  {
    prop: 'location',
    label: '行政区域'
  },
  {
    prop: 'capins',
    label: '装机容量(kW)'
  },
  {
    prop: 'createTime',
    label: '创建时间'
  }
])

const selectedData = ref<Record<string, any>[]>([])
const handleSelectionChange = (rows: Record<string, any>[]) => {
  selectedData.value = [...rows]
}

let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})

let searchData = ref({
  safeZoneId: props.selectedId,
  pageSize: 10,
  pageNum: 1
})

let tableLoading = ref(false)
const getTableData = async () => {
  try {
    await getExistNode()
    const { data } = await request({
      url: '/operate/operation-project/getOperationCompanyTree/stationsV2',
      method: 'get',
      loading: [tableLoading],
      params: {
        pageSize: searchData.value.pageSize,
        pageNum: searchData.value.pageNum,
        code: props.selectedCode || 'root',
        depth: props.depth ?? 0
      }
    })
    tableData.data = data?.data || []
    tableData.total = data?.total || 0
  } catch (e) {
    tableData.data = []
    tableData.total = 0
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

onMounted(async () => {
  getTableData()
})
</script>
