<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户触达情况"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="600px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <div class="touch-up-content">
        <!-- 电站信息 -->
        <div class="info-section">
          <h4 class="section-title">电站信息</h4>
          <div class="info-item">
            <span class="label">电站名称：</span>
            <span class="value">{{ touchUpData.stationName }}</span>
          </div>
          <div class="info-item">
            <span class="label">电站地址：</span>
            <span class="value">{{ touchUpData.stationAddress }}</span>
          </div>
          <div class="info-item">
            <span class="label">故障预判原因：</span>
            <span class="value">{{ touchUpData.faultReason }}</span>
          </div>
        </div>

        <!-- 户主信息 -->
        <div class="info-section">
          <h4 class="section-title">户主信息</h4>
          <div class="info-item">
            <span class="label">姓名/手机号：</span>
            <span class="value"
              >{{ touchUpData.ownerInfo.name }}/{{
                touchUpData.ownerInfo.phone
              }}</span
            >
          </div>
        </div>

        <!-- 维护人信息 -->
        <div class="info-section">
          <h4 class="section-title">维护人信息</h4>
          <div v-if="!isEditing" class="info-item">
            <span class="label">姓名/手机号：</span>
            <span class="value"
              >{{ touchUpData.maintainerInfo.name }}/{{
                touchUpData.maintainerInfo.phone
              }}</span
            >
            <el-button
              link
              type="primary"
              style="margin-left: 10px"
              @click="startEdit"
            >
              编辑
            </el-button>
          </div>

          <!-- 编辑表单 -->
          <el-form
            v-else
            ref="formRef"
            :model="editForm"
            :rules="formRules"
            label-width="80px"
          >
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model="editForm.name"
                placeholder="请输入维护人姓名"
                maxlength="20"
                clearable
              />
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="editForm.phone"
                placeholder="请输入维护人手机号"
                maxlength="11"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                :loading="saveLoading"
                @click="saveEdit"
              >
                保存
              </el-button>
              <el-button @click="cancelEdit">取消</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 短信发送情况 -->
        <div class="info-section">
          <h4 class="section-title">短信发送情况</h4>
          <div class="info-item">
            <span class="label">发送状态：</span>
            <span class="value">{{ touchUpData.smsStatus }}</span>
            <el-button
              link
              type="primary"
              style="margin-left: 10px"
              @click="resendSms"
            >
              再次发送
            </el-button>
          </div>
          <div v-if="touchUpData.smsStatus === '已发送'" class="info-item">
            <span class="label">短信发送链接：</span>
            <el-button link type="primary" @click="handleSmsLink">
              链接{{ touchUpData.stationCode }}
            </el-button>
          </div>
        </div>
      </div>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { setUpdateUser, setSendSms } from '@/api/warningAnalysis'

// Props定义
const props = defineProps<{
  isShow: boolean
  rowData?: any
}>()

// Emits定义
const emit = defineEmits(['closeDialog', 'updateData', 'refreshList'])

// 响应式数据
const dialogVisible = ref(false)
const isEditing = ref(false)
const saveLoading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const editForm = ref({
  name: '',
  phone: ''
})

// 用户触达数据
const touchUpData = ref({
  stationName: '某某户用电站',
  stationAddress: '某某省某某市某某县2号村4组34号',
  faultReason: '组串掉落',
  ownerInfo: {
    name: '李利利',
    phone: '12343455535'
  },
  maintainerInfo: {
    name: '张力',
    phone: '13434450033'
  },
  smsStatus: '已发送'
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入维护人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/,
      message: '姓名只能包含中文和英文字母',
      trigger: 'blur'
    }
  ],
  phone: [
    { required: true, message: '请输入维护人手机号', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号格式',
      trigger: 'blur'
    }
  ]
}

// 监听props变化
watch(
  () => props.isShow,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal && props.rowData) {
      // 根据传入的行数据初始化touchUpData
      initTouchUpData(props.rowData)
    }
  }
)

// 初始化数据
const initTouchUpData = (rowData: any) => {
  // 根据实际的数据结构来映射
  touchUpData.value = {
    stationName: rowData.stationName || '某某户用电站',
    stationAddress: rowData.stationAddress || '某某省某某市某某县2号村4组34号',
    faultReason: rowData.warnTypeName || '组串掉落', // 故障预判原因对应预警分析故障类型
    stationCode: rowData.stationCode || '', // 电站编码
    ownerInfo: {
      name: rowData.owner || '李利利', // 户主姓名
      phone: rowData.phone || '12343455535' // 户主手机号
    },
    maintainerInfo: {
      name: rowData.updateUser || '张力', // 维护人
      phone: rowData.updatePhone || '13434450033' // 维护人手机号
    },
    smsStatus:
      rowData.touchUp === 0
        ? '未发送'
        : rowData.touchUp === 1
          ? '已发送'
          : '已发送'
  }
}

// 开始编辑
const startEdit = () => {
  isEditing.value = true
  editForm.value = {
    name: touchUpData.value.maintainerInfo.name,
    phone: touchUpData.value.maintainerInfo.phone
  }
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  editForm.value = {
    name: '',
    phone: ''
  }
}

// 保存编辑
const saveEdit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      saveLoading.value = true
      try {
        // 调用API保存维护人信息
        await setUpdateUser({
          id: props.rowData?.id,
          updateUser: editForm.value.name,
          updatePhone: editForm.value.phone
        })

        // 更新本地数据
        touchUpData.value.maintainerInfo = {
          name: editForm.value.name,
          phone: editForm.value.phone
        }

        isEditing.value = false
        ElMessage({
          message: '维护人信息更新成功！',
          type: 'success'
        })

        // 通知父组件数据已更新
        emit('updateData', touchUpData.value)
      } catch (error) {
        ElMessage({
          message: '保存失败，请重试',
          type: 'error'
        })
      } finally {
        saveLoading.value = false
      }
    }
  })
}

// 重发短信
const resendSms = async () => {
  try {
    // 调用发送短信的API
    await setSendSms({
      phone: props.rowData?.phone
    })

    // 更新本地短信状态
    touchUpData.value.smsStatus = '已发送'

    ElMessage({
      message: '短信发送成功！',
      type: 'success'
    })

    // 通知父组件刷新列表
    emit('refreshList')
  } catch (error) {
    ElMessage({
      message: '短信发送失败，请重试',
      type: 'error'
    })
  }
}

// 短信发送链接
const handleSmsLink = () => {
  const phone = touchUpData.value.ownerInfo.phone
  const stationCode = touchUpData.value.stationCode
  // const baseUrl =
  //   'https://cloud.fastgpt.cn/chat/share?shareId=rdt1Sgxrpy3pEhXDAUTtenFl'
  const baseUrl =
    'https://agent.aiwork.spic/chat/share?shareId=wOxI1wou3XzNYKQJBjsB89RH'
  const fullUrl = `${baseUrl}&phone=${phone}&stationCode=${stationCode}`

  window.open(fullUrl, '_blank')
}

// 关闭弹窗
const dialogClose = () => {
  isEditing.value = false
  emit('closeDialog')
}
</script>

<style lang="scss" scoped>
.touch-up-content {
  .info-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 12px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
    }

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .label {
        font-weight: 500;
        color: #606266;
        min-width: 120px;
      }

      .value {
        color: #303133;
        flex: 1;
      }
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
