<template>
  <el-dialog
    v-model="dialogVisible"
    title="问题分析报告"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="800px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <div class="report-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <h4 class="section-title">基本信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">电站名称：</span>
              <span class="value">{{ reportData.stationName }}</span>
            </div>
            <div class="info-item">
              <span class="label">预警编号：</span>
              <span class="value">{{ reportData.warnNumber }}</span>
            </div>
            <div class="info-item">
              <span class="label">故障类型：</span>
              <span class="value">{{ reportData.warnTypeName }}</span>
            </div>
            <!-- <div class="info-item">
              <span class="label">生成时间：</span>
              <span class="value">{{ formatDate(reportData.createTime) }}</span>
            </div> -->
          </div>
        </div>

        <!-- 报告内容 -->
        <div class="info-section">
          <h4 class="section-title">分析报告</h4>
          <div class="report-text">
            <div v-if="reportData.problemAnalysisReport" class="content">
              <MarkdownRenderer :content="reportData.problemAnalysisReport" />
            </div>
            <div v-else class="no-content">
              <el-empty description="暂无问题分析报告" />
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import MarkdownRenderer from '@/components/MarkdownRenderer.vue'

// Props定义
const props = defineProps<{
  isShow: boolean
  rowData?: any
}>()

// Emits定义
const emit = defineEmits(['closeDialog'])

// 响应式数据
const dialogVisible = ref(false)

// 报告数据
const reportData = ref({
  stationName: '',
  warnNumber: '',
  warnTypeName: '',
  createTime: '',
  problemAnalysisReport: ''
})

// 监听props变化
watch(
  () => props.isShow,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal && props.rowData) {
      initReportData(props.rowData)
    }
  }
)

// 初始化数据
const initReportData = (rowData: any) => {
  reportData.value = {
    stationName: rowData.stationName || '',
    warnNumber: rowData.warnNumber || '',
    warnTypeName: rowData.warnTypeName || '',
    createTime: rowData.createTime || new Date().toISOString(),
    problemAnalysisReport: rowData.problemAnalysisReport || ''
  }
}

// 格式化日期
// const formatDate = (dateStr: string) => {
//   if (!dateStr) return '--'
//   try {
//     const date = new Date(dateStr)
//     return date.toLocaleString('zh-CN', {
//       year: 'numeric',
//       month: '2-digit',
//       day: '2-digit',
//       hour: '2-digit',
//       minute: '2-digit'
//     })
//   } catch {
//     return dateStr
//   }
// }

// 关闭弹窗
const dialogClose = () => {
  emit('closeDialog')
}
</script>

<style lang="scss" scoped>
.report-content {
  .info-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          font-weight: 500;
          color: #606266;
          min-width: 80px;
        }

        .value {
          color: #303133;
          flex: 1;
        }
      }
    }

    .report-text {
      .content {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 16px;
        min-height: 200px;

        // Markdown渲染器样式重置
        :deep(.markdown-renderer) {
          background: transparent;
          color: #495057;
        }
      }

      .no-content {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
      }
    }
  }
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr !important;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
