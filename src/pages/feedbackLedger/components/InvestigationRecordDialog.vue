<template>
  <el-dialog
    v-model="dialogVisible"
    title="排查完整记录"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="600px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <!-- 电站信息展示 -->
      <div class="info-section">
        <div class="info-item">
          <span class="label">电站名称：</span>
          <span class="value">{{ recordData.stationName }}</span>
        </div>
        <div class="info-item">
          <span class="label">预警编号：</span>
          <span class="value">{{ recordData.warnNumber }}</span>
        </div>
      </div>

      <!-- 对话记录展示 -->
      <div class="conversation-container">
        <div v-if="loading" class="loading-container">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span>加载中...</span>
        </div>
        <div v-else-if="conversations.length === 0" class="empty-container">
          <el-empty description="暂无排查记录" />
        </div>
        <div v-else class="conversation-list">
          <div
            v-for="(message, index) in conversations"
            :key="index"
            :class="[
              'message-item',
              message.role === 'user' ? 'user-message' : 'ai-message'
            ]"
          >
            <div class="message-avatar">
              <el-avatar
                :size="32"
                :style="{
                  backgroundColor:
                    message.role === 'user' ? '#409eff' : '#67c23a',
                  color: 'white'
                }"
              >
                <el-icon v-if="message.role === 'user'" :size="18">
                  <User />
                </el-icon>
                <el-icon v-else :size="18">
                  <ChatDotRound />
                </el-icon>
              </el-avatar>
            </div>
            <div class="message-content">
              <div class="message-bubble">
                <div class="message-text">
                  <MarkdownRenderer :content="message.content" />
                </div>
                <div
                  v-if="message.hasFile && message.fileUrl"
                  class="message-file"
                >
                  <el-image
                    :src="message.fileUrl"
                    fit="cover"
                    style="
                      max-width: 200px;
                      max-height: 200px;
                      border-radius: 8px;
                    "
                    :preview-src-list="[message.fileUrl]"
                    preview-teleported
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Loading, User, ChatDotRound } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { getInvestigationRecordDetail } from '@/api/warningAnalysis'
import MarkdownRenderer from '@/components/MarkdownRenderer.vue'

// Props定义
const props = defineProps<{
  isShow: boolean
  rowData?: any
}>()

// Emits定义
const emit = defineEmits(['closeDialog'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)

// 记录数据
const recordData = ref({
  stationName: '',
  warnNumber: ''
})

// 对话数据
const conversations = ref<
  Array<{
    role: 'user' | 'assistant'
    content: string
    timestamp: string
    hasFile?: boolean
    fileUrl?: string
  }>
>([])

// 解析AI对话JSON数据
const parseJsonConversation = (jsonData: any[]) => {
  const conversations = []

  for (const item of jsonData) {
    // 跳过隐藏的消息
    if (item.hideInUI) {
      continue
    }

    // 确定消息角色
    const role = item.obj === 'Human' ? 'user' : 'assistant'

    let content = ''
    let hasFile = false
    let fileUrl = ''

    // 处理消息内容
    if (item.value && Array.isArray(item.value)) {
      const textParts = []

      for (const valueItem of item.value) {
        if (valueItem.type === 'text' && valueItem.text?.content) {
          // 处理文本内容
          const textContent = valueItem.text.content
            .replace(/AUTO_EXECUTE/g, '')
            .replace(/\\n/g, '\n') // 将\n转换为真正的换行符
            .replace(/\\/g, '') // 去掉其他反斜杠
            .trim()

          if (textContent) {
            textParts.push(textContent)
          }
        } else if (valueItem.type === 'file' && valueItem.file) {
          // 处理文件内容
          hasFile = true
          fileUrl = valueItem.file.url || ''
          const fileName = valueItem.file.name || '图片'
          if (fileName.trim()) {
            textParts.push(`[${fileName}]`)
          } else {
            textParts.push('[图片]')
          }
        } else if (
          valueItem.type === 'interactive' &&
          valueItem.interactive?.params
        ) {
          // 处理交互式内容
          const params = valueItem.interactive.params
          if (params.description) {
            let interactiveText = `【${params.description}】`

            // 添加用户选择结果
            if (params.userSelectedVal) {
              interactiveText += `\n【村联络人选择】：${params.userSelectedVal}`
            }

            textParts.push(interactiveText)
          }
        }
      }

      content = textParts.join('\n\n')
    }

    // 使用dayjs处理时间戳
    const timestamp = item.time
      ? dayjs(item.time).format('YYYY-MM-DD HH:mm:ss')
      : dayjs().format('YYYY-MM-DD HH:mm:ss')

    // 添加到对话列表
    if (content || hasFile) {
      conversations.push({
        role,
        content: content || '[文件]',
        timestamp,
        hasFile,
        fileUrl
      })
    }
  }

  return conversations
}

// 解析对话数据
const parseConversationData = (data: any) => {
  try {
    // 如果没有数据，返回空数组
    if (!data) {
      // return parseJsonConversation(getMockJsonData()) // 保留以后使用
      return []
    }

    // 如果是数组，直接解析
    if (Array.isArray(data)) {
      return parseJsonConversation(data)
    }

    // 如果是字符串，尝试解析为JSON
    if (typeof data === 'string') {
      try {
        // 清理字符串中的转义字符
        const parsedData = JSON.parse(data)
        if (Array.isArray(parsedData)) {
          return parseJsonConversation(parsedData)
        }
      } catch (parseError) {
        console.error('JSON解析失败:', parseError)
        // 解析失败时返回空数组，不使用模拟数据
        // return parseJsonConversation(getMockJsonData()) // 保留以后使用
        return []
      }
    }

    // 其他情况返回空数组
    // return parseJsonConversation(getMockJsonData()) // 保留以后使用
    return []
  } catch (error) {
    console.error('解析对话数据失败:', error)
    // return parseJsonConversation(getMockJsonData()) // 保留以后使用
    return []
  }
}

// 获取真实对话数据 - 保留以后使用
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getMockJsonData = () => {
  return [
    {
      _id: '6879b1aa669f49475990b0e0',
      dataId: 'pgjHk9FHr8Kos2KaxN0r9X8i',
      hideInUI: true,
      obj: 'Human',
      value: [
        {
          type: 'text',
          text: {
            content: 'AUTO_EXECUTE'
          }
        }
      ],
      customFeedbacks: [],
      time: '2025-07-18T02:30:02.873Z'
    },
    {
      _id: '6879b1aa669f49475990b0e1',
      dataId: 'twJbNg5ChgEGPzqlwfMzsz0l',
      hideInUI: false,
      obj: 'AI',
      value: [
        {
          type: 'text',
          text: {
            content:
              '\n111111尊敬的村联络人您好，当前薛海娟的电站归属人电话号码为13087629230，电站地址为陕西省渭南市临渭区三张镇薛家村八组，目前电站出现巡检，目前未显示欠费，请确认是否为您负责的电站，如果是请到现场对电站问题协助排查，谢谢。'
          }
        },
        {
          type: 'interactive',
          interactive: {
            type: 'userSelect',
            params: {
              description: '请帮忙确认农户是否主动拉闸 ',
              userSelectOptions: [
                {
                  value: '是',
                  key: 'option1'
                },
                {
                  value: '否',
                  key: 'option2'
                }
              ],
              userSelectedVal: '否'
            }
          }
        },
        {
          type: 'text',
          text: {
            content:
              '\n请按照以下流程及示例上传故障照片：XXXXXXXXXXXXXX（流程示例待补充）'
          }
        }
      ],
      time: '2025-07-18T02:30:02.873Z'
    },
    {
      _id: '6879b1c5669f49475990b555',
      dataId: 'zII75IurlV7Tv9GWnPEETUl9',
      hideInUI: false,
      obj: 'Human',
      value: [
        {
          type: 'file',
          file: {
            type: 'image',
            name: '',
            url: 'https://cloud.fastgpt.cn/api/common/file/read/bb71162a7ef334d9083b79051d2af2a4.png.JPG?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJidWNrZXROYW1lIjoiY2hhdCIsInRlYW1JZCI6IjY4NmI4NTY3MGUyMGUwZDg4ZjUwNTYzYyIsInVpZCI6IjY4Nzc1N2M3ZmVkZjYwNmJiNTBlMGNiZSIsImZpbGVJZCI6IjY4NzliMWM0ODA4YmU2M2UwMWU3ODkxOSIsImV4cCI6MTc1MzQxMDYyOCwiaWF0IjoxNzUyODA1ODI4fQ.KOGNcwOe_dXFGkE-RQN8Xk-RCow90HzpwwZ5RHkRaaA'
          }
        }
      ],
      time: '2025-07-18T02:30:29.018Z'
    },
    {
      _id: '6879b1c5669f49475990b556',
      dataId: 'sr759VG8PJUvXtTaq0aVukZo',
      hideInUI: false,
      obj: 'AI',
      value: [
        {
          type: 'interactive',
          interactive: {
            type: 'userSelect',
            params: {
              description: '是否需要重新上传图片',
              userSelectOptions: [
                {
                  value: '是',
                  key: 'option1'
                },
                {
                  value: '否',
                  key: 'option2'
                }
              ],
              userSelectedVal: '否'
            }
          }
        },
        {
          type: 'text',
          text: {
            content:
              '\n正在对上传的照片进行问题识别及分析，请稍后。\n已将问题反馈给工程师，后续会派工程师上门进行处理，给您带来的不便深表歉意。您的奖励积分已下发到您的账号中，请注意查收。\nappid 686b8567c132f580f49d2e91  chatid kYWHmqL8g0nDSSIEacAgopG4'
          }
        }
      ],
      time: '2025-07-18T02:30:29.019Z'
    }
  ]
}

// 监听props变化
watch(
  () => props.isShow,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal && props.rowData) {
      initRecordData(props.rowData)
    }
  }
)

// 初始化数据
const initRecordData = async (rowData: any) => {
  recordData.value = {
    stationName: rowData.stationName || '',
    warnNumber: rowData.warnNumber || ''
  }
  const result: any = await getInvestigationRecordDetail({
    phone: rowData.phone
  })

  // 直接解析排查记录数据
  // loadInvestigationRecord(rowData.investigationRecords)
  loadInvestigationRecord(result.data.message)
}

// 加载排查记录
const loadInvestigationRecord = (investigationRecords: any) => {
  loading.value = true
  try {
    // 直接解析investigationRecords字段中的JSON数据
    const conversationData = parseConversationData(investigationRecords)
    conversations.value = conversationData
  } catch (error) {
    console.error('解析排查记录失败:', error)
    ElMessage({
      message: '解析排查记录失败',
      type: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const dialogClose = () => {
  conversations.value = []
  emit('closeDialog')
}
</script>

<style lang="scss" scoped>
.info-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-weight: 500;
      color: #606266;
      min-width: 100px;
    }

    .value {
      color: #303133;
      flex: 1;
    }
  }
}

.conversation-container {
  margin-top: 16px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;

  .el-icon {
    margin-right: 8px;
  }
}

.empty-container {
  padding: 20px;
}

.conversation-list {
  padding: 16px 0;
}

.message-item {
  display: flex;
  margin-bottom: 16px;

  &.user-message {
    flex-direction: row-reverse;

    .message-content {
      margin-right: 12px;
    }

    .message-bubble {
      background-color: #409eff;
      color: white;
    }

    .message-time {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  &.ai-message {
    .message-content {
      margin-left: 12px;
    }

    .message-bubble {
      background-color: #f5f7fa;
      color: #303133;
    }

    .message-time {
      color: #909399;
    }
  }
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: calc(100% - 60px);
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
  line-height: 1.5;
}

.message-text {
  margin-bottom: 8px;
  white-space: pre-wrap;
  word-break: break-word;

  // Markdown渲染器样式调整
  :deep(.markdown-renderer) {
    // 重置默认边距，适应消息气泡
    p {
      margin: 8px 0 !important;
      &:first-child {
        margin-top: 0 !important;
      }
      &:last-child {
        margin-bottom: 0 !important;
      }
    }

    // 图片样式调整
    img {
      max-width: 100%;
      max-height: 200px;
      border-radius: 8px;
      margin: 8px 0;
      display: block;
    }

    // 链接样式
    a {
      color: inherit;
      text-decoration: underline;
      &:hover {
        opacity: 0.8;
      }
    }

    // 代码块样式调整
    pre {
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 6px;
      padding: 8px;
      margin: 8px 0;
      font-size: 12px;
    }

    // 行内代码样式
    code {
      background-color: rgba(0, 0, 0, 0.05);
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 12px;
    }

    // 标题样式调整
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin: 8px 0 4px 0 !important;
      font-size: 14px !important;
      font-weight: 600;
    }

    // 列表样式
    ul,
    ol {
      margin: 8px 0;
      padding-left: 20px;
    }

    // 引用样式
    blockquote {
      margin: 8px 0;
      padding: 8px 12px;
      border-left: 3px solid rgba(0, 0, 0, 0.1);
      background-color: rgba(0, 0, 0, 0.02);
      border-radius: 4px;
    }
  }
}

.message-file {
  margin: 8px 0;
}

.message-time {
  font-size: 12px;
  opacity: 0.8;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
