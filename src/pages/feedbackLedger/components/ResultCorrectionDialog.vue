<template>
  <el-dialog
    v-model="dialogVisible"
    title="结果修正"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="500px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="130px"
        label-suffix=""
      >
        <!-- 电站信息展示 -->
        <div class="info-section">
          <div class="info-item">
            <span class="label">电站名称：</span>
            <span class="value">{{ correctionData.stationName }}</span>
          </div>
          <div class="info-item">
            <span class="label">电站地址：</span>
            <span class="value">{{ correctionData.stationAddress }}</span>
          </div>
          <div class="info-item">
            <span class="label">故障预判原因：</span>
            <span class="value">{{ correctionData.faultPrediction }}</span>
          </div>
          <div class="info-item">
            <span class="label">反馈后故障类型：</span>
            <span class="value">{{ correctionData.feedbackFaultType }}</span>
          </div>
        </div>

        <!-- 故障预判类型选择 -->
        <el-form-item label="故障预判类型" prop="isAccurate">
          <el-radio-group v-model="formData.isAccurate">
            <el-radio :value="true">准确</el-radio>
            <el-radio :value="false">不准确</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 条件显示：选择不准确时显示 -->
        <template v-if="!formData.isAccurate">
          <el-form-item label="修正后故障类型" prop="correctedFaultType">
            <el-select
              v-model="formData.correctedFaultType"
              placeholder="请选择修正后故障类型"
              style="width: 100%"
              clearable
              filterable
            >
              <el-option
                v-for="item in faultTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="补充说明" prop="supplementDescription">
            <el-input
              v-model="formData.supplementDescription"
              type="textarea"
              :rows="4"
              placeholder="请输入补充说明"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </template>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSave">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { getFaultTypeList, setRevised } from '@/api/warningAnalysis'

// Props定义
const props = defineProps<{
  isShow: boolean
  rowData?: any
}>()

// Emits定义
const emit = defineEmits(['closeDialog', 'refreshList'])

// 响应式数据
const dialogVisible = ref(false)
const saveLoading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = ref({
  isAccurate: true, // 故障预判类型（对应precise字段：0不准确，1准确）
  correctedFaultType: '', // 修正后故障类型（对应revisedType字段）
  supplementDescription: '' // 补充说明（对应describe字段）
})

// 修正数据
const correctionData = ref({
  stationName: '',
  stationAddress: '',
  faultPrediction: '',
  feedbackFaultType: ''
})

// 故障类型选项
const faultTypeOptions = ref<Array<{ label: string; value: string }>>([])

// 获取故障类型列表
const getFaultTypeList_ = async () => {
  try {
    const { data } = await getFaultTypeList({})
    faultTypeOptions.value = data.data.map((item: any) => ({
      label: item.faultTypeName,
      value: item.faultCode
    }))
  } catch (e: any) {
    console.error('获取故障类型列表失败:', e)
    faultTypeOptions.value = []
  }
}

// 表单验证规则
const formRules: FormRules = {
  isAccurate: [
    { required: true, message: '请选择故障预判类型', trigger: 'change' }
  ],
  correctedFaultType: [
    {
      required: true,
      message: '请选择修正后故障类型',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!formData.value.isAccurate && !value) {
          callback(new Error('请选择修正后故障类型'))
        } else {
          callback()
        }
      }
    }
  ],
  supplementDescription: [
    {
      required: true,
      message: '请输入补充说明',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (!formData.value.isAccurate && !value) {
          callback(new Error('请输入补充说明'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 监听props变化
watch(
  () => props.isShow,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal && props.rowData) {
      initCorrectionData(props.rowData)
      // 弹窗打开时获取故障类型列表
      getFaultTypeList_()
    }
  }
)

// 获取选中故障类型的名称
const getSelectedFaultTypeName = (value: string) => {
  const selectedOption = faultTypeOptions.value.find(
    (option) => option.value === value
  )
  return selectedOption ? selectedOption.label : ''
}

// 初始化数据
const initCorrectionData = (rowData: any) => {
  correctionData.value = {
    stationName: rowData.stationName || '某某户用电站',
    stationAddress: rowData.stationAddress || '某某省某某市某某县2号村4组34号',
    faultPrediction: rowData.warnTypeName || '组串掉落', // 故障预判原因使用预警分析故障类型
    feedbackFaultType: rowData.faultTypeName || '逆变器断电' // 反馈后故障类型使用反馈后分析故障类型
  }

  // 重置表单数据，根据precise字段初始化
  formData.value = {
    isAccurate: rowData.precise === 1, // precise（0：不准确，1：准确）
    correctedFaultType: rowData.revisedType || '', // 修改后故障类型
    supplementDescription: rowData.describe || '' // 补充说明
  }
}

// 保存修正结果
const handleSave = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      saveLoading.value = true
      try {
        // 构建保存数据
        const saveData: any = {
          id: props.rowData?.id, // 记录ID
          precise: formData.value.isAccurate ? 1 : 0 // 故障预判类型（0：不准确，1：准确）
        }

        // 如果选择不准确，才添加修正相关字段
        if (!formData.value.isAccurate) {
          saveData.revisedType = formData.value.correctedFaultType // 修改后故障类型
          saveData.revisedTypeName = getSelectedFaultTypeName(
            formData.value.correctedFaultType
          ) // 修正后故障类型描述
          saveData.describe = formData.value.supplementDescription // 补充说明
        }

        // 调用API保存修正结果
        await setRevised(saveData)

        ElMessage({
          message: '结果修正保存成功！',
          type: 'success'
        })

        // 通知父组件刷新列表
        emit('refreshList')

        // 关闭弹窗
        dialogClose()
      } catch (error) {
        ElMessage({
          message: '保存失败，请重试',
          type: 'error'
        })
      } finally {
        saveLoading.value = false
      }
    }
  })
}

// 关闭弹窗
const dialogClose = () => {
  formRef.value?.resetFields()
  emit('closeDialog')
}

// 监听准确性选择变化，重置条件字段
watch(
  () => formData.value.isAccurate,
  (newVal) => {
    if (newVal) {
      formData.value.correctedFaultType = ''
      formData.value.supplementDescription = ''
    }
  }
)
</script>

<style lang="scss" scoped>
.info-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-weight: 500;
      color: #606266;
      min-width: 120px;
    }

    .value {
      color: #303133;
      flex: 1;
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-radio-group) {
  .el-radio {
    margin-right: 24px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
