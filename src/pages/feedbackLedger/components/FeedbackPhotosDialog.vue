<template>
  <el-dialog
    v-model="dialogVisible"
    title="现场反馈照片"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="800px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <div class="photos-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <h4 class="section-title">基本信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">电站名称：</span>
              <span class="value">{{ photosData.stationName || '--' }}</span>
            </div>
            <div class="info-item">
              <span class="label">预警编号：</span>
              <span class="value">{{ photosData.warnNumber || '--' }}</span>
            </div>
            <div class="info-item">
              <span class="label">互动记录编号：</span>
              <span class="value">{{
                photosData.interactionNumber || '--'
              }}</span>
            </div>
          </div>
        </div>

        <!-- 照片展示区域 -->
        <div class="info-section">
          <h4 class="section-title">现场反馈照片</h4>
          <div class="photos-container">
            <template v-if="photoList.length > 0">
              <div class="photo-grid">
                <div
                  v-for="(photo, index) in photoList"
                  :key="index"
                  class="photo-item"
                >
                  <el-image
                    :src="photo"
                    :preview-src-list="photoList"
                    :initial-index="index"
                    fit="cover"
                    class="photo-image"
                    :preview-teleported="true"
                    :z-index="4000"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <span>加载失败</span>
                      </div>
                    </template>
                    <template #placeholder>
                      <div class="image-loading">
                        <el-icon class="is-loading"><Loading /></el-icon>
                        <span>图片加载中...</span>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="no-photos">
                <div v-if="isLoadingPhotos" class="loading-container">
                  <el-icon class="is-loading" size="40"><Loading /></el-icon>
                  <p>正在加载图片...</p>
                </div>
                <el-empty v-else description="暂无现场反馈照片">
                  <template #image>
                    <el-icon size="60" color="#c0c4cc"><Picture /></el-icon>
                  </template>
                </el-empty>
              </div>
            </template>
          </div>
        </div>
      </div>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Picture, Loading } from '@element-plus/icons-vue'
import request from '@/utils/request'

// Props定义
const props = defineProps<{
  isShow: boolean
  rowData?: any
}>()

// Emits定义
const emit = defineEmits<{
  closeDialog: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const photosData = ref<any>({})
const photoList = ref<string[]>([])
const imageLoadingStates = ref<Record<string, boolean>>({})
const imageErrorStates = ref<Record<string, boolean>>({})
const objectUrls = ref<string[]>([]) // 存储创建的ObjectURL，用于清理
const isLoadingPhotos = ref(false) // 整体加载状态

// 默认图片链接
const DEFAULT_IMAGE_URL =
  'https://cloud.fastgpt.cn/api/common/file/read/除雪.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJidWNrZXROYW1lIjoiY2hhdCIsInRlYW1JZCI6IjY4NmI4NTY3MGUyMGUwZDg4ZjUwNTYzYyIsInVpZCI6InNoYXJlQ2hhdC0xNzUwNzQ5NTI0MzQ1LXZwM1hEUHBrdHpYZWdsZlVTMUpPYlc4UyIsImZpbGVJZCI6IjY4NzRkMTI2NWViMjc1MGFiZDRhNjhlNCIsImV4cCI6MTc1MzA5MDk4MiwiaWF0IjoxNzUyNDg2MTgyfQ.tj3-rRdd9sBF7evDxeuWcz67EI-hYZjz4ZzwELltUHg'

// 下载图片接口
const downloadImage = async (filePath: string): Promise<string> => {
  try {
    const { data } = await request({
      url: '/file/downloadFile',
      method: 'get',
      params: { filePath },
      responseType: 'blob',
      loading: false
    })

    // 将blob转换为ObjectURL
    const objectUrl = window.URL.createObjectURL(data)
    objectUrls.value.push(objectUrl) // 记录URL用于清理
    return objectUrl
  } catch (error) {
    console.error('图片下载失败:', error)
    throw error
  }
}

// 监听弹窗显示状态
watch(
  () => props.isShow,
  async (newVal) => {
    dialogVisible.value = newVal
    if (newVal && props.rowData) {
      await initPhotosData()
    }
  },
  { immediate: true }
)

// 初始化照片数据
const initPhotosData = async () => {
  photosData.value = { ...props.rowData }
  await processPhotoList()
}

// 处理照片列表
const processPhotoList = async () => {
  const photos = photosData.value.feedbackPhotos
  isLoadingPhotos.value = true

  if (!photos || photos.trim() === '') {
    // 如果没有照片数据，使用默认图片
    photoList.value = [DEFAULT_IMAGE_URL]
    isLoadingPhotos.value = false
    return
  }

  // 解析照片路径
  let photoPaths: string[] = []
  if (typeof photos === 'string') {
    photoPaths = photos
      .split(',')
      .map((photo) => photo.trim())
      .filter((photo) => photo !== '')
  } else if (Array.isArray(photos)) {
    photoPaths = photos.filter((photo) => photo && photo.trim() !== '')
  }

  if (photoPaths.length === 0) {
    photoList.value = [DEFAULT_IMAGE_URL]
    isLoadingPhotos.value = false
    return
  }

  // 清空之前的状态
  imageLoadingStates.value = {}
  imageErrorStates.value = {}
  photoList.value = []

  // 下载所有图片
  for (const photoPath of photoPaths) {
    imageLoadingStates.value[photoPath] = true
    imageErrorStates.value[photoPath] = false

    try {
      const imageUrl = await downloadImage(photoPath)
      photoList.value.push(imageUrl)
      imageLoadingStates.value[photoPath] = false
    } catch (error) {
      console.error(`图片下载失败: ${photoPath}`, error)
      imageErrorStates.value[photoPath] = true
      imageLoadingStates.value[photoPath] = false
      // 下载失败时使用默认图片
      photoList.value.push(DEFAULT_IMAGE_URL)
    }
  }

  // 如果所有图片都下载失败，至少显示一张默认图片
  if (photoList.value.length === 0) {
    photoList.value = [DEFAULT_IMAGE_URL]
  }

  isLoadingPhotos.value = false
}

// 清理ObjectURL
const cleanupObjectUrls = () => {
  objectUrls.value.forEach((url) => {
    window.URL.revokeObjectURL(url)
  })
  objectUrls.value = []
}

// 关闭弹窗
const dialogClose = () => {
  cleanupObjectUrls() // 清理内存
  dialogVisible.value = false
  emit('closeDialog')
}

// 组件卸载时清理
onUnmounted(() => {
  cleanupObjectUrls()
})
</script>

<style lang="scss" scoped>
.photos-content {
  .info-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 12px;
    }

    .info-item {
      display: flex;
      align-items: center;

      .label {
        font-weight: 500;
        color: #606266;
        min-width: 100px;
      }

      .value {
        color: #303133;
        flex: 1;
      }
    }
  }

  .photos-container {
    .photo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 16px;

      .photo-item {
        .photo-image {
          width: 100%;
          height: 150px;
          border-radius: 8px;
          border: 1px solid #ebeef5;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
          }
        }
      }
    }

    .no-photos {
      text-align: center;
      padding: 40px 0;

      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;

        .el-icon {
          margin-bottom: 12px;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }

    .image-error,
    .image-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #c0c4cc;
      font-size: 14px;

      .el-icon {
        margin-bottom: 8px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
