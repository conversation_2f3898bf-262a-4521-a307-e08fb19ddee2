<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>互动反馈台账</p>
      </div>
      <TablePagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :show-overflow-tooltip="true"
        background
        :columns="columns"
        :data="tableData.data"
        :total="tableData.total"
        :loading="tableLoading"
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        class="table-pagination"
        :scroll-x="true"
        :min-width="1400"
        @handle-size-change="changeSize"
        @handle-current-change="changeCurrent"
      >
        <template #stationName="{ row }">
          <span
            style="color: #29cca0; cursor: pointer"
            @click="toViewDetail(row)"
            >{{ row.stationName }}</span
          >
        </template>
        <template #touchUp="{ row }">
          <span
            style="color: #29cca0; cursor: pointer"
            @click="toViewTouchUp(row)"
            >{{
              row.touchUp === 0 ? '未发送' : row.touchUp === 1 ? '已发送' : '--'
            }}</span
          >
        </template>
        <template #feedbackPhotos="{ row }">
          <span
            style="color: #29cca0; cursor: pointer"
            @click="toViewPhotos(row)"
            >查看</span
          >
        </template>
        <template #investigationRecords="{ row }">
          <span
            style="color: #29cca0; cursor: pointer"
            @click="toViewRecords(row)"
            >查看</span
          >
        </template>
        <template #problemAnalysisReport="{ row }">
          <span
            style="color: #29cca0; cursor: pointer"
            @click="toViewReport(row)"
            >查看</span
          >
        </template>
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="resultCorrection(row)">结果修正</el-button>
            <el-button link @click="dispatch(row)">派单</el-button>
          </div>
        </template>
      </TablePagination>
    </div>
  </div>

  <!-- 用户触达情况弹出框 -->
  <TouchUpDialog
    :is-show="showTouchUpDialog"
    :row-data="currentRowData"
    @close-dialog="closeTouchUpDialog"
    @update-data="handleTouchUpUpdate"
    @refresh-list="handleRefreshList"
  />

  <!-- 结果修正弹出框 -->
  <ResultCorrectionDialog
    :is-show="showResultCorrectionDialog"
    :row-data="currentRowData"
    @close-dialog="closeResultCorrectionDialog"
    @refresh-list="handleRefreshList"
  />

  <!-- 排查记录弹出框 -->
  <InvestigationRecordDialog
    :is-show="showInvestigationRecordDialog"
    :row-data="currentRowData"
    @close-dialog="closeInvestigationRecordDialog"
    @refresh-list="handleRefreshList"
  />

  <!-- 问题分析报告弹出框 -->
  <ProblemAnalysisDialog
    :is-show="showProblemAnalysisDialog"
    :row-data="currentRowData"
    @close-dialog="closeProblemAnalysisDialog"
  />

  <!-- 现场反馈照片弹出框 -->
  <FeedbackPhotosDialog
    :is-show="showFeedbackPhotosDialog"
    :row-data="currentRowData"
    @close-dialog="closeFeedbackPhotosDialog"
  />

  <!-- 选择工单 -->
  <el-dialog
    v-model="dialogVisible"
    title="派单"
    width="436"
    :before-close="() => (dialogVisible = false)"
    class="vis-dialog"
  >
    <el-radio-group v-model="radioType" class="radio-box">
      <el-radio value="1" size="large">运维工单</el-radio>
      <el-radio value="2" size="large">巡检工单</el-radio>
      <el-radio value="3" size="large">清洗工单</el-radio>
    </el-radio-group>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogSumbit(radioType)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import useTableData from '@/hooks/useTableData'
import TouchUpDialog from './components/TouchUpDialog.vue'
import ResultCorrectionDialog from './components/ResultCorrectionDialog.vue'
import InvestigationRecordDialog from './components/InvestigationRecordDialog.vue'
import ProblemAnalysisDialog from './components/ProblemAnalysisDialog.vue'
import FeedbackPhotosDialog from './components/FeedbackPhotosDialog.vue'
import * as api from '@/api/index.ts'
import { getFaultTypeList, setDispatch } from '@/api/warningAnalysis'

const router = useRouter()

// 用户触达情况弹出框相关状态
const showTouchUpDialog = ref(false)
const currentRowData = ref<any>(null)

// 结果修正弹出框相关状态
const showResultCorrectionDialog = ref(false)

// 排查记录弹出框相关状态
const showInvestigationRecordDialog = ref(false)

// 问题分析报告弹出框相关状态
const showProblemAnalysisDialog = ref(false)

// 现场反馈照片弹出框相关状态
const showFeedbackPhotosDialog = ref(false)

// 新建工单
const radioType = ref('')
const dialogVisible = ref(false)

const searchProps = ref<Obj[]>([
  {
    prop: 'nameOrNumber',
    label: '电站名称/编码',
    width: '120px',
    type: 'input'
  },
  {
    prop: 'faultCode',
    label: '故障类型',
    width: '80px',
    type: 'select',
    options: []
  }
])
let searchData = reactive<Obj>({
  nameOrNumber: '',
  faultCode: '',
  pageSize: 10,
  pageNum: 1
})
const handleSearch = async (val: any) => {
  Object.keys(searchData).forEach((key) => {
    searchData[key] = val[key]
  })
  changeData(true)
}

const columns = [
  {
    prop: 'interactionNumber',
    label: '互动记录编号',
    width: 140
  },
  {
    prop: 'warnNumber',
    label: '预警编号',
    width: 140
  },
  {
    prop: 'stationName',
    label: '电站名称',
    width: 120,
    slotName: 'stationName'
  },
  {
    prop: 'stationAddress',
    label: '电站地址',
    width: 120
  },
  {
    prop: 'owner',
    label: '业主姓名',
    width: 120
  },
  {
    prop: 'phone',
    label: '业主手机号',
    width: 140
  },
  {
    prop: 'touchUp',
    label: '触达情况',
    width: 120,
    slotName: 'touchUp',
    formatter: (row: any) => {
      return row.touchUp === 0 ? '未发送' : row.touchUp === 1 ? '已发送' : '--'
    }
  },
  {
    prop: 'feedback',
    label: '互动反馈状态',
    width: 140
    // formatter: (row: any) => {
    //   return row.feedback === 0
    //     ? '未反馈'
    //     : row.feedback === 1
    //       ? '反馈中'
    //       : row.feedback === 2
    //         ? '反馈完成'
    //         : '--'
    // }
  },
  {
    prop: 'warnTypeName',
    label: '预警分析故障类型',
    width: 140
  },
  {
    prop: 'faultTypeName',
    label: '反馈后分析故障类型',
    width: 150
  },
  {
    prop: 'feedbackPhotos',
    label: '现场反馈照片',
    width: 140,
    slotName: 'feedbackPhotos'
  },
  {
    prop: 'investigationRecords',
    label: '排查完整记录',
    width: 140,
    slotName: 'investigationRecords'
  },
  {
    prop: 'problemAnalysisReport',
    label: '问题分析报告',
    width: 140,
    slotName: 'problemAnalysisReport'
  },
  {
    prop: 'precise',
    label: '反馈后判断是否准确',
    width: 160,
    formatter: (row: any) => {
      return row.precise === 0 ? '不准确' : row.precise === 1 ? '准确' : '--'
    }
  },
  {
    prop: 'revisedTypeName',
    label: '修正后故障类型',
    width: 160
  },
  {
    prop: 'describe',
    label: '补充说明'
  },
  {
    prop: 'operate',
    label: '操作',
    slotName: 'operate',
    minWidth: 130,
    fixed: 'right'
  }
]

const getTableData = async (data: Obj, loading: Loading) => {
  return await api.post('/opAi/interactionLedgerList', { ...data }, loading)
}
const {
  tableData,
  tablePage,
  tableLoading,
  changeCurrent,
  changeSize,
  changeData
} = useTableData(getTableData, searchData, { immediate: true })

const toViewDetail = (row: any) => {
  console.log(row)
  router.push({
    path: `/feedbackLedger/detail`
  })
  // router.push({
  //   path: `/station/all/detail/17403888944461223832940680793586`,
  //   query: {
  //     hideBack: 'true'
  //   }
  // })
}
const toViewTouchUp = (row: any) => {
  console.log(row)
  currentRowData.value = row
  showTouchUpDialog.value = true
}
const toViewPhotos = (row: any) => {
  console.log('查看现场反馈照片:', row)
  currentRowData.value = row
  showFeedbackPhotosDialog.value = true
}

const toViewRecords = (row: any) => {
  console.log('查看排查完整记录:', row)
  currentRowData.value = row
  showInvestigationRecordDialog.value = true
}

const toViewReport = (row: any) => {
  console.log('查看问题分析报告:', row)
  currentRowData.value = row
  showProblemAnalysisDialog.value = true
}

// 关闭用户触达情况弹出框
const closeTouchUpDialog = () => {
  showTouchUpDialog.value = false
  currentRowData.value = null
}

// 处理用户触达情况数据更新
const handleTouchUpUpdate = (updatedData: any) => {
  console.log('用户触达情况数据已更新:', updatedData)
  // 这里可以更新表格数据或执行其他操作
}

// 处理刷新列表
const handleRefreshList = () => {
  console.log('刷新列表数据')
  // 重新获取表格数据
  changeData(true)
}

// 关闭结果修正弹出框
const closeResultCorrectionDialog = () => {
  showResultCorrectionDialog.value = false
  currentRowData.value = null
}

// 关闭排查记录弹出框
const closeInvestigationRecordDialog = () => {
  showInvestigationRecordDialog.value = false
  currentRowData.value = null
}

// 关闭问题分析报告弹出框
const closeProblemAnalysisDialog = () => {
  showProblemAnalysisDialog.value = false
  currentRowData.value = null
}

// 关闭现场反馈照片弹出框
const closeFeedbackPhotosDialog = () => {
  showFeedbackPhotosDialog.value = false
  currentRowData.value = null
}

// 结果修正
const resultCorrection = (row: any) => {
  console.log(row)
  currentRowData.value = row
  showResultCorrectionDialog.value = true
}

// 派单
const dispatch = (row: any) => {
  console.log(row)
  currentRowData.value = row // 设置当前行数据
  dialogVisible.value = true
  radioType.value = '1'
}
const dialogSumbit = async (val: string) => {
  console.log('派单类型:', val)
  try {
    // 调用派单接口
    await setDispatch({
      id: currentRowData.value?.id
    })

    ElMessage({
      message: '派单成功！',
      type: 'success'
    })

    // 关闭弹窗
    dialogVisible.value = false

    // 刷新列表
    changeData(true)

    // 跳转工单演示页面
    setTimeout(() => {
      // window.open('http://117.72.220.213:82/#/orders', '_blank')
      window.open('http://**************/h5/#/orders', '_blank')
    }, 1000)
  } catch (error) {
    ElMessage({
      message: '派单失败，请重试',
      type: 'error'
    })
  }
}
const getFaultTypeList_ = async () => {
  try {
    const { data } = await getFaultTypeList({})
    searchProps.value[1].options = data.data.map((item: any) => ({
      label: item.faultTypeName,
      value: item.faultCode
    }))
  } catch (e: any) {
    faultTypeList.value = []
  }
}
onMounted(() => {
  getFaultTypeList_()
})
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.radio-box {
  display: flex !important;
  flex-direction: column !important;
  align-items: baseline !important;
  margin-bottom: 24px;
}
</style>
