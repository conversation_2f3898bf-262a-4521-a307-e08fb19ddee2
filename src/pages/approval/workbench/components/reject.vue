<!--
 * @Description: 驳回
 * @Author: zwcong
 * @Date: 2024-04-30 10:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-05-20 14:56:46
-->
<template>
  <el-form ref="formRef" :model="formData" label-suffix="" :rules="formRules">
    <el-form-item label="驳回原因" prop="turnDownMsg" class="form-item-column">
      <el-input
        v-model.trim="formData.turnDownMsg"
        maxlength="100"
        placeholder="请输入驳回原因"
        show-word-limit
        :rows="6"
        type="textarea"
      />
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
import useDialogForm from '../hooks/useDialogForm.ts'

const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  turnDownMsg: '' // 角色名称
})
defineProps({
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const validatorMsg = (rule: any, value: any, callback: any) => {
  if (!value || value.trim() === '') {
    return callback(new Error('请输入驳回原因'))
  } else {
    callback()
  }
}

const formRules = reactive<FormRules<Record<string, any>>>({
  turnDownMsg: [
    {
      required: true,
      // message: '请输入驳回原因',
      validator: validatorMsg,
      trigger: 'input'
    }
  ]
})

const emits = defineEmits(['closeDialog', 'submitData'])
const { submitForm, resetFields } = useDialogForm(
  formRef,
  formData.value,
  emits
)

defineExpose({ submitForm, resetFields })
</script>
<style scoped src="../../assets/dialogForm.scss"></style>
