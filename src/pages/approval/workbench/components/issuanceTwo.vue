<!--  签发审批-工作许可人 -->
<template>
  <div class="title">【电气二种票签发审批-工作许可人】需填写补充信息</div>
  <el-form ref="formRef" :model="formData" label-suffix="" :rules="formRules">
    <el-form-item
      label="补充安全措施"
      prop="secureMsg"
      class="form-item-column"
    >
      <el-input
        v-model.trim="formData.secureMsg"
        maxlength="100"
        placeholder="请输入补充安全措施"
        show-word-limit
        :rows="6"
        type="textarea"
      />
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
import useDialogForm from '../hooks/useDialogForm.ts'

const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  secureMsg: '' // 安全措施
})
defineProps({
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const validatorMsg = (rule: any, value: any, callback: any) => {
  if (!value || value.trim() === '') {
    return callback(new Error('请输入补充安全措施'))
  } else {
    callback()
  }
}

const formRules = reactive<FormRules<Record<string, any>>>({
  secureMsg: [
    {
      required: true,
      validator: validatorMsg,
      trigger: 'input'
    }
  ]
})

const emits = defineEmits(['closeDialog', 'submitData'])
const { submitForm, resetFields } = useDialogForm(
  formRef,
  formData.value,
  emits
)

defineExpose({ submitForm, resetFields })
</script>
<style scoped src="../../assets/dialogForm.scss"></style>
