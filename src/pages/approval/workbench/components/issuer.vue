<!--
 * @Description: 工作票签发人审批
 * @Author: zwcong
 * @Date: 2024-04-30 10:34:23
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-09-12 16:28:58
-->
<template>
  <div class="title">
    <span>*</span>工作地点保留带电部分或注意事项（工作票签发人填写）
  </div>
  <el-form
    ref="formRef"
    :rules="formRules"
    :model="formData"
    label-suffix=""
    label-position="right"
    :inline="true"
  >
    <el-form-item label="" prop="name">
      <el-input
        v-model.trim="formData.name"
        placeholder="请输入内容"
        maxlength="1024"
        clearable
        style="width: 80%"
      />
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import type { FormRules, FormInstance } from 'element-plus'

defineProps({
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const formRef = ref<FormInstance>()
const formRules = reactive<FormRules<Record<string, any>>>({
  name: [{ required: true, message: '请输入内容', trigger: 'blur' }]
})
const formData = ref<Record<string, any>>({
  name: ''
})

const emits = defineEmits(['closeDialog', 'submitData'])
const submitForm = () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      emits('submitData', {
        lookOutList: [formData.value.name]
      })
    }
  })
}
defineExpose({ submitForm })
</script>
<style scoped src="../../assets/dialogForm.scss"></style>
<style scoped lang="scss">
.title {
  span {
    color: #e62e32;
    margin-right: 4px;
  }
}
</style>
