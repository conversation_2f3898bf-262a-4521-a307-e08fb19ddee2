<!--
 * @Description: 工作票签发人审批
 * @Author: zwcong
 * @Date: 2024-04-30 10:34:23
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-09-12 16:16:42
-->
<template>
  <div class="title">
    <span>*</span>工作地点保留带电部分或注意事项（工作票签发人填写）
  </div>
  <table-row
    ref="tableRowRef"
    :columns="tableColumns"
    class="mb-32px"
    @get-tabel-data="getTabelData"
  ></table-row>
</template>
<script lang="ts" setup>
import tableRow from '../../../ticket/components/tableRow.vue'

defineProps({
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

// 表格
const tableColumns = ref([
  { prop: 'index', label: '' },
  { prop: 'name1', label: '内容', slotName: 'name1', width: 344 },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 100,
    fixed: 'right'
  }
])
const tableRowRef = ref<any>()
const getTabelData = (data: Obj) => {
  const res: any = {
    lookOutList: []
  }
  if (data?.valid) {
    data?.data.map((item: any) => {
      item.name1 !== '' && res.lookOutList.push(item.name1)
    })
    emits('submitData', res)
  }
}

const emits = defineEmits(['closeDialog', 'submitData'])

const submitForm = () => {
  tableRowRef.value.submitTable()
}

defineExpose({ submitForm })
</script>
<style scoped src="../../assets/dialogForm.scss"></style>
<style scoped lang="scss">
.title {
  span {
    color: #e62e32;
    margin-right: 4px;
  }
}
</style>
