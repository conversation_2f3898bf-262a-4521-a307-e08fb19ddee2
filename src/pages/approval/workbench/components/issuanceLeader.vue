<!--
 * @Description: 签发审批-值长
 * @Author: zwcong
 * @Date: 2024-04-30 10:34:23
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-09-13 11:10:12
-->
<template>
  <div class="title">【电气一种票签发审批-值长】需填写补充信息</div>
  <el-form ref="formRef" :model="formData" label-suffix="" :rules="formRules">
    <el-form-item
      label="批准工作时间"
      prop="permitWorkTime"
      class="form-item-column"
    >
      <el-date-picker
        v-model="formData.permitWorkTime"
        type="datetimerange"
        :editable="false"
        start-placeholder="请选择批准工作开始时间"
        end-placeholder="请选择操批准工作结束时间"
        format="YYYY-MM-DD HH:mm"
        value-format="YYYY-MM-DD HH:mm"
        clearable
        @change="changeTime"
      />
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import type { FormInstance, FormRules } from 'element-plus'
const props = defineProps({
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  permitWorkTime: [] // 批准工作时间
})
const formRules = reactive<FormRules<Record<string, any>>>({
  permitWorkTime: [
    {
      required: true,
      message: '请选择批准工作时间',
      trigger: 'change'
    }
  ]
})

const changeTime = (val: any) => {
  formData.value.permitWorkTime = val || []
}
const resetFields = () => {
  formRef.value && formRef.value.resetFields()
}
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid: any) => {
    if (valid) {
      emits('submitData', {
        workStartTime: formData.value.permitWorkTime[0] + ":00",
        permitWorkEndTime: formData.value.permitWorkTime[1] + ":00"
      })
    }
  })
}
defineExpose({ submitForm, resetFields })
const emits = defineEmits(['closeDialog', 'submitData'])
onMounted(() => {
  formData.value.permitWorkTime = [
    props.rows.workStartTime
      ? dayjs(props.rows.workStartTime).format('YYYY-MM-DD HH:mm')
      : '',
    props.rows.workEndTime
      ? dayjs(props.rows.workEndTime).format('YYYY-MM-DD HH:mm')
      : ''
  ]
})
</script>
<style scoped src="../../assets/dialogForm.scss"></style>
