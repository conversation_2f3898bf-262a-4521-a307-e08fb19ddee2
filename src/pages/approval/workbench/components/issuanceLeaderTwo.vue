<!--
 * @Description: 签发审批-值长
-->
<template>
  <div class="title">【电气二种票签发审批-值长】需填写补充信息</div>
  <el-form ref="formRef" :model="formData" label-suffix="" :rules="formRules">
    <el-form-item
      label="批准工作时间"
      prop="timeRange"
      class="form-item-column"
    >
      <!-- <el-date-picker
        v-model="formData.permitWorkEndTime"
        type="datetime"
        placeholder="请选择批准工作时间"
        format="YYYY-MM-DD HH:mm"
        value-format="YYYY-MM-DD HH:mm"
      /> -->
      <el-date-picker
        v-model="formData.timeRange"
        type="datetimerange"
        placeholder="请选择批准工作时间"
        :editable="false"
        start-placeholder="请选择批准工作开始时间"
        end-placeholder="请选择批准工作结束时间"
        format="YYYY-MM-DD HH:mm"
        value-format="YYYY-MM-DD HH:mm"
      />
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
import useDialogForm from '../hooks/useDialogForm.ts'

const props = defineProps({
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  timeRange: [],
  workStartTime: props.rows.workStartTime || '', // 批准工作开始时间
  permitWorkEndTime: props.rows.workEndTime || '' // 批准工作结束时间
})

if (props.rows.workStartTime) {
  formData.value.timeRange = [
    props.rows.workStartTime.slice(0, 16),
    props.rows.workEndTime.slice(0, 16)
  ]
}
watch(
  () => formData.value.timeRange,
  (val) => {
    if (val?.length) {
      if (formData.value.timeRange[0].length <= 16) {
        formData.value.workStartTime = formData.value.timeRange[0] + ':00'
        formData.value.permitWorkEndTime = formData.value.timeRange[1] + ':00'
      } else {
        formData.value.workStartTime = formData.value.timeRange[0]
        formData.value.permitWorkEndTime = formData.value.timeRange[1]
      }
    }
  }
)

const formRules = reactive<FormRules<Record<string, any>>>({
  timeRange: [
    {
      required: true,
      message: '请选择批准工作时间',
      trigger: 'change'
    }
  ]
})

const emits = defineEmits(['closeDialog', 'submitData'])
const { submitForm, resetFields } = useDialogForm(
  formRef,
  formData.value,
  emits
)

defineExpose({ submitForm, resetFields })
</script>
<style scoped src="../../assets/dialogForm.scss"></style>
