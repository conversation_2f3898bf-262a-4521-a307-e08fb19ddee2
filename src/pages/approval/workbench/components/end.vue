<!--
 * @Description: 终结审批-许可人
 * @Author: zwcong
 * @Date: 2024-06-19 10:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-06-19 10:18:44
-->
<template>
  <div class="title">【电气一种票终结审批-工作许可人】需填写补充信息</div>
  <el-form
    ref="formRef"
    :model="formData"
    label-suffix=""
    :rules="formRules"
    label-position="top"
    class="end-dialog"
  >
    <div class="group">
      <div class="title">
        <div class="line"></div>
        临时遮拦、标示牌已拆除，常设遮拦已恢复。
      </div>
      <el-form-item label="未拆除或未拉开的接地线编号" prop="noOne">
        <el-input
          v-model.trim="formData.noOne"
          placeholder="请输入未拆除或未拉开的接地线编号"
          autocomplete="off"
          maxlength="64"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="共多少组" prop="numOne">
        <el-input
          v-model.trim="formData.numOne"
          placeholder="请输入共多少组"
          autocomplete="off"
          maxlength="5"
          clearable
          @input="limitNum1"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="未拆除或未拉开的接地刀闸编号" prop="noTwo">
        <el-input
          v-model.trim="formData.noTwo"
          placeholder="请输入未拆除或未拉开的接地刀闸编号"
          autocomplete="off"
          maxlength="64"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="共多少组" prop="numTwo">
        <el-input
          v-model.trim="formData.numTwo"
          placeholder="请输入共多少组"
          autocomplete="off"
          maxlength="5"
          clearable
          @input="limitNum2"
        >
        </el-input>
      </el-form-item>
    </div>
  </el-form>
</template>
<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
import useDialogForm from '../hooks/useDialogForm.ts'

const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  noOne: '', // 	未拆除或未拉开的接地线编号
  noTwo: '', // 接地刀闸编号
  numOne: '', // 	组数 1
  numTwo: '' //	组数 2
})
defineProps({
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const limitNum1 = (val: any) => {
  formData.value.numOne = val.replace(/^(0)|\D/g, '')
}
const limitNum2 = (val: any) => {
  formData.value.numTwo = val.replace(/^(0)|\D/g, '')
}

const formRules = reactive<FormRules<Record<string, any>>>({
  noOne: [
    {
      required: true,
      message: '请输入未拆除或未拉开的接地线编号',
      trigger: 'blur'
    }
  ],
  numOne: [{ required: true, message: '请输入共多少组', trigger: 'blur' }],
  noTwo: [
    {
      required: true,
      message: '请输入未拆除或未拉开的接地刀闸编号多少组',
      trigger: 'blur'
    }
  ],
  numTwo: [{ required: true, message: '请输入共多少组', trigger: 'blur' }]
})

const emits = defineEmits(['closeDialog', 'submitData'])
const { submitForm, resetFields } = useDialogForm(
  formRef,
  formData.value,
  emits
)

defineExpose({ submitForm, resetFields })
</script>
<style scoped src="../../assets/dialogForm.scss"></style>
<style scoped lang="scss">
.el-form .el-form-item--default {
  margin-bottom: 18px !important;
  :deep(.el-form-item__label) {
    height: 22px !important;
    line-height: 22px !important;
  }
}
.group {
  .title {
    display: flex;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 12px;
    .line {
      flex-shrink: 0;
      width: 4px;
      height: 16px;
      background: rgba(42, 203, 160, 1);
      margin-right: 8px;
      margin-top: 3px;
    }
  }
}
</style>
