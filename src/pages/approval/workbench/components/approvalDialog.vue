<!--
 * @Description: 审批弹窗
 * @Author: zwcong
 * @Date: 2024-04-30 10:34:23
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-09-13 10:50:38
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    align-top
    :before-close="dialogClose"
    :close-on-click-modal="false"
    :width="dialogWidth"
    class="vis-dialog"
  >
    <el-scrollbar max-height="calc(100vh - 140px)">
      <component
        :is="componentName"
        ref="component"
        :rows="rows"
        @submit-data="submitData"
      ></component>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="submit">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { approval as approvalAPI } from '@/api/index.ts'
import issuance from './issuance.vue'
import issuanceTwo from './issuanceTwo.vue'
import issuanceLeader from './issuanceLeader.vue'
import issuanceLeaderTwo from './issuanceLeaderTwo.vue'
import permission from './permission.vue'
import permissionTwo from './permissionTwo.vue'
import issuer from './issuer.vue'
import reject from './reject.vue'
import end from './end.vue'

const component = ref<any>()

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  },
  //审批类型
  type: {
    type: String,
    default: 'issuance'
  }
})

const componentsMap: any = {
  issuance: issuance,
  issuanceLeader: issuanceLeader,
  permission: permission,
  issuer: issuer,
  reject: reject,
  end: end,
  issuanceTwo,
  issuanceLeaderTwo,
  permissionTwo
}

/**
 * 动态组件名称
 */
const componentName = computed(() => {
  const component = componentsMap[props.type]
  return markRaw(component)
})

const dialogTitle = computed(() => {
  let title = ''
  if (props.type === 'reject') {
    title = '审核驳回'
  } else {
    title = '填写信息'
  }
  return title
})

/**
 * 弹窗宽度
 */
const dialogWidth = computed(() => {
  if (props.type === 'issuer') {
    return '600'
  } else {
    return '446'
  }
})

const dialogVisible = ref(false)
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
  },
  {
    deep: true
  }
)

const btnLoading = ref(false)

// 点击确定
const emit = defineEmits(['closeDialog', 'updateList'])
const submit = () => {
  component.value.submitForm()
}

const submitData = async (formData: any) => {
  btnLoading.value = true
  // 驳回
  if (props.type === 'reject') {
    await approvalAPI
      .turnDown({
        ...formData,
        detailsId: props.rows.detailsId,
        instanceId: props.rows.instanceId,
        instanceType: props.rows.instanceType,
        runTimeTaskId: props.rows.runTimeTaskId,
        ticketType: props.rows.ticketType,
        userRoleType: props.rows.userRoleType,
        nextUserId: props.rows.nextUserId,
        nextUserName: props.rows.nextUserName
      })
      .then(({ data }) => {
        if (data.code == 200) {
          ElMessage({
            message: '操作成功',
            type: 'success'
          })
          dialogClose()
          emit('updateList')
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        btnLoading.value = false
      })
  } else {
    //通过
    await approvalAPI
      .passInstance({
        ...formData,
        detailsId: props.rows.detailsId,
        instanceId: props.rows.instanceId,
        instanceType: props.rows.instanceType,
        runTimeTaskId: props.rows.runTimeTaskId,
        ticketType: props.rows.ticketType,
        userRoleType: props.rows.userRoleType,
        nextUserId: props.rows.nextUserId,
        nextUserName: props.rows.nextUserName
      })
      .then(({ data }) => {
        if (data.code == 200) {
          ElMessage({
            message: '操作成功',
            type: 'success'
          })
          dialogClose()
          emit('updateList')
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        btnLoading.value = false
      })
  }
}

// 关闭弹窗
const dialogClose = () => {
  component?.value?.resetFields && component?.value?.resetFields()
  emit('closeDialog', false)
}
</script>
<style lang="scss" scoped></style>
