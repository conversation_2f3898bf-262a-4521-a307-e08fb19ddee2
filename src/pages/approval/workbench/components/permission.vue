<!--
 * @Description: 许可审批
 * @Author: zwcong
 * @Date: 2024-04-30 10:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-05-11 20:40:07
-->
<template>
  <div class="title">【电气一种票许可审批-工作许可人】需填写补充信息</div>
  <el-form ref="formRef" :model="formData" label-suffix="" :rules="formRules">
    <el-form-item
      label="许可工作开始时间"
      prop="permitWorkStartTime"
      class="form-item-column"
    >
      <el-date-picker
        v-model="formData.permitWorkStartTime"
        type="datetime"
        placeholder="请选择许可工作开始时间"
        format="YYYY-MM-DD HH:mm"
        value-format="YYYY-MM-DD HH:mm"
      />
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import type { FormInstance, FormRules } from 'element-plus'
import useDialogForm from '../hooks/useDialogForm.ts'

const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  permitWorkStartTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm') // 许可工作开始时间
})
defineProps({
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const formRules = reactive<FormRules<Record<string, any>>>({
  permitWorkStartTime: [
    {
      required: true,
      message: '请选择许可工作开始时间',
      trigger: 'change'
    }
  ]
})

const emits = defineEmits(['closeDialog', 'submitData'])
const { submitForm, resetFields } = useDialogForm(
  formRef,
  formData.value,
  emits
)

defineExpose({ submitForm, resetFields })
</script>
<style scoped src="../../assets/dialogForm.scss"></style>
