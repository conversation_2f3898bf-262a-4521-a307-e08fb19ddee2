<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main" :class="`main-${activeName}`">
      <div>
        <el-tabs v-model="activeName" @tab-change="handleTabChange">
          <el-tab-pane label="我的待办" name="first"></el-tab-pane>
          <el-tab-pane label="我的已办" name="second"></el-tab-pane>
          <el-tab-pane label="我发起的" name="third"></el-tab-pane>
        </el-tabs>
      </div>
      <card-pagination
        title-key="title"
        :total="listTotal"
        :loading="tableLoading"
        :finished="finished"
        layout="total, prev, pager, next, jumper"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :data="listData"
        :columns="columns"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #status="{ row }">
          <!-- <el-tag v-if="row.instanceState == 0" type="danger">待流转</el-tag> -->
          <el-tag v-if="row.instanceState == 1" type="warning">审核中</el-tag>
          <el-tag v-if="row.instanceState == 2" type="success">审核通过</el-tag>
          <el-tag v-if="row.instanceState == 3" type="info">审核驳回</el-tag>
        </template>
        <template #viewProcess> 查看审批流程 </template>
        <template v-if="activeName === 'first'" #footer="{ row }">
          <el-button @click="onShowReject(row)"> 驳回 </el-button>
          <el-button
            type="primary"
            :loading="row.loading"
            @click="onShowApprovalDialog(row)"
          >
            通过
          </el-button>
        </template>
        <template v-else #footer="{ row }">
          <el-button type="primary" @click="onViewProcess(row)">
            查看审批流程
          </el-button>
        </template>
      </card-pagination>
    </div>
  </div>
  <!-- 审核记录 -->
  <process-dialog
    :is-show="isShow"
    :rows="rows"
    @close-dialog="() => (isShow = false)"
  ></process-dialog>

  <!-- 审批弹窗 -->
  <approval-dialog
    :is-show="showApprovalDialog"
    :rows="rows"
    :type="dialogType"
    @update-list="getTableData"
    @close-dialog="onCloseApprovalDialog"
  ></approval-dialog>
</template>

<script setup lang="ts">
import { approval as approvalAPI } from '@/api/index.ts'
import cardPagination from '@/components/card-pagination.vue'
import processDialog from './components/processDialog.vue'
import approvalDialog from './components/approvalDialog.vue'
import filter from '@/utils/filter.js'

const router = useRouter()

const activeName = ref('first')
const rows = ref({})

const isShow = ref(false)
const showApprovalDialog = ref(false)

const dialogType = ref('')

// 搜索
const searchData = ref({
  approvalType: '',
  pageNum: 1,
  pageSize: 6
})
const searchProps = ref([
  {
    type: 'select',
    label: '审批类型',
    prop: 'approvalType',
    span: 7,
    width: '68px',
    filterable: true,
    options: []
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 6
  }
  await getTableData()
}

const columns: any = ref([
  {
    prop: 'stationName',
    label: '电站名称',
    notShowItemFn: (row: any) => {
      return (
        row.instanceType === 'EXPERIENCE_FEEDBACK_APPROVAL_KEY' ||
        row.instanceType === 'EMERGENCY_DRILL_APPROVAL_KEY'
      )
    }
  },
  {
    prop: 'startTime',
    label: '发起时间'
  },
  {
    prop: 'detailsName',
    label: '相关记录详情',
    type: 'click',
    click: (row: any) => {
      //JSA票
      if (row.ticketType === 2) {
        router.push(
          `/approval/workbench/JSAInfo/look/${row.detailsId}/approval`
        )
      } else if (row.ticketType === 1) {
        // 工作票
        router.push(`/approval/workbench/detail/${row.detailsId}`)
      } else if (row.ticketType === 3) {
        // 经验反馈单
        router.push(`/approval/workbench/feedback/detail/${row.detailsId}`)
      } else if (row.ticketType === 4) {
        // 应急演练
        router.push(`/approval/emergency/detail/${row.detailsId}`)
      } else if (row.ticketType === 5) {
        // 二种工作票
        router.push(`/approval/workbench/two/detail/${row.detailsId}`)
      } else if (row.ticketType === 6) {
        // 工单
        router.push(`/approval/workbench/lookorder/${row.detailsId}`)
      } else if (row.ticketType === 7) {
        // 操作票
        router.push(`/approval/workbench/handleInfo/look/${row.detailsId}`)
      } else if (row.ticketType === 8) {
        // 操作票
        router.push(`/approval/workbench/location/${row.detailsId}`)
      }
    }
  },
  {
    prop: '',
    label: '审批流程',
    slotName: 'viewProcess',
    className: 'view-process',
    type: 'click',
    click: (row: any) => {
      rows.value = row
      isShow.value = true
    }
  }
])

const onViewProcess = (row: any) => {
  rows.value = row
  isShow.value = true
}
/**
 * 点击通过按钮
 */
const onShowApprovalDialog = (row: any) => {
  const dialogMapping: any = {
    [1]: {
      WORK_ISSUANCE_APPROVA_KEY: 'issuer' // 电气一种工作票签发审批&工作票签发人-工作地点保留带电部分或注意事项（工作票签发人填写）
    },
    [2]: {
      WORK_ISSUANCE_APPROVA_KEY: 'issuance', // 电气一种工作票签发审批&工作许可人-补充安全措施
      WORK_LICENSE_APPROVAL_KEY_NO_TEAM: 'permission', // 电气一种票许可审批&工作许可人-许可工作开始时间
      WORK_TERMINATION_OF_APPROVAL_KEY: 'end', // 电气一种工作票终结审批&工作许可人-补充信息(编号)
      TWO_KINDS_OF_ISSUANCE_APPROVA_KEY: 'issuanceTwo', // 电气二种票签发审批 工作许可人审批
      TWO_KINDS_OF_PERMISSION_APPROVA_KEY: 'permissionTwo' //电气二种票许可审批 值长审核 许可工作时间
    },
    [3]: {
      WORK_ISSUANCE_APPROVA_KEY: 'issuanceLeader', // 电气一种工作票签发审批&值长-批准工作时间
      TWO_KINDS_OF_ISSUANCE_APPROVA_KEY: 'issuanceLeaderTwo' // 电气二种票签发审批  值长 补充信息
    }
  }

  if (dialogMapping[row.userRoleType]?.[row.instanceType]) {
    dialogType.value = dialogMapping[row.userRoleType][row.instanceType]
  }

  if (dialogType.value !== '') {
    rows.value = row
    showApprovalDialog.value = true
  } else {
    onPass(row)
  }
}
/**
 * 通过接口调用
 */
const onPass = async (row: any) => {
  row.loading = true
  //通过
  await approvalAPI
    .passInstance({
      detailsId: row.detailsId,
      instanceId: row.instanceId,
      instanceType: row.instanceType,
      runTimeTaskId: row.runTimeTaskId,
      ticketType: row.ticketType,
      userRoleType: row.userRoleType,
      nextUserId: row.nextUserId,
      nextUserName: row.nextUserName
    })
    .then(({ data }) => {
      if (data.code == 200) {
        ElMessage({
          message: '操作成功',
          type: 'success'
        })
        getTableData()
      } else {
        ElMessage({
          message: data.message,
          type: 'error'
        })
      }
    })
    .catch((err) => {
      console.log(err)
    })
    .finally(() => {
      row.loading = false
    })
}
/**
 * 显示驳回弹窗
 */
const onShowReject = (row: any) => {
  dialogType.value = 'reject'
  rows.value = row
  showApprovalDialog.value = true
}

/**
 * 显示审批弹窗
 */
const onCloseApprovalDialog = () => {
  dialogType.value = ''
  showApprovalDialog.value = false
}

const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const finished = ref(false)

const apiMethods = {
  first: approvalAPI.getMyToDoList,
  second: approvalAPI.getMyHaveDoneList,
  third: approvalAPI.getMyInitiateList
}

const fetchData = async (apiMethod: any, params: Obj) => {
  let { data } = await apiMethod(params, [tableLoading])
  return data.data
}

const getTableData = async () => {
  try {
    let result = await fetchData(apiMethods[activeName.value], {
      ...searchData.value
    })
    listTotal.value = result.total

    listData.value = result.taskBoList.map((item: any) => ({
      ...item,
      loading: false,
      title: getTitle(item)
    }))
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  } finally {
    finished.value = true
  }
}

const getTitle = (item: Obj) => {
  const instanceText = filter.instanceType(item.instanceType)
  const starterText = `【${item.starterUserName}】发起的【${instanceText}】`

  let title = starterText
  if (
    item.instanceType === 'WORK_LICENSE_APPROVAL_KEY_NO_TEAM' &&
    item?.userList?.length
  ) {
    const userListNames = item.userList
      .slice(0, 2)
      .map((user) => user.name)
      .join('、')
      .concat(item.userList.length > 2 ? '等' : '')

    title +=
      item.instanceState === 3
        ? `被【${userListNames}】审核驳回`
        : item.instanceState === 2
        ? `被【${userListNames}】审核通过`
        : `待【${userListNames}】进行审批`
  } else {
    const nextStateUser = item.nextUserName
    title +=
      item.instanceState === 3
        ? `被【${nextStateUser}】审核驳回`
        : item.instanceState === 2
        ? `被【${nextStateUser}】审核通过`
        : `待【${nextStateUser}】进行审批`
  }

  return title
}

const handleSizeChange = (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: any) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const viewProcessCss = ref('flex')
const handleTabChange = () => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = 6
  viewProcessCss.value = activeName.value === 'first' ? 'flex' : 'none'
  getTableData()
}

const getApprovalType = () => {
  searchProps.value[0].options = Object.entries(filter.instanceType()).map(
    ([key, value]) => ({
      label: value,
      value: key
    })
  )
}

onMounted(() => {
  getTableData()
  getApprovalType()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.page-main {
  :deep(.view-process) {
    display: v-bind(viewProcessCss);
  }
  .main-first {
    :deep(.card-body) {
      min-height: 183px;
    }
  }
}
</style>
