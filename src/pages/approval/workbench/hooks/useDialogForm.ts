/*
 * @Description:
 * @Author: zwcong
 * @Date: 2024-04-30 14:09:07
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-09-13 11:03:00
 */

export default function (formRef: any, formData: any, emits: any) {
  const btnLoading = ref(false)

  /**
   * 提交表单
   * @returns void
   */
  const submitForm = async () => {
    if (!formRef.value) return
    await formRef.value.validate(async (valid: any) => {
      if (valid) {
        btnLoading.value = true
        console.log('submit...')
        emits('submitData', formData)
      }
    })
  }

  const resetFields = () => {
    formRef.value && formRef.value.resetFields()
  }

  return {
    submitForm,
    resetFields
  }
}
