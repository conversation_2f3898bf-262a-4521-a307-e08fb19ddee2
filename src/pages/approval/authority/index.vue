<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>审批权限列表</p>
        <div>
          <el-button class="new-role-btn" @click="showRole = true"
            >新建审批角色</el-button
          >
          <el-button type="primary" @click="editDetail(row, 'add')"
            ><el-icon> <Plus /> </el-icon>添加人员</el-button
          >
        </div>
      </div>
      <vis-table-pagination
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="columns"
        :total="listTotal"
        :data="listData"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="editDetail(row, 'edit')">编辑</el-button>
            <el-popconfirm title="确定要删除吗？" @confirm="handleDelete(row)">
              <template #reference>
                <el-button link @click.stop>删除</el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
  <!-- 新建审批角色弹窗  -->
  <add-role :is-show="showRole" @close-dialog="showRole = false"></add-role>
  <!-- 新建人员/编辑人员弹窗 -->
  <add-personnel
    :is-show="showPersonnel"
    :type-val="typeVal"
    :rows="rows"
    @close-dialog="showPersonnel = false"
    @update-list="getTableData"
  ></add-personnel>
</template>

<script setup lang="ts">
import { approval as approvalAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import addRole from './components/addRole.vue'
import addPersonnel from './components/addPersonnel.vue'

// 搜索
const searchData = ref({
  userNo: '',
  userName: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    prop: 'userNo',
    label: '用户编码',
    span: 8,
    width: '68px'
  },
  {
    prop: 'userName',
    label: '用户姓名',
    span: 8,
    width: '110px'
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  await getTableData()
}

const columns = [
  {
    prop: 'ucUserId',
    label: '用户编码',
    minWidth: 70
  },
  {
    prop: 'ucUserName',
    label: '姓名',
    minWidth: 100
  },
  {
    prop: 'ucUserPhone',
    label: '手机号',
    minWidth: 100
  },
  {
    prop: 'roleList',
    label: '审批角色',
    minWidth: 220,
    formatter: (row: Obj) => {
      return row.roleList.map((item: Obj) => item.roleName).join(',')
    }
  },
  {
    prop: 'createUser',
    label: '创建人',
    minWidth: 100,
    formatter: (row: Obj) => {
      return row.createUser ? row.createUser?.split('_')[0] : '--'
    }
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: 150
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 130,
    fixed: 'right'
  }
]

// 弹窗
const showRole = ref(false)
const showPersonnel = ref(false)
const typeVal = ref('')
const rows = ref({})
const editDetail = (ag1: any, ag2: string) => {
  switch (ag2) {
    case 'add': // 新增
      showPersonnel.value = true
      rows.value = {}
      typeVal.value = 'add'
      break
    case 'edit': // 编辑
      showPersonnel.value = true
      rows.value = ag1
      typeVal.value = 'edit'
      break
    default:
      return
  }
}

const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await approvalAPI.getApprovalUserRoleList(
      { ...searchData.value },
      [tableLoading]
    )
    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}
const handleDelete = async (row: any) => {
  await approvalAPI
    .deleteApprovalUserRole({
      id: row.id
    })
    .then(({ data }: any) => {
      if (data.code == '200') {
        if (data.data) {
          ElMessage({
            message: '删除成功',
            type: 'success'
          })
          getTableData()
        } else {
          ElMessage({
            message: data.message,
            type: 'warning'
          })
        }
      }
    })
    .catch((err: any) => {
      console.log(err)
    })
}

onMounted(() => {
  getTableData()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.new-role-btn {
  &.el-button:focus {
    color: #666;
    border-color: #e6e6e8;
    background-color: #fff;
  }
}
</style>
