<!--
 * @Description: 新建审批角色
 * @Author: zwcong
 * @Date: 2024-04-29 15:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-05-23 09:55:25
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="新建审批角色"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="446px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-suffix=""
        :rules="formRules"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model.trim="formData.roleName"
            :maxlength="64"
            autocomplete="off"
            placeholder="请输入角色名称"
          />
        </el-form-item>
        <el-form-item label="角色编码" prop="roleNo">
          <el-input
            v-model.trim="formData.roleNo"
            :maxlength="64"
            autocomplete="off"
            placeholder="请输入角色编码"
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="submitForm()"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { approval as approvalAPI } from '@/api/index.ts'
import type { FormInstance, FormRules } from 'element-plus'
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  roleName: '', // 角色名称
  roleNo: '' // 角色编码
})
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  }
})

const dialogVisible = ref(false)
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
  },
  {
    deep: true
  }
)

const formRules = reactive<FormRules<Record<string, any>>>({
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  roleNo: [{ required: true, message: '请输入角色编码', trigger: 'blur' }]
})

const btnLoading = ref(false)

// 点击确定
const emit = defineEmits(['closeDialog', 'updateList'])
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      btnLoading.value = true

      await approvalAPI
        .addApprovalRole({
          ...formData.value
        })
        .then(({ data }) => {
          if (data.code == 200) {
            ElMessage({
              message: '新建成功',
              type: 'success'
            })
            dialogClose()
          } else {
            ElMessage({
              message: data.message,
              type: 'error'
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          btnLoading.value = false
        })
    }
  })
}
// 关闭弹窗
const dialogClose = () => {
  formRef.value && formRef.value.resetFields()
  emit('closeDialog', false)
}
</script>
<style lang="scss" scoped></style>
