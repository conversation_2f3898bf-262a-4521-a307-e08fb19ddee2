<!--
 * @Description: 新建审批角色
 * @Author: zwcong
 * @Date: 2024-04-29 15:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-05-15 10:34:56
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="titleVal"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="546px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-suffix=""
        :rules="formRules"
      >
        <el-form-item label="选择人员" prop="ucUserId">
          <el-select
            v-model="formData.ucUserId"
            placeholder="请选择人员"
            filterable
            :disabled="typeVal === 'edit'"
          >
            <el-option
              v-for="item in personnelList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审批角色" prop="roleNo">
          <el-select
            v-model="formData.roleNo"
            placeholder="请选择审批角色"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            filterable
          >
            <el-option
              v-for="item in roleList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="submitForm()"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { approval as approvalAPI } from '@/api/index.ts'
import type { FormInstance, FormRules } from 'element-plus'
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  ucUserId: '',
  roleNo: []
})
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  typeVal: {
    type: String,
    default: '',
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const dialogVisible = ref(false)
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      getAllPersonnel()
      getRoleList()
      onDetail()
    }
  },
  {
    deep: true
  }
)

const titleVal = ref('标题')
watch(
  () => props.typeVal,
  (val: string) => {
    if (val === 'add') {
      titleVal.value = '添加人员'
    } else if (val === 'edit') {
      titleVal.value = '编辑审批权限'
    }
  }
)

const onDetail = () => {
  if (props.typeVal === 'edit') {
    formData.value.ucUserId =
      props.rows.ucUserName + ' ' + props.rows.ucUserPhone
    props.rows.roleList.forEach((item: any) => {
      formData.value.roleNo.push(item.roleNo)
    })
  }
}

const formRules = reactive<FormRules<Record<string, any>>>({
  ucUserId: [{ required: true, message: '请选择审批人员', trigger: 'change' }],
  roleNo: [{ required: true, message: '请选择审批角色', trigger: 'change' }]
})

const personnelList = ref<Obj[]>([])
const roleList = ref<Obj[]>([])
const personnelListData = ref<Obj[]>([])

const btnLoading = ref(false)

// 点击确定
const emit = defineEmits(['closeDialog', 'updateList'])
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      btnLoading.value = true

      const userInfo = personnelListData.value.find(
        (item) => item.id === formData.value.ucUserId
      )
      const params = {
        ucUserId: userInfo?.id,
        ucUserName: userInfo?.employeeName,
        ucUserPhone: userInfo?.employeePhone,
        approvalRoleNo: formData.value.roleNo.join(',')
      }

      if (props.typeVal == 'add') {
        await approvalAPI
          .addApprovalUserRole(params, false)
          .then(({ data }) => {
            if (data.code == 200) {
              ElMessage({
                message: '添加成功',
                type: 'success'
              })
              dialogClose()
              emit('updateList')
            } else {
              ElMessage({
                message: data.message,
                type: 'error'
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            btnLoading.value = false
          })
      } else {
        await approvalAPI
          .updateApprovalUserRole(
            {
              ...params,
              id: props.rows.id
            },
            false
          )
          .then(({ data }) => {
            if (data.code == 200) {
              ElMessage({
                message: '编辑成功',
                type: 'success'
              })
              dialogClose()
              emit('updateList')
            } else {
              ElMessage({
                message: data.message,
                type: 'error'
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            btnLoading.value = false
          })
      }
    }
  })
}
// 关闭弹窗
const dialogClose = () => {
  formRef.value && formRef.value.resetFields()
  formData.value.ucUserId = ''
  formData.value.roleNo = []
  emit('closeDialog', false)
}

const getAllPersonnel = async () => {
  let { data } = await approvalAPI.getUserInfoList({
    pageSize: 10000,
    pageNo: 1
  })
  if (data && data.data) {
    personnelListData.value = data.data.list
    personnelList.value = data.data.list
      .filter((item: any) => item.id != null)
      .map(({ employeeName, employeePhone, id }: any) => ({
        label: `${employeeName} ${employeePhone}`,
        value: id
      }))
  }
}

const getRoleList = async () => {
  let { data } = await approvalAPI.getRoleList()
  if (data && data.data) {
    roleList.value = data.data.map(
      (item: { roleName: string; roleNo: string; id: number | string }) => {
        return {
          label: item.roleName,
          value: item.roleNo,
          id: item.id
        }
      }
    )
  }
}
</script>
<style lang="scss" scoped></style>
