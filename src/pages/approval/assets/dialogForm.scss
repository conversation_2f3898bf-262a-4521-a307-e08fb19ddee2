.form-item-column {
  flex-direction: column;

  :deep(.el-form-item__label) {
    justify-content: flex-start;
  }

  :deep(.el-textarea .el-input__count) {
    bottom: 5px !important;
  }
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  margin-bottom: 10px;
}
.sub-title{
    margin-bottom: 8px;
    padding-left: 12px;
    position: relative;
    color: rgba(0, 0, 0, 0.85);

    &:after{
        content: '';
        position: absolute;
        left: 0;
        top: 2.5px;
        width: 4px;
        height: 16px;
        background: #2ACBA0;
    }
}
.tips{
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 24px;
}