<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="100px"
        @submit-emits="handleSearch"
        @change-height="changeHeight"
      ></searchForm>
    </div>
    <div class="main">
      <div class="operate">
        <p>考试列表</p>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新建考试
        </el-button>
      </div>
      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="tableColumns"
        :total="tableData.total"
        :data="tableData.data"
        :height="tableHeight"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="toDetail(row)">查看详情</el-button>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import { educate as educateAPI } from '@/api/index.ts'

const router = useRouter()

let tableHeight = ref(window.innerHeight - 352)
const changeHeight = (formHeight: number) => {
  tableHeight.value = window.innerHeight - formHeight - 302
}

let searchData = ref({
  examType: '', //  考试类型
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    label: '考试类型',
    width: '74px',
    prop: 'examType',
    type: 'select',
    options: [
      { label: '工作许可人考试', value: 1 },
      { label: '工作负责人考试', value: 2 },
      { label: '工作票签发人考试', value: 3 }
    ]
  }
])
const tableColumns = [
  { prop: 'examNo', label: '考试编号', minWidth: 140 },
  { prop: 'examTypeName', label: '考试类型', minWidth: 124 },
  { prop: 'examTitle', label: '考试主题', minWidth: 100 },
  { prop: 'examNum', label: '考试人数' },
  { prop: 'examLocation', label: '考试地点', minWidth: 100 },
  { prop: 'examStartTime', label: '考试开始时间', minWidth: 110 },
  { prop: 'examEndTime', label: '考试结束时间', minWidth: 110 },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 80,
    fixed: 'right'
  }
]
const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageNum = 1
  searchData.value.pageSize = 10
  getTableData()
}

const getTableData = async () => {
  try {
    let { data } = await educateAPI.getExamList(searchData.value, true)
    tableData.data = data?.data?.records || []
    tableData.total = data?.data?.total || 0
  } catch (e: any) {
    tableData.data = []
    tableData.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

onMounted(async () => {
  getTableData()
})

const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const handleAdd = async () => {
  router.push({
    name: 'EducateExamAdd'
  })
}
const toDetail = async (row: any) => {
  router.push({
    name: 'EducateExamDetail',
    query: {
      id: row.id
    }
  })
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
