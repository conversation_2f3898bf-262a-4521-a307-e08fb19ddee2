<script setup lang="ts">
import TablePagination from '@/components/table-pagination.vue'
import useTableData from '@/hooks/useTableData.ts'

const columns: Obj[] = [
  {
    prop: 'userName',
    label: '考试人员'
  },
  {
    prop: 'examGrade',
    label: '考试成绩',
    slotName: 'examGrade'
  },
  {
    prop: 'makeUpExamGrade',
    label: '补考成绩',
    slotName: 'makeUpExamGrade'
  },
  {
    prop: 'examResult',
    label: '考试结果',
    slotName: 'examResult'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 60,
    fixed: 'right'
  }
]

const formData = reactive(
  (function () {
    return {
      row: [Object.fromEntries(columns.map((e) => [e.prop || e.slotName, '']))]
    }
  })()
)
const formRef = ref()
const formKey = ref(0)
const localData = ref<any[]>([])
const getTableData: any = async () => {
  const offer = (tablePage.pageNum - 1) * tablePage.pageSize
  const data = {
    total: localData.value.length,
    records: localData.value.slice(offer, offer + tablePage.pageSize)
  }
  return { data }
}
const {
  tableData,
  tablePage,
  tableLoading,
  changeCurrent,
  changeSize,
  changeData
} = useTableData(getTableData)
const emit = defineEmits(['echoData'])

const add = async (arr: Obj[]) => {
  localData.value = arr.map((e: any) => {
    return {
      ...e,
      examResult: 1,
      ...localData.value.find((o: any) => o.id === e.id)
    }
  })
  tablePage.pageNum = 1
  await changeData()
  formData.row = tableData.data
  formKey.value = Date.now()
  emit('echoData', localData.value)
}
const clear = async () => {
  localData.value = []
  await changeData()
  formData.row = []
  emit('echoData', localData.value)
}

const submit = async () => {
  if (localData.value.length === 0) {
    ElMessage({
      type: 'warning',
      message: '请添加人员！'
    })
    return
  }
  if (
    await formRef.value.validate(async (valid: boolean) => {
      if (!valid) {
        ElMessage({
          type: 'warning',
          message: '您在当前分页有考试成绩未填写，请检查！'
        })
      }
    })
  ) {
    const checkall = localData.value.every((e: any) => {
      return e.examGrade
    })
    if (!checkall) {
      ElMessage({
        type: 'warning',
        message: `您在其他分页有考试成绩未填写，请检查！`
      })
      return
    }
    return localData.value
  }
}
const deleteItem = async (row: Obj) => {
  if (tablePage.pageSize === 0) return
  localData.value = localData.value.filter((e) => e.id !== row.id)
  // tablePage.pageNum = 1
  const totalPage = Math.ceil(tableData.total / tablePage.pageSize)
  tablePage.pageNum =
    tablePage.pageNum > totalPage ? totalPage : tablePage.pageNum
  await changeData()
  formData.row = tableData.data
  formKey.value = Date.now()
  emit('echoData', localData.value)
}

const limitExamGrade = (row: Obj, prop: string) => {
  if (row[prop] === 0 || row[prop] === '0') return
  row[prop] = (row[prop] && String(row[prop]).replace(/^(0)|\D/g, '')) || ''
}
defineExpose({
  add,
  submit,
  clear
})
</script>
<template>
  <el-form ref="formRef" :key="formKey" :model="formData">
    <TablePagination
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :show-overflow-tooltip="true"
      background
      :columns="columns"
      :data="tableData.data"
      :total="tableData.total"
      :loading="tableLoading"
      :current-page="tablePage.pageNum"
      :page-size="tablePage.pageSize"
      class="table-pagination"
      @handle-size-change="
        async (params: Obj) => (
          await changeSize(params),
          (formData.row = tableData.data),
          (formKey = Date.now())
        )
      "
      @handle-current-change="
        async (params: Obj) => (
          await changeCurrent(params),
          (formData.row = tableData.data),
          (formKey = Date.now())
        )
      "
    >
      <!-- @vue-ignore -->
      <template #examGrade="{ row, index }">
        <el-form-item
          v-if="index >= 0"
          :key="row.id"
          :prop="`row.${index}.examGrade`"
          :show-message="false"
          :rules="{
            required: true,
            message: '',
            trigger: 'blur'
          }"
          class="w-100px"
        >
          <el-input
            v-model.trim="row.examGrade"
            :maxlength="3"
            @input="() => limitExamGrade(row, 'examGrade')"
          />
        </el-form-item>
      </template>
      <!-- @vue-ignore -->
      <template #makeUpExamGrade="{ row, index }">
        <el-form-item
          v-if="index >= 0"
          :key="row.id"
          :prop="`row.${index}.makeUpExamGrade`"
          :show-message="false"
          class="w-100px"
        >
          <el-input
            v-model.trim="row.makeUpExamGrade"
            :maxlength="3"
            @input="() => limitExamGrade(row, 'makeUpExamGrade')"
          />
        </el-form-item>
      </template>
      <!-- @vue-ignore -->
      <template #examResult="{ row, index }">
        <el-form-item
          v-if="index >= 0"
          :key="row.id"
          :prop="`row.${index}.examResult`"
          :show-message="false"
          class="w-100px"
        >
          <el-select v-model="row.examResult" placeholder="请选择考试结果">
            <el-option label="通过" :value="1"></el-option>
            <el-option label="未通过" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </template>
      <template #operate="{ row }">
        <el-popconfirm title="确认删除？" @confirm="deleteItem(row)">
          <template #reference>
            <el-button link>删除</el-button>
          </template>
        </el-popconfirm>
      </template>
    </TablePagination>
  </el-form>
</template>
<style scoped lang="scss">
:deep(.el-table .el-form-item) {
  margin-bottom: 0 !important;
  .el-input__wrapper {
    height: inherit !important;
    line-height: inherit !important;
  }
}
.el-table-fixed-column--right {
  .el-button.is-link {
    color: #29cca0 !important;
    &:hover {
      color: var(--el-button-hover-link-text-color) !important;
    }
  }
}
:deep(.el-table .cell) {
  overflow: inherit;
}
</style>
