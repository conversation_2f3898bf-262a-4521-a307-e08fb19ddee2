<template>
  <div class="page-operate">
    <div class="operate-title">查看详情</div>
  </div>
  <div class="info-base">
    <div class="operate">
      <p>考试信息</p>
    </div>
    <el-form ref="companyFormRef" label-suffix="" label-width="94px">
      <el-row :gutter="24" class="info">
        <el-col :span="12">
          <el-form-item label="考试类型">
            {{ examData?.examTypeName }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="考试主题">
            {{ examData?.examTitle || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="考试时间">
            {{ examData?.examStartTime }} - {{ examData?.examEndTime }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="考试地点" class="patorSps">
            {{ examData?.examLocation || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="关联培训">
            {{ examData?.trainName || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="考试记录表"
            :class="files?.length ? 'hasImg' : 'noImg'"
          >
            <SpicUpload v-model="files" type="image" :limit="5" />
          </el-form-item>
          <div class="waringInfoBas__">
            <el-icon color="#FF9900">
              <WarningFilled />
            </el-icon>
            提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>

  <div class="info-tab info-tab-height-show">
    <div class="operate">
      <p>考试人员</p>
    </div>
    <vis-table-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchData.pageSize"
      :current-page="searchData.pageNum"
      :columns="tableColumns"
      :total="tableData.total"
      :data="tableData.data"
      :show-overflow-tooltip="true"
      background
      class="vis-table-pagination"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    >
      <template #makeUpExamGrade="{ row }">
        {{ row.makeUpExamGrade || '--' }}
      </template>
    </vis-table-pagination>
  </div>
  <div class="page-footer">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button type="primary" @click="onSumbit" v-preventReClick="1000">
      提交
    </el-button>
  </div>
</template>

<script setup lang="ts">
import SpicUpload from '@/components/spic-upload'
import visTablePagination from '@/components/table-pagination.vue'
import { educate as educateAPI } from '@/api/index.ts'

const router = useRouter()
const route = useRoute()

let files = ref<any[]>([])

let searchData = ref({
  recordsId: route.query.id,
  pageNum: 1,
  pageSize: 10
})
const tableColumns = [
  { prop: 'operationUserName', label: '考试人员', minWidth: 100 },
  { prop: 'examGrade', label: '考试成绩', minWidth: 100 },
  {
    prop: 'makeUpExamGrade',
    label: '补考成绩',
    slotName: 'makeUpExamGrade',
    minWidth: 100
  },
  { prop: 'examResultStr', label: '考试结果' }
]
onMounted(async () => {
  getExamDetail()
  getTableData()
})
let examData: any = ref()
// 获取考试详情
const getExamDetail = async () => {
  try {
    let { data } = await educateAPI.getExamDetail({ id: route.query.id }, true)
    examData.value = data.data
    if (data.data.examRecord) {
      files.value = data.data.examRecord.split(',')
    }
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
let examRecord: any = ref()
watch(
  () => files.value,
  (val) => {
    examRecord.value = val.length ? val.join(',') : ''
  },
  {
    deep: true
  }
)
const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
// 获取考试人员列表
const getTableData = async () => {
  try {
    let { data } = await educateAPI.getExamUserList(searchData.value, true)
    tableData.data = data?.data?.records || []
    tableData.total = data?.data?.total || 0
  } catch (e: any) {
    tableData.data = []
    tableData.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
const onSumbit = async () => {
  const { status } = await educateAPI.examRecordsEdit({
    examRecord: examRecord.value,
    id: route.query.id
  })
  if (status == 200) {
    ElMessage({
      type: 'success',
      message: '提交成功！'
    })
    router.push('/educate/exam')
  } else {
    ElMessage({
      message: '提交失败',
      type: 'error'
    })
  }
}
const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}
const onCancel = () => {
  router.go(-1)
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>

<style lang="scss" scoped>
.page-container {
  overflow: scroll;
}
.info-base {
  padding-bottom: 0;
  .operate {
    p {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
.hasImg {
  margin-bottom: 0px !important;
}
.noImg {
  margin-bottom: 8px !important;
}
.info-tab {
  .operate {
    p {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
:deep(.el-form .el-form-item--default .el-form-item__label) {
  color: rgba(0, 0, 0, 0.45) !important;
}
:deep(.el-form .el-form-item--default .el-form-item__content > span) {
  color: rgba(0, 0, 0, 0.85) !important;
}
:deep(.el-form-item--default .el-form-item__label) {
  height: 22px !important;
  line-height: 22px !important;
  padding-right: 8px;
}
:deep(.el-form-item--default .el-form-item__content) {
  line-height: 22px !important;
}
.page-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
  line-height: 64px;
  margin: 0 24px 24px 24px;
  height: 64px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  & > .el-button + .el-button {
    margin-left: 16px !important;
  }
  & > .el-button {
    width: 72px !important;
    height: 40px !important;
  }
}
.waringInfoBas__ {
  padding: 0 0 24px 94px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  color: #ff9900;
  font-size: 12px;
  .el-icon {
    margin: 0 4px 0 0;
    font-size: 16px;
  }
}
.info-tab-height-show {
  min-height: 528px;
}
.patorSps {
  line-height: 40px !important;
  word-wrap: break-word;
  word-break: break-all;
}
</style>
