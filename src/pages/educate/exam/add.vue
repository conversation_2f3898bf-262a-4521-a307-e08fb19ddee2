<template>
  <div class="page-operate">
    <div class="operate-title">新建考试</div>
  </div>
  <div class="info-base">
    <div class="operate">
      <p>考试信息</p>
    </div>
    <div class="info">
      <el-form
        ref="formRef"
        :rules="formRules"
        :model="formData"
        label-suffix=""
        label-position="right"
        :inline="true"
        label-width="84px"
        class="w-full"
      >
        <el-row :gutter="24" justify="space-between">
          <el-col :span="10">
            <el-form-item label="考试类型" prop="examType">
              <el-select
                v-model="formData.examType"
                placeholder="请选择考试类型"
              >
                <el-option label="工作许可人考试" :value="1"></el-option>
                <el-option label="工作负责人考试" :value="2"></el-option>
                <el-option label="工作票签发人考试" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="2">
            <el-form-item label="考试主题" prop="examTitle">
              <el-input
                v-model.trim="formData.examTitle"
                placeholder="请输入考试主题"
                autocomplete="off"
                maxlength="20"
                clearable
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="考试时间" prop="examTime">
              <el-date-picker
                v-model="formData.examTime"
                type="datetimerange"
                :editable="false"
                start-placeholder="请选择考试开始时间"
                end-placeholder="请选择考试结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="2">
            <el-form-item label="考试地点" prop="examLocation">
              <el-input
                v-model.trim="formData.examLocation"
                placeholder="请输入考试地点"
                autocomplete="off"
                maxlength="50"
                clearable
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="关联培训" prop="trainId">
              <el-select
                v-model="formData.trainId"
                placeholder="请选择关联培训"
                autocomplete="off"
                clearable
                filterable
                :filter-method="filterTrain"
              >
                <el-option
                  v-for="item in filterData"
                  :key="item.id"
                  :label="item.training_manage_no"
                  :value="item.id"
                >
                  {{ item.training_manage_no }}-({{ item.training_title }})
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="考试记录表"
              :class="files?.length ? 'hasImg' : 'noImg'"
            >
              <SpicUpload v-model="files" type="image" :limit="5" />
            </el-form-item>
            <div class="waringInfoBas__">
              <el-icon color="#FF9900">
                <WarningFilled />
              </el-icon>
              提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <div class="demo-date-picker"></div>
  <div class="info-tab info-tab-height-edit">
    <div class="operate">
      <p>考试人员</p>
      <el-button type="primary" @click="selectPerson">
        <el-icon><Plus /></el-icon>
        添加人员
      </el-button>
    </div>
    <SubstanceTable ref="substanceTableRef" @echo-data="echoData" />
  </div>
  <div class="page-footer">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button type="primary" @click="onSumbit" v-preventReClick="1000">
      提交
    </el-button>
  </div>
  <select-staff
    :arr-list="arrList"
    :is-show="isShow"
    @uplate-data="getGoodsData"
    @close-dialog="isShow = false"
  ></select-staff>
</template>

<script setup lang="ts">
import SubstanceTable from './components/substanceTable.vue'
import selectStaff from '../components/selectStaff.vue'
import SpicUpload from '@/components/spic-upload'
import { educate as educateAPI } from '@/api/index.ts'

const router = useRouter()

onMounted(async () => {
  getPxData()
})

const substanceTableRef = ref()
let files = ref<any[]>([])
watch(
  () => files.value,
  (val) => {
    formData.value.examRecord = val.length ? val.join(',') : ''
  },
  {
    deep: true
  }
)
const isShow = ref<boolean>(false)
const selectPerson = () => {
  isShow.value = true
}
const getGoodsData = (arr: Obj[]) => {
  substanceTableRef.value.add(
    arr.map((e: Obj) => {
      return {
        ...e,
        oldInventoryNum_: e.inventoryNum
      }
    })
  )
}
const arrList = ref([])
const echoData = (val: any) => {
  arrList.value = val
}

const formRef = ref()
const formRules = reactive<any>({
  examType: [{ required: true, message: '请选择考试类型', trigger: 'change' }],
  examTitle: [{ required: true, message: '请输入考试主题', trigger: 'blur' }],
  examTime: [
    {
      required: true,
      message: '请选择考试时间',
      trigger: 'change'
    }
  ],
  examLocation: [
    { required: true, message: '请输入考试地点', trigger: 'blur' }
  ],
  trainId: [
    {
      required: true,
      message: '请选择关联培训',
      trigger: 'change'
    }
  ]
})

const formData = ref<Record<string, any>>({
  examType: '', // 考试类型
  examTitle: '', // 考试主题
  examTime: '', // 考试时间
  examLocation: '', // 考试地点
  trainId: '', // 关联培训id
  examRecord: '' // 考试记录表 上传图片
})

const onSumbit = async () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const data = await substanceTableRef.value.submit()
      if (data) {
        const { status } = await educateAPI.examRecordsAdd({
          examType: formData.value.examType,
          examTitle: formData.value.examTitle,
          examStartTime: formData.value.examTime[0] + ':00',
          examEndTime: formData.value.examTime[1] + ':00',
          examLocation: formData.value.examLocation,
          trainId: formData.value.trainId,
          examRecord: formData.value.examRecord,
          examUserList: data.map((e: any) => {
            return {
              examGrade: e.examGrade,
              examResult: e.examResult,
              makeUpExamGrade: e.makeUpExamGrade,
              operationUserId: e.id,
              ucUserId: e.ucUserId || ''
            }
          })
        })
        if (status == 200) {
          ElMessage({
            type: 'success',
            message: '提交成功！'
          })
          router.push('/educate/exam')
        } else {
          ElMessage({
            message: '提交失败',
            type: 'error'
          })
        }
      }
    }
  })
}

// 获取培训列表
let trainData: any = ref()
let filterData: any = ref()
const getPxData = async () => {
  try {
    let { data } = await educateAPI.getExamTrainList({}, true)
    trainData.value = data.data
    filterData.value = data.data
  } catch (e: any) {
    trainData.value = []
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
const filterTrain = (val: any) => {
  if (val) {
    filterData.value = trainData.value.filter((row: any) =>
      row.training_title.toLowerCase().includes(val)
    )
  } else {
    filterData.value = trainData.value
  }
}

const onCancel = () => {
  router.go(-1)
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.info-base {
  padding-bottom: 0;
  .operate {
    p {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
.info-tab {
  .operate {
    margin-bottom: 24px;
    p {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.85);
    }
    .el-icon {
      margin-right: 4px;
    }
  }
}
.page-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
  line-height: 64px;
  margin: 0 24px 24px 24px;
  height: 64px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  & > .el-button + .el-button {
    margin-left: 16px !important;
  }
  & > .el-button {
    width: 72px !important;
    height: 40px !important;
  }
}
.p_select {
  padding: 20px !important;
  :deep(.el-input__wrapper) {
    height: 40px;
    margin-bottom: 16px;
  }
  :deep(.el-input__wrapper .el-input__inner::placeholder) {
    font-size: 14px;
    -webkit-text-fill-color: rgba(0, 0, 0, 0.25) !important;
  }
  :deep(.el-table__default) {
    background: red;
  }
}
.mx-1 {
  color: rgba(46, 141, 230, 1);
  font-size: 14px;
  font-weight: 400;
}
.mx-2 {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-weight: 400;
}
:deep(.el-table__empty-text) {
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
  font-weight: 400;
}
.waringInfoBas__ {
  padding: 0 0 24px 84px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  color: #ff9900;
  font-size: 12px;
  .el-icon {
    margin: 0 4px 0 0;
    font-size: 16px;
  }
}
.hasImg {
  margin-bottom: 0px !important;
}
.noImg {
  margin-bottom: 8px !important;
}
.info-tab-height-edit {
  min-height: 528px;
}
.nodata {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  > span {
    margin-right: 8px;
    font-size: 14px;
    color: rgb(153, 153, 153);
  }
  .el-icon {
    cursor: pointer;
  }
}
</style>
<style lang="scss">
.p_table {
  width: 450px !important;
  .el-table__row .radio-selection {
    .cell {
      display: none;
    }
    &::before {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      border: solid 1px rgba(0, 0, 0, 0.15);
      border-radius: 50%;
      cursor: pointer;
    }
  }
  .el-table__row.current-row .radio-selection::before {
    border: solid 3px rgba(41, 204, 160, 1);
  }
}
</style>
