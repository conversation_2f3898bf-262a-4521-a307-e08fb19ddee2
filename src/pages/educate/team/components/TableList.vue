<template>
  <TablePagination
    ref="tableList"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :show-overflow-tooltip="true"
    background
    :columns="columns"
    :data="tableData.data"
    :total="tableData.total"
    :loading="tableLoading"
    :current-page="tablePage.pageNum"
    :page-size="tablePage.pageSize"
    class="table-pagination"
    @handle-size-change="changeSize"
    @handle-current-change="changeCurrent"
  >
    <template #goodsTypeName="{ row }">
      <el-radio
        v-model="isLeader"
        size="large"
        :label="row.id"
        class="ml-15px"
        @change="getCurrentRow(row)"
      >
      </el-radio>
    </template>
    <template #operate="{ row }">
      <el-popconfirm title="确认删除？" @confirm="deleteItem(row)">
        <template #reference>
          <el-button link>删除</el-button>
        </template>
      </el-popconfirm>
    </template>
  </TablePagination>
</template>
<script setup lang="ts">
import TablePagination from '@/components/table-pagination.vue'
import useTableData from '@/hooks/useTableData.ts'
import { team as teamAPI } from '@/api'

interface Props {
  type?: 'add' | 'detail'
}
const props = withDefaults(defineProps<Props>(), {
  type: 'detail'
})

const isLeader = ref(false)
const tableList = ref<any>(null)

const columns: Obj[] = [
  {
    prop: 'goodsTypeName',
    label: '班组组长',
    slotName: 'goodsTypeName',
    width: 120
  },
  {
    prop: 'userName',
    label: '姓名'
  },
  {
    prop: 'userAge',
    label: '年龄'
  },
  {
    prop: 'userSex',
    label: '性别',
    formatter: (v: any) => {
      return v.userSex == 1 ? `男` : '女'
    }
  },
  {
    prop: 'userPhone',
    label: '手机号'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    fixed: 'right',
    width: 80
  }
]

const getCurrentRow = (row: any) => {
  isLeader.value = row.id
  localData.value.forEach((item) => {
    if (item.id == row.id) {
      item.isLeader = 1
    } else {
      item.isLeader = 2
    }
  })
}

const route = useRoute()
const localData = ref<any[]>([])

const getTableData: any = async () => {
  const offer = (tablePage.pageNum - 1) * tablePage.pageSize
  const data = {
    total: localData.value.length,
    records: localData.value.slice(offer, offer + tablePage.pageSize)
  }
  return { data }
}
const tableLoading = ref(false)
onMounted(async () => {
  if (props.type === 'detail') {
    const { data } = await teamAPI.getTeamsMemberList(
      {
        id: route.query.id as string
      },
      [tableLoading]
    )
    localData.value = data.map((e: any) => {
      return {
        ...e,
        id: e.operationUserId || e.id,
        rowId: e.id
      }
    })
    await changeData()
    emit('echoData', localData.value)
  }
})
const { tableData, tablePage, changeCurrent, changeSize, changeData } =
  useTableData(
    getTableData,
    {},
    {
      callback: () => {
        getCurrentRow(
          localData.value.find((e: any) => e.id == isLeader.value) ||
            localData.value.find((e: any) => e.isLeader == 1) ||
            {}
        )
      }
    }
  )

const emit = defineEmits(['echoData'])

const add = async (arr: Obj[]) => {
  const keepIds = arr.map((e) => e.id)
  const ids = localData.value
    .filter((e) => e.rowId)
    .filter((e: any) => !keepIds.includes(e.id))
    .map((e) => e.rowId)
  deleteIds.value = deleteIds.value.concat(ids)
  localData.value = arr.map((e: any) => {
    return {
      ...e,
      ...localData.value.find((o: any) => o.id === e.id)
    }
  })
  tablePage.pageNum = 1
  await changeData()
  emit('echoData', localData.value)
  tableList.value.tablePagination.$refs.scrollBarRef.setScrollTop(0)
}
const clear = async () => {
  localData.value = []
  changeData()
  emit('echoData', localData.value)
}
const submit = async () => {
  if (localData.value.length === 0) {
    ElMessage({
      type: 'warning',
      message: '请添加成员！'
    })
    return
  }

  if (!isLeader.value) {
    ElMessage({
      type: 'warning',
      message: '请选择班组组长！'
    })
    return
  }

  return localData.value
}
const deleteIds = ref<number[]>([])
const deleteItem = async (row: Obj) => {
  if (tablePage.pageSize === 0) return
  localData.value = localData.value.filter((e) => e.id !== row.id)
  if (row.rowId) {
    deleteIds.value.push(row.rowId)
  }
  const totalPage = Math.ceil(localData.value.length / tablePage.pageSize)
  tablePage.pageNum =
    tablePage.pageNum > totalPage ? totalPage : tablePage.pageNum
  await changeData()
  emit('echoData', localData.value)

  tableList.value.tablePagination.$refs.scrollBarRef.setScrollTop(0)
}

defineExpose({
  add,
  submit,
  clear,
  deleteIds
})
</script>
<style scoped lang="scss">
:deep(.el-table .el-form-item) {
  margin-bottom: 0 !important;
  .el-input__wrapper {
    height: inherit !important;
    line-height: inherit !important;
  }
}
.el-table-fixed-column--right {
  .el-button.is-link {
    color: #29cca0 !important;
    &:hover {
      color: var(--el-button-hover-link-text-color) !important;
    }
  }
}
:deep(.el-table .cell) {
  overflow: inherit;
}
.el-input.input7343 {
  margin-top: -7px;
  margin-bottom: 7px;
}
div.tip7343 {
  position: absolute;
  top: 28px;
  left: -8px;
  width: auto;
  z-index: 99999;
  color: #f56c6c;
  font-size: 12px;
  line-height: 12px;
  white-space: nowrap;
  transform: scale(0.9);
}
:deep(.el-radio__label) {
  display: none;
}
</style>
