<template>
  <div class="page-operate">
    <div class="operate-title">{{ titleVal }}</div>
  </div>
  <div class="info-base">
    <div class="operate">
      <p>班组信息</p>
    </div>
    <div class="info">
      <el-form
        ref="formRef"
        :rules="formRules"
        :model="formData"
        label-suffix=""
        label-position="right"
        :inline="true"
        class="w-full"
      >
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item
              v-if="type === 'add'"
              label="运维公司"
              prop="operatorsId"
            >
              <el-select
                v-model="formData.operatorsId"
                placeholder="请选择运维公司"
                filterable
                :disabled="isDetail"
              >
                <el-option
                  v-for="item in companyList"
                  :key="item.value"
                  :label="item.label"
                  :value="String(item.value)"
                />
              </el-select>
            </el-form-item>
            <span
              v-else
              class="block mb-20px ellipsis"
              :title="getCompanyName(formData.operatorsId)"
              >运维公司：{{ getCompanyName(formData.operatorsId) }}</span
            >
          </el-col>
          <el-col :span="6"></el-col>
          <el-col :span="8">
            <el-form-item
              v-if="type === 'add'"
              label="班组名称"
              prop="teamsGroupsName"
            >
              <el-input
                v-model.trim="formData.teamsGroupsName"
                placeholder="请输入班组名称"
                autocomplete="off"
                maxlength="20"
                clearable
                :disabled="isDetail"
              ></el-input>
            </el-form-item>
            <span
              v-else
              class="block mb-20px ellipsis"
              :title="formData.teamsGroupsName"
              >班组名称：{{ formData.teamsGroupsName }}</span
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <div v-auto-height="{ bottom: 88 }" class="info-tab">
    <div class="operate">
      <p>班组成员</p>
      <el-button
        type="primary"
        :disabled="!formData.operatorsId"
        @click="() => (isShow = true)"
      >
        <el-icon><Plus /></el-icon>
        添加人员
      </el-button>
    </div>
    <table-list
      ref="tableListRef"
      :type="type"
      @echo-data="echoData"
    ></table-list>
  </div>
  <div class="page-footer">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button type="primary" @click="onSumbit">提交</el-button>
  </div>
  <select-staff
    :is-show="isShow"
    :arr-list="arrList"
    :operators-id="formData.operatorsId"
    @uplate-data="updateListData"
    @close-dialog="isShow = false"
  ></select-staff>
</template>

<script setup lang="ts">
import TableList from './components/TableList.vue'
import { team as teamAPI, opsPersonnel as opsPersonnelAPI } from '@/api'
import selectStaff from '../components/selectStaff.vue'

const companyList = ref<Obj[]>([])
const isDetail = ref(false)

const router = useRouter()
const route = useRoute()

const titleVal = ref('标题')
const type = ref<any>('add')
if (route.name === 'EducateTeamAdd') {
  titleVal.value = '新建班组'
  type.value = 'add'
} else {
  isDetail.value = true
  titleVal.value = '查看详情'
  type.value = 'detail'
}

const tableListRef = ref()
const isShow = ref<boolean>(false)
const updateListData = (arr: Obj[]) => {
  tableListRef.value.add(
    arr.map((e: Obj) => {
      return {
        ...e
      }
    })
  )
}
const arrList = ref([])
const echoData = (val: any) => {
  arrList.value = val
}

const formRef = ref()
const formRules = reactive<any>({
  operatorsId: [
    { required: true, message: '请选择运维公司', trigger: 'change' }
  ],
  teamsGroupsName: [
    { required: true, message: '请输入班组名称', trigger: 'blur' }
  ]
})
const formData = ref<Record<string, any>>({
  operatorsId: route.query.operatorsId || '',
  teamsGroupsName: route.query.teamsGroupsName || ''
})

const onCancel = () => {
  router.go(-1)
}
const onSumbit = async () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const data = await tableListRef.value.submit()
      if (data) {
        const params: any = {
          delTeamsMemberIds: tableListRef.value.deleteIds || [],
          operatorsId: formData.value.operatorsId,
          teamsGroupsName: formData.value.teamsGroupsName,
          teamsGroupUserList: data.map((e: any) => {
            return {
              isLeader: e.isLeader,
              operationUserId: e.id,
              id: e.rowId || null
            }
          })
        }
        if (isDetail.value) {
          params.id = route.query.id
        }
        const result = await teamAPI.addUpdateTeamsGroups(params)
        if (result?.data?.code == 200) {
          ElMessage({
            type: 'success',
            message: '提交成功！'
          })
          onCancel()
        } else {
          ElMessage({
            message: '提交失败',
            type: 'error'
          })
        }
      }
    }
  })
}

const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    companyList.value = data.data.map((item: any) => {
      return {
        label: item.companyName || '',
        value: item.id || ''
      }
    })
  } catch (e: any) {
    companyList.value = []
  }
}
// 获取运维公司名称
const getCompanyName = (operatorsId: number) => {
  return (
    (operatorsId &&
      companyList.value.find((item: any) => operatorsId == item.value)
        ?.label) ||
    '--'
  )
}
watch(
  () => formData.value.operatorsId,
  () => tableListRef.value && tableListRef.value.clear()
)
onMounted(() => {
  getCompany()
})
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
