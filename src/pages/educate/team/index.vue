<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>班组列表</p>
        <el-button type="primary" @click="handleAdd"
          ><el-icon> <Plus /> </el-icon>新建班组</el-button
        >
      </div>
      <vis-table-pagination
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="columns"
        :total="listTotal"
        :data="listData"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #status="{ row }">
          <el-tag v-if="row.status == 1" type="info">启用</el-tag>
          <el-tag v-if="row.status == 2" type="danger">停用</el-tag>
        </template>

        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="handleDetail(row)">查看详情</el-button>
            <el-button link @click="handleEnable(row)">{{
              row.status == 1 ? '停用' : '启用'
            }}</el-button>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  team as teamAPI,
  opsPersonnel as opsPersonnelAPI
} from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import type { PageObject } from './types'

const router = useRouter()

// 搜索
const searchData = ref({
  operatorsId: '',
  teamsGroupsName: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    type: 'select',
    label: '运维公司',
    prop: 'operatorsId',
    span: 7,
    width: '68px',
    filterable: true,
    options: []
  },
  {
    prop: 'teamsGroupsName',
    label: '班组名称',
    placeholder: '请输入班组名称',
    span: 8,
    width: '110px'
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  await getTableData()
}

const companyList = ref<Obj[]>([])

const columns = [
  {
    prop: 'teamsGroupsNo',
    label: '班组编号'
  },
  {
    prop: 'teamsGroupsName',
    label: '班组名称'
  },
  // {
  //   prop: 'operatorsId',
  //   label: '运维公司',
  //   formatter: (v: any) => {
  //     return getCompanyName(v.operatorsId)
  //   }
  // },
  {
    prop: 'teamGroupNum',
    label: '班组成员'
  },
  {
    prop: 'teamGroupLeader',
    label: '班组组长'
  },
  {
    prop: 'createTime',
    label: '创建时间'
  },
  {
    prop: 'updateTime',
    label: '更新时间'
  },
  {
    prop: 'status',
    label: '状态',
    slotName: 'status'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 130,
    fixed: 'right'
  }
]

// 获取运维公司名称
// const getCompanyName = (operatorsId: number) => {
//   return (
//     (operatorsId &&
//       companyList.value.find((item: any) => operatorsId == item.id)
//         ?.companyName) ||
//     '--'
//   )
// }

const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await teamAPI.queryByPage({ ...searchData.value }, [
      tableLoading
    ])
    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: PageObject) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: PageObject) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}

const handleAdd = () => {
  router.push('/educate/team/add')
}

const handleDetail = (row: Record<string, any>) => {
  router.push(
    `/educate/team/detail?id=${JSON.stringify(row.id)}&operatorsId=${
      row.operatorsId
    }&teamsGroupsName=${row.teamsGroupsName}`
  )
}

const handleEnable = (row: any) => {
  let title = '启用'
  if (row.status === 1) {
    title = '停用'
  }
  ElMessageBox.confirm(`是否确认${title}该班组？`, undefined, {
    type: 'warning'
  }).then(() => {
    teamAPI
      .updateTeamsGroupsStatus({
        id: row.id,
        status: row.status === 1 ? 2 : 1
      })
      .then(({ data }) => {
        if (data.code == '200') {
          if (data.data) {
            ElMessage({
              message: `${title}成功`,
              type: 'success'
            })
            getTableData()
          } else {
            ElMessage({
              message: data.message,
              type: 'warning'
            })
          }
        } else {
          ElMessage({
            message: data.message,
            type: 'warning'
          })
        }
      })
      .catch((err) => {
        console.info(err)
      })
  })
}

const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    companyList.value = data?.data || []
    searchProps.value[0].options = data.data.map((item: any) => {
      return {
        label: item.companyName || '',
        value: item.id || ''
      }
    })
  } catch (e: any) {
    searchProps.value[0].options = []
  }
}

onMounted(() => {
  getTableData()
  getCompany()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
