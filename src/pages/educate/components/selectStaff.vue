<template>
  <el-dialog
    v-model="transferDialogVisible"
    :title="dialogTitle"
    class="vis-dialog pb100__"
    align-center
    :before-close="closeDialog"
    :close-on-click-modal="false"
    width="900"
  >
    <el-transfer
      ref="transferRef"
      v-model="leftValue"
      filterable
      :data="dataList"
      :titles="['全选', '全选']"
      :left-default-checked="leftCheckArray"
      :right-default-checked="rightCheckArray"
      style="text-align: left; display: inline-block"
      :format="{
        noChecked: '（0/${total}）',
        hasChecked: '（${checked}/${total}）'
      }"
      target-order="unshift"
      filter-placeholder="请输入公司名称/人员姓名"
    >
      <template #default="{ option }">
        <div :title="option.infoName">
          <span class="title1__">{{ option.userName }}</span>
          <span class="title2__">({{ option.companyName }})</span>
        </div>
      </template>
    </el-transfer>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSave">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { educate as educateAPI } from '@/api/index.ts'
import { loadingOpen, loadingClose } from '@/utils/common.ts'

const leftValue = ref<any[]>([]) // 已选列表的数据
const leftCheckArray = ref([]) // 默认选中的数据
const rightCheckArray = ref([]) // 已选列表的默认选中的数据

const transferDialogVisible = ref(false)
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  arrList: {
    type: Array,
    default: () => {
      return []
    }
  },
  disabledList: {
    type: Array,
    default: () => {
      return []
    }
  },
  dialogTitle: {
    type: String,
    default: '添加人员',
    required: false
  },
  // 运维公司ID
  operatorsId: {
    type: String,
    default: ''
  },
  isValidate: {
    type: Boolean,
    default: true
  }
})
watch(
  () => props.isShow,
  async (val: boolean) => {
    transferDialogVisible.value = val
    if (val) {
      // 获取数据
      await generateData(props.operatorsId)
    }
  },
  {
    deep: true
  }
)

const dataList = ref<Obj[]>([])
const generateData = async (operatorsId: string) => {
  loadingOpen()
  try {
    let { data } = await educateAPI.getOperationUserAllList({
      operatorsId: operatorsId ? operatorsId : ''
    })
    if (data.code == 200) {
      let arr = data.data || []
      arr.forEach((item: any) => {
        item.key = item.id
        item.disabled = false
        item.infoName = item.userName + '(' + item.companyName + ')'
        item.label = item.userName + item.companyName
      })
      if (props.arrList.length) {
        leftValue.value = props.arrList.map((e: any) => e.id)
      }
      if (props.disabledList.length) {
        props.disabledList.forEach((item: any) => {
          arr.forEach((items: any) => {
            if (item.id == items.id) {
              items.disabled = true
            }
          })
        })
      }
      dataList.value = arr
      loadingClose()
    }
  } catch (e: any) {
    loadingClose()
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
// 点击取消
const transferRef = ref()
const emit = defineEmits(['closeDialog', 'uplateData'])
const closeDialog = () => {
  dataList.value = []
  leftValue.value = []
  leftCheckArray.value = []
  rightCheckArray.value = []
  transferDialogVisible.value = false

  transferRef.value?.clearQuery('left')
  transferRef.value?.clearQuery('right')
  emit('closeDialog')
}
// 点击确认
const handleSave = () => {
  if (leftValue.value.length) {
    let arr: any = []
    leftValue.value.forEach((item: any) => {
      dataList.value.forEach((items: any) => {
        if (items.key == item) {
          arr.push(items)
        }
      })
    })
    emit('uplateData', arr)
    closeDialog()
  } else {
    if (props.isValidate) {
      ElMessage({
        message: '请选择人员添加至已选列表中',
        type: 'warning'
      })
    } else {
      emit('uplateData', [])
      closeDialog()
    }
  }
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
:deep(.el-transfer) {
  margin-bottom: 100px !important;
}
:deep(.el-transfer-panel) {
  width: 366px !important;
}
:deep(.el-transfer__button) {
  padding: 0;
  margin: 0 0 16px 0;
  display: block !important;
  width: 36px;
  height: 36px;
}

:deep(.el-transfer__button.is-disabled) {
  background: rgba(238, 238, 238, 1);
  border-color: rgba(238, 238, 238, 1);
}

:deep(
    .el-transfer__button.is-disabled .el-icon,
    .el-transfer__button.is-disabled span
  ) {
  color: rgba(169, 169, 169, 1);
}
:deep(
    .el-transfer-panel
      .el-transfer-panel__header
      .el-checkbox
      .el-checkbox__label
      span
  ) {
  // position: static !important;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  font-weight: 400;
}
:deep(.el-transfer-panel .el-transfer-panel__header .el-checkbox__label) {
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 400;
}
:deep(.el-transfer-panel__item .el-checkbox__label) {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
:deep(.el-transfer__button > span > i) {
  // display: none;
  font-size: 20px;
}
:deep(.el-transfer-panel .el-transfer-panel__header) {
  background: #fff;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
  box-sizing: content-box;
  position: relative;
}

:deep(.el-transfer-panel:first-child) {
  .el-transfer-panel__header::after {
    content: '人员列表';
    position: absolute;
    left: 50%;
    margin-left: -28px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
}
:deep(.el-transfer-panel:last-child) {
  .el-transfer-panel__header::after {
    content: '已选列表';
    position: absolute;
    left: 50%;
    margin-left: -28px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
}
:deep(.el-transfer-panel__empty) {
  margin-top: 150px;
  color: rgba(0, 0, 0, 0.45);
}
:deep(.el-transfer-panel__empty::before) {
  content: '暂无数据';
  position: absolute;
  width: 100px;
  background: #fff;
  left: 148px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}
:deep(.el-transfer-panel__body) {
  height: 404px;
}
:deep(.el-transfer-panel .el-input__wrapper) {
  height: 40px;
}
:deep(.el-transfer-panel__filter .el-input__inner) {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85) !important;
}
:deep(.el-transfer-panel__filter .el-input__inner::placeholder) {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.25) !important;
}
.el-dialog__footer .el-button {
  width: 72px;
  height: 40px;
}
</style>
<style lang="scss">
.el-dialog.vis-dialog.pb100__ .el-dialog__body {
  padding: 24px 0 24px 35px !important;
}
.title1__ {
  font-size: 14px;
  margin-right: 12px;
}
.title2__ {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
