<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div class="main">
      <div class="operate">
        <p>培训列表</p>
        <el-button type="primary" @click="handleAdd"
          ><el-icon> <Plus /> </el-icon>新建培训</el-button
        >
      </div>
      <div class="tables">
        <vis-table-pagination
          :loading="tableLoading"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchData.pageSize"
          :current-page="searchData.pageNum"
          :columns="columns"
          :total="listTotal"
          :data="listData"
          :height="tableHeight"
          :show-overflow-tooltip="true"
          background
          class="vis-table-pagination"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
        >
          <template #trainingStatus="{ row }">
            <el-tag v-if="row.trainingStatus == 1" type="danger">
              未完成
            </el-tag>
            <el-tag v-if="row.trainingStatus == 2" type="success"
              >已完成</el-tag
            >
          </template>

          <template #operate="{ row }">
            <div class="table-operate">
              <el-button
                v-if="row.trainingStatus == 1"
                link
                @click="handleBtn(row, 1)"
              >
                处理
              </el-button>
              <el-button v-else link @click="handleBtn(row, 2)">
                查看详情
              </el-button>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { educate as educateAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import type { PageObject } from './types'
import useEnums from './hooks/useEnums'
// import useCompanyCodeStore from '@/store/companyCode'

// const companyCode = useCompanyCodeStore()
const router = useRouter()

// let startWatch = false
// watch(
//   () => companyCode.data,
//   () => {
//     startWatch && getTableData()
//   }
// )
onMounted(async () => {
  await getTableData()
  // startWatch = true
})

// 搜索
const searchData = ref({
  trainingLevel: '',
  trainingType: '',
  trainingStatus: '',
  pageNum: 1,
  pageSize: 10
})
const { typesArr, levelArr, trainingStatusArr } = useEnums()
const searchProps = ref([
  {
    type: 'select',
    label: '培训级别',
    prop: 'trainingLevel',
    span: 7,
    width: '68px',
    options: levelArr
  },
  {
    type: 'select',
    label: '培训类型',
    prop: 'trainingType',
    span: 7,
    width: '80px',
    options: typesArr
  },
  {
    type: 'select',
    label: '培训状态',
    prop: 'trainingStatus',
    span: 7,
    width: '80px',
    options: trainingStatusArr
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  await getTableData()
}

const columns = [
  {
    prop: 'trainingManageNo',
    label: '培训编号'
  },
  {
    prop: 'trainingLevelName',
    label: '培训级别'
  },
  {
    prop: 'trainingTypeName',
    label: '培训类型'
  },
  {
    prop: 'trainingTitle',
    label: '培训主题'
  },
  {
    prop: 'beginTime',
    label: '培训开始时间',
    formatter: (row: Obj) =>
      row.beginTime?.slice(0, row.beginTime.lastIndexOf(':'))
  },
  {
    prop: 'endTime',
    label: '培训结束时间',
    formatter: (row: Obj) => row.endTime?.slice(0, row.endTime.lastIndexOf(':'))
  },
  {
    prop: 'trainingStatus',
    label: '培训状态',
    slotName: 'trainingStatus'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 100,
    fixed: 'right'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
let tableHeight = ref(window.innerHeight - 350)
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await educateAPI.getList({ ...searchData.value }, [
      tableLoading
    ])
    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: PageObject) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: PageObject) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}

// 操作
const handleAdd = () => {
  router.push('/educate/train/trainInfo?pageStatus=add')
}
const handleBtn = (row: Record<string, any>, type: number) => {
  router.push(
    `/educate/train/trainInfo?pageStatus=${
      type == 1 ? 'handle' : 'edit'
    }&row=${JSON.stringify(row)}`
  )
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
