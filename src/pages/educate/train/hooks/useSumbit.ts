import { educate as educateAPI } from '@/api/index.ts'
export default async function (
  route: any,
  formData: any,
  typesArr: any,
  levelArr: any,
  id_: any,
  formRef: any,
  onCancel: any,
  getName: any,
  isGetInfo: any
) {
  let params = {}
  let ApiStr = ''
  if (route.query.pageStatus == 'add' || route.query.pageStatus == 'handle') {
    if (route.query.pageStatus == 'add') {
      ApiStr = 'trainingAdd'
      params = {
        ...formData.value,
        trainingTypeName: getName(
          typesArr.value,
          formData.value.trainingType,
          'value',
          'label'
        ),
        trainingLevelName: getName(
          levelArr.value,
          formData.value.trainingLevel,
          'value',
          'label'
        ),
        beginTime:
          formData.value.timeData.length && formData.value.timeData[0] + ':00', // 开始时间
        endTime:
          formData.value.timeData.length && formData.value.timeData[1] + ':00', // 结束时间
        trainingStatusName:
          formData.value.trainingStatus == 1 ? '未完成' : '已完成',
        // 所属项目/班组
        belong:
          formData.value.trainingLevel == 1
            ? ''
            : formData.value.trainingLevel == 2
            ? formData.value.belong1
            : formData.value.belong2
      }
      // 未完成
      if (params.trainingStatus == 1) {
        params.participatorList = []
        params.participator = ''

        params.nonparticipatorList = []
        params.nonparticipator = ''

        params.numStr1 = ''
        params.numStr2 = ''

        params.signUrl = ''
        params.trainingRecordUrl = ''
      }
    } else {
      ApiStr = 'update'
      params = {
        participator: formData.value.participator,
        nonparticipator: formData.value.nonparticipator,
        trainingContent: formData.value.trainingContent,
        signUrl: formData.value.signUrl,
        trainingRecordUrl: formData.value.trainingRecordUrl,
        id: id_.value,
        trainingStatusName:
          formData.value.trainingStatus == 1 ? '未完成' : '已完成',
        participatorList: formData.value.participatorList,
        nonparticipatorList: formData.value.nonparticipatorList
      }
    }

    if (formData.value.trainingStatus == 1) {
      isGetInfo.value = true
    } else {
      isGetInfo.value = formData.value.participatorList.length ? true : false
    }
    formRef.value?.validate(async (valid: boolean) => {
      if (valid) {
        try {
          let { data } = await educateAPI[ApiStr](params)
          if (data.code == 200) {
            ElMessage({
              message: '提交成功！',
              type: 'success'
            })
          }
          onCancel()
        } catch (e) {
          console.log(e)
        }
      }
    })
  } else {
    params = {
      trainingContent: formData.value.trainingContent,
      signUrl: formData.value.signUrl,
      trainingRecordUrl: formData.value.trainingRecordUrl,
      id: id_.value,
      trainingStatusName:
        formData.value.trainingStatus == 1 ? '未完成' : '已完成'
    }
    try {
      let { data } = await educateAPI.update(params)
      if (data.code == 200) {
        ElMessage({
          message: '提交成功！',
          type: 'success'
        })
      }
      onCancel()
    } catch (e) {
      console.log(e)
    }
  }
}
