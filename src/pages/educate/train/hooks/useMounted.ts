export default function (
  route: any,
  id_: any,
  formData: any,
  changeItem: any,
  attendArr: any,
  dutyArr: any,
  files1: any,
  files2: any,
  isDisable: any
) {
  // 查看详情和处理
  if (
    route.query &&
    (route.query.pageStatus == 'edit' || route.query.pageStatus == 'handle')
  ) {
    let rowInfo = route.query.row && JSON.parse(route.query.row)
    console.log(rowInfo)
    id_.value = rowInfo.id
    formData.value.trainingType = rowInfo.trainingType
    formData.value.trainingTypeName = rowInfo.trainingTypeName

    formData.value.trainingLevel = rowInfo.trainingLevel
    formData.value.trainingLevelName = rowInfo.trainingLevelName
    changeItem(rowInfo.trainingLevel, Number(rowInfo.belong), '')

    formData.value.trainingTitle = rowInfo.trainingTitle
    formData.value.timeData = [rowInfo.beginTime, rowInfo.endTime]
    formData.value.trainingStatus = Number(rowInfo.trainingStatus)
    formData.value.trainingStatusName = rowInfo.trainingStatusName

    formData.value.participator = rowInfo.participator
    formData.value.participatorList = rowInfo.participatorList
      ? rowInfo.participatorList
      : []
    if (rowInfo.participator) {
      attendArr.value = rowInfo.participator
        .split(',')
        .map((item: any, index: number) => {
          return {
            userName: item,
            id: index
          }
        })
      formData.value.numStr1 = attendArr.value.length
    } else {
      attendArr.value = []
      formData.value.numStr1 = ''
    }

    formData.value.nonparticipator = rowInfo.nonparticipator
    formData.value.nonparticipatorList = rowInfo.nonparticipatorList
      ? rowInfo.nonparticipatorList
      : []

    if (rowInfo.nonparticipator) {
      dutyArr.value = rowInfo.nonparticipator
        .split(',')
        .map((item: any, index: number) => {
          return {
            userName: item,
            id: index
          }
        })
      formData.value.numStr2 = dutyArr.value.length
    } else {
      dutyArr.value = []
      formData.value.numStr2 = '0'
    }

    formData.value.trainingContent = rowInfo.trainingContent

    formData.value.signUrl = rowInfo.signUrl || ''
    formData.value.trainingRecordUrl = rowInfo.trainingRecordUrl || ''

    files1.value = rowInfo.signUrl ? rowInfo.signUrl.split(',') : []
    files2.value = rowInfo.trainingRecordUrl
      ? rowInfo.trainingRecordUrl.split(',')
      : []
    if (route.query.pageStatus == 'handle') {
      isDisable.value = true
      formData.value.trainingStatus = '2'
    }
  }
}
