export default function (
  files1: any,
  formData: any,
  files2: any,
  attendArr: any,
  formRef: any,
  dutyArr: any
) {
  watch(
    () => files1.value,
    (val) => {
      if (val.length) {
        formData.value.signUrl = val.join(',')
      } else {
        formData.value.signUrl = ''
      }
    },
    {
      deep: true
    }
  )
  watch(
    () => files2.value,
    (val) => {
      if (val.length) {
        formData.value.trainingRecordUrl = val.join(',')
      } else {
        formData.value.trainingRecordUrl = ''
      }
    },
    {
      deep: true
    }
  )
  watch(
    () => attendArr.value,
    (val) => {
      if (val.length) {
        let arr1: any = []
        let arr2: any = []
        val.forEach((item: any) => {
          arr1.push(item.id)
          arr2.push(item.userName)
        })
        formData.value.participatorList = arr1
        formData.value.participator = arr2.join(',')

        formRef.value?.validateField('participator')
      } else {
        formData.value.participatorList = []
        formData.value.participator = ''
      }
    },
    {
      deep: true
    }
  )
  watch(
    () => dutyArr.value,
    (val) => {
      if (val.length) {
        let arr1: any = []
        let arr2: any = []
        val.forEach((item: any) => {
          arr1.push(item.id)
          arr2.push(item.userName)
        })
        formData.value.nonparticipatorList = arr1
        formData.value.nonparticipator = arr2.join(',')
      } else {
        formData.value.nonparticipatorList = []
        formData.value.nonparticipator = ''
      }
    },
    {
      deep: true
    }
  )
}
