<template>
  <div class="page-operate">
    <div class="operate-title">{{ pageName }}</div>
  </div>
  <div class="info-base">
    <div class="operate">
      <p>基础信息</p>
    </div>
    <div class="info">
      <el-form
        ref="formRef"
        :rules="formRules"
        :model="formData"
        label-suffix=""
        label-position="right"
        :inline="true"
        class="w-full"
      >
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="培训类型" prop="trainingType">
              <span v-if="route.query.pageStatus == 'edit'">
                {{ formData.trainingTypeName }}
              </span>
              <el-select
                v-model="formData.trainingType"
                placeholder="请选择培训类型"
                clearable
                :disabled="isDisable"
                v-else
              >
                <template v-for="item in typesArr" :key="item.value">
                  <el-option :label="item.label" :value="item.value" />
                </template>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4"></el-col>
          <el-col :span="10">
            <el-form-item label="培训级别" prop="trainingLevel">
              <span v-if="route.query.pageStatus == 'edit'">
                {{ formData.trainingLevelName }}
              </span>

              <el-select
                v-model="formData.trainingLevel"
                placeholder="请选择培训级别"
                clearable
                @change="changeItem(formData.trainingLevel, '', 'change')"
                :disabled="isDisable"
                v-else
              >
                <template v-for="item in levelArr" :key="item.value">
                  <el-option :label="item.label" :value="item.value" />
                </template>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="10" v-if="formData.trainingLevel == 2">
            <el-form-item label="所属项目" prop="belong1">
              <span v-if="route.query.pageStatus == 'edit'">
                {{
                  getName(
                    belong1Arr,
                    formData.belong1,
                    'id',
                    'operationProjectName'
                  )
                }}
              </span>

              <el-select
                v-model="formData.belong1"
                placeholder="请选择所属项目"
                clearable
                :disabled="isDisable"
                filterable
                v-else
              >
                <template v-for="item in belong1Arr" :key="item.id">
                  <el-option
                    :label="item.operationProjectName"
                    :value="item.id"
                  />
                </template>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="10" v-if="formData.trainingLevel == 3">
            <el-form-item label="所属班组" prop="belong2">
              <span v-if="route.query.pageStatus == 'edit'">
                {{ getName(belong2Arr, formData.belong2, 'id', 'name') }}
              </span>

              <el-select
                v-model="formData.belong2"
                placeholder="请选择所属班组"
                clearable
                :disabled="isDisable"
                filterable
                v-else
              >
                <template v-for="item in belong2Arr" :key="item.id">
                  <el-option :label="item.name" :value="item.id" />
                </template>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col
            :span="4"
            v-if="formData.trainingLevel == 2 || formData.trainingLevel == 3"
          ></el-col>

          <el-col :span="10">
            <el-form-item label="培训主题" prop="trainingTitle">
              <span v-if="route.query.pageStatus == 'edit'">
                {{ formData.trainingTitle }}
              </span>
              <el-input
                v-model.trim="formData.trainingTitle"
                placeholder="请输入培训主题"
                autocomplete="off"
                clearable
                maxlength="20"
                :disabled="isDisable"
                v-else
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col
            :span="4"
            v-if="formData.trainingLevel == 1 || formData.trainingLevel == ''"
          ></el-col>

          <el-col :span="10">
            <el-form-item label="培训时间" prop="timeData">
              <span v-if="route.query.pageStatus == 'edit'">
                {{
                  formData.timeData.length &&
                  formData.timeData[0].slice(
                    0,
                    formData.timeData[0].lastIndexOf(':')
                  ) +
                    ' - ' +
                    formData.timeData[1].slice(
                      0,
                      formData.timeData[1].lastIndexOf(':')
                    )
                }}
              </span>

              <el-date-picker
                v-model="formData.timeData"
                type="datetimerange"
                :editable="false"
                start-placeholder="请选择培训开始时间"
                end-placeholder="请选择培训结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                :disabled="isDisable"
                v-else
              />
            </el-form-item>
          </el-col>
          <el-col
            :span="4"
            v-if="formData.trainingLevel == 2 || formData.trainingLevel == 3"
          ></el-col>
          <el-col :span="10">
            <el-form-item label="培训状态" prop="trainingStatus">
              <span v-if="route.query.pageStatus == 'edit'">
                {{ formData.trainingStatusName }}
              </span>
              <el-select
                v-model="formData.trainingStatus"
                placeholder="请选择培训状态"
                clearable
                :disabled="isDisable"
                v-else
              >
                <el-option
                  v-for="item in trainingStatusArr"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row
          :gutter="24"
          v-if="formData.trainingStatus != 1 && formData.trainingStatus != ''"
        >
          <el-col :span="10">
            <el-form-item label="参加人员" prop="participator">
              <span
                v-if="route.query.pageStatus == 'edit'"
                class="patorSps"
                :title="formData.participator"
              >
                {{ formData.participator }}
              </span>

              <div
                v-else
                :class="[isGetInfo ? 'tagBorder__' : 'redB_ tagBorder__']"
              >
                <div v-if="attendArr.length == 0" class="placeholeder__">
                  请选择参加人员
                </div>
                <span
                  v-for="(item, index) in attendArr.slice(0, 3)"
                  :key="index"
                  v-else
                  >{{ item.userName }}</span
                >

                <el-popover
                  v-if="attendArr.length > 3"
                  placement="top-start"
                  trigger="hover"
                  width="300px"
                >
                  <div style="display: flex; flex-wrap: wrap">
                    <span
                      class="sps__"
                      v-for="(item, index) in attendArr.slice(
                        3,
                        attendArr.length
                      )"
                      :key="index"
                      >{{ item.userName }}</span
                    >
                  </div>
                  <template #reference>
                    <span>+{{ attendArr.length - 3 }}...</span>
                  </template>
                </el-popover>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button
              type="primary"
              size="large"
              @click="addStaff(1)"
              v-if="route.query.pageStatus != 'edit'"
              ><el-icon><Plus /></el-icon>添加人员</el-button
            ></el-col
          >
          <el-col :span="10">
            <el-form-item label="参加人数" prop="numStr1" class="numStr1__">
              <span v-if="route.query.pageStatus == 'edit'">{{
                formData.numStr1
              }}</span>
              <el-input
                v-model.trim="formData.numStr1"
                placeholder="添加人员后自动计算"
                autocomplete="off"
                clearable
                disabled
                v-else
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row
          :gutter="24"
          v-if="formData.trainingStatus != 1 && formData.trainingStatus != ''"
        >
          <el-col :span="10" class="mL24__">
            <el-form-item label="缺勤人员" prop="nonparticipator">
              <span
                v-if="route.query.pageStatus == 'edit'"
                class="patorSps"
                :title="formData.nonparticipator"
              >
                {{ formData.nonparticipator || '--' }}
              </span>

              <div class="tagBorder__" v-else>
                <div v-if="dutyArr.length == 0" class="placeholeder__">
                  请选择缺勤人员
                </div>
                <span
                  v-for="(item, index) in dutyArr.slice(0, 3)"
                  :key="index"
                  v-else
                  >{{ item.userName }}</span
                >
                <el-popover
                  v-if="dutyArr.length > 3"
                  placement="top-start"
                  trigger="hover"
                  width="300px"
                >
                  <div style="display: flex; flex-wrap: wrap">
                    <span
                      class="sps__"
                      v-for="(item, index) in dutyArr.slice(3, dutyArr.length)"
                      :key="index"
                      >{{ item.userName }}</span
                    >
                  </div>
                  <template #reference>
                    <span>+{{ dutyArr.length - 3 }}...</span>
                  </template>
                </el-popover>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button
              type="primary"
              size="large"
              @click="addStaff(2)"
              v-if="route.query.pageStatus != 'edit'"
              ><el-icon><Plus /></el-icon>添加人员</el-button
            >
          </el-col>
          <el-col :span="10" class="mL24__">
            <el-form-item label="缺勤人数" prop="numStr2">
              <span v-if="route.query.pageStatus == 'edit'">
                {{ formData.numStr2 || '0' }}
              </span>

              <el-input
                v-model.trim="formData.numStr2"
                placeholder="添加人员后自动计算"
                autocomplete="off"
                clearable
                disabled
                v-else
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="24" class="mL24__">
            <el-form-item
              label="培训内容"
              prop="trainingContent"
              class="trainingContent__"
            >
              <el-input
                v-model="formData.trainingContent"
                :autosize="{ minRows: 5, maxRows: 7 }"
                type="textarea"
                placeholder="请输入培训内容"
                maxlength="100"
                :show-word-limit="true"
              /> </el-form-item
          ></el-col>
        </el-row>

        <el-row
          :gutter="24"
          v-if="formData.trainingStatus != 1 && formData.trainingStatus != ''"
        >
          <el-col :span="10" class="mL24__">
            <el-form-item
              label="现场签到表"
              prop="signUrl"
              class="cardPhotoClass__"
            >
              <SpicUpload
                :key="uploadKey"
                v-model="files1"
                type="image"
                :limit="5"
              />
            </el-form-item>
            <div class="waringInfoBas__">
              <el-icon color="#FF9900">
                <WarningFilled />
              </el-icon>
              提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件
            </div>
          </el-col>
          <el-col :span="4"></el-col>
          <el-col :span="10" class="mL24__">
            <el-form-item
              label="培训记录"
              prop="trainingRecordUrl"
              class="cardPhotoClass__"
            >
              <SpicUpload
                :key="uploadKey"
                v-model="files2"
                type="image"
                :limit="5"
              />
            </el-form-item>
            <div class="waringInfoBas__">
              <el-icon color="#FF9900">
                <WarningFilled />
              </el-icon>
              提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <div class="page-footer">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button type="primary" @click="onSumbit" v-preventReClick="1000"
      >提交</el-button
    >
  </div>
  <!-- 添加人员弹窗 -->
  <select-goods
    :is-show="isShow"
    :arr-list="arrList"
    :disabled-list="disabledList"
    :is-validate="isValidate"
    @uplate-data="uplateData"
    @close-dialog="isShow = false"
  ></select-goods>
</template>
<script setup lang="ts">
import type { FormRules, FormInstance } from 'element-plus'
import { educate as educateAPI } from '@/api/index.ts'
import selectGoods from '../components/selectStaff.vue'
import useEnums from './hooks/useEnums'
import useWatch from './hooks/useWatch'
import useMounted from './hooks/useMounted'
import useSumbit from './hooks/useSumbit'

const route = useRoute()
const pageName = computed(() => {
  if (route.query && route.query.pageStatus == 'add') {
    return '新建培训'
  }
  if (route.query && route.query.pageStatus == 'edit') {
    return '查看详情'
  }
  if (route.query && route.query.pageStatus == 'handle') {
    return '处理'
  }
})

const router = useRouter()
const { typesArr, levelArr, trainingStatusArr } = useEnums()
const belong1Arr = ref<any[]>([])
const belong2Arr = ref<any[]>([])

// 表单
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  trainingType: '', // 培训类型
  trainingLevel: '', // 培训级别
  trainingTitle: '', // 培训主题
  timeData: [], // 培训时间
  trainingStatus: '', // 培训状态
  participator: '', // 参加人员
  participatorList: [],
  numStr1: '', // 参加人数
  nonparticipator: '', // 缺勤人员
  nonparticipatorList: [],
  numStr2: '0', // 缺勤人数
  trainingContent: '', // 培训内容
  signUrl: '', // 现场签到表
  trainingRecordUrl: '', // 培训记录
  belong1: '', // 所属项目
  belong2: '' // 所属班组
})
const formRules = reactive<FormRules<Record<string, any>>>({
  trainingType: [
    { required: true, message: '请选择培训类型', trigger: 'change' }
  ],
  trainingLevel: [
    { required: true, message: '请选择培训级别', trigger: 'change' }
  ],
  trainingTitle: [
    { required: true, message: '请输入培训主题', trigger: 'blur' }
  ],
  timeData: [{ required: true, message: '请选择培训时间', trigger: 'change' }],
  trainingStatus: [
    { required: true, message: '请选择培训状态', trigger: 'change' }
  ],
  participator: [
    { required: true, message: '请添加参加人员', trigger: 'change' }
  ],
  numStr1: [{ required: true, message: '', trigger: 'change' }],
  belong1: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
  belong2: [{ required: true, message: '请选择所属班组', trigger: 'change' }]
})
const changeItem = async (val: string, str: string, type: string) => {
  if (val == '2') {
    try {
      let { data } = await educateAPI.getOperationProjectList({})
      belong1Arr.value = data.data || []
    } catch (e) {
      belong1Arr.value = []
      console.log(e)
    }
  }

  if (val == '3') {
    try {
      let { data } = await educateAPI.getOperationTeamsGroup_({})
      belong2Arr.value =
        data.data?.map((e: any) => ({ ...e, name: e.groupname })) || []
    } catch (e) {
      belong2Arr.value = []
      console.log(e)
    }
  }

  if (type == 'change') {
    formData.value.belong1 = ''
    formData.value.belong2 = ''
  } else {
    formData.value.belong1 = str
    formData.value.belong2 = str
  }
}

const uploadKey = ref<any>('')
uploadKey.value = Date.now()
const files1 = ref<any[]>([])
const files2 = ref<any[]>([])

// 添加人员
const isValidate = ref(true)
const isGetInfo = ref(true)
const arrList = ref([])
const disabledList = ref([])
const typeStr = ref()
const isShow = ref(false)
const addStaff = (val: number) => {
  isShow.value = true
  typeStr.value = val
  disabledList.value = val == 1 ? dutyArr.value : attendArr.value
  arrList.value = val == 1 ? attendArr.value : dutyArr.value
  isValidate.value = val == 1 ? true : false
}
const attendArr = ref<any[]>([]) // 参加人员
const dutyArr = ref<any[]>([]) // 缺勤人员
const uplateData = (arr: Obj[]) => {
  if (typeStr.value == 1) {
    attendArr.value = arr
    formData.value.numStr1 = arr.length
    isGetInfo.value = arr.length ? true : false
  } else {
    dutyArr.value = arr
    formData.value.numStr2 = arr.length || '0'
  }
}
useWatch(files1, formData, files2, attendArr, formRef, dutyArr)

// 查找name
const getName = (arr: any, val: string, key: string, label: string) => {
  let str = ''
  arr.length &&
    arr.forEach((item: any) => {
      if (item[key] == val) {
        str = item[label]
      }
    })
  return str
}
// 返回
const onCancel = () => {
  router.push('/educate/train')
}
// 提交
const id_ = ref('')
const onSumbit = async () => {
  useSumbit(
    route,
    formData,
    typesArr,
    levelArr,
    id_,
    formRef,
    onCancel,
    getName,
    isGetInfo
  )
}
const isDisable = ref(false)
onMounted(() => {
  useMounted(
    route,
    id_,
    formData,
    changeItem,
    attendArr,
    dutyArr,
    files1,
    files2,
    isDisable
  )
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
<style lang="scss">
.sps__ {
  border-radius: 2px;
  background: #f6f8fa;
  font-size: 14px;
  height: 30px;
  line-height: 30px;
  padding: 0 8px;
  color: #000;
  margin: 0 10px 10px 0;
}
.redB_ {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}
.tagBorder__ {
  width: 100%;
  height: 40px;
  line-height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px 12px;
  box-sizing: border-box;
  display: flex;
  span {
    border-radius: 2px;
    background: #f6f8fa;
    font-size: 14px;
    height: 30px;
    line-height: 30px;
    padding: 0 8px;
    color: #000;
    margin-right: 10px;
  }
  .placeholeder__ {
    color: rgba(0, 0, 0, 0.25) !important;
    line-height: 28px !important;
  }
}
.mL24__ {
  padding-left: 24px !important;
  box-sizing: border-box;
}
.waringInfoBas__ {
  padding: 0 0 20px 69px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  color: #ff9900;
  font-size: 12px;
  .el-icon {
    margin: 0 4px 0 0;
    font-size: 16px;
  }
}
.cardPhotoClass__.el-form-item--default {
  margin-bottom: 10px !important;
}
.trainingContent__ {
  .el-textarea.el-input--default {
    position: relative;
    .el-input__count {
      position: absolute;
      bottom: 10px !important;
      right: 10px !important;
    }
  }
}
.numStr1__.el-form-item.is-error .el-input__wrapper {
  box-shadow: none !important;
}
.patorSps {
  // overflow: hidden;
  // display: inline-block;
  // width: 100%;
  // text-overflow: ellipsis;
  // white-space: nowrap;
  line-height: 40px !important;
}
</style>
