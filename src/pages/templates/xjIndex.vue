<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import * as api from '@/api/index.ts'
import useTableData from '@/hooks/useTableData'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const router = useRouter()
const templateTypes = [
  {
    label: '巡检模板',
    value: 1
  }
]
const searchProps = ref<Obj[]>([
  {
    prop: 'insTemName',
    label: '模板名称',
    width: '80px',
    type: 'input'
  },
  {
    prop: 'insTemType',
    label: '模板类型',
    width: '80px',
    type: 'select',
    options: templateTypes
  }
])
let searchData = reactive<Obj>({
  insTemName: '',
  insTemType: 1,
  pageSize: 10,
  pageNum: 1
})
const columns = [
  {
    prop: 'insTemNo',
    label: '模板ID'
  },
  {
    prop: 'insTemType',
    label: '模板类型',
    formatter: (row: any) =>
      templateTypes.find((item) => item.value == row.insTemType)?.label || '--'
  },
  {
    prop: 'insTemName',
    label: '模板名称'
  },
  {
    prop: 'updateUser',
    label: '上次修改人',
    formatter: (row: any) => row.updateUser?.split('_')?.at(0) || '--'
  },
  {
    prop: 'updateTime',
    label: '上次修改时间'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 110,
    fixed: 'right'
  }
]
const getTableData = async (data: Obj, loading: Loading) => {
  return await api.post(
    '/operate/inspectionMainTemplateController/getInspectionMainTemplateList',
    { ...data, code: localStorage.getItem('PVOM_COMPANY_CODE') },
    loading
  )
}
const {
  tableData,
  tablePage,
  tableLoading,
  changeCurrent,
  changeSize,
  changeData
} = useTableData(getTableData, searchData, { immediate: false })

const route = useRoute()
watch(
  () => [companyCode.data, route],
  () => {
    companyCode.data && changeData()
  },
  { immediate: true }
)

const handleSearch = async (val: any) => {
  Object.keys(searchData).forEach((key) => {
    searchData[key] = val[key]
  })
  changeData(true)
}

const handleDelete = async (row: any) => {
  await api.post({
    url:
      '/operate/inspectionMainTemplateController/deleteInspectionMainTemplate' +
      '?id=' +
      row.id,
    loading: true
  })
  ElMessage({
    message: '删除成功！',
    type: 'success'
  })
  changeData('delete')
}

const handleAdd = () => {
  router.push(`/templates/xj/add`)
}
const handleView = (row: Obj) => {
  router.push(`/templates/xj/detail?id=${row.id}`)
}
</script>

<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>巡检模板列表</p>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          <span>新建模板</span>
        </el-button>
      </div>
      <TablePagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :show-overflow-tooltip="true"
        background
        :columns="columns"
        :data="tableData.data"
        :total="tableData.total"
        :loading="tableLoading"
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        class="table-pagination"
        @handle-size-change="changeSize"
        @handle-current-change="changeCurrent"
      >
        <template #operate="{ row }">
          <el-button type="primary" link @click="() => handleView(row)">
            查看
          </el-button>
          <el-popconfirm
            title="是否确认删除模板？"
            width="180px"
            @confirm="() => handleDelete(row)"
          >
            <template #reference>
              <el-button type="primary" link :disabled="row.id === 1">
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </TablePagination>
    </div>
  </div>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
