import { ticket as ticketAPI } from '@/api/index.ts'
export default function () {
  // 工作票的jsa作业类别和作业风险
  const homeworkTyepList = ref<any[]>([])
  const homeworkRiskList = ref<any[]>([])
  const getHomeworkList = async () => {
    //  作业类别
    try {
      const { data } = await ticketAPI.getTwoTicketTyepList({})
      homeworkTyepList.value = data?.data || []
    } catch (e) {
      homeworkTyepList.value = []
    }

    //  作业风险
    try {
      const { data } = await ticketAPI.getTwoTicketRiskList({})
      homeworkRiskList.value = data.data || []
    } catch (e) {
      homeworkRiskList.value = []
    }
  }
  onMounted(() => {
    getHomeworkList()
  })

  return {
    homeworkTyepList,
    homeworkRiskList
  }
}
