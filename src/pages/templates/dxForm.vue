<template>
  <div class="page-operate">
    <div class="operate-title">{{ pageTitle }}</div>
    <div class="custom-right">
      <el-button
        v-if="type === 'detail'"
        type="primary"
        @click="router.replace(`/templates/dx/edit?id=${id}`)"
      >
        <el-icon><Edit /></el-icon> &nbsp;编辑
      </el-button>
    </div>
  </div>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :inline="true"
    label-width="100px"
    label-position="right"
    :disabled="type === 'detail'"
  >
    <div class="info-base pb-0px" style="margin-bottom: 0">
      <el-row :gutter="50">
        <el-col :span="12">
          <el-form-item label="模板类型" prop="modelType">
            <el-select
              v-model="formData.modelType"
              placeholder="请选择模板类型"
              @change="changeModelType"
            >
              <el-option
                v-for="item in templateTypes"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效范围" prop="operationInfoDtoList">
            <el-select
              v-model="formData.operationInfoDtoList"
              placeholder="请选择生效范围"
              clearable
              multiple
              value-key="operationCode"
            >
              <el-option
                v-for="item in companyList"
                :key="item.operationCode"
                :label="item.operationName"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板名称" prop="modelName">
            <el-input
              v-model.trim="formData.modelName"
              placeholder="请输入模板名称"
              maxlength="20"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </el-form>
  <DxSafe
    ref="dxSafeRef"
    :work-ticket-data="formData"
    :v-model="formData.modelType"
  />
  <DxJsa
    v-if="formData.modelType !== 3"
    ref="dxJsaRef"
    :work-ticket-data="formData"
  />
  <DxRisk
    v-if="formData.modelType !== 3"
    ref="dxRiskRef"
    :work-ticket-data="formData"
  />
  <div v-if="type !== 'detail'" class="page-footer">
    <el-button
      plain
      @click="
        type === 'edit'
          ? router.replace(`/templates/dx/detail?id=${id}`)
          : router.back()
      "
    >
      返回
    </el-button>
    <el-button type="primary" @click="onSumbit">提交</el-button>
  </div>
</template>
<script setup lang="ts">
import * as api from '@/api/index.ts'
import useDist from './hooks/useDist'
import DxSafe from './components/dxSafe.vue'
import DxJsa from './components/dxJsa.vue'
import DxRisk from './components/dxRisk.vue'

const { templateTypes } = useDist()
const router = useRouter()
const route = useRoute()
const type = route.params.type as string
const id = route.query.id

const dxSafeRef = ref()
const dxJsaRef = ref()
const dxRiskRef = ref()

const pageTitle = computed(() => {
  const title = route.meta.title as Obj
  if (Object.hasOwnProperty.call(title, 'data')) {
    return title.data[type] || ''
  }
  return title
})
const formRef = ref()
const onSumbit = async () => {
  if (!formRef.value) return
  try {
    formData.value.modelTemplateDto.safeStepDtoList =
      await dxSafeRef.value?.submitForm()
    if (dxJsaRef.value) {
      const { homeworkType, homeworkRisk } = await dxJsaRef.value?.submitForm()
      formData.value.modelTemplateDto.jsaTicketDto.homeworkType = homeworkType
      formData.value.modelTemplateDto.jsaTicketDto.homeworkRisk = homeworkRisk
    }

    if (dxRiskRef.value) {
      formData.value.modelTemplateDto.jsaTicketDto.processRiskDtoList =
        await dxRiskRef.value?.submitForm()
    }
    console.log(formData.value.modelTemplateDto.jsaTicketDto.homeworkType)
    await formRef.value.validate(async (valid: any) => {
      if (valid) {
        let url = '/operate/modelTicketController/addModelTicket'
        if (id) {
          url = '/operate/modelTicketController/updateModelTicket'
        }
        await api.post({
          url,
          data: {
            ...formData.value
          },
          loading: true
        })
        type === 'edit'
          ? router.replace(`/templates/dx/detail?id=${id}`)
          : router.back()
      } else {
        ElMessage.warning('有必填项未填写，请检查！')
      }
    })
  } catch (error) {}
}

const formData = ref<any>({
  modelType: Number(route.query.modelType) || 2,
  operationInfoDtoList: (function () {
    if (route.query.company) {
      return [
        {
          operationName: String(route.query.company)?.split('__')?.[0],
          operationCode: String(route.query.company)?.split('__')?.[1]
        }
      ]
    }
    return []
  })(),
  modelName: '',
  modelTemplateDto: {
    safeStepDtoList: [],
    jsaTicketDto: {}
  }
})
const changeModelType = () => {
  formData.value.modelTemplateDto = {
    safeStepDtoList: [],
    jsaTicketDto: {}
  }

  dxSafeRef.value?.resetData()
  dxJsaRef.value?.resetData()
  dxRiskRef.value?.resetData()
}
const rules = reactive<any>({
  modelType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
  operationInfoDtoList: [
    { required: true, message: '请选择生效范围', trigger: 'change' }
  ],
  modelName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }]
})

const companyList = ref<any>([])
onMounted(async () => {
  await getCompany()
  if (id) {
    const { data } = await api.get({
      url: '/operate/modelTicketController/getModelTicketById',
      data: { id },
      loading: true
    })
    formData.value = data || {}
  }
})
const getCompany = async () => {
  try {
    const { data } = await api.get(
      '/operate/operationUser/getOperatorsListByUser'
    )
    companyList.value =
      data?.map((item: any) => ({
        operationName: item.companyName,
        operationCode: item.companyCode
      })) || []
    if (
      !companyList.value
        ?.map((e: any) => e.operationCode)
        ?.includes(formData.value.operationInfoDtoList?.[0]?.operationCode)
    ) {
      formData.value.operationInfoDtoList = []
    }
  } catch (e: any) {
    companyList.value = []
  }
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.pb-0px {
  padding-bottom: 0px;
}
.custom-right {
  display: flex;
  padding-right: 4px;
  .el-select {
    width: 120px;
  }
  .el-button {
    margin-left: 10px;
  }
}
</style>
