<template>
  <el-form
    ref="formRef"
    :model="formData"
    :disabled="$route.params.type === 'detail'"
    class="info-base safe-content"
  >
    <div class="safe-title">
      <div class="line"></div>
      {{ workTicketData.modelType === 3 ? '操作内容' : '安全措施' }}
    </div>
    <div v-if="workTicketData.modelType === 1" class="breif">
      <el-text class="mx-1" type="danger">*</el-text
      >应拉开的断路器、隔离开关，应取下的熔断器。（包括填写前已断开、取下的，注明编号）
    </div>
    <TableRow
      ref="t1Ref"
      :columns="safeColumns"
      :table-data="safeList1"
      :maxlength="maxlength"
      :max-len="workTicketData.modelType === 3 ? 200 : 50"
      @get-tabel-data="getT1Data"
    ></TableRow>
    <template v-if="workTicketData.modelType === 1">
      <div class="breif">
        <el-text class="mx-1" type="danger">*</el-text
        >应解除的继电保护连接片等（包括填写前已解除的，注明编号）
      </div>
      <TableRow
        ref="t2Ref"
        :columns="safeColumns"
        :table-data="safeList2"
        :maxlength="maxlength"
        @get-tabel-data="getT2Data"
      ></TableRow>
      <div class="breif">
        <el-text class="mx-1" type="danger">*</el-text
        >应装接地线、应合接地刀闸（注明确实地点、名称及接地线编号）
      </div>
      <TableRow
        ref="t3Ref"
        :columns="safeColumns"
        :table-data="safeList3"
        :maxlength="maxlength"
        @get-tabel-data="getT3Data"
      ></TableRow>

      <div class="breif">
        <el-text class="mx-1" type="danger">*</el-text
        >应设遮拦、应挂标识牌及防止二次回路误碰等措施
      </div>
      <TableRow
        ref="t4Ref"
        :columns="safeColumns"
        :table-data="safeList4"
        :maxlength="maxlength"
        @get-tabel-data="getT4Data"
      ></TableRow>
      <div class="breif">
        <el-text class="mx-1" type="danger">*</el-text
        >应取下下列开关的合闸、操作、信号保险
      </div>
      <TableRow
        ref="t5Ref"
        :columns="safeColumns"
        :table-data="safeList5"
        :maxlength="maxlength"
        @get-tabel-data="getT5Data"
      ></TableRow>
    </template>
  </el-form>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import TableRow from './tableRow.vue'

const props = defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  }
})
// 表格
const safeColumns = ref([
  {
    prop: 'selection',
    label: '',
    selectable: (_row: any, index: number) => index !== 0
  },
  { prop: 'index', label: '', width: 55 },
  {
    prop: 'safeStepContext',
    label: '安全措施',
    slotName: 'safeStepContext',
    minWidth: 300,
    placeholder: '请输入安全措施'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 80,
    fixed: 'right'
  }
])
const safeList1 = ref<any[]>([])
const safeList2 = ref<any[]>([])
const safeList3 = ref<any[]>([])
const safeList4 = ref<any[]>([])
const safeList5 = ref<any[]>([])

watch(
  () => props?.workTicketData,
  (val: any) => {
    if (val.modelTemplateDto.safeStepDtoList) {
      resetData()
      val.modelTemplateDto.safeStepDtoList.forEach((item: any) => {
        if (item.safeType == 1) {
          safeList1.value.push(item)
        } else if (item.safeType == 2) {
          safeList2.value.push(item)
        } else if (item.safeType == 3) {
          safeList3.value.push(item)
        } else if (item.safeType == 4) {
          safeList4.value.push(item)
        } else if (item.safeType == 5) {
          safeList5.value.push(item)
        }
      })
    }
  }
)
const maxlength = ref(100)
// 安全措施第1部分
const t1Ref = ref<any>(null)
const t1Data = ref<any>([])
const t1Valid = ref(false)
const getT1Data = (val: any) => {
  t1Valid.value = val.valid
  if (val.data?.length) {
    t1Data.value = val.data.map((item: any) => {
      return {
        id: item.id || '',
        safeStepContext: item.safeStepContext || '',
        safeType: 1
      }
    })
  }
}
// 安全措施第2部分
const t2Ref = ref<any>(null)
const t2Data = ref<any>([])
const t2Valid = ref(false)
const getT2Data = (val: any) => {
  t2Valid.value = val.valid
  if (val.data?.length) {
    t2Data.value = val.data.map((item: any) => {
      return {
        id: item.id || '',
        safeStepContext: item.safeStepContext || '',
        safeType: 2
      }
    })
  }
}
// 安全措施第3部分
const t3Ref = ref<any>(null)
const t3Data = ref<any>([])
const t3Valid = ref(false)
const getT3Data = (val: any) => {
  t3Valid.value = val.valid
  if (val.data?.length) {
    t3Data.value = val.data.map((item: any) => {
      return {
        id: item.id || '',
        safeStepContext: item.safeStepContext || '',
        safeType: 3
      }
    })
  }
}
// 安全措施第4部分
const t4Ref = ref<any>(null)
const t4Data = ref<any>([])
const t4Valid = ref(false)
const getT4Data = (val: any) => {
  t4Valid.value = val.valid
  if (val.data?.length) {
    t4Data.value = val.data.map((item: any) => {
      return {
        id: item.id || '',
        safeStepContext: item.safeStepContext || '',
        safeType: 4
      }
    })
  }
}
// 安全措施第5部分
const t5Ref = ref<any>(null)
const t5Data = ref<any>([])
const t5Valid = ref(false)
const getT5Data = (val: any) => {
  t5Valid.value = val.valid
  if (val.data?.length) {
    t5Data.value = val.data.map((item: any) => {
      return {
        id: item.id || '',
        safeStepContext: item.safeStepContext || '',
        safeType: 5
      }
    })
  }
}

// 提交 保存草稿1 签发审批2
let formData = ref()
const formRef = ref<FormInstance>()
const submitForm = async (type: number) => {
  if (!formRef.value) return Promise.reject()
  await t1Ref.value?.submitTable(type)
  await t2Ref.value?.submitTable(type)
  await t3Ref.value?.submitTable(type)
  await t4Ref.value?.submitTable(type)
  await t5Ref.value?.submitTable(type)
  formData.value = [
    ...t1Data.value,
    ...t2Data.value,
    ...t3Data.value,
    ...t4Data.value,
    ...t5Data.value
  ]
  let valid = false
  if (props.workTicketData.modelType === 1) {
    valid =
      t1Valid.value &&
      t2Valid.value &&
      t3Valid.value &&
      t4Valid.value &&
      t5Valid.value
  } else {
    valid = t1Valid.value
  }

  if (!valid) {
    ElMessage.warning('有必填项未填写，请检查！')
    return Promise.reject()
  }
  return formData.value
}
const resetData = () => {
  safeList1.value = []
  safeList2.value = []
  safeList3.value = []
  safeList4.value = []
  safeList5.value = []
  if (props.workTicketData.modelType === 3) {
    safeColumns.value[2].label = '操作项目'
    safeColumns.value[2].placeholder = '请输入操作项目'
  } else {
    safeColumns.value[2].label = '安全措施'
    safeColumns.value[2].placeholder = '请输入安全措施'
  }
}

defineExpose({
  submitForm,
  resetData
})
</script>
<style scoped src="../assets/work.scss"></style>
<style scoped lang="scss">
.safe-content {
  padding-bottom: 24px !important;
}
</style>
