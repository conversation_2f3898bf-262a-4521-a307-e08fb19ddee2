<template>
  <el-form
    ref="ruleFormRef"
    :key="formKey"
    :model="ruleForm"
    :rules="rules"
    :disabled="$route.params.type === 'detail'"
    class="info-base two-jsa"
  >
    <div class="safe-title">
      <div class="line"></div>
      过程风险预控
    </div>
    <TableRow
      ref="riskRef"
      class="mb-12px"
      :columns="riskColumns"
      :table-data="ticketProcessRiskList"
      :maxlength="1024"
      @get-tabel-data="getRiskData"
    ></TableRow>
  </el-form>
</template>
<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import TableRow from './tableRow.vue'

interface RuleForm {
  value1: string
  value3: string
}
const formKey = ref(0)
formKey.value = Date.now()
const props = defineProps({
  workTicketData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const riskColumns = ref([
  {
    prop: 'selection',
    label: '',
    selectable: (_row: any, index: number) => index !== 0
  },
  { prop: 'index', label: '序号', width: 55 },
  {
    prop: 'process',
    label: '作业过程',
    slotName: 'process',
    minWidth: 100,
    placeholder: '请输入作业过程'
  },
  {
    prop: 'riskDesc',
    label: '风险描述',
    slotName: 'riskDesc',
    minWidth: 100,
    placeholder: '请输入风险描述'
  },
  {
    prop: 'riskLevel',
    label: '风险等级',
    slotName: 'riskLevel',
    minWidth: 100,
    placeholder: '请输入风险等级'
  },
  {
    prop: 'control',
    label: '管控措施',
    slotName: 'control',
    minWidth: 100,
    placeholder: '请输入管控措施'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 80,
    fixed: 'right'
  }
])
const ticketProcessRiskList = ref<any[]>([])
watch(
  () => props.workTicketData,
  async (val: any) => {
    ticketProcessRiskList.value =
      val.modelTemplateDto.jsaTicketDto.processRiskDtoList || []
  }
)
// 过程风险预控
const riskRef = ref<any>(null)
const riskData = ref<any>([])
const riskValid = ref(false)
const getRiskData = (val: any) => {
  riskValid.value = val.valid
  if (val.data?.length) {
    riskData.value = val.data
  }
}

// 表单
const ruleFormRef = ref<FormInstance>()
const ruleForm = ref({
  value1: '',
  value3: ''
})
const rules = reactive<FormRules<RuleForm>>({
  value1: [{ required: true, message: '请输入补充说明', trigger: 'blur' }],
  value3: [{ required: true, message: '请输入补充说明', trigger: 'blur' }]
})

// 校验
const submitForm = async (type: number) => {
  // jsa部分的必填校验
  let valid: boolean = true
  // 过程风险预防未填写
  await riskRef.value.submitTable(type)
  if (!riskValid.value) {
    valid = false
  }
  if (!valid) {
    ElMessage.warning('有必填项未填写，请检查！')
    return Promise.reject()
  }
  return riskData.value
}
const resetData = () => {
  ticketProcessRiskList.value = []
}
defineExpose({
  submitForm,
  resetData
})
</script>
<style src="../assets/work.scss"></style>
