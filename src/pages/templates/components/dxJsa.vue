<template>
  <el-form
    ref="ruleFormRef"
    :key="formKey"
    :model="ruleForm"
    :rules="rules"
    :disabled="$route.params.type === 'detail'"
    class="info-base two-jsa"
  >
    <div class="safe-title">
      <div class="line"></div>
      JSA票补充填写信息
    </div>
    <!-- 作业类别 -->
    <div class="breif">
      <el-text class="mx-1" type="danger">*</el-text>作业类别
    </div>
    <el-checkbox-group
      v-model="homeworkType"
      :disabled="isDisabled"
      :class="homeworkType.length === 0 && checkField1 ? 'custom-error' : null"
      @change="() => (checkField1 = true)"
    >
      <el-checkbox
        v-for="item in homeworkTyepList"
        :key="item.value"
        :label="item.value"
        :value="item.value"
        class="checkbox__"
      >
        {{ item.name }}
        <el-form-item
          v-if="item.value == 20 && homeworkType.includes(20)"
          prop="value1"
          class="lastItem"
        >
          <el-input
            v-model.trim="ruleForm.value1"
            placeholder="请输入补充说明"
            clearable
            maxlength="64"
            :disabled="isDisabled"
          />
        </el-form-item>
      </el-checkbox>
    </el-checkbox-group>

    <!-- 作业风险 -->
    <div class="brief">
      <el-text class="mx-1" type="danger">*</el-text>作业风险
    </div>
    <el-checkbox-group
      v-model="homeworkRisk"
      :disabled="isDisabled"
      :class="homeworkRisk.length === 0 && checkField2 ? 'custom-error' : null"
      @change="() => (checkField2 = true)"
    >
      <el-checkbox
        v-for="item in homeworkRiskList"
        :key="item.value"
        :label="item.value"
        :value="item.value"
        class="checkbox__"
      >
        {{ item.name }}
        <el-form-item
          v-if="item.value == 13 && homeworkRisk.includes(13)"
          prop="value3"
          class="lastItem"
        >
          <el-input
            v-model.trim="ruleForm.value3"
            placeholder="请输入补充说明"
            clearable
            :disabled="isDisabled"
            maxlength="64"
          />
        </el-form-item>
      </el-checkbox>
    </el-checkbox-group>
  </el-form>
</template>
<script setup lang="ts">
import useWork from '../hooks/useWork.ts'
import type { FormInstance, FormRules } from 'element-plus'

interface RuleForm {
  value1: string
  value3: string
}
const formKey = ref(0)
formKey.value = Date.now()
const route = useRoute()
const isDisabled = computed(() => {
  return route.params && route.params.pageStatus == 'look'
})
const homeworkType = ref<any>([]) // 作业类别
const homeworkRisk = ref<any>([]) // 作业风险
const { homeworkTyepList, homeworkRiskList } = useWork()
const props = defineProps({
  workTicketData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const checkField1 = ref(false)
const checkField2 = ref(false)
watch(
  () => props.workTicketData,
  async (val: any) => {
    const typeData = val?.modelTemplateDto?.jsaTicketDto?.homeworkType || ''
    homeworkType.value = typeData?.length
      ? typeData.split('_').map((item: any) => {
          if (item.includes('20@')) {
            ruleForm.value.value1 = item.replace('20@', '')
            return 20
          }
          return Number(item)
        })
      : []

    const riskData = val?.modelTemplateDto?.jsaTicketDto?.homeworkRisk || ''
    homeworkRisk.value = riskData?.length
      ? riskData.split('_').map((item: any) => {
          if (item.includes('13@')) {
            ruleForm.value.value3 = item.replace('13@', '')
            return 13
          }
          return Number(item)
        })
      : []
  }
)
// 表单
const ruleFormRef = ref<FormInstance>()
const ruleForm = ref({
  value1: '',
  value3: ''
})
const rules = reactive<FormRules<RuleForm>>({
  value1: [{ required: true, message: '请输入补充说明', trigger: 'blur' }],
  value3: [{ required: true, message: '请输入补充说明', trigger: 'blur' }]
})

// 校验
const submitForm = async () => {
  checkField1.value = true
  checkField2.value = true
  // jsa部分的必填校验
  let valid: boolean = true
  // 未勾选 作业类别、作业风险
  if (
    !homeworkType.value ||
    !homeworkType.value.length ||
    !homeworkRisk ||
    !homeworkRisk.value.length
  ) {
    valid = false
  }

  // 作业类别和作业风险，勾选了其他，未填写输入框的内容
  await ruleFormRef.value?.validate((v: boolean) => {
    if (!v) {
      valid = false
    }
  })

  if (!valid) {
    ElMessage.warning('有必填项未填写，请检查！')
    return Promise.reject('warning')
  }

  let homeworkTyepArr: any = []
  if (homeworkType.value?.includes(20) && ruleForm.value.value1) {
    homeworkTyepArr = homeworkType.value.filter((item: any) => item != 20)
    homeworkTyepArr.push('20@' + ruleForm.value.value1)
  } else {
    homeworkTyepArr = homeworkType.value
  }

  let homeworkRiskArr: any = []
  if (homeworkRisk.value?.includes(13) && ruleForm.value.value3) {
    homeworkRiskArr = homeworkRisk.value.filter((item: any) => item != 13)
    homeworkRiskArr.push('13@' + ruleForm.value.value3)
  } else {
    homeworkRiskArr = homeworkRisk.value
  }

  return {
    homeworkType: homeworkTyepArr.length ? homeworkTyepArr.join('_') : '',
    homeworkRisk: homeworkRiskArr.length ? homeworkRiskArr.join('_') : ''
  }
}
const resetData = () => {
  homeworkType.value = []
  homeworkRisk.value = []
}
defineExpose({
  submitForm,
  resetData
})
</script>
<style src="../assets/work.scss"></style>
<style scoped lang="scss">
.two-jsa {
  padding-bottom: 0 !important;
}
.custom-error {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}
</style>
