<script setup lang="ts">
const data = defineModel({ required: true, type: Object })
const props = defineProps({
  index: {
    type: Number,
    required: true
  }
})
const route = useRoute()
const type = route.params.type as string
// const id = route.query.id

const itemData: any = computed(() => data.value[props.index])

const addField = () => {
  itemData.value.inspectionSubclassDtoList.push({
    subclassName: '',
    standardName: '',
    standardType: '4',
    standardResult: '20',
    isRequired: 1,
    __key__: Date.now()
  })
}

const selectedData = ref<Obj[]>([])
const changeSelection = (val: Obj[]) => {
  selectedData.value = [...val]
}
const deleteField = (index?: number) => {
  if (typeof index !== 'undefined') {
    data.value[props.index].inspectionSubclassDtoList.splice(index, 1)
  } else {
    data.value[props.index].inspectionSubclassDtoList = data.value[
      props.index
    ].inspectionSubclassDtoList.filter((item: Obj) => {
      return !selectedData.value.find((o: Obj) => Object.isEqual(o, item))
    })
  }

  if (data.value[props.index].inspectionSubclassDtoList.length === 0) {
    data.value.splice(props.index, 1)
  }
}

const fieldTypes = [
  {
    value: '4',
    label: '单行文本'
  },
  {
    value: '2',
    label: '多行文本'
  },
  {
    value: '3',
    label: '图片'
  },
  {
    value: '1',
    label: '单选'
  },
  {
    value: '5',
    label: '多选'
  },
  {
    value: '6',
    label: '时间日期'
  }
]

const changeFieldType = (index: number) => {
  const obj: any = {
    4: '20',
    2: '100',
    3: '3',
    1: [],
    5: [],
    6: '1'
  }
  itemData.value.inspectionSubclassDtoList[index].standardResult =
    obj[itemData.value.inspectionSubclassDtoList[index].standardType]
}
// Tags操作
const inputVisible = ref<any>({})
const inputValue = ref<any>({})
const inputRef = ref<any>({})
const getInputRef = (el: any, index: number) => {
  if (el) {
    inputRef.value[index] = el
  }
}
const tagClose = (index: number, tag: string) => {
  itemData.value.inspectionSubclassDtoList[index].standardResult.splice(
    itemData.value.inspectionSubclassDtoList[index].standardResult.indexOf(tag),
    1
  )
}
const tagInputConfirm = (index: number) => {
  if (inputValue.value[index]) {
    if (
      ['[', ']', '{', '}', "'", '"', '\,'].includes(inputValue.value[index])
    ) {
      ElMessage({
        type: 'warning',
        message: '巡检结果选项不能包含特殊字符！'
      })
      return
    }
    if (
      itemData.value.inspectionSubclassDtoList[index].standardResult.includes(
        inputValue.value[index]
      )
    ) {
      ElMessage({
        type: 'warning',
        message: '巡检结果选项不能重复！'
      })
      return
    }
    itemData.value.inspectionSubclassDtoList[index].standardResult.push(
      inputValue.value[index]
    )
  }
  inputVisible.value[index] = false
  inputValue.value[index] = ''
}
const showInput = (index: number) => {
  inputVisible.value[index] = true
  nextTick(() => {
    inputRef.value[index].input?.focus()
  })
}
</script>
<template>
  <div class="info-base">
    <el-row :gutter="50">
      <el-col :span="12">
        <el-form-item
          :label="`巡检大类${index + 1}`"
          :prop="`inspectionClassDtoList.${index}.className`"
          :rules="{
            required: true,
            message: '请输入巡检大类',
            trigger: 'blur'
          }"
        >
          <el-input
            v-model.trim="itemData.className"
            placeholder="请输入巡检大类"
            maxlength="20"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-button
          v-if="type !== 'detail'"
          type="primary"
          style="float: right; margin-top: 8px"
          @click="() => deleteField()"
        >
          <el-icon><Delete /></el-icon> &nbsp;批量删除
        </el-button>
      </el-col>
    </el-row>
    <el-table
      :data="itemData.inspectionSubclassDtoList"
      :border="true"
      @selection-change="changeSelection"
    >
      <el-table-column v-if="type !== 'detail'" type="selection" width="55" />
      <el-table-column type="index" label="序号" width="55" />
      <el-table-column prop="subclassName" label="巡检项">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`inspectionClassDtoList.${index}.inspectionSubclassDtoList.${$index}.subclassName`"
            :rules="{
              required: true,
              trigger: 'blur',
              message: '请输入巡检项'
            }"
          >
            <el-input
              v-model.trim="row.subclassName"
              placeholder="请输入巡检项"
              maxlength="20"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="standardName" label="巡检标准">
        <template #default="{ row, $index }">
          <el-form-item
            :prop="`inspectionClassDtoList.${index}.inspectionSubclassDtoList.${$index}.standardName`"
            :rules="{
              required: true,
              trigger: 'blur',
              message: '请输入巡检标准'
            }"
          >
            <el-input
              v-model.trim="row.standardName"
              placeholder="请输入巡检标准"
              maxlength="20"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="standardType" label="字段类型">
        <template #default="{ row, $index }">
          <el-select
            v-model="row.standardType"
            placeholder="请选择字段类型"
            @change="() => changeFieldType($index)"
          >
            <el-option
              v-for="item in fieldTypes"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="standardResult" label="巡检结果">
        <template #default="{ row, $index }">
          <el-select
            v-if="row.standardType === '4' || row.standardType === '2'"
            v-model="row.standardResult"
          >
            <el-option
              :value="row.standardResult"
              :label="`不超过${row.standardResult}字`"
            />
          </el-select>
          <el-select
            v-if="row.standardType === '3'"
            v-model="row.standardResult"
          >
            <el-option :value="3" :label="`不超过3张图片`" />
            <el-option :value="5" :label="`不超过5张图片`" />
          </el-select>
          <template v-if="row.standardType === '1' || row.standardType === '5'">
            <el-form-item
              :prop="`inspectionClassDtoList.${index}.inspectionSubclassDtoList.${$index}.standardResult`"
              :rules="{
                required: true,
                trigger: 'change',
                validator: (_rule: any, value: any, callback: any) => {
                  value.length > 0
                    ? callback()
                    : callback(new Error('巡检结果至少要有一个选项！'))
                }
              }"
              :error="
                row.standardResult.length === 0
                  ? '巡检结果至少要有一个选项！'
                  : ''
              "
            >
              <el-tag
                v-for="tag in row.standardResult"
                :key="tag"
                effect="plain"
                :closable="type !== 'detail'"
                :disable-transitions="false"
                style="margin: 3px 6px 3px 0"
                @close="() => tagClose($index, tag)"
              >
                {{ tag }}
              </el-tag>
              <el-input
                v-if="inputVisible[$index]"
                :ref="(el: any) => getInputRef(el, $index)"
                v-model.trim="inputValue[$index]"
                @keyup.enter="() => tagInputConfirm($index)"
                @blur="() => tagInputConfirm($index)"
              />
              <el-button
                v-else-if="type !== 'detail'"
                size="small"
                type="primary"
                style="margin: 3px 0"
                @click="() => showInput($index)"
              >
                <el-icon><Plus /></el-icon> &nbsp;新增
              </el-button>
            </el-form-item>
          </template>
          <el-select
            v-if="row.standardType === '6'"
            v-model="row.standardResult"
          >
            <el-option value="1" label="时间点" />
            <el-option value="2" label="时间段" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="isRequired" label="是否必填">
        <template #default="{ row }">
          <el-switch
            v-model="row.isRequired"
            inline-prompt
            active-text="是"
            inactive-text="否"
            :active-value="0"
            :inactive-value="1"
          />
        </template>
      </el-table-column>
      <el-table-column v-if="type !== 'detail'" label="操作" width="70px">
        <template #default="{ $index }">
          <el-button type="primary" link @click="() => deleteField($index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="type !== 'detail'" class="table-footer">
      <el-button :plain="true" @click="addField">
        <el-icon><Plus /></el-icon> &nbsp;添加行
      </el-button>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.info-base {
  padding-bottom: 24px !important;
}
.table-footer {
  height: 48px;
  line-height: 48px;
  text-align: left;
  border: 1px solid #e6e6e8;
  border-top: none;
  > .el-button {
    border: none;
  }
}
</style>
