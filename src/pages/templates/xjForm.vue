<template>
  <div class="page-operate">
    <div class="operate-title">{{ pageTitle }}</div>
    <div class="custom-right">
      <el-select
        v-if="type === 'detail'"
        v-model="currentVersion"
        @change="getDetailByVersion"
      >
        <el-option
          v-for="version in formData.versionList"
          :key="version"
          :value="version"
          :label="`V${version}版本`"
        />
      </el-select>
      <el-button
        v-if="type === 'detail' && currentVersion === latestVersion"
        type="primary"
        @click="router.replace(`/templates/xj/edit?id=${id}`)"
      >
        <el-icon><Edit /></el-icon> &nbsp;编辑
      </el-button>
    </div>
  </div>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :inline="true"
    label-width="100px"
    label-position="right"
    :disabled="type === 'detail'"
  >
    <div class="info-base pb-0px">
      <el-row :gutter="50">
        <el-col :span="12">
          <el-form-item label="模板类型" prop="insTemType">
            <el-select
              v-model="formData.insTemType"
              placeholder="请选择模板类型"
            >
              <el-option label="巡检模板" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效范围" prop="operationInfosDtoList">
            <el-select
              v-model="formData.operationInfosDtoList"
              placeholder="请选择生效范围"
              clearable
              multiple
              value-key="operationCode"
            >
              <el-option
                v-for="item in companyList"
                :key="item.operationCode"
                :label="item.operationName"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板名称" prop="insTemName">
            <el-input
              v-model="formData.insTemName"
              placeholder="请输入模板名称"
              maxlength="20"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="currentVersion === latestVersion" :span="12">
          <el-form-item label="模板版本" prop="insTemVersion">
            <el-select
              v-model="formData.insTemVersion"
              placeholder="请选择模板版本"
              :disabled="true"
            >
              <el-option
                v-for="version in formData.versionList"
                :key="version"
                :value="version"
                :label="`V${version}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-else :span="12">
          <el-form-item label="修改人" prop="updateUser">
            <el-input v-model.trim="updateUser" disabled />
          </el-form-item>
        </el-col>
        <el-col v-if="currentVersion !== latestVersion" :span="12">
          <el-form-item label="修改时间" prop="updateTime">
            <el-input v-model.trim="formData.updateTime" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <template
      v-for="(_item, index) in formData.inspectionClassDtoList"
      :key="index"
    >
      <XjFormItem v-model="formData.inspectionClassDtoList" :index="index" />
    </template>
    <el-button
      v-if="type !== 'detail'"
      type="primary"
      style="margin: 0 0 24px 24px"
      @click="addItem"
    >
      <el-icon><Plus /></el-icon> &nbsp;添加大类
    </el-button>
  </el-form>
  <div v-if="type !== 'detail'" class="page-footer">
    <el-button
      plain
      @click="
        type === 'edit'
          ? router.replace(`/templates/xj/detail?id=${id}`)
          : router.back()
      "
    >
      返回
    </el-button>
    <el-button type="primary" @click="onSumbit">提交</el-button>
  </div>
</template>
<script setup lang="ts">
import * as api from '@/api/index.ts'
import XjFormItem from './components/xjFormItem.vue'
const router = useRouter()
const route = useRoute()
const type = route.params.type as string
const id = route.query.id

const currentVersion = ref<string>('')
const latestVersion = ref('')
const pageTitle = computed(() => {
  const title = route.meta.title as Obj
  if (Object.hasOwnProperty.call(title, 'data')) {
    return title.data[type] || ''
  }
  return title
})
const updateUser = computed(() => {
  return formData.value.updateUser.split('_')[0] || ''
})
const formRef = ref()
const onSumbit = async () => {
  if (!formRef.value) return
  if (Object.isEqual(formDataOrigin, formData.value)) {
    ElMessage.success('未做任何修改！')
    type === 'edit'
      ? router.replace(`/templates/xj/detail?id=${id}`)
      : router.back()
  } else {
    let url =
      '/operate/inspectionMainTemplateController/addInspectionMainTemplate'
    if (id) {
      url =
        '/operate/inspectionMainTemplateController/updateInspectionMainTemplate'
    }
    if (formData.value.inspectionClassDtoList.length === 0) {
      ElMessage.warning('至少添加一个巡检大类！')
      return
    }
    const isRepeat = formData.value.inspectionClassDtoList.some(
      (item: any, _index: any, array: any) => {
        return (
          array.filter(
            (innerItem: any) => innerItem.className === item.className
          ).length > 1
        )
      }
    )
    if (isRepeat) {
      ElMessage.warning('巡检大类不能重复！')
      return
    }
    const isEmpty = formData.value.inspectionClassDtoList.some((item: any) => {
      return item.inspectionSubclassDtoList.length === 0
    })
    if (isEmpty) {
      ElMessage.warning('巡检大类下应该至少有一条数据！')
      return
    }
    await formRef.value.validate(async (valid: any) => {
      if (valid) {
        ElMessageBox.confirm(
          '当前模版内容发生变更，点击保存将生成新的模版版本！',
          '确认提示',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消'
          }
        )
          .then(async () => {
            await api.post({
              url,
              data: {
                ...formData.value,
                insTemVersion:
                  type === 'edit'
                    ? (Number(formData.value.insTemVersion) + 0.1).toFixed(1)
                    : '1.0'
              },
              loading: true
            })
            type === 'edit'
              ? router.replace(`/templates/xj/detail?id=${id}`)
              : router.back()
          })
          .catch()
      } else {
        ElMessage.warning('有必填项未填写，请检查！')
      }
    })
  }
}

const defaultTemplate = Object.freeze([
  {
    subclassName: '光伏组件',
    standardName: '无损伤划痕污物遮挡',
    standardType: '4',
    standardResult: '20',
    isRequired: 1
  },
  {
    subclassName: '光伏组件',
    standardName: '固定牢靠、无松动',
    standardType: '2',
    standardResult: '100',
    isRequired: 1
  },
  {
    subclassName: '金属支架',
    standardName: '无腐蚀、无变形',
    standardType: '3',
    standardResult: '3',
    isRequired: 1
  },
  {
    subclassName: '线缆',
    standardName: '布置规整、无脱落现象',
    standardType: '1',
    standardResult: ['A', 'B'],
    isRequired: 1
  },
  {
    subclassName: '线缆接头',
    standardName: '无松动烧毁现象',
    standardType: '6',
    standardResult: '1',
    isRequired: 1
  },
  {
    subclassName: '接地检查',
    standardName: '接地连接正常',
    standardType: '5',
    standardResult: ['A', 'B', 'C'],
    isRequired: 1
  },
  {
    subclassName: '关键部位温度',
    standardName: '抽查组件接线盒温度',
    standardType: '2',
    standardResult: '100',
    isRequired: 1
  },
  {
    subclassName: '关键部位温度',
    standardName: '抽查组件背板温度',
    standardType: '2',
    standardResult: '100',
    isRequired: 1
  }
])
const formData = ref<any>({
  insTemType: 1,
  operationInfosDtoList: [],
  insTemName: '',
  insTemVersion: '1.0',
  versionList: ['1.0'],
  inspectionClassDtoList: [
    {
      className: '',
      inspectionSubclassDtoList: Object.jsonClone(defaultTemplate)
    }
  ]
})

const addItem = () => {
  formData.value.inspectionClassDtoList.push({
    className: '',
    inspectionSubclassDtoList: [
      {
        subclassName: '',
        standardName: '',
        standardType: '4',
        standardResult: '20',
        isRequired: 1,
        __key__: Date.now()
      },
      {
        subclassName: '',
        standardName: '',
        standardType: '4',
        standardResult: '20',
        isRequired: 1,
        __key__: Date.now()
      },
      {
        subclassName: '',
        standardName: '',
        standardType: '4',
        standardResult: '20',
        isRequired: 1,
        __key__: Date.now()
      },
      {
        subclassName: '',
        standardName: '',
        standardType: '4',
        standardResult: '20',
        isRequired: 1,
        __key__: Date.now()
      },
      {
        subclassName: '',
        standardName: '',
        standardType: '4',
        standardResult: '20',
        isRequired: 1,
        __key__: Date.now()
      },
      {
        subclassName: '',
        standardName: '',
        standardType: '4',
        standardResult: '20',
        isRequired: 1,
        __key__: Date.now()
      },
      {
        subclassName: '',
        standardName: '',
        standardType: '4',
        standardResult: '20',
        isRequired: 1,
        __key__: Date.now()
      },
      {
        subclassName: '',
        standardName: '',
        standardType: '4',
        standardResult: '20',
        isRequired: 1,
        __key__: Date.now()
      }
    ]
  })
}
const rules = reactive<any>({
  insTemType: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ],
  operationInfosDtoList: [
    { required: true, message: '请选择生效范围', trigger: 'change' }
  ],
  insTemName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }]
})

const companyList = ref<any>([])
let formDataOrigin = {}
onMounted(async () => {
  getCompany()
  if (id) {
    const { data } = await api.get({
      url: '/operate/inspectionMainTemplateController/getInspectionMainTemplateById',
      data: { id },
      loading: true
    })
    formData.value = data || {}
    formDataOrigin = Object.jsonClone(formData.value)
    currentVersion.value = data.insTemVersion
    latestVersion.value = data.insTemVersion
  }
})
const getCompany = async () => {
  try {
    const { data } = await api.get(
      '/operate/operationUser/getOperatorsListByUser'
    )
    companyList.value =
      data?.map((item: any) => ({
        operationName: item.companyName,
        operationCode: item.companyCode
      })) || []
  } catch (e: any) {
    companyList.value = []
  }
}
const getDetailByVersion = async (version: string) => {
  const { data } = await api.post({
    url: `/operate/inspectionMainTemplateController/getInspectionMainTemplateByIdAndVersion?id=${id}&insTemVersion=${version}`,
    loading: true
  })
  formData.value = data || {}
  currentVersion.value = version
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.pb-0px {
  padding-bottom: 0px;
}
.custom-right {
  display: flex;
  padding-right: 4px;
  .el-select {
    width: 120px;
  }
  .el-button {
    margin-left: 10px;
  }
}
</style>
