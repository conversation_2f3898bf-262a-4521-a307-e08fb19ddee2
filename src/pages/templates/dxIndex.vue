<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import * as api from '@/api/index.ts'
import useTableData from '@/hooks/useTableData'
import useDist from './hooks/useDist'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const { templateTypes } = useDist()
const router = useRouter()

const searchProps = ref<Obj[]>([
  {
    prop: 'modelName',
    label: '典型票名称',
    width: '100px',
    type: 'input'
  },
  {
    prop: 'modelType',
    label: '典型票类型',
    width: '100px',
    type: 'select',
    options: templateTypes
  }
])
let searchData = reactive<Obj>({
  modelName: '',
  modelType: '',
  pageSize: 10,
  pageNum: 1
})
const columns = [
  {
    prop: 'modelNo',
    label: '模板ID'
  },
  {
    prop: 'modelType',
    label: '典型票类型',
    formatter: (row: any) =>
      templateTypes.find((item) => item.value == row.modelType)?.label || '--'
  },
  {
    prop: 'modelName',
    label: '典型票名称'
  },
  {
    prop: 'updateUser',
    label: '上次修改人',
    formatter: (row: any) => row.updateUser?.split('_')?.at(0) || '--'
  },
  {
    prop: 'updateTime',
    label: '上次修改时间'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 110,
    fixed: 'right'
  }
]
const getTableData = async (data: Obj, loading: Loading) => {
  return await api.post(
    '/operate/modelTicketController/getModelTicketList',
    { ...data, code: localStorage.getItem('PVOM_COMPANY_CODE') },
    loading
  )
}
const {
  tableData,
  tablePage,
  tableLoading,
  changeCurrent,
  changeSize,
  changeData
} = useTableData(getTableData, searchData, { immediate: false })

const route = useRoute()
watch(
  () => [companyCode.data, route],
  () => {
    companyCode.data && changeData()
  },
  { immediate: true }
)

const handleSearch = async (val: any) => {
  Object.keys(searchData).forEach((key) => {
    searchData[key] = val[key]
  })
  changeData(true)
}

const handleDelete = async (row: any) => {
  await api.post({
    url: '/operate/modelTicketController/deleteModelTicket' + '?id=' + row.id,
    loading: true
  })
  ElMessage({
    message: '删除成功！',
    type: 'success'
  })
  changeData('delete')
}

const handleAdd = () => {
  router.push(`/templates/dx/add`)
}
const handleView = (row: Obj) => {
  router.push(`/templates/dx/detail?id=${row.id}`)
}
</script>

<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="100px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>典型票列表</p>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          <span>新建典型票</span>
        </el-button>
      </div>
      <TablePagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :show-overflow-tooltip="true"
        background
        :columns="columns"
        :data="tableData.data"
        :total="tableData.total"
        :loading="tableLoading"
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        class="table-pagination"
        @handle-size-change="changeSize"
        @handle-current-change="changeCurrent"
      >
        <template #operate="{ row }">
          <el-button type="primary" link @click="() => handleView(row)">
            查看
          </el-button>
          <el-popconfirm
            title="是否确认删除典型票？"
            width="200px"
            @confirm="() => handleDelete(row)"
          >
            <template #reference>
              <el-button type="primary" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </TablePagination>
    </div>
  </div>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
