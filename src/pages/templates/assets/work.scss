// 工作票 详情

.work-detail {
  word-wrap: break-word;
  word-break: break-all;
  .operate-title {
    display: flex;
    align-items: center;
    .tag {
      margin-left: 16px;
    }
  }
  .btn-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 16px 24px 8px 24px;
    .el-button {
      margin: 0 16px 16px 0 !important;
      padding: 5px 16px;
    }
    .disabled {
      background: rgba(0, 0, 0, 0.1);
      color: rgba(255, 255, 255, 1);
      border: 1px solid transparent;
      outline: none;
    }
  }
  .info-base {
    padding-bottom: 0;
    margin-top: 0;
    &.safe-content {
      padding-bottom: 24px;
    }
    .operate {
      p {
        font-size: 16px;
        font-weight: 500;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .mt16 {
      margin-top: 16px;
    }
  }
  .fhc {
    color: rgba(46, 141, 230, 1);
  }
  :deep(.el-form-item--default .el-form-item__label) {
    color: rgba(0, 0, 0, 0.45) !important;
    height: 22px !important;
    line-height: 22px !important;
    padding-right: 8px;
  }
  :deep(.el-form-item--default .el-form-item__content) {
    color: rgba(0, 0, 0, 0.85) !important;
    line-height: 22px !important;
  }

  .el-row .el-col:nth-child(odd) {
    :deep(.el-form-item__label) {
      // width: 94px !important;
      width: 130px !important;
    }
  }
  .priviate-label .el-row .el-col:nth-child(odd) {
    :deep(.el-form-item__label) {
      width: 204px !important;
    }
  }
  .el-row .el-col:nth-child(even) {
    :deep(.el-form-item__label) {
      width: 168px !important;
    }
  }
}

// 工作票 新建
.work-add {
  word-wrap: break-word;
  word-break: break-all;
  .safe-content {
    padding-bottom: 24px;
  }
  .el-row .el-col {
    :deep(.el-form-item__label) {
      width: 145px !important;
    }
  }
  // .el-row .el-col .special-label {
  //   :deep(.el-form-item__label) {
  //     width: 178px !important;
  //     margin-left: -70px;
  //   }
  // }

  // .el-row .el-col:nth-child(odd) {
  //   :deep(.el-form-item__label) {
  //     width: 108px !important;
  //   }
  // }
  .el-row .el-col:nth-child(even) {
    :deep(.el-form-item__label) {
      width: 178px !important;
    }
  }
}

// 工作票 补充信息
.work-supply {
  word-wrap: break-word;
  word-break: break-all;
  .safe-title {
    padding-bottom: 24px;
  }
  .safe-contentmb24 {
    padding-bottom: 24px;
    .safe-title {
      padding-bottom: 0;
    }
  }
}

// 工作票详情和新增公用样式
.safe-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  .line {
    width: 4px;
    height: 16px;
    background: rgba(42, 203, 160, 1);
    margin-right: 8px;
  }
}
.breif {
  margin-top: 24px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 12px;
  &.breif-nopa {
    margin-top: 0;
  }
}

.two-jsa {
  .el-checkbox-group {
    margin-top: 12px;
    background: rgba(246, 248, 250, 1);
    padding: 20px 20px 0;
    box-sizing: border-box;
    margin-bottom: 24px;
    border-radius: 4px;
    .el-checkbox {
      margin-bottom: 20px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      height: 22px !important;
      .el-checkbox__label {
        display: flex;
        align-items: center;
        .lastItem.el-form-item--default {
          margin: 0 0 0 10px !important;
        }
      }
    }
    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: rgba(0, 0, 0, 0.85);
    }
    .el-checkbox__input.is-disabled + span.el-checkbox__label {
      color: rgba(0, 0, 0, 0.85);
    }

    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
      border-color: #fff;
    }

    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      background: #ccc;
    }
    .el-input.is-disabled .el-input__wrapper {
      background: rgba(246, 248, 250, 1) !important;
    }
  }
}
