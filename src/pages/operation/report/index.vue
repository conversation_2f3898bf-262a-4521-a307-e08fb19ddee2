<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>检修及时率统计报表</p>
        <div>
          <el-button
            type="primary"
            :loading="exportLoading"
            @click="handleExport"
            ><el-icon> <Download /> </el-icon>导出报表</el-button
          >
        </div>
      </div>
      <vis-table-pagination
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="columns"
        :total="listTotal"
        :data="listData"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #done="{ row }">
          <el-table-column
            prop="doneDuly"
            label="按时完成工单总数"
            :formatter="formatter"
          >
          </el-table-column>
          <el-table-column
            prop="doneTimeOut"
            label="超时完成工单总数"
            :formatter="formatter"
          />
        </template>
        <template #undone="{ row }">
          <el-table-column
            prop="notDoneDuly"
            label="未超时工单总数"
            :formatter="formatter"
          />
          <el-table-column
            prop="notdoneTimeOut"
            label="已超时工单总数"
            :formatter="formatter"
          />
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  operation as operationAPI,
  opsPersonnel as opsPersonnelAPI
} from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import dayjs from 'dayjs'

const time = [
  dayjs(new Date()).format('YYYY-MM-DD'),
  dayjs(new Date()).format('YYYY-MM-DD')
]

const exportLoading = ref(false)

// 搜索
const searchData = ref({
  companyCode: '',
  time,
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    prop: 'companyCode',
    label: '运维公司',
    span: 8,
    width: '68px',
    type: 'select',
    filterable: true,
    options: []
  },
  {
    prop: 'time',
    label: '选择时间',
    type: 'monthrange',
    format: 'YYYY-MM-DD',
    span: 8,
    width: '110px',
    clearable: false
  }
])
const handleSearch = (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  getTableData()
}

const columns = [
  {
    prop: 'companyName',
    label: '运维公司',
    minWidth: 150
  },
  // {
  //   prop: 'twoDayrate',
  //   label: '两日处理率',
  //   minWidth: 80
  // },
  // {
  //   prop: 'sevenDayrate',
  //   label: '七日处理率',
  //   minWidth: 80
  // },
  {
    prop: 'rate',
    label: '工单闭环率',
    minWidth: 80
  },
  {
    prop: 'done',
    label: '已完成工单总数',
    align: 'center',
    slotName: 'done'
  },
  {
    prop: 'undone',
    label: '未完成工单总数',
    align: 'center',
    slotName: 'undone'
  },
  {
    prop: 'total',
    label: '工单总数',
    minWidth: 80
  }
]

const formatter = (_row: any, _column: any, cellValue: any) => {
  const isNull = [undefined, null, ''].includes(cellValue)
  return !isNull ? cellValue : '--'
}

const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    const params: any = getParams()

    // let { data } = await operationAPI.getOverhaulReportVOList({ ...params }, [
    //   tableLoading
    // ])
    let { data } = await operationAPI.getOverhaulReportVOList(
      { ...params },
      true
    )
    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const getParams = () => {
  const params: any = { ...searchData.value }

  //开始时间为1号，结束时间为1号并加一个月，并格式化时间为 YYYY-MM-DD hh:mm:ss
  const [startDateStr, endDateStr] = searchData.value.time

  const [startYear, startMonth] = startDateStr.split('-')
  const startDate = `${startYear}-${startMonth}-01`

  const endDate = dayjs(endDateStr)
    .endOf('month')
    .add(1, 'day')
    .format('YYYY-MM-DD')

  params.startTime = `${startDate} 00:00:00`
  params.endTime = `${endDate} 00:00:00`

  return params
}

const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    searchProps.value[0].options = data.data.map((item: any) => {
      return {
        label: item.companyName || '',
        value: item.companyCode || ''
      }
    })
  } catch (e: any) {
    searchProps.value[0].options = []
  }
}
/**
 * 导出
 */
const handleExport = async () => {
  try {
    exportLoading.value = true
    const params = getParams()
    delete params.time
    delete params.pageNum
    delete params.pageSize

    let res = await operationAPI.download({ ...params })

    let str = res.headers['content-disposition']
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(res.data)
    link.download = decodeURIComponent(str.split('=')[1])
    link.click()
  } catch (e) {
    console.log(e)
    ElMessage({
      type: 'warning',
      message: '导出失败'
    })
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  getTableData()
  getCompany()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss"></style>
