<script setup lang="ts">
import DetailPage from './components/detailPage.vue'
import StationPage from './components/stationPage.vue'

const router = useRouter()
const route = useRoute()
const status = Number(route.query.status) || 0
const tabName = ref(status)
const activeTabName = ref(tabName)
const handleChangeTab = () => {
  router.replace(
    `${route.path}?status=${activeTabName.value}&operationCompanyCode=${route.query.operationCompanyCode}`
  )
}
</script>

<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-title">
        <el-icon class="backIcon"><ArrowLeft /></el-icon>
        <span class="goback" @click="() => router.go(-1)">返回上一级</span>
        <span class="detailLine">|</span>
        <router-link to="/ops-project">运维项目管理</router-link>
        <span>></span>
        <span>运维项目详情</span>
      </div>
    </div>
    <div v-auto-height class="info-tab">
      <el-tabs
        v-model="activeTabName"
        v-auto-height
        @tab-change="handleChangeTab"
      >
        <el-tab-pane label="运维项目信息" :name="0">
          <DetailPage v-if="activeTabName === 0"></DetailPage>
        </el-tab-pane>
        <el-tab-pane label="关联电站" :name="1">
          <StationPage v-if="activeTabName === 1"></StationPage>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.info-tab {
  margin-top: 24px;
  height: calc(100vh - 108px);
}
</style>
