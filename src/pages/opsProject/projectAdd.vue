<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import request from '@/utils/request'
import useSelectedElectricStore from '@/store/selectedElectric'
import usePaginationLocal from './hooks/usePaginationLocal'
import ServiceForm from './components/serviceForm.vue'

const selectedElectric = useSelectedElectricStore()

const opsElectricData = ref<Record<string, any>[]>([])
opsElectricData.value = [...selectedElectric.data]
const opsElectricTotal = computed(() => {
  return opsElectricData.value.length
})
const formData = ref<Record<string, any>>({})
formData.value = { ...selectedElectric.formData }

const {
  localData,
  handleSliceData,
  selectedSizeChange,
  selectedCurrentChange,
  pageObject: selectedPageObj
} = usePaginationLocal({ pageSize: 10 })
handleSliceData(opsElectricData.value)

// 路由信息
const router = useRouter()
const route = useRoute()
const status = Number(route.query.status) || 0

// 进度条
const stepsActive = ref(status)
const changeStep = (status: number) => {
  if (![0, 1].includes(status)) return
  stepsActive.value = status
  router.replace(`${route.path}?status=${stepsActive.value}`)
}
const serviceFormRef = ref()

// 运维电站列表
const selectedTableRef = ref()
const opsElectricColumns: Record<string, any>[] = [
  {
    prop: 'selection',
    label: '选择',
    fixed: 'left',
    reserve: true,
    selectable: (row: { isSelect: string }) => {
      return row.isSelect !== '1'
    }
  },
  {
    prop: 'stationType',
    label: '电站类型',
    formatter: (row: any) => {
      if (row.stationType === 0) {
        return '户用'
      }
      if (row.stationType >= 1) {
        return '工商业'
      }
      return row.stationType || '--'
    }
  },
  {
    prop: 'stationCode',
    label: '电站编号'
  },
  {
    prop: 'stationName',
    label: '电站名称'
  },
  {
    prop: 'area',
    label: '行政区划',
    minWidth: '160'
  },
  {
    prop: 'capins',
    label: '装机容量(kW)',
    minWidth: '120'
  },
  {
    prop: 'projectName',
    label: '运维项目名称',
    minWidth: '120'
  },
  {
    prop: 'projectCompanyName',
    label: '资产所属公司',
    minWidth: '120'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: '120'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: '60',
    fixed: 'right'
  }
]

const selectedData = ref<Record<string, any>[]>([])
const selectedCount = computed(() => {
  return selectedData.value.length
})
const handleSelectionChange = (rows: Record<string, any>[]) => {
  selectedData.value = [...rows]
}
const selectOpsElectric = async () => {
  const res = await serviceFormRef.value.save(true)
  if (res) {
    selectedElectric.updateFormData(serviceFormRef.value.formData)
    router.push(`/ops-project/add/electric`)
  } else {
    changeStep(stepsActive.value - 1)
  }
}
const handleDeleteSelectedData = async (row: Record<string, any>) => {
  let codes: any[] = []
  if (row.stationCode) {
    codes = [row.stationCode]
  } else {
    codes = selectedData.value.map((e: any) => e.stationCode)
  }
  selectedElectric.update(
    selectedElectric.data.filter((e: any) => !codes.includes(e.stationCode))
  )
  opsElectricData.value = [...selectedElectric.data]
  selectedPageObj.pageNum = 1
  handleSliceData(opsElectricData.value)
  selectedTableRef.value.tablePagination.clearSelection()
  ElMessage({
    type: 'success',
    message: '删除成功！'
  })
}
const onSaveService = async (onlyValidate: boolean = false) => {
  const res = await serviceFormRef.value.save(onlyValidate)
  if (onlyValidate && res) {
    changeStep(stepsActive.value + 1)
  } else if (res) {
    const { projectCode, operationCompanyCode } = res
    const requestData = opsElectricData.value.map((e: any) => {
      return {
        ...e,
        operationProjectCode: projectCode
      }
    })
    if (opsElectricTotal.value) {
      request({
        url: '/operate/operation-project/addOperationProjectStationList',
        method: 'post',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
          stationList: JSON.stringify(requestData),
          operationCompanyCode
        }
      })
    }
    router.replace('/ops-project')
  }
}
</script>

<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-title">
        <el-icon class="backIcon"><ArrowLeft /></el-icon>
        <span class="goback" @click="() => router.go(-1)">返回上一级</span>
        <span class="detailLine">|</span>
        <router-link to="/ops-project">运维项目管理</router-link>
        <span>></span>
        <span>新增运维项目</span>
      </div>
    </div>
    <div class="page-main">
      <div class="steps-container">
        <el-steps :active="stepsActive" finish-status="success">
          <el-step>
            <template #icon>
              <div class="icon-warpper" @click="changeStep(0)">
                <div class="icon">1</div>
                <div class="title">服务信息</div>
              </div>
            </template>
          </el-step>
          <el-step>
            <template #icon>
              <div class="icon-warpper" @click="onSaveService(true)">
                <div class="icon">2</div>
                <div class="title">运维电站</div>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>
      <div v-auto-height class="main">
        <div v-show="stepsActive === 0">
          <el-scrollbar height="calc(100vh - 300px)">
            <ServiceForm ref="serviceFormRef" :data="formData"></ServiceForm>
          </el-scrollbar>
        </div>
        <div v-show="stepsActive === 1">
          <div class="operate">
            <p>
              运维电站列表 <span>已选数量（{{ selectedCount }}）</span>
            </p>
            <div>
              <el-popconfirm
                :title="`将要删除${selectedCount}条数据，是否确认？`"
                @confirm="handleDeleteSelectedData"
              >
                <template #reference>
                  <el-button plain :disabled="selectedData.length === 0"
                    >批量删除</el-button
                  >
                </template>
              </el-popconfirm>
              <el-button type="primary" @click="selectOpsElectric"
                >关联电站</el-button
              >
            </div>
          </div>
          <vis-table-pagination
            v-if="stepsActive === 1"
            ref="selectedTableRef"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="selectedPageObj.pageSize"
            :current-page="selectedPageObj.pageNum"
            :columns="opsElectricColumns"
            :total="opsElectricTotal"
            :data="localData"
            :show-overflow-tooltip="true"
            row-key="stationCode"
            background
            class="vis-table-pagination"
            @handle-selection-change="handleSelectionChange"
            @handle-size-change="selectedSizeChange"
            @handle-current-change="selectedCurrentChange"
          >
            <template #operate="{ row }">
              <div class="table-operate">
                <el-popconfirm
                  title="确认删除？"
                  @confirm="handleDeleteSelectedData(row)"
                >
                  <template #reference>
                    <el-button link type="danger">删除</el-button>
                  </template>
                </el-popconfirm>
              </div>
            </template>
          </vis-table-pagination>
        </div>
      </div>
    </div>
    <div class="page-footer">
      <el-button
        plain
        :disabled="stepsActive <= 0"
        @click="changeStep(stepsActive - 1)"
      >
        上一步
      </el-button>
      <el-button
        plain
        :disabled="stepsActive >= 1"
        @click="onSaveService(true)"
      >
        下一步
      </el-button>
      <el-button
        v-if="stepsActive === 1"
        type="primary"
        @click="onSaveService()"
        >提交</el-button
      >
    </div>
  </div>
</template>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.steps-container {
  padding: 30px 30%;
  padding-bottom: 20px;
  margin-bottom: 10px;
  background: #fff;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.el-step :deep(.el-step__icon.is-icon) {
  width: auto;
}
.el-step {
  height: 28px;
  .icon-warpper {
    display: flex;
    flex-wrap: nowrap;
    padding: 10px;
    cursor: pointer;
    .icon {
      width: 28px;
      height: 28px;
      border: solid 1px #e6e9ec;
      border-radius: 50%;
      line-height: 28px;
      text-align: center;
      font-size: 16px;
      color: #666;
      margin-right: 5px;
    }
    .title {
      font-size: 16px;
      color: #666;
      line-height: 28px;
    }
  }
  :deep(.el-step__head.is-process),
  :deep(.el-step__head.is-finish),
  :deep(.el-step__head.is-success) {
    .icon-warpper .icon {
      background-color: #29cca0;
      color: #fff;
    }
    .title {
      color: #333;
    }
  }
}
.border-bottom {
  border-bottom: 1px solid #f4f4f4;
  margin-bottom: 10px;
}
.page-main {
  height: calc(100vh - 166px);
}
</style>
