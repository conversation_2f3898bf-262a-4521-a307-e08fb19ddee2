import request from '@/utils/request'

export default function () {
  const companyList = ref<Record<string, any>[]>([])
  const getCompanyList = async (requestData: Record<string, any> = {}) => {
    try {
      const { data } = await request({
        url: '/operate/operatorsManage/getCompanyList',
        method: 'post',
        data: {
          ...requestData,
          companyCode: '',
          companyName: '',
          companyTypeId: '',
          pageNum: 1,
          pageSize: 20000,
          socialUniformCreditCode: ''
        }
      })
      companyList.value = data.data
    } catch (e) {
      companyList.value = []
    }
  }
  onMounted(async () => {
    getCompanyList()
  })
  return {
    companyList,
    getCompanyList
  }
}
