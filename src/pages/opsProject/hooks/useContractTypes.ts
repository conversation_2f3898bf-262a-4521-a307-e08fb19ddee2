import request from '@/utils/request'

interface ContractTypes {
  name: string
  value: number
}

export default function () {
  // 公司类型
  const contractTypes = ref<ContractTypes[]>([])
  const getContractTypes = async () => {
    try {
      const { data } = await request({
        url: '/operate/operatorsManage/getContractTypeList',
        method: 'get'
      })
      contractTypes.value = [...data.data]
    } catch (e) {
      contractTypes.value = []
    }
  }
  onMounted(async () => {
    getContractTypes()
  })
  return contractTypes
}
