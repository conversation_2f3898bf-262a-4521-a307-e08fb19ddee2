import request from '@/utils/request'
import type { UploadUserFile } from 'element-plus'

export default function (companyCode?: string, formRef: any = null) {
  // 合同信息
  const serviceData = ref<Record<string, any>>({
    contractState: 1,
    type: null,
    effectiveTime: null,
    lapseTime: null,
    ownerCode: null,
    operationCode: null,
    id: null,
    num: ''
  })
  const serviceFiles = ref<UploadUserFile[]>([])
  const getServiceData = async () => {
    try {
      const { data } = await request({
        url: '/operate/operatorsManage/getContractInfo',
        headers: { 'Content-Type': 'text/plan' },
        method: 'post',
        data: companyCode
      })
      serviceData.value = { ...serviceData.value, ...data.data }
      serviceData.value.operationCode = companyCode
      if (serviceData.value.contractState === null) {
        serviceData.value.contractState = 1
      }
      serviceFiles.value = data.data?.uploadContractInfoFileVOList || []
    } catch (e) {
      serviceData.value = { ...serviceData.value }
      serviceFiles.value = []
    }
  }
  onMounted(async () => {
    await getServiceData()
    if (formRef?.value) {
      formRef?.value?.clearValidate()
    }
  })
  return { serviceData, serviceFiles, getServiceData }
}
