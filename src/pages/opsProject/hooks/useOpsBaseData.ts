import request from '@/utils/request'

export default function (companyCode: string) {
  // 运维商基本信息
  const opsBaseData = ref<Record<string, any>>({})
  const getOpsBaseData = async () => {
    try {
      const { data } = await request({
        url: '/operate/operatorsManage/getOperatorsBasisInfo',
        headers: { 'Content-Type': 'text/plan' },
        method: 'post',
        data: companyCode
      })
      opsBaseData.value = { ...data.data[0] }
    } catch (e) {
      opsBaseData.value = {}
    }
  }
  onMounted(async () => {
    getOpsBaseData()
  })
  return opsBaseData
}
