import request from '@/utils/request'

interface electricProject {
  projectCode: string
  projectName: string
}

export default function () {
  // 运维项目名称列表
  const electricProjects = ref<electricProject[]>([])
  const getElectricProjects = async () => {
    try {
      const { data } = await request({
        url: '/operate/operatorsManage/getProjectList',
        method: 'get'
      })
      electricProjects.value = [...data.data]
    } catch (e) {
      electricProjects.value = []
    }
  }
  onMounted(async () => {
    getElectricProjects()
  })
  return electricProjects
}
