import request from '@/utils/request'

interface CompanyTypes {
  companyTypeId: string
  companyTypeName: string
}

export default function () {
  // 公司类型
  const companyTypes = ref<CompanyTypes[]>([])
  const getCompanyTypes = async () => {
    try {
      const { data } = await request({
        url: '/operate/operatorsManage/getCompanyTypeList',
        method: 'get'
      })
      companyTypes.value = [...data.data]
    } catch (e) {
      companyTypes.value = []
    }
  }
  onMounted(async () => {
    getCompanyTypes()
  })
  return companyTypes
}
