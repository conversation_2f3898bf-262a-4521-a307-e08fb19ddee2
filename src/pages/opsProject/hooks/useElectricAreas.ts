import request from '@/utils/request'

interface electricArea {
  code: string
  name: string
  subDistrict: electricArea[]
}

export default function () {
  // 行政区划列表
  const electricAreas = ref<electricArea[]>([])
  const getElectricAreas = async () => {
    try {
      const { data } = await request({
        url: '/operate/operatorsManage/getdistrictInfotreeList',
        method: 'get'
      })
      electricAreas.value = [...data.data]
    } catch (e) {
      electricAreas.value = []
    }
  }
  onMounted(async () => {
    getElectricAreas()
  })
  return electricAreas
}
