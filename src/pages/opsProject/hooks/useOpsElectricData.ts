import request from '@/utils/request'

export default function (projectCode: string, loading?: any) {
  // 电站列表
  const opsElectricColumns: Record<string, any>[] = [
    {
      prop: 'stationType',
      label: '电站类型',
      formatter: (row: any) => {
        if (row.stationType === 0) {
          return '户用'
        }
        if (row.stationType >= 1) {
          return '工商业'
        }
        return row.stationType || '--'
      }
    },
    {
      prop: 'stationCode',
      label: '电站编号',
      minWidth: '120'
    },
    {
      prop: 'stationName',
      label: '电站名称',
      minWidth: '120'
    },
    {
      prop: 'area',
      label: '行政区划',
      minWidth: '160'
    },
    {
      prop: 'capins',
      label: '装机容量(kW)',
      minWidth: '120'
    },
    {
      prop: 'projectName',
      label: '项目名称'
    },
    {
      prop: 'projectCompanyName',
      label: '资产所属公司',
      minWidth: '120'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      minWidth: '120'
    }
  ]
  const opsElectricTotal = ref<number>(0)
  const opsElectricData = ref<Record<string, any>[]>([])
  const opsElectricPage = reactive({
    pageNum: 1,
    pageSize: 10
  })
  onMounted(async () => {
    getOpsElectricData()
  })
  const getOpsElectricData = async () => {
    try {
      const { data } = await request({
        url: '/operate/operation-project/getOperationProjectStations',
        method: 'post',
        data: {
          operationProjectCode: projectCode,
          ...opsElectricPage
        },
        loading: loading
      })
      opsElectricTotal.value = data?.data?.total || 0
      opsElectricData.value = [...(data?.data?.records || [])]
    } catch (e) {
      opsElectricTotal.value = 0
      opsElectricData.value = []
    }
  }
  const opsElectricSizeChange = (params: Record<string, any>) => {
    opsElectricPage.pageNum = 1
    opsElectricPage.pageSize = params.pageSize
    getOpsElectricData()
  }
  const opsElectricCurrentChange = (params: Record<string, any>) => {
    opsElectricPage.pageNum = params.currentPage
    opsElectricPage.pageSize = params.pageSize
    getOpsElectricData()
  }
  return {
    opsElectricColumns,
    opsElectricTotal,
    opsElectricData,
    getOpsElectricData,
    opsElectricPage,
    opsElectricSizeChange,
    opsElectricCurrentChange
  }
}
