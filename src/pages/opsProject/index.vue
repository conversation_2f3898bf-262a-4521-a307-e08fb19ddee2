<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import type { FormInstance } from 'element-plus'
import request from '@/utils/request'
import useSelectedElectricStore from '@/store/selectedElectric'

const selectedElectric = useSelectedElectricStore()
const router = useRouter()

onMounted(async () => {
  await getTableData()
})

const searchFormRef = ref<FormInstance>()
const searchData = reactive({
  assetSaleCompanyName: '',
  operationCompanyName: '',
  operationProjectCode: '',
  operationProjectName: '',
  serviceStatus: ''
})
const handleReset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  pageObj.pageSize = 10
  handleSearch(formEl)
}
const handleSearch = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  pageObj.pageNum = 1
  await getTableData()
}

const columns = [
  {
    prop: 'operationProjectCode',
    label: '运维项目编码',
    minWidth: 120
  },
  {
    prop: 'operationProjectName',
    label: '运维项目名称',
    minWidth: '120'
  },
  {
    prop: 'assetSaleCompanyName',
    label: '资产公司'
  },
  {
    prop: 'operationCompanyName',
    label: '运维商'
  },
  {
    prop: 'serviceType',
    label: '服务类型',
    formatter: (row: any) => {
      return (
        { 1: '保发电量', 2: '不保发电量' }?.[row.serviceType as number] ||
        row.serviceType ||
        '--'
      )
    }
  },
  {
    prop: 'serviceStatus',
    label: '服务状态',
    formatter: (row: any) => {
      return (
        { 1: '待开始', 2: '服务中', 3: '已结束' }?.[
          row.serviceStatus as number
        ] ||
        row.serviceStatus ||
        '--'
      )
    }
  },
  {
    prop: 'totalCapins',
    label: '装机容量(kW)',
    minWidth: '120'
  },
  {
    prop: 'relevanceStationsCount',
    label: '关联电站数量',
    minWidth: '120'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: '130',
    fixed: 'right'
  }
]
const tableTotal = ref<number>(0)
const tableData = ref<Record<string, any>[]>([])
let pageObj = reactive({
  pageNum: 1,
  pageSize: 10
})
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    const { data } = await request({
      url: '/operate/operation-project/getOperationProjectList',
      method: 'post',
      data: { ...searchData, ...pageObj },
      loading: [tableLoading]
    })
    tableTotal.value = data?.data?.total || 0
    tableData.value = data?.data?.records || []
  } catch (e) {
    tableData.value = []
    tableTotal.value = 0
  }
}
const handleSizeChange = async (params: Record<string, any>) => {
  pageObj.pageNum = 1
  pageObj.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: Record<string, any>) => {
  pageObj.pageNum = params.currentPage
  pageObj.pageSize = params.pageSize
  await getTableData()
}

// 操作
const handleAdd = () => {
  selectedElectric.update()
  selectedElectric.updateFormData()
  router.push('/ops-project/add')
}
const handleDetail = (row: Record<string, any>, status: number = 0) => {
  router.push(
    `/ops-project/detail/${row.operationProjectCode}?status=${status}&operationCompanyCode=${row.operationCompanyCode}`
  )
}
</script>

<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-title">
        <span>运维项目管理</span>
      </div>
    </div>
    <div class="page-main">
      <div class="page-search">
        <el-form ref="searchFormRef" :inline="true" :model="searchData">
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="运维商" prop="operationCompanyName">
                <el-input
                  v-model="searchData.operationCompanyName"
                  placeholder="请输入运维商"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="资产公司" prop="assetSaleCompanyName">
                <el-input
                  v-model="searchData.assetSaleCompanyName"
                  placeholder="请输入资产公司"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="服务状态" prop="serviceStatus">
                <el-select
                  v-model="searchData.serviceStatus"
                  placeholder="请选择服务状态"
                  clearable
                >
                  <el-option label="待开始" :value="1"></el-option>
                  <el-option label="服务中" :value="2"></el-option>
                  <el-option label="已结束" :value="3"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="运维项目编码" prop="operationProjectCode">
                <el-input
                  v-model="searchData.operationProjectCode"
                  placeholder="请输入运维项目编码"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="运维项目名称" prop="operationProjectName">
                <el-input
                  v-model="searchData.operationProjectName"
                  placeholder="请输入运维项目名称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" class="search-buttons">
              <el-form-item style="width: auto; margin-right: 0">
                <el-button plain @click="handleReset(searchFormRef)"
                  >重置</el-button
                >
                <el-button type="primary" @click="handleSearch(searchFormRef)"
                  >查询</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div v-auto-height class="main">
        <div class="operate end">
          <el-button type="primary" @click="handleAdd">
            <el-icon m-r-4px> <Plus /> </el-icon>新增
          </el-button>
        </div>
        <vis-table-pagination
          :loading="tableLoading"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageObj.pageSize"
          :current-page="pageObj.pageNum"
          :columns="columns"
          :total="tableTotal"
          :data="tableData"
          :show-overflow-tooltip="true"
          background
          class="vis-table-pagination"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
        >
          <template #operate="{ row }">
            <div class="table-operate">
              <el-button link @click="handleDetail(row, 0)">查看</el-button>
              <el-button link @click="handleDetail(row, 1)">关联电站</el-button>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
  </div>
</template>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
:deep(.page-main .page-search .el-form .el-form-item label) {
  padding-right: 12px;
}
</style>
