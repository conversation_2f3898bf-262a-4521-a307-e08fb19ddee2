<script setup lang="ts">
import jpgSvg from '@/assets/svgs/jpg.svg'
import pngSvg from '@/assets/svgs/png.svg'
import pdfSvg from '@/assets/svgs/pdf.svg'
import docSvg from '@/assets/svgs/doc.svg'
import docxSvg from '@/assets/svgs/docx.svg'
import uploadSvg from '@/assets/svgs/upload.svg'
import type {
  FormInstance,
  FormRules,
  UploadFile,
  UploadFiles
} from 'element-plus'
import request from '@/utils/request'

const route = useRoute()
const projectCode = route.params.projectCode as string

const props = withDefaults(defineProps<{ data?: Record<string, any> }>(), {
  data() {
    return {}
  }
})
const formData = ref<Record<string, any>>({
  operationProjectName: '',
  serviceType: null,
  serviceStatus: 2,
  serviceStart: null,
  serviceEnd: null,
  assetSaleCompanyCode: null,
  assetSaleCompanyName: '',
  operationCompanyCode: null,
  operationCompanyName: '',
  totalCapins: '',
  files: []
})
formData.value = {
  ...formData.value,
  ...props.data
}

const companyList1 = ref<Record<string, any>[]>([])
const companyList2 = ref<Record<string, any>[]>([])

const getCompanyList = async (companyTypeId: number | null = null) => {
  try {
    const { data } = await request({
      url: '/operate/operation-project/getCompanyList',
      method: 'post',
      data: {
        pageNum: 1,
        pageSize: 20000,
        companyTypeId
      }
    })
    companyList1.value = data?.data || []
  } catch (e) {
    companyList1.value = []
  }
}
const getOperationCompanyList = async () => {
  try {
    const { data } = await request({
      url: '/operate/operation-project/getOperationCompanyList',
      method: 'post',
      data: {
        pageNum: 1,
        pageSize: 20000
      }
    })
    companyList2.value = data?.data || []
  } catch (e) {
    companyList2.value = []
  }
}
onMounted(() => {
  getCompanyList()
  getOperationCompanyList()
})
const changeAsset = (code: string) => {
  formData.value.assetSaleCompanyName =
    companyList1.value.find((e: any) => e.companyCode === code)?.companyName ||
    ''
}
const changeOperation = (code: string) => {
  formData.value.operationCompanyName =
    companyList2.value.find((e: any) => e.companyCode === code)?.companyName ||
    ''
}
const formRef = ref<FormInstance>()
const cssValue = computed(() => {
  return formData.value.files?.length >= 20 ? 'none' : 'inline-flex'
})

const serviceFormRules = reactive<FormRules<Record<string, any>>>({
  operationProjectName: [
    { required: true, message: '请输入运维项目名称', trigger: 'blur' }
  ],
  serviceType: [
    { required: true, message: '请选择服务类型', trigger: 'change' }
  ],
  serviceStatus: [
    { required: true, message: '请选择服务状态', trigger: 'change' }
  ],
  serviceStart: [
    { required: true, message: '请选择服务生效时间', trigger: 'change' }
  ],
  serviceEnd: [
    { required: true, message: '请选择服务失效时间', trigger: 'change' }
  ],
  assetSaleCompanyCode: [
    { required: true, message: '请选择资产公司', trigger: 'change' }
  ],
  operationCompanyCode: [
    { required: true, message: '请选择运维商', trigger: 'change' }
  ],
  totalCapins: [{ required: true, message: '请输入装机容量', trigger: 'blur' }]
})

const effectiveDisabled = (date: Date) => {
  if (formData.value.serviceEnd) {
    return date.getTime() >= Date.parse(`${formData.value.serviceEnd} 00:00:00`)
  }
  if (
    date.getTime() < Date.parse('1900-01-01 00:00:00') ||
    date.getTime() > Date.parse('2099-12-31 23:59:59')
  ) {
    return true
  } else {
    return false
  }
}
const lapseDisabled = (date: Date) => {
  if (formData.value.serviceStart) {
    return (
      date.getTime() <= Date.parse(`${formData.value.serviceStart} 00:00:00`)
    )
  }
  if (
    date.getTime() < Date.parse('1900-01-01 00:00:00') ||
    date.getTime() > Date.parse('2099-12-31 23:59:59')
  ) {
    return true
  } else {
    return false
  }
}
// 合同文件
const changeFile = (file: UploadFile, files: UploadFiles) => {
  var FileExt = file.name.replace(/.+\./, '')
  const fileSize = (file?.size || 0) / 1024 / 1024 < 50
  var extension = ['jpg', 'png', 'pdf', 'doc', 'docx'].includes(
    FileExt.toLowerCase()
  )
  if (!extension) {
    ElMessage({
      message: '不支持的文件类型',
      type: 'error'
    })
    files.pop()
  }
  if (!fileSize) {
    ElMessage({
      message: '文件大小不能超过50MB',
      type: 'error'
    })
    files.pop()
  }
}
const ids: any[] = []
const handleRemove = async (file: Record<string, any>) => {
  if (file.id) {
    ids.push(file.id)
  }
  const index = formData.value.files.findIndex((e: any) => e.uid === file.uid)
  formData.value.files.splice(index, 1)
}

let oldValue: number | ''
const limitInput = (value: any) => {
  if (!value) return
  formData.value.totalCapins = value
    .replace(/[^\d^\.]+/g, '')
    .replace(/^0+(\d)/, '$1')
    .replace(/^\./, '0.')
  if (formData.value.totalCapins.match(/^\d{0,7}(\.\d{0,2})?$/g)) {
    oldValue = formData.value.totalCapins || ''
  } else {
    formData.value.totalCapins = oldValue
  }
}

const resetFields = () => {
  formRef.value?.resetFields()
}
const save = async (onlyValidate: boolean = false) => {
  if (!formRef.value) return
  if (await formRef.value.validate(() => {})) {
    if (formData.value.files.length === 0) {
      ElMessage({
        message: '请上传合同文件',
        type: 'error'
      })
      return false
    }
    if (!onlyValidate) {
      try {
        const requestData = JSON.parse(JSON.stringify(formData.value))
        delete requestData.files
        requestData.serviceStart = requestData.serviceStart.split(' ')[0]
        requestData.serviceEnd = requestData.serviceEnd.split(' ')[0]

        let fd = new FormData()
        fd.append('operationProjectJson', JSON.stringify(requestData))
        fd.append('ids', JSON.stringify(ids))
        formData.value.files.forEach((e: any) => {
          if (!e.id) {
            fd.append('file', e.raw)
          }
        })
        const { data } = await request({
          url: projectCode
            ? '/operate/operation-project/updateProjectInfo'
            : '/operate/operation-project/addProjectInfo',
          method: 'post',
          data: fd,
          loading: true
        })
        if (data.code === '200') {
          ElMessage({
            message: data.message || '服务信息保存成功',
            type: 'success'
          })
          resetFields()
          return {
            projectCode: data?.data || '',
            operationCompanyCode: requestData.operationCompanyCode || ''
          }
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
          return false
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
        return false
      }
    } else {
      return true
    }
  } else {
    return false
  }
}
const getFileSize = function (size: number) {
  if (size === 0) return '0 B'
  var k = 1024,
    sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
    i = Math.floor(Math.log(size) / Math.log(k))

  return (size / Math.pow(k, i)).toPrecision(3) + sizes[i]
}
defineExpose({
  save,
  resetFields,
  formData
})
</script>
<template>
  <el-form
    ref="formRef"
    :rules="serviceFormRules"
    :model="formData"
    label-suffix=""
    label-position="right"
    label-width="130px"
    style="padding: 0 15px 0 0"
  >
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="运维项目名称" prop="operationProjectName">
          <el-input
            v-model="formData.operationProjectName"
            placeholder="请输入运维项目名称"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="服务类型" prop="serviceType">
          <el-select
            v-model="formData.serviceType"
            placeholder="请选择服务类型"
            clearable
          >
            <el-option label="保发电量" :value="1" />
            <el-option label="不保发电量" :value="2" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="服务状态" prop="serviceStatus">
          <el-radio-group
            v-model="formData.serviceStatus"
            placeholder="请选择服务状态"
          >
            <el-radio :label="1">待开始</el-radio>
            <el-radio :label="2">服务中</el-radio>
            <el-radio :label="3">已结束</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="服务生效时间" prop="serviceStart">
          <el-date-picker
            v-model="formData.serviceStart"
            :disabled-date="effectiveDisabled"
            type="date"
            placeholder="请选择服务生效时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="服务失效时间" prop="serviceEnd">
          <el-date-picker
            v-model="formData.serviceEnd"
            :disabled-date="lapseDisabled"
            type="date"
            placeholder="请选择服务失效时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="资产公司" prop="assetSaleCompanyCode">
          <el-select
            key="companyList1"
            v-model="formData.assetSaleCompanyCode"
            placeholder="请选择资产公司"
            clearable
            filterable
            @change="changeAsset"
          >
            <template v-for="item in companyList1" :key="item.companyCode">
              <el-option :label="item.companyName" :value="item.companyCode" />
            </template>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="运维商" prop="operationCompanyCode">
          <el-select
            key="companyList2"
            v-model="formData.operationCompanyCode"
            placeholder="请选择运维商"
            clearable
            filterable
            @change="changeOperation"
          >
            <template v-for="item in companyList2" :key="item.companyCode">
              <el-option :label="item.companyName" :value="item.companyCode" />
            </template>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="装机容量" prop="totalCapins">
          <el-input
            v-model="formData.totalCapins"
            placeholder="请输入装机容量"
            autocomplete="off"
            @input="limitInput"
          >
            <template #append><span class="color-#666">kW</span></template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <div class="operate">
    <p>
      <span style="color: #e62e32">*</span>
      合同文件
      <span> 支持jpg/png/pdf/word格式，最多20个文件，单个文件不超过50MB </span>
    </p>
  </div>
  <el-upload
    v-model:file-list="formData.files"
    list-type="picture-card"
    :limit="20"
    :disabled="formData.files.length >= 20"
    :auto-upload="false"
    :on-change="changeFile"
    accept=".jpg,.png,.pdf,.doc,.docx"
    class="mb-15px"
  >
    <el-icon><Plus /></el-icon>
    <template #file="{ file }">
      <div class="file-wrapper">
        <img
          v-if="
            file.fileName?.replace(/.+\./, '') === 'jpg' ||
            file.name?.replace(/.+\./, '') === 'jpg'
          "
          :src="jpgSvg"
        />
        <img
          v-else-if="
            file.fileName?.replace(/.+\./, '') === 'png' ||
            file.name?.replace(/.+\./, '') === 'png'
          "
          :src="pngSvg"
        />
        <img
          v-else-if="
            file.fileName?.replace(/.+\./, '') === 'pdf' ||
            file.name?.replace(/.+\./, '') === 'pdf'
          "
          :src="pdfSvg"
        />
        <img
          v-else-if="
            file.fileName?.replace(/.+\./, '') === 'doc' ||
            file.name?.replace(/.+\./, '') === 'doc'
          "
          :src="docSvg"
        />
        <img
          v-else-if="
            file.fileName?.replace(/.+\./, '') === 'docx' ||
            file.name?.replace(/.+\./, '') === 'docx'
          "
          :src="docxSvg"
        />
        <img v-else :src="uploadSvg" />
        <div class="title">{{ file.originalFileName || file.name }}</div>
        <div class="note">
          <span>{{ file.fileSize || getFileSize(file.size) || '--' }}</span>
          <span>{{ file?.createTime?.slice(0, 10) || '--' }}</span>
        </div>
      </div>
      <span class="el-upload-list__item-actions">
        <el-popconfirm title="确认删除？" @confirm="handleRemove(file)">
          <template #reference>
            <span class="el-upload-list__item-delete">
              <el-icon><Delete /></el-icon>
            </span>
          </template>
        </el-popconfirm>
      </span>
    </template>
  </el-upload>
</template>
<style lang="scss" scoped>
.operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  height: 32px;
  line-height: 32px;
  p {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    span {
      font-size: 14px;
      margin-left: 8px;
      color: #2acba0;
      font-weight: 500;
    }
  }
  &.end {
    justify-content: flex-end;
  }
}
:deep(.el-upload--picture-card) {
  display: v-bind(cssValue);
}
</style>
<style lang="scss">
.file-wrapper {
  width: 148px;
  height: 148px;
  padding: 10px;
  padding-top: 15px;
  padding-bottom: 1px;
  img {
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    height: 70px;
  }

  .title {
    overflow: hidden;
    height: 24px;
    font-size: 14px;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
    line-height: 24px;
    text-align: center;
  }

  .note {
    display: flex;
    justify-content: space-around;
    height: 18px;
    font-size: 12px;
    color: #999;
    line-height: 24px;
    span {
      display: block;
      flex: none;
    }
  }
}
</style>
