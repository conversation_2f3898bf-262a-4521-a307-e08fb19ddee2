<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import useOpsElectricData from '../hooks/useOpsElectricData'
import request from '@/utils/request'

const router = useRouter()
const route = useRoute()
const projectCode = route.params.projectCode as string
const tableRef = ref()

const tableLoading = ref(false)
const {
  opsElectricColumns,
  opsElectricTotal,
  opsElectricData,
  opsElectricSizeChange,
  opsElectricCurrentChange,
  getOpsElectricData,
  opsElectricPage
} = useOpsElectricData(projectCode, [tableLoading])
opsElectricColumns.unshift({
  prop: 'selection',
  label: '选择',
  fixed: 'left',
  reserve: true,
  selectable: (row: { isSelect: string }) => {
    return row.isSelect !== '1'
  }
})
opsElectricColumns.push({
  prop: 'operate',
  slotName: 'operate',
  label: '操作',
  minWidth: '60',
  fixed: 'right'
})

const selectedData = ref<Record<string, any>[]>([])
const selectedCount = computed(() => {
  return selectedData.value.length
})
const handleSelectionChange = (rows: Record<string, any>[]) => {
  selectedData.value = [...rows]
}

const handleDelete = async (row: Record<string, any>) => {
  try {
    const { data } = await request({
      url: '/operate/operation-project/deleteOperationProjectStations',
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: {
        stationsIds: JSON.stringify(
          row?.id ? [row.id] : selectedData.value.map((item) => item.id)
        ),
        operationCompanyCode: route.query.operationCompanyCode
      }
    })
    if (data.code === '200') {
      ElMessage({
        message: '删除成功！',
        type: 'success'
      })
      if (row?.id) {
        const index = selectedData.value.findIndex((e) => e.id == row.id)
        if (index > -1) {
          tableRef.value.tablePagination.toggleRowSelection(row, false)
        }
      } else {
        tableRef.value.tablePagination.clearSelection()
      }
      opsElectricPage.pageNum = 1
      tableRef.value.tablePagination.clearSelection()
      getOpsElectricData()
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
const handleRelation = () => {
  router.push(
    `/ops-project/detail/${projectCode}/electric?operationCompanyCode=${route.query.operationCompanyCode}`
  )
}
</script>
<template>
  <div class="operate">
    <p>
      关联电站列表 <span>已选数量（{{ selectedCount }}）</span>
    </p>
    <div>
      <el-popconfirm
        :title="`将要删除${selectedCount}条数据，是否确认？`"
        width="250px"
        @confirm="handleDelete"
      >
        <template #reference>
          <el-button plain :disabled="selectedData.length === 0"
            >批量删除</el-button
          >
        </template>
      </el-popconfirm>
      <el-button type="primary" @click="handleRelation">关联电站</el-button>
    </div>
  </div>
  <vis-table-pagination
    ref="tableRef"
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="opsElectricPage.pageSize"
    :current-page="opsElectricPage.pageNum"
    :columns="opsElectricColumns"
    :total="opsElectricTotal"
    :data="opsElectricData"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    row-key="stationCode"
    @handle-selection-change="handleSelectionChange"
    @handle-size-change="opsElectricSizeChange"
    @handle-current-change="opsElectricCurrentChange"
  >
    <template #operate="{ row }">
      <div class="table-operate">
        <el-popconfirm title="确认删除？" @confirm="handleDelete(row)">
          <template #reference>
            <el-button link type="danger">删除</el-button>
          </template>
        </el-popconfirm>
      </div>
    </template>
  </vis-table-pagination>
</template>
<style lang="scss" scoped>
.operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  p {
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    color: #333;
    span {
      margin-left: 8px;
      color: #2acba0;
      font-weight: 500;
    }
  }
  &.end {
    justify-content: flex-end;
  }
}
</style>
