<script setup lang="ts">
import ServiceForm from './serviceForm.vue'
import request from '@/utils/request'
import jpgSvg from '@/assets/svgs/jpg.svg'
import pngSvg from '@/assets/svgs/png.svg'
import pdfSvg from '@/assets/svgs/pdf.svg'
import docSvg from '@/assets/svgs/doc.svg'
import docxSvg from '@/assets/svgs/docx.svg'
import uploadSvg from '@/assets/svgs/upload.svg'

const route = useRoute()
const projectCode = route.params.projectCode as string

const detailData = ref<Record<string, any>>({})

const detailLoading = ref(false)
const getDetailData = async () => {
  try {
    const { data } = await request({
      url: '/operate/operation-project/getProjectInfo',
      method: 'get',
      params: {
        operationProjectCode: projectCode
      },
      loading: [detailLoading]
    })
    detailData.value = data?.data?.operationProject || {}
    detailData.value.files = data?.data?.list || []
  } catch (e) {
    detailData.value = {}
  }
}

onMounted(async () => {
  getDetailData()
})
const serviceFormRef = ref()
const formData = ref<Record<string, any>>({
  operationProjectName: '',
  serviceType: 1,
  serviceStatus: 2,
  serviceStart: null,
  serviceEnd: null,
  assetSaleCompanyCode: null,
  operationCompanyCode: null,
  totalCapins: '',
  files: []
})
const dialogFormVisible = ref(false)
const dialogOpen = () => {
  dialogFormVisible.value = true
  formData.value = JSON.parse(JSON.stringify(detailData.value))
  formData.value.serviceStart = formData.value.serviceStartTime
  formData.value.serviceEnd = formData.value.serviceEndTime
}
const dialogClose = () => {
  dialogFormVisible.value = false
  serviceFormRef.value.resetFields()
}
const saveForm = async () => {
  if (await serviceFormRef.value.save()) {
    dialogClose()
    getDetailData()
  }
}

const handleDownload = async (file: {
  fileName: string
  originalFileName: string
}) => {
  try {
    const { data } = await request({
      url: '/operate/operatorsManage/downloadFile',
      headers: { 'Content-Type': 'text/plan' },
      responseType: 'blob',
      method: 'post',
      data: file.fileName,
      loading: true
    })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(data)
    link.download = file.originalFileName
    link.click()
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
</script>
<template>
  <div class="operate end">
    <el-button type="primary" @click="dialogOpen">编辑运维项目信息</el-button>
  </div>
  <el-scrollbar v-loading="detailLoading" height="calc(100% - 44px)">
    <el-descriptions class="border-bottom" :column="1">
      <el-descriptions-item label="运维项目编码：">
        {{ detailData.operationProjectCode || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="运维项目名称：">
        {{ detailData.operationProjectName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="服务类型：">
        {{
          { 1: '保发电量', 2: '不保发电量' }[
            detailData.serviceType as number
          ] ||
          detailData.serviceType ||
          '--'
        }}
      </el-descriptions-item>
      <el-descriptions-item label="服务状态：">
        {{
          { 1: '待开始', 2: '服务中', 3: '已结束' }[
            detailData.serviceStatus as number
          ] ||
          detailData.serviceStatus ||
          '--'
        }}
      </el-descriptions-item>
      <el-descriptions-item label="服务生效时间：">
        {{ detailData.serviceStart || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="服务失效时间：">
        {{ detailData.serviceEnd || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="资产公司：">
        {{ detailData.assetSaleCompanyName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="运维商：">
        {{ detailData.operationCompanyName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="装机容量：">
        {{ detailData.totalCapins ?? '--' }} kW
      </el-descriptions-item>
    </el-descriptions>
    <div class="operate">
      <p>合同文件</p>
    </div>
    <el-upload
      :file-list="detailData.files"
      list-type="picture-card"
      :limit="20"
      :auto-upload="true"
      :disabled="true"
      accept=".jpg,.png,.pdf,.doc,.docx"
      class="disabled"
    >
      <template #file="{ file }">
        <div class="file-wrapper">
          <img
            v-if="file.fileName?.replace(/.+\./, '') === 'jpg'"
            :src="jpgSvg"
          />
          <img
            v-else-if="file.fileName?.replace(/.+\./, '') === 'png'"
            :src="pngSvg"
          />
          <img
            v-else-if="file.fileName?.replace(/.+\./, '') === 'pdf'"
            :src="pdfSvg"
          />
          <img
            v-else-if="file.fileName?.replace(/.+\./, '') === 'doc'"
            :src="docSvg"
          />
          <img
            v-else-if="file.fileName?.replace(/.+\./, '') === 'docx'"
            :src="docxSvg"
          />
          <img v-else :src="uploadSvg" />
          <div class="title">{{ file.originalFileName }}</div>
          <div class="note">
            <span>{{ file.fileSize || '未知大小' }}</span>
            <span>{{ file?.createTime?.slice(0, 10) || '--' }}</span>
          </div>
        </div>
        <span class="el-upload-list__item-actions">
          <span
            class="el-upload-list__item-delete"
            @click="handleDownload(file)"
          >
            <el-icon><Download /></el-icon>
          </span>
        </span>
      </template>
    </el-upload>
  </el-scrollbar>
  <el-dialog
    v-model="dialogFormVisible"
    title="编辑运维项目信息"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="900px"
    class="vis-dialog"
  >
    <el-scrollbar max-height="calc(100vh - 200px)">
      <ServiceForm
        v-if="dialogFormVisible"
        ref="serviceFormRef"
        :data="formData"
      ></ServiceForm>
    </el-scrollbar>
    <template #footer>
      <el-button plain @click="dialogClose">取消</el-button>
      <el-button type="primary" @click="saveForm">提交</el-button>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  p {
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    color: #333;
    span {
      margin-left: 8px;
      color: #2acba0;
      font-weight: 500;
    }
  }
  &.end {
    justify-content: flex-end;
  }
}
.disabled :deep(.el-upload--picture-card) {
  display: none;
}
</style>
<style lang="scss">
.file-wrapper {
  width: 148px;
  height: 148px;
  padding: 10px;
  padding-top: 15px;
  padding-bottom: 1px;

  img {
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    height: 70px;
  }

  .title {
    overflow: hidden;
    height: 24px;
    font-size: 14px;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #333;
    line-height: 24px;
    text-align: center;
  }

  .note {
    display: flex;
    justify-content: space-around;
    height: 18px;
    font-size: 12px;
    color: #999;
    line-height: 24px;
    span {
      display: block;
      flex: none;
    }
  }
}
</style>
