<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import type { TabPaneName } from 'element-plus'
import { overhaul as api } from '@/api/index.ts'
import filters from '@/utils/filter.js'

const router = useRouter()
const route = useRoute()

const cdsta = route.params.cdsta
const tab = route.params.tab
const path = route.query.path as string
const pathName = route.query.pathName as string
const DEFAULT_PATH: string = import.meta.env.VITE_APP_DEFAULT_PATH

type TabName = 'info' | 'jx' | 'xj'
const activeTabName = ref<TabName>('info')
const changeTabs = (k: TabPaneName) => {
  if (k === 'info') {
  } else if (k === 'jx') {
    getJxData()
  } else if (k === 'xj') {
    getXjData()
  }
}
let jxForm = ref({
  pageSize: 10,
  pageNum: 1
})
const jxColumns = [
  {
    prop: 'workOrderNo',
    label: '工单编号'
  },
  {
    prop: 'title',
    label: '工单标题'
  },
  {
    prop: 'workState',
    label: '工单状态',
    slotName: 'workState',
    width: 108
  },
  // {
  //   prop: 'priority',
  //   label: '优先级'
  // },
  {
    prop: 'defectClassName',
    label: '缺陷大类',
    minWidth: 120
  },
  {
    prop: 'deviceSn',
    label: '设备sn码'
  },
  {
    prop: 'createTime',
    label: '工单创建时间',
    minWidth: 120
  },
  {
    prop: 'workDcompleted',
    label: '工单完成时间',
    minWidth: 120
  },
  {
    prop: 'createUser',
    label: '创建人'
  },
  {
    prop: 'engineer',
    label: '工程师',
    formatter: (row: any) => {
      return row.engineer?.split('_')[0] || '--'
    }
  },
  {
    prop: 'allocated',
    label: '区域监盘人',
    minWidth: 120,
    formatter: (row: any) => {
      return (
        row.allocated
          ?.split(',')
          ?.map((e: any) => e?.split('_')[0])
          ?.join(', ') || '--'
      )
    }
  }
]
const jxData = reactive<{ list: Array<any>; total: number }>({
  list: [],
  total: 0
})
const jxLoading = ref(false)
const getJxData = async () => {
  let { data } = await api.inspectionList(
    {
      ...jxForm.value,
      workType: 1, // 工单类型
      stationCode: cdsta
    },
    [jxLoading]
  )
  if (data.data.records.length > 0) {
    data.data.records.forEach((value: Record<string, any>) => {
      // value.workState = filters.workStateFilter(value.workState)
      value.urgency = filters.priorityFilter(value.urgency)
      value.createUser = value.createUser
        ? value.createUser.split('_')[0]
        : '--'
      value.handlers = value.handlers ? value.handlers.split('_')[0] : '--'
      value.acceptancePerson = value.acceptancePerson
        ? value.acceptancePerson.split('_')[0]
        : '--'
    })
    jxData.list = data.data.records
    jxData.total = data.data.total
  } else {
    jxData.list = []
    jxData.total = 0
  }
}
const jxSizeChange = async (params: any) => {
  jxForm.value.pageNum = 1
  jxForm.value.pageSize = params.pageSize
  getJxData()
}
const jxCurrentChange = async (params: any) => {
  jxForm.value.pageNum = params.currentPage
  getJxData()
}

let xjForm = ref({
  pageSize: 10,
  pageNum: 1
})
const xjColumns = [
  {
    prop: 'workOrderNo',
    label: '工单编号'
  },
  {
    prop: 'title',
    label: '工单标题'
  },
  {
    prop: 'workState',
    label: '工单状态',
    slotName: 'workState',
    width: 108
  },
  {
    prop: 'concluded',
    label: '巡检结论'
  },
  {
    prop: 'createTime',
    label: '工单创建时间',
    minWidth: 120
  },
  {
    prop: 'workDcompleted',
    label: '工单完成时间',
    minWidth: 120
  },
  {
    prop: 'createUser',
    label: '创建人'
  },
  {
    prop: 'engineer',
    label: '工程师',
    formatter: (row: any) => {
      return row.engineer?.split('_')[0] || '--'
    }
  },
  {
    prop: 'allocated',
    label: '区域监盘人',
    minWidth: 120,
    formatter: (row: any) => {
      return (
        row.allocated
          ?.split(',')
          ?.map((e: any) => e?.split('_')[0])
          ?.join(', ') || '--'
      )
    }
  }
]
const xjData = reactive<{ list: Array<any>; total: number }>({
  list: [],
  total: 0
})
const xjLoading = ref(false)
const getXjData = async () => {
  let { data } = await api.inspectionList(
    {
      ...xjForm.value,
      workType: 2, // 工单类型
      stationCode: cdsta
    },
    [xjLoading]
  )
  if (data.data.records.length > 0) {
    data.data.records.forEach((value: Record<string, any>) => {
      // value.workState = filters.workStateFilter(value.workState)
      value.urgency = filters.priorityFilter(value.urgency)

      value.createUser = value.createUser
        ? value.createUser.split('_')[0]
        : '--'
      value.handlers = value.handlers ? value.handlers.split('_')[0] : '--'
      value.acceptancePerson = value.acceptancePerson
        ? value.acceptancePerson.split('_')[0]
        : '--'
    })
    xjData.list = data.data.records
    xjData.total = data.data.total
  } else {
    xjData.list = []
    xjData.total = 0
  }
}
const xjSizeChange = async (params: any) => {
  xjForm.value.pageNum = 1
  xjForm.value.pageSize = params.pageSize
  getXjData()
}
const xjCurrentChange = async (params: any) => {
  xjForm.value.pageNum = params.currentPage
  getXjData()
}
</script>
<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-title">
        <el-icon class="backIcon"><ArrowLeft /></el-icon>
        <span class="goback" @click="() => router.go(-1)">返回上一级</span>
        <span class="detailLine">|</span>
        <router-link
          :to="
            pathName
              ? path
              : tab === 'stationLow'
              ? `/station-low`
              : tab === 'workorder'
              ? `/workorder/listIndex`
              : `/station/${tab}`
          "
        >
          {{
            pathName
              ? pathName
              : tab === 'okay'
              ? '优选电站'
              : tab === 'await'
              ? '需处理电站'
              : tab === 'stationLow'
              ? '低效电站统计'
              : tab === 'workorder'
              ? '工单列表'
              : '全部运维电站'
          }}</router-link
        >
        <a>></a>
        <span>电站详情</span>
      </div>
    </div>
    <div class="page-main">
      <div class="main">
        <el-tabs v-model="activeTabName" @tab-change="changeTabs">
          <el-tab-pane label="电站运行信息" name="info">
            <iframe
              :src="`${DEFAULT_PATH}/omscreen/pv-web/stationContent?stationId=${cdsta}&hideBack=true`"
            ></iframe>
          </el-tab-pane>
          <el-tab-pane v-if="tab !== 'okay'" label="电站检修记录" name="jx">
            <vis-table-pagination
              v-if="activeTabName === 'jx'"
              layout="total, sizes, prev, pager, next, jumper"
              :loading="jxLoading"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="10"
              :columns="jxColumns"
              :total="jxData.total"
              :data="jxData.list"
              :show-overflow-tooltip="true"
              :current-page="jxForm.pageNum"
              background
              class="vis-table-pagination"
              @handle-size-change="jxSizeChange"
              @handle-current-change="jxCurrentChange"
            >
              <template #workState="{ row }">
                <el-tag v-if="row.workState == 1" class="tag__ tag__1"
                  >待指派</el-tag
                >
                <el-tag v-if="row.workState == 2" class="tag__ tag__2"
                  >待接单</el-tag
                >
                <el-tag v-if="row.workState == 3" class="tag__ tag__3"
                  >待开始</el-tag
                >
                <el-tag v-if="row.workState == 4" class="tag__ tag__4"
                  >处理中</el-tag
                >
                <el-tag v-if="row.workState == 5" class="tag__ tag__5"
                  >待验证</el-tag
                >
                <el-tag v-if="row.workState == 6" class="tag__ tag__6"
                  >已完成</el-tag
                >
                <el-tag v-if="row.workState == 7" class="tag__ tag__7"
                  >已取消</el-tag
                >
              </template>
            </vis-table-pagination>
          </el-tab-pane>
          <el-tab-pane v-if="tab !== 'okay'" label="电站巡检记录" name="xj">
            <vis-table-pagination
              v-if="activeTabName === 'xj'"
              :loading="xjLoading"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="10"
              :columns="xjColumns"
              :total="xjData.total"
              :data="xjData.list"
              :show-overflow-tooltip="true"
              :current-page="xjForm.pageNum"
              background
              class="vis-table-pagination"
              @handle-size-change="xjSizeChange"
              @handle-current-change="xjCurrentChange"
            >
              <template #workState="{ row }">
                <el-tag v-if="row.workState == 1" class="tag__ tag__1"
                  >待指派</el-tag
                >
                <el-tag v-if="row.workState == 2" class="tag__ tag__2"
                  >待接单</el-tag
                >
                <el-tag v-if="row.workState == 3" class="tag__ tag__3"
                  >待开始</el-tag
                >
                <el-tag v-if="row.workState == 4" class="tag__ tag__4"
                  >处理中</el-tag
                >
                <el-tag v-if="row.workState == 5" class="tag__ tag__5"
                  >待验证</el-tag
                >
                <el-tag v-if="row.workState == 6" class="tag__ tag__6"
                  >已完成</el-tag
                >
                <el-tag v-if="row.workState == 7" class="tag__ tag__7"
                  >已取消</el-tag
                >
              </template>
            </vis-table-pagination>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scope>
iframe {
  width: 100%;
  height: calc(100vh - 179px);
  border: none;
  z-index: 2;
}
</style>
<style lang="scss" scoped>
.tag__ {
  font-size: 14px;
  font-weight: 400;
  width: 66px;
  height: 22px;
  line-height: 22px;
  text-align: center;
}
.tag__1 {
  background: rgba(255, 0, 0, 0.1);
  color: rgba(255, 0, 0, 1);
}

.tag__2 {
  background: rgba(16, 140, 255, 0.1);
  color: rgba(16, 140, 255, 1);
}

.tag__3 {
  background: rgba(41, 204, 160, 0.1);
  color: rgba(41, 204, 160, 1);
}
.tag__4 {
  background: rgba(92, 66, 255, 0.1);
  color: rgba(92, 66, 255, 1);
}
.tag__5 {
  background: rgba(251, 110, 30, 0.1);
  color: rgba(251, 110, 30, 1);
}
.tag__6 {
  background: rgba(248, 183, 16, 0.1);
  color: rgba(248, 183, 16, 1);
}
.tag__7 {
  background: rgba(122, 138, 153, 0.1);
  color: rgba(122, 138, 153, 1);
}
</style>
