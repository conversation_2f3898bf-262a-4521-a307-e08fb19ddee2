<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        v-if="activeTabName === 'all'"
        :search-props="searchData"
        :search-data="allSearchData"
        :btn-info="{ span: 24 }"
        @submit-emits="submitEmitsFn"
      ></searchForm>
      <searchForm
        v-if="activeTabName === 'okay'"
        :search-props="okaySearchData"
        :search-data="okayProductionlineForm"
        :btn-info="{ span: 8 }"
        @submit-emits="okaySubmitEmitsFn"
      ></searchForm>
      <searchForm
        v-if="activeTabName === 'await'"
        :search-props="awaitSearchData"
        :search-data="awaitProductionlineForm"
        :btn-info="{ span: 8 }"
        @submit-emits="awaitSubmitEmitsFn"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <template v-if="activeTabName === 'all'">
        <div class="operate">
          <p></p>
          <el-button type="primary" @click="exportBtn">
            <img class="el-icon" src="@/assets/svgs/icon-export.svg" alt="" />
            导出
          </el-button>
        </div>
      </template>
      <vis-table-pagination
        v-if="activeTabName === 'all'"
        :loading="allLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="allSearchData.pageSize"
        :columns="allColumns"
        :total="list.total"
        :data="list.data"
        :show-overflow-tooltip="true"
        :current-page="allSearchData.pageNum"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <!-- :default-sort="{ prop: 'dailyPowerGeneration', order: 'descending' }" -->
        <template #dailyPowerGeneration="{ row }">
          {{ row.dailyPowerGeneration || '--' }}
        </template>
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="viewDetail(row)">查看</el-button>
          </div>
        </template>
      </vis-table-pagination>
      <vis-table-pagination
        v-if="activeTabName === 'okay'"
        :loading="okayLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="20"
        :columns="okayColumns"
        :total="0"
        :data="okayList.data"
        :show-overflow-tooltip="true"
        :current-page="1"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="viewDetail(row)">查看</el-button>
          </div>
        </template>
      </vis-table-pagination>
      <vis-table-pagination
        v-if="activeTabName === 'await'"
        :loading="awaitLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="awaitProductionlineForm.size"
        :columns="awaitColumns"
        :total="awaitList.total"
        :data="awaitList.data"
        :show-overflow-tooltip="true"
        :current-page="awaitProductionlineForm.current"
        background
        class="vis-table-pagination"
        @array-item-click="tooltipClick"
        @handle-size-change="awaitSizeChange"
        @handle-current-change="awaitCurrentChange"
      >
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="viewDetail(row)">查看</el-button>
            <el-button link @click="createOrder(row)">创建工单</el-button>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
  <el-dialog
    v-model="dialogFormVisible"
    :title="`创建工单`"
    align-center
    :before-close="beforeCloseDialog"
    :close-on-click-modal="false"
    width="600px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 180px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-suffix=""
        label-width="110px"
        :rules="formRules"
      >
        <el-form-item label="工单类型" prop="gongdanleixing">
          检修
        </el-form-item>
        <el-form-item label="模板名称" prop="gongdanmoban">
          户用光伏检修
        </el-form-item>
        <el-form-item label="工单标题" prop="gongdanbiaoti">
          <el-input
            v-model="formData.gongdanbiaoti"
            :maxlength="100"
            autocomplete="off"
            placeholder="请输入工单标题"
          />
        </el-form-item>
        <el-form-item label="优先级" prop="youxianji">
          <el-select v-model="formData.youxianji" placeholder="请选择">
            <el-option label="高" value="高"> </el-option>
            <el-option label="中" value="中"> </el-option>
            <el-option label="低" value="低"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择电站" prop="dianzhan">
          <el-select
            v-model="formData.dianzhan"
            placeholder="请选择"
            :disabled="true"
          >
            <el-option
              :label="formData.dianzhanmingchen"
              :value="formData.dianzhan"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系人姓名" prop="lianxirenxingming">
          {{ formData.lianxirenxingming }}
        </el-form-item>
        <el-form-item label="联系人电话" prop="lianxirendianhua">
          {{ formData.lianxirendianhua }}
        </el-form-item>
        <el-form-item label="行政区划" prop="province">
          {{
            (formData.province || '') +
            (formData.city || '') +
            (formData.county || '')
          }}
        </el-form-item>
        <el-form-item label="详细地址" prop="xiangxidizhi">
          {{ formData.xiangxidizhi }}
        </el-form-item>
        <el-form-item label="备注" prop="beizhu">
          <el-input
            v-model="formData.beizhu"
            :rows="5"
            :maxlength="200"
            type="textarea"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="故障部件类型" prop="guzhangbujianleixing">
          <el-select
            v-model="formData.guzhangbujianleixing"
            placeholder="请选择"
          >
            <el-option label="逆变器" value="逆变器"></el-option>
            <el-option label="组件" value="组件"></el-option>
            <el-option label="汇流箱" value="汇流箱"></el-option>
            <el-option label="并网柜" value="并网柜"></el-option>
            <el-option label="支架" value="支架"></el-option>
            <el-option label="其他" value="其他"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="故障部件" prop="guzhangbujiansn">
          <el-input
            v-model="formData.guzhangbujiansn"
            :maxlength="100"
            autocomplete="off"
            placeholder="设备名称/sn码"
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog(formRef)">取消</el-button>
        <el-button
          type="primary"
          :loading="saveLoading"
          @click="onSaveForm(formRef)"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="dialogDetailVisible"
    :title="`工单详情`"
    align-center
    :before-close="() => (dialogDetailVisible = false)"
    :close-on-click-modal="false"
    width="400px"
    class="vis-dialog"
  >
    <el-scrollbar max-height="calc(100vh - 180px)" style="padding-right: 15px">
      <el-descriptions title="" :column="1">
        <el-descriptions-item label="工单编号：">{{
          detailData.workOrderNo
        }}</el-descriptions-item>
        <el-descriptions-item label="工单标题："
          ><a
            :href="`https://servicego.tsy.spic.com.cn/site/custom-object/28422/records/view/${detailData.id}`"
            target="_blank"
            >{{ detailData.workOrderName }}</a
          ></el-descriptions-item
        >
        <el-descriptions-item label="工单状态：">{{
          detailData.orderState
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间：">
          {{ detailData.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人：">{{
          detailData.createUser
        }}</el-descriptions-item>
        <el-descriptions-item label="工程师：">{{
          detailData.handlers
        }}</el-descriptions-item>
        <el-descriptions-item label="处理描述：">{{
          detailData.processDescription
        }}</el-descriptions-item>
        <el-descriptions-item label="处理完成图片：">
          <el-image
            v-if="detailData.processedImage"
            :src="WF_PATH + detailData.processedImage"
            :preview-src-list="[WF_PATH + detailData.processedImage]"
            fit="cover"
            class="w-80px h-80px"
          >
          </el-image>
          <span v-else>--</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-scrollbar>
  </el-dialog>
</template>

<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import address from '@/api/data/area.json'
import filters from '@/utils/filter.js'
import request from '@/utils/request'
import type { FormInstance, FormRules } from 'element-plus'
// import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'
import { allStation as allStationAPI } from '@/api/index.ts'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const WF_PATH = import.meta.env.VITE_APP_WF_PATH
const router = useRouter()
const route = useRoute()

type TabName = 'all' | 'okay' | 'await'
const activeTabName = ref<TabName>('all')
const list = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
const okayList = reactive<{ data: any[] }>({
  data: []
})
const awaitList = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})

// 全部电站表格列
const allColumns = [
  {
    prop: 'index',
    label: '序号',
    minWidth: 80
  },
  {
    prop: 'operationCompanyName',
    label: '运维商',
    minWidth: 100
  },
  {
    prop: 'uniqueId',
    label: '电站编号',
    minWidth: 100
  },
  {
    prop: 'numCstrPggrd',
    label: '发电户号',
    minWidth: 100
  },
  {
    prop: 'stationName',
    label: '电站名称',
    minWidth: 100
  },
  {
    prop: 'stationRate',
    label: '电站评级',
    minWidth: 100
  },
  {
    prop: 'operationProjectName',
    label: '项目名称',
    minWidth: '120'
  },
  {
    prop: 'districtAddress',
    label: '行政区划',
    minWidth: 180
  },
  {
    prop: 'capins',
    label: '装机容量(kW)',
    minWidth: 140
  },
  {
    prop: 'dailyPowerGeneration',
    label: '当日发电量(kWh)',
    minWidth: 164,
    // sortable: 'custom',
    slotName: 'dailyPowerGeneration'
  },
  {
    prop: 'dayUtilizationHours',
    label: '日利用小时数',
    minWidth: 140
  },
  {
    prop: 'projectCompanyName',
    label: '资产所属公司',
    minWidth: 140
  },

  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: 120
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 60,
    fixed: 'right'
  }
]
// 当日发电量排序
// const sortChange = (val: any) => {
//   // 当日发电量排序 0-降序 1-升序
//   allSearchData.value.pageNum = 1
//   if (val.order == 'ascending') {
//     allSearchData.value.dailyPowerSort = 1
//   } else {
//     allSearchData.value.dailyPowerSort = 0
//   }
//   getListFun()
// }
// 全部电站查询参数
let allSearchData = ref<any>({
  areaArr: '', // 行政区域数据
  operationProjectCode: '', // 项目名称
  projectCompanyCode: '', // 资产所属公司
  operationCompanyCode: '', // 运维商
  stationRate: '', // 电站评级
  name: '', // 电站名称
  // dailyPowerSort: 0, // 当日发电量排序 0-降序 1-升序
  areaCodeFlag: null,
  areaCode: null,
  pageSize: 10,
  pageNum: 1
})
// 全部电站表单数据
const searchData = ref([
  {
    label: '项目名称',
    placeholder: '请选择项目名称',
    type: 'projectSelect',
    prop: 'operationProjectCode',
    span: 8,
    width: 100
  },
  {
    label: '资产所属公司',
    placeholder: '请选择资产所属公司',
    type: 'companySelect',
    prop: 'projectCompanyCode',
    span: 8,
    width: 100
  },
  // {
  //   label: '运维商',
  //   placeholder: '请选择运维商',
  //   type: 'select',
  //   prop: 'operationCompanyCode',
  //   span: 8,
  //   width: 100,
  //   filterable: true,
  //   options: []
  // },
  {
    label: '电站评级',
    placeholder: '请选择电站评级',
    type: 'select',
    prop: 'stationRate',
    span: 8,
    width: 100,
    options: [
      { label: 'A', value: 'A' },
      { label: 'B', value: 'B' },
      { label: 'C', value: 'C' },
      { label: 'D', value: 'D' },
      { label: 'E', value: 'E' }
    ]
  },
  {
    label: '发电户号',
    placeholder: '请输入发电户号',
    type: 'input',
    prop: 'numCstrPggrd',
    span: 8,
    width: 100
  },
  {
    label: '电站名称',
    placeholder: '请输入电站名称',
    type: 'input',
    prop: 'name',
    span: 8,
    width: 100
  },
  {
    label: '行政区划',
    placeholder: '请选择行政区划',
    type: 'cascader',
    prop: 'areaArr',
    options: address,
    props: {
      value: 'value',
      label: 'label',
      children: 'children',
      expandTrigger: 'hover',
      checkStrictly: true,
      emitPath: true
    },
    span: 8,
    width: 100,
    class: 'popper-width-540px'
  }
])
// 全部电站查询按钮
const submitEmitsFn = async (val: any) => {
  if (Object.keys(val).length) {
    allSearchData.value = val
    allSearchData.value.pageNum = 1
    allSearchData.value.pageSize = 10
  } else {
    allSearchData.value = {
      areaArr: '',
      operationProjectCode: '', // 项目名称
      projectCompanyCode: '', // 资产所属公司
      operationCompanyCode: '', // 运维商
      stationRate: '', // 电站评级
      name: '', // 电站名称
      // dailyPowerSort: 0, // 当日发电量排序 0-降序 1-升序
      areaCodeFlag: null,
      areaCode: null,
      pageSize: 10,
      pageNum: 1
    }
  }
  getListFun()
}

// 优选电站表格列
const okayColumns = [
  {
    prop: 'index',
    label: '序号',
    minWidth: 80
  },
  {
    prop: 'ledgerFullCapacityHours1d',
    label: '日满发小时数',
    minWidth: 120
  },
  {
    prop: 'stationInstalledCapacity',
    label: '装机容量(kW)',
    minWidth: 140
  },
  {
    prop: 'cdsta',
    label: '电站编号',
    minWidth: 100
  },
  {
    prop: 'stationName',
    label: '电站名称',
    minWidth: 100
  },
  {
    prop: 'projectName',
    label: '项目名称',
    minWidth: 120
  },
  {
    prop: 'districtAddress',
    label: '行政区划',
    minWidth: 180
  },
  {
    prop: 'assetSellerName',
    label: '资产出售方',
    minWidth: 140
  },
  {
    prop: 'assetOwnerCompanyName',
    label: '资产所属公司',
    minWidth: 140
  },
  // {
  //   prop: 'operatorBusinessName',
  //   label: '运维商',
  //   minWidth: 140
  // },
  // {
  //   prop: 'productionDate',
  //   label: '并网日期',
  //   minWidth: 120
  // },
  {
    prop: 'stationCreateTime',
    label: '创建时间',
    minWidth: 120
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 60,
    fixed: 'right'
  }
]
// 优选电站查询参数
let okayProductionlineForm = ref<Record<string, any>>({
  assetOwnerCompanyName: '',
  assetSellerName: '',
  cityCode: '',
  districtCode: '',
  projectName: '',
  provinceCode: '',
  stationName: '',
  districtAddress: []
})
// 优选电站表单数据
const okaySearchData = ref([
  {
    label: '项目名称',
    placeholder: '请输入项目名称',
    type: 'input',
    prop: 'projectName',
    span: 8,
    width: 100
  },
  {
    label: '资产所属公司',
    placeholder: '请输入资产所属公司',
    type: 'input',
    prop: 'assetOwnerCompanyName',
    span: 8,
    width: 100
  },
  {
    label: '行政区划',
    placeholder: '请选择行政区划',
    type: 'cascader',
    prop: 'districtAddress',
    options: address,
    props: {
      value: 'value',
      label: 'label',
      children: 'children',
      expandTrigger: 'hover',
      checkStrictly: true,
      emitPath: true
    },
    span: 8,
    width: 100,
    class: 'popper-width-540px'
  },
  {
    label: '电站名称',
    placeholder: '请输入电站名称',
    type: 'input',
    prop: 'stationName',
    span: 8,
    width: 100
  },
  {
    label: '资产出售方',
    placeholder: '请输入资产出售方',
    type: 'input',
    prop: 'assetSellerName',
    span: 8,
    width: 100
  }
])
// 优选电站查询按钮
const okaySubmitEmitsFn = async (val: any) => {
  if (Object.keys(val).length) {
    okayProductionlineForm.value = { ...val }
  } else {
    okayProductionlineForm.value = {
      assetOwnerCompanyName: '',
      assetSellerName: '',
      cityCode: '',
      districtCode: '',
      projectName: '',
      provinceCode: '',
      stationName: '',
      districtAddress: []
    }
  }
  getOkayList()
}

// 需处理电站表格列
const awaitColumns = [
  {
    prop: 'index',
    label: '序号',
    minWidth: 80
  },
  {
    prop: 'eventType',
    label: '事件类型',
    minWidth: 100
  },
  {
    prop: 'duration',
    label: '时长',
    minWidth: 100,
    formatter: (v: { duration: string }) => v.duration + '天'
  },
  {
    prop: 'stationInstalledCapacity',
    label: '装机容量(kW)',
    minWidth: 140
  },
  {
    prop: 'cdsta',
    label: '电站编号',
    minWidth: 100
  },
  {
    prop: 'stationName',
    label: '电站名称',
    minWidth: 100
  },
  {
    prop: 'projectName',
    label: '项目名称',
    minWidth: 120
  },
  {
    prop: 'districtAddress',
    label: '行政区划',
    minWidth: 180
  },
  {
    prop: 'assetSellerName',
    label: '资产出售方',
    minWidth: 140
  },
  {
    prop: 'assetOwnerCompanyName',
    label: '资产所属公司',
    minWidth: 140
  },
  // {
  //   prop: 'operatorBusinessName',
  //   label: '运维商',
  //   minWidth: 140
  // },
  // {
  //   prop: 'productionDate',
  //   label: '并网日期',
  //   minWidth: 120
  // },
  {
    prop: 'screenWorkOrderVoList',
    label: '关联工单',
    type: 'array',
    emit: 'click',
    minWidth: 200
  },
  {
    prop: 'stationCreateTime',
    label: '创建时间',
    minWidth: 120
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 124,
    fixed: 'right'
  }
]
// 需处理电站查询参数
let awaitProductionlineForm = ref<Record<string, any>>({
  assetOwnerCompanyName: '',
  assetSellerName: '',
  cityCode: '',
  current: 1,
  districtCode: '',
  projectName: '',
  provinceCode: '',
  size: 10,
  stationName: '',
  districtAddress: []
})
// 需处理电站表单数据
const awaitSearchData = ref([
  {
    label: '项目名称',
    placeholder: '请输入项目名称',
    type: 'input',
    prop: 'projectName',
    span: 8,
    width: 100
  },
  {
    label: '资产所属公司',
    placeholder: '请输入资产所属公司',
    type: 'input',
    prop: 'assetOwnerCompanyName',
    span: 8,
    width: 100
  },
  {
    label: '行政区划',
    placeholder: '请选择行政区划',
    type: 'cascader',
    prop: 'districtAddress',
    options: address,
    props: {
      value: 'value',
      label: 'label',
      children: 'children',
      expandTrigger: 'hover',
      checkStrictly: true,
      emitPath: true
    },
    span: 8,
    width: 100,
    class: 'popper-width-540px'
  },
  {
    label: '电站名称',
    placeholder: '请输入电站名称',
    type: 'input',
    prop: 'stationName',
    span: 8,
    width: 100
  },
  {
    label: '资产出售方',
    placeholder: '请输入资产出售方',
    type: 'input',
    prop: 'assetSellerName',
    span: 8,
    width: 100
  }
])
// 需处理电站查询按钮
const awaitSubmitEmitsFn = async (val: any) => {
  if (Object.keys(val).length) {
    awaitProductionlineForm.value = { ...val }
    awaitProductionlineForm.value.current = 1
    awaitProductionlineForm.value.size = 10
  } else {
    awaitProductionlineForm.value = {
      assetOwnerCompanyName: '',
      assetSellerName: '',
      cityCode: '',
      current: 1,
      districtCode: '',
      projectName: '',
      provinceCode: '',
      size: 10,
      stationName: '',
      districtAddress: []
    }
  }
  getAwaitList()
}

let allLoading = ref(false)
// 获取全部电站列表
const getListFun = async () => {
  try {
    if (allSearchData.value.areaArr?.length) {
      allSearchData.value.areaCodeFlag = allSearchData.value.areaArr?.length
      allSearchData.value.areaCode = Number(
        allSearchData.value.areaArr[allSearchData.value.areaArr.length - 1]
      )
    } else {
      allSearchData.value.areaCodeFlag = null
      allSearchData.value.areaCode = null
    }

    let result = await request({
      url: '/operate/stationLedger/getStationLedgerVOList',
      method: 'post',
      data: {
        ...allSearchData.value,
        code: localStorage.getItem('PVOM_COMPANY_CODE')
      },
      loading: [allLoading]
    })
    if (result.data.code == 200) {
      if (result.data.data.records.length > 0) {
        result.data.data.records.forEach((value: any) => {
          value.workState = filters.workStateFilter(value.workState)
          value.priority = filters.priorityFilter(value.priority)
          value.urgency = filters.priorityFilter(value.urgency)
          value.createTime = filters.datetimeFilter(
            new Date(value.createTime),
            'YYYY-MM-DD hh:mm:ss'
          )
          value.startTime = filters.datetimeFilter(
            new Date(value.startTime),
            'YYYY-MM-DD hh:mm:ss'
          )
          value.endTime = filters.datetimeFilter(
            new Date(value.endTime),
            'YYYY-MM-DD hh:mm:ss'
          )
          value.updateTime = filters.datetimeFilter(
            new Date(value.updateTime),
            'YYYY-MM-DD hh:mm:ss'
          )
        })
        list.data = result.data?.data?.records || []
        list.total = result.data?.data?.total || 0
      } else {
        list.data = []
        list.total = 0
      }
    }
  } catch (err: any) {
    ElMessage({
      type: 'error',
      message: err.message
    })
  }
}
let okayLoading = ref(false)
// 获取优选电站列表
const getOkayList = async () => {
  const pm = { ...okayProductionlineForm.value }
  if (Array.isArray(pm.districtAddress)) {
    pm.provinceCode = pm.districtAddress?.[0] || ''
    pm.cityCode = pm.districtAddress?.[1] || ''
    pm.districtCode = pm.districtAddress?.[2] || ''
  }
  delete pm.districtAddress
  try {
    const { data } = await request({
      url: '/operate/stationLedger/getExcellentStationVOList',
      method: 'post',
      data: { ...pm, code: localStorage.getItem('PVOM_COMPANY_CODE') },
      loading: [okayLoading]
    })
    okayList.data = data.data || []
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
let awaitLoading = ref(false)
// 获取需处理电站列表
const getAwaitList = async () => {
  const pm = { ...awaitProductionlineForm.value }
  if (Array.isArray(pm.districtAddress)) {
    pm.provinceCode = pm.districtAddress?.[0] || ''
    pm.cityCode = pm.districtAddress?.[1] || ''
    pm.districtCode = pm.districtAddress?.[2] || ''
  }
  // delete pm.districtAddress
  try {
    const { data } = await request({
      url: '/operate/stationLedger/getDisposeStationVOList',
      method: 'post',
      data: { ...pm, code: localStorage.getItem('PVOM_COMPANY_CODE') },
      loading: [awaitLoading]
    })
    awaitList.data = data?.data?.records || []
    awaitList.total = data?.data?.total || 0
  } catch (e: any) {
    awaitList.data = []
    awaitList.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    if (startWatch) {
      if (activeTabName.value === 'await') {
        getAwaitList()
      } else if (activeTabName.value === 'okay') {
        getOkayList()
      } else {
        getListFun()
      }
    }
  }
)
onMounted(async () => {
  activeTabName.value = (route.params.tab as TabName) || 'all'
  if (activeTabName.value === 'await') {
    await getAwaitList()
    startWatch = true
  } else if (activeTabName.value === 'okay') {
    await getOkayList()
    startWatch = true
  } else {
    // getCompanyList()
    companyCode.data && (await getListFun())
    startWatch = true
  }
})

// 获取运维商列表
// const getCompanyList = async () => {
//   try {
//     const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
//     searchData.value[2].options = data.data.map((item: any) => {
//       return {
//         value: item.companyCode || '',
//         label: item.companyName || ''
//       }
//     })
//   } catch (e: any) {
//     searchData.value[2].options = []
//   }
// }

const viewDetail = (row: any) => {
  if (!row.uniqueId && !row.cdsta) {
    ElMessage({
      type: 'error',
      message: '缺少电站编号'
    })
    return false
  }
  router.push({
    path: `/station/${activeTabName.value}/detail/${row.uniqueId || row.cdsta}`,
    query: {
      hideBack: 'true'
    }
  })
}

const handleSizeChange = async (params: any) => {
  allSearchData.value.pageNum = 1
  allSearchData.value.pageSize = params.pageSize
  await getListFun()
}
const handleCurrentChange = async (params: any) => {
  allSearchData.value.pageNum = params.currentPage
  await getListFun()
}
const awaitSizeChange = async (params: any) => {
  awaitProductionlineForm.value.current = 1
  awaitProductionlineForm.value.size = params.pageSize
  await getAwaitList()
}
const awaitCurrentChange = async (params: any) => {
  awaitProductionlineForm.value.current = params.currentPage
  await getAwaitList()
}

const createOrder = async (row: any) => {
  try {
    let result: any = await request({
      url: `/operate/stationLedger/getWorkStationInfoVO?stationUniqueId=${row.cdsta}`,
      method: 'post'
    })
    if (result.data.code == 200) {
      formData.value.dianzhanmingchen = result.data.data.stationName || ''
      formData.value.dianzhan = result.data.data.stationUniqueId || ''
      formData.value.lianxirenxingming = result.data.data.nameHoh || ''
      formData.value.lianxirendianhua = result.data.data.nimmphoh || ''
      formData.value.province = result.data.data.prvName || ''
      formData.value.city = result.data.data.cityName || ''
      formData.value.county = result.data.data.distName || ''
      formData.value.xiangxidizhi = result.data.data.lco || ''
    }
    dialogFormVisible.value = true
  } catch (e) {
    ElMessage({
      type: 'error',
      message: '请求电站信息错误！'
    })
  }
}
const formRef = ref<FormInstance>()
const dialogFormVisible = ref(false)
let formData = ref<Record<string, any>>({
  gongdanleixing: '检修',
  gongdanmoban: '户用光伏检修',
  gongdanbiaoti: '',
  youxianji: '高',
  dianzhan: '',
  dianzhanmingchen: '',
  lianxirenxingming: '',
  lianxirendianhua: '',
  province: '',
  city: '',
  county: '',
  xiangxidizhi: '',
  beizhu: '',
  guzhangbujianleixing: '逆变器',
  guzhangbujiansn: ''
})
const formRules = reactive<FormRules<Record<string, any>>>({
  gongdanleixing: [
    { required: true, message: '请输入工单工单类型', trigger: 'change' }
  ],
  gongdanmoban: [
    { required: true, message: '请输入模板名称', trigger: 'change' }
  ],
  gongdanbiaoti: [
    { required: true, message: '请输入工单标题', trigger: 'change' }
  ],
  youxianji: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  dianzhan: [{ required: true, message: '请选择电站', trigger: 'change' }]
  // lianxirenxingming: [
  //   { required: true, message: '请输入联系人姓名', trigger: 'change' }
  // ],
  // lianxirendianhua: [
  //   {
  //     required: true,
  //     pattern: /^1[3456789]\d{9}$/,
  //     message: '请输入有效的联系人电话',
  //     trigger: 'change'
  //   }
  // ],
  // province: [{ required: true, message: '请输入行政区划', trigger: 'change' }],
  // xiangxidizhi: [
  //   { required: true, message: '请输入详细地址', trigger: 'change' }
  // ]
})
const beforeCloseDialog = (done: () => void) => {
  formRef.value && formRef.value.resetFields()
  dialogFormVisible.value = false
  done()
}
const closeDialog = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formRef.value && formRef.value.resetFields()
  dialogFormVisible.value = false
}

const saveLoading = ref(false)
const onSaveForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid) => {
    if (valid) {
      const params = {
        ...formData.value,
        lianxirenxingming: formData.value.lianxirenxingming || null,
        lianxirendianhua: formData.value.lianxirendianhua || null
      }
      try {
        const { data } = await request({
          url: '/operate/remote-access/add',
          method: 'post',
          data: params,
          loading: [saveLoading]
        })
        if (data.code === '200') {
          ElMessage({
            message: '创建成功',
            type: 'success'
          })
          closeDialog(formEl)
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}

const dialogDetailVisible = ref(false)
const detailData = ref<Record<string, any>>({})
const tooltipClick = (val: Record<string, any>) => {
  detailData.value = {}
  dialogDetailVisible.value = true
  detailData.value = val
}
const exportBtn = async () => {
  try {
    if (allSearchData.value.areaArr?.length) {
      allSearchData.value.areaCodeFlag = allSearchData.value.areaArr?.length
      allSearchData.value.areaCode = Number(
        allSearchData.value.areaArr[allSearchData.value.areaArr.length - 1]
      )
    } else {
      allSearchData.value.areaCodeFlag = null
      allSearchData.value.areaCode = null
    }
    let res = await allStationAPI.downloadAllStation({
      operationProjectCode: allSearchData.value.operationProjectCode, // 项目名称
      projectCompanyCode: allSearchData.value.projectCompanyCode, // 资产所属公司
      operationCompanyCode: allSearchData.value.operationCompanyCode, // 运维商
      stationRate: allSearchData.value.stationRate, // 电站评级
      name: allSearchData.value.name, // 电站名称
      // dailyPowerSort: allSearchData.value.dailyPowerSort, // 当日发电量排序 0-降序 1-升序
      areaCodeFlag: allSearchData.value.areaCodeFlag, // 省市区 123
      areaCode: allSearchData.value.areaCode // 区域code
    })
    let str = res.headers['content-disposition']
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(res.data)
    link.download =
      '电站数据统计报表.' + str.split('.')[str.split('.').length - 1]
    link.click()
  } catch (e: any) {
    console.log('导出失败！')
  }
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss">
.el-dialog.padding-bottom .el-dialog__body {
  padding: 15px;
  padding-bottom: 15px;
}
</style>
