<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        :btn-info="{ span: 24 }"
        @submit-emits="submitEmitsFn"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="tables">
        <vis-table-pagination
          :loading="tableLoading"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchData.pageSize"
          :current-page="searchData.pageNum"
          :columns="columns"
          :total="listTotal"
          :data="listData"
          :show-overflow-tooltip="true"
          background
          class="vis-table-pagination"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
          @sort-change="sortChange"
        >
          <template #stationMark="{ row }">
            <span
              :style="{
                color:
                  Number(row.stationMark) < 0
                    ? 'rgba(230, 46, 50, 1)'
                    : 'rgba(230, 135, 46, 1)'
              }"
              >{{ row.stationMark || '--' }}</span
            >
          </template>
        </vis-table-pagination>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import address from '@/api/data/area.json'
// import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'
import { score as scoreAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import Big from 'big.js'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
interface PageObject {
  currentPage: number
  pageSize: number
}
const Time = dayjs(new Date()).add(-1, 'month').format('YYYY-MM-DD')
// 搜索
const searchData = ref({
  companyCode: '',
  operationCompanyCode: '',
  areaCode: '',
  areaCodeFlag: '', // 区域编码标志 1-省,2-市,3-区
  Time,
  scoreSort: 0, // 0-降序 1-升序
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    label: '所属资产公司',
    type: 'companySelect',
    prop: 'companyCode',
    span: 8,
    width: 100
  },
  {
    label: '行政区划',
    type: 'cascader',
    prop: 'areaCode',
    options: address,
    props: {
      value: 'value',
      label: 'label',
      children: 'children',
      expandTrigger: 'hover',
      checkStrictly: true,
      emitPath: true
    },
    span: 8,
    width: 100,
    class: 'popper-width-540px'
  },
  {
    prop: 'Time',
    label: '选择时间',
    type: 'date',
    pickerType: 'month',
    format: 'YYYY-MM-DD',
    span: 8,
    width: 100,
    clearable: false,
    disabledDate: (time: any) => {
      return time.getTime() > dayjs(new Date()).add(-1, 'month')
    }
  }
])
const submitEmitsFn = (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  getTableData()
}

const columns = [
  {
    prop: 'stationCode',
    label: '电站编码'
  },
  {
    prop: 'stationName',
    label: '电站名称'
  },
  {
    prop: 'projectCompanyName',
    label: '所属资产公司'
  },
  {
    prop: 'operationCompanyName',
    label: '运维公司'
  },
  {
    prop: 'area',
    label: '行政区划'
  },
  {
    prop: 'generationPlanCr',
    label: '发电计划完成率'
  },
  {
    prop: 'workOrderCr',
    label: '工单完成率'
  },
  {
    prop: 'eliminationTr',
    label: '消缺及时率'
  },
  {
    prop: 'inspectionCr',
    label: '巡检完成率'
  },
  {
    prop: 'twoVotesPassRate',
    label: '两票合格率'
  },
  {
    prop: 'safetyIncidentsNum',
    label: '安全事故数'
  },
  {
    prop: 'stationMark',
    label: '电站评分',
    sortable: 'custom',
    slotName: 'stationMark',
    minWidth: 120
  }
]
const getParams = () => {
  const params: any = { ...searchData.value }
  //开始时间为1号，结束时间为1号并加一个月，并格式化时间为 YYYY-MM-DD hh:mm:ss
  const startDateStr = searchData.value.Time

  const [startYear, startMonth] = startDateStr.split('-')
  const startDate = `${startYear}-${startMonth}-01`
  params.startTime = `${startDate} 00:00:00`
  return params
}
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await scoreAPI.getStationMarkList(
      {
        ...getParams(),
        areaCode:
          searchData.value.areaCode && searchData.value.areaCode.length
            ? searchData.value.areaCode[searchData.value.areaCode.length - 1]
            : '',
        areaCodeFlag:
          searchData.value.areaCode && searchData.value.areaCode.length
            ? searchData.value.areaCode.length
            : '',
        code: localStorage.getItem('PVOM_COMPANY_CODE')
      },
      true
    )
    listTotal.value = data.data.total
    data.data.stationInfoMarkVoList.forEach((item: any) => {
      item.generationPlanCr =
        item.generationPlanCr == '--'
          ? '--'
          : new Big(Number(item.generationPlanCr))
              .times(new Big(100))
              .toNumber() + '%' // '发电计划完成率'

      item.workOrderCr =
        item.workOrderCr == '--'
          ? '--'
          : new Big(Number(item.workOrderCr)).times(new Big(100)).toNumber() +
            '%' // '工单完成率'
      item.eliminationTr =
        item.eliminationTr == '--'
          ? '--'
          : new Big(Number(item.eliminationTr)).times(new Big(100)).toNumber() +
            '%' // '消缺及时率'
      item.inspectionCr =
        item.inspectionCr == '--'
          ? '--'
          : new Big(Number(item.inspectionCr)).times(new Big(100)).toNumber() +
            '%' // '巡检完成率'
      item.twoVotesPassRate =
        item.twoVotesPassRate == '--'
          ? '--'
          : new Big(Number(item.twoVotesPassRate))
              .times(new Big(100))
              .toNumber() + '%' // '两票合格率'
    })
    listData.value = data.data.stationInfoMarkVoList || []
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: PageObject) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: PageObject) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}
const sortChange = (val: any) => {
  // 电站评分排序 0-降序 1-升序
  searchData.value.pageNum = 1
  if (val.order == 'ascending') {
    searchData.value.scoreSort = 1
  } else {
    searchData.value.scoreSort = 0
  }
  getTableData()
}
// 获取运维商列表
// const getCompanyList = async () => {
//   try {
//     const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
//     searchProps.value[1].options = data.data.map((item: any) => {
//       return {
//         value: item.companyCode || '',
//         label: item.companyName || ''
//       }
//     })
//   } catch (e: any) {
//     searchProps.value[1].options = []
//   }
// }

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  // getCompanyList()
  companyCode.data && (await getTableData())
  startWatch = true
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
