<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>运维商列表</p>
        <div>
          <el-radio-group
            v-model="searchData.scoreSort"
            size="small"
            :disabled="listData.length == 0 ? true : false"
            @change="handleSort"
          >
            <el-radio-button :label="0"
              ><el-icon><SortDown /></el-icon>降序</el-radio-button
            >
            <el-radio-button :label="1"
              ><el-icon><SortUp /></el-icon>升序</el-radio-button
            >
          </el-radio-group>
        </div>
      </div>
      <charts-pagination
        :loading="tableLoading"
        :finished="finished"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :total="listTotal"
        :data="listData"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      ></charts-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  operation as operationAPI
  // opsPersonnel as opsPersonnelAPI
} from '@/api/index.ts'
import chartsPagination from './components/charts-pagination.vue'
import dayjs from 'dayjs'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const time = dayjs(new Date()).add(-1, 'month').format('YYYY-MM-DD')

// 搜索
const searchData = ref({
  operateCompanyCode: '',
  time,
  scoreSort: 0,
  pageNum: 1,
  pageSize: 10
})

const searchProps = ref([
  {
    prop: 'time',
    label: '选择时间',
    type: 'date',
    pickerType: 'month',
    format: 'YYYY-MM-DD',
    span: 8,
    width: 100,
    clearable: false,
    disabledDate: (time: any) => {
      return time.getTime() > dayjs(new Date()).add(-1, 'month')
    }
  }
])
const handleSearch = (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  getTableData()
}

const getChartOption = () => {
  return {
    title: {
      // 无数据时占位用
      show: false, // 判断有没有数据，没有则show为true
      textStyle: {
        color: '#ccc',
        fontSize: 18
      },
      text: '暂无数据',
      left: 'center',
      top: 'center'
    },
    legend: {
      itemHeight: 6,
      itemWidth: 8,
      left: 'center',
      top: '90%',
      data: ['月发电量', '月计划发电量', '月计划完成率']
    },
    tooltip: {
      trigger: 'axis',
      formatter(params: any) {
        let xName
        if (params.length > 0) {
          xName = params[0].name
        }
        var res = `<div>`
        params.forEach((t: any) => {
          let filtered = params.filter((obj: any) => obj.seriesIndex === 1)
          let percentage = ''
          if (filtered.length > 0 && t.seriesIndex == 2) {
            percentage = t.value === '0' ? '0' : `${Number(t.value)}%`
          }
          res += `
					<div style="width:170px;display:flex;align-items: center;justify-content: space-between;">
						<div>${t.marker}</div>
						<div>${t.seriesName}</div>
						<div style="flex:1;text-align:right;">
							${percentage ? percentage : Number(t.value) || 0}
						</div>
					</div>
				`
        })
        res += `</div>`
        return `${xName}${res}`
      }
    },
    grid: {
      top: 30,
      bottom: 40,
      // left: 40,
      left: 0,
      right: 0,
      containLabel: true //防止标签溢出
    },
    xAxis: {
      data: []
    },
    yAxis: [
      {
        type: 'value',
        name: 'kWh',
        min: 0
      },
      {
        type: 'value',
        name: '%',
        min: 0,
        show: false
      }
    ],
    series: [
      {
        name: '月发电量',
        type: 'bar',
        data: [],
        color: ['#29CCA0'],
        barMaxWidth: '10%',
        barMinWidth: '6'
        // yAxisIndex: 0
      },
      {
        name: '月计划发电量',
        type: 'bar',
        data: [],
        color: ['#1890FF'],
        barMaxWidth: '10%',
        barMinWidth: '6'
        // yAxisIndex: 1
      },
      {
        name: '月计划完成率',
        type: 'line',
        data: [],
        color: ['#FFBD3D'],
        symbol: 'none',
        smooth: true //是否平滑曲线显示
      }
    ]
  }
}

const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const finished = ref(false)
const getTableData = async () => {
  if (tableLoading.value) return
  try {
    const params: any = getParams()
    tableLoading.value = true
    let { data } = await operationAPI.queryOperateCompanyScore(
      { ...params },
      true
    )

    const processItem = (item: any, powerGenList: any[]) => {
      const chartOption: any = getChartOption()

      item.chartOption = chartOption
      item.formatScore = formatScale(item.totalScore)

      powerGenList?.forEach((t: any) => {
        const mon = t.mon.split('-')[1]
        let mounth = parseInt(mon)

        chartOption.xAxis.data.push(mounth)
        chartOption.series[0].data.push(t.monPowerGeneration || 0)
        chartOption.series[1].data.push(t.monPlanPowerGeneration || 0)
        chartOption.series[2].data.push(t.monPlanCompletionRate || 0)
      })
    }

    if (data && data.data && data.data.records) {
      data.data.records.forEach((item: any) => {
        processItem(item, item.powerGenerationList)
      })
    }

    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  } finally {
    finished.value = true
    tableLoading.value = false
  }
}
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const getParams = () => {
  const params: any = { ...searchData.value }

  //开始时间为1号，结束时间为1号并加一个月，并格式化时间为 YYYY-MM-DD hh:mm:ss
  const startDateStr = searchData.value.time

  const [startYear, startMonth] = startDateStr.split('-')
  const startDate = `${startYear}-${startMonth}-01`

  params.startLocalDate = `${startDate} 00:00:00`

  return params
}

// const getCompany = async () => {
//   try {
//     const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
//     searchProps.value[0].options = data.data.map((item: any) => {
//       return {
//         label: item.companyName || '',
//         value: item.companyCode || ''
//       }
//     })
//   } catch (e: any) {
//     searchProps.value[0].options = []
//   }
// }
/**
 * 排序
 */
const handleSort = async () => {
  searchData.value.pageNum = 1
  getTableData()
}

const formatScale = (percentScore: number) => {
  let fivePointScore = (percentScore / 100) * 5
  // return fivePointScore.toFixed(2)
  console.log('fivePointScore', fivePointScore)
  fivePointScore = fivePointScore > 5 ? 5 : fivePointScore
  return fivePointScore
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  // getCompany()
  companyCode.data && (await getTableData())
  startWatch = true
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.page-main {
  box-shadow: none;

  .page-search {
    box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.1);
    border-radius: 8px;
  }

  .main {
    background: transparent;
    padding: 0;
  }
  .operate {
    margin: 24px 0;
    margin-bottom: 16px;
  }
}

.btn-sort {
  box-shadow: 0px 0px 8px 0px rgba(10, 61, 47, 0.15);
  &:hover,
  &:focus {
    color: #666;
    border-color: #e6e6e8;
    background-color: #fff;
    outline: 0;
  }
}
</style>
