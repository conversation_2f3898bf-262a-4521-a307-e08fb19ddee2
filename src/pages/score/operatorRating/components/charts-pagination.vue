<!--
 * @Description: 卡片列表
 * @Author: zwcong
 * @Date: 2024-04-28 16:26:44
 * @LastEditors: zwcong
 * @LastEditTime: 2024-06-05 18:21:55
-->
<template>
  <div>
    <div
      ref="chartsRef"
      v-loading="loading"
      class="charts-pagination"
      :style="{ height: height || defaultHeight }"
    >
      <div v-for="item in data" :key="item.id" class="box">
        <div class="box-header">
          <div class="header-item left-item">
            <span class="mr-24px">{{ item.companyName }}</span>
            <el-rate
              v-model="item.formatScore"
              class="mr-8px"
              disabled
              show-score
              :text-color="
                (item.powerGenerationScore || 0) +
                  (item.operationIdxScore || 0) +
                  (item.safeIdxScore || 0) <
                0
                  ? '#E62E32'
                  : '#E6872E'
              "
              disabled-void-color="#EBF0F5"
              :score-template="`${Number(
                (item.powerGenerationScore || 0) +
                  (item.operationIdxScore || 0) +
                  (item.safeIdxScore || 0)
              ).toFixed(2)}分`"
            />
          </div>
        </div>
        <div class="box-body">
          <div class="body-item">
            <div class="body-title">
              <span>发电计划完成情况</span
              ><span
                class="score"
                :class="{ 'score-warn': item.powerGenerationScore < 0 }"
                >{{ item.powerGenerationScore }}分</span
              >
            </div>
            <div class="chart-item chart-container">
              <charts
                v-if="
                  item.powerGenerationList && item.powerGenerationList.length
                "
                :option="item.chartOption"
              ></charts>
              <div v-else class="no-data">暂无数据</div>
            </div>
          </div>
          <div class="body-item">
            <div class="body-title">
              <span>运维指标完成情况</span
              ><span
                class="score"
                :class="{ 'score-warn': item.operationIdxScore < 0 }"
                >{{ item.operationIdxScore }}分</span
              >
            </div>
            <div class="chart-item progress-item">
              <div class="item">
                <div class="label">工单完成率</div>
                <div class="progress">
                  <div
                    class="bar"
                    :style="`width: ${item.workOrderCompletedRate}%`"
                  ></div>
                </div>
                <div class="score">{{ item.workOrderCompletedRate }}%</div>
              </div>
              <div class="item">
                <div class="label">消缺及时率</div>
                <div class="progress">
                  <div
                    class="bar"
                    :style="`width: ${item.solveTimelyRate}%`"
                  ></div>
                </div>
                <div class="score">{{ item.solveTimelyRate }}%</div>
              </div>
              <div class="item">
                <div class="label">巡检完成率</div>
                <div class="progress">
                  <div
                    class="bar"
                    :style="`width: ${item.inspectionCompletedRate}%`"
                  ></div>
                </div>
                <div class="score">{{ item.inspectionCompletedRate }}%</div>
              </div>
              <div class="item">
                <div class="label">两票合格率</div>
                <div class="progress">
                  <div
                    class="bar"
                    :style="`width: ${item.twoTicketQualifiedRate}%`"
                  ></div>
                </div>
                <div class="score">{{ item.twoTicketQualifiedRate }}%</div>
              </div>
            </div>
          </div>
          <div class="body-item">
            <div class="body-title">
              <span>安全指标完成情况</span
              ><span
                class="score"
                :class="{ 'score-warn': item.safeIdxScore < 0 }"
                >{{ item.safeIdxScore }}分</span
              >
            </div>
            <div class="chart-item progress-item special-item">
              <div class="special-box">
                <div class="box-item">
                  <div class="label">一般事故</div>
                  <div class="number">
                    <span>{{ item.ordinaryAccident }}</span
                    >个
                  </div>
                </div>
                <div class="box-item">
                  <div class="label">重大事故</div>
                  <div class="number">
                    <span>{{ item.majorAccidents }}</span
                    >个
                  </div>
                </div>
                <div class="box-item">
                  <div class="label">严重事故</div>
                  <div class="number">
                    <span>{{ item.seriousAccident }}</span
                    >个
                  </div>
                </div>
              </div>
              <div class="item">
                <div class="label">安全培训完成率</div>
                <div class="progress">
                  <div
                    class="bar"
                    :style="`width: ${item.safetyTrainCompletedRate}%`"
                  ></div>
                </div>
                <div class="score">{{ item.safetyTrainCompletedRate }}%</div>
              </div>
              <div class="item">
                <div class="label">安全考试合格率</div>
                <div class="progress">
                  <div
                    class="bar"
                    :style="`width: ${item.safetyTestCompletedRate}%`"
                  ></div>
                </div>
                <div class="score">{{ item.safetyTestCompletedRate }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 无数据 -->
      <div
        v-if="data && data.length === 0 && finished"
        class="el-table__empty-block"
      >
        <span class="el-table__empty-text">暂无数据</span>
      </div>
    </div>
    <el-pagination
      v-if="total"
      ref="paginationRef"
      m-t-16px
      justify-end
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      v-bind="$attrs"
      :total="total"
      :pager-count="5"
      :page-count="pageCount"
      :prev-text="
        !($attrs.layout as string).includes('pager') ? '上一页' : undefined
      "
      :next-text="
        !($attrs.layout as string).includes('pager') ? '下一页' : undefined
      "
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script lang="ts" setup>
import { debounce } from '@/utils'
import useSearchFormStore from '@/store/searchForm'
import charts from '@/components/charts.vue'

const searchFormStore = useSearchFormStore()

const chartsRef = ref<any>()
const paginationRef = ref<any>()
const defaultHeight = ref<number | string>('auto')

type PropsType = {
  data: Record<string, any>[]
  columns?: any[]
  total?: number
  pageSize?: number
  currentPage?: number
  loading?: boolean
  finished?: boolean
  pageCount?: number
  height?: number | string
  titleKey?: string // 标题key
}
const props = withDefaults(defineProps<PropsType>(), {
  data: () => [],
  columns: () => [],
  total: 0,
  pageSize: 0,
  currentPage: 1,
  loading: false,
  finished: false,
  pageCount: undefined,
  height: undefined,
  titleKey: ''
})

const total = ref(props?.total || 0)
let data: any = ref(props.data)
const currentPage = ref(props.currentPage || 1)
const pageSize = ref(props.pageSize || 10)

const resizeStatus = ref(Date.now())
const changeResizeStatus = () => {
  resizeStatus.value = Date.now()
}
const debounceFn = debounce(changeResizeStatus)
onMounted(() => {
  window.addEventListener('resize', debounceFn)
})
onUnmounted(() => {
  window.removeEventListener('resize', debounceFn)
})

watch(
  () => props?.pageSize,
  (val: number | undefined) => {
    if (val) pageSize.value = val
  }
)
watch(
  () => props?.currentPage,
  (val: number | undefined) => {
    if (val) currentPage.value = val
  }
)

watch(
  () => props.data,
  () => {
    data.value = props.data
    total.value = props.total || 0
  },
  {
    deep: true,
    immediate: true
  }
)

const computedHeight = async () => {
  if (!props.height) {
    await nextTick()
    const chartsEl = chartsRef.value
    const parentEl = chartsEl && chartsEl.offsetParent

    if (!parentEl) return
    const parentHeight = parentEl.offsetHeight
    const tableTop = chartsEl.offsetTop
    const paginationHeight = props.total ? 48 : 0
    const paddingBottomHeight =
      Number(
        window.getComputedStyle(parentEl)?.paddingBottom?.replace('px', '')
      ) || 0
    defaultHeight.value =
      parentHeight - tableTop - paginationHeight - paddingBottomHeight + 'px'
  }
}
watch(
  [() => props.total, () => resizeStatus.value, () => searchFormStore.height],
  () => {
    computedHeight()
  },
  { deep: true, immediate: true }
)

const emits = defineEmits([
  'handleSizeChange',
  'handleCurrentChange',
  'update:pageSize',
  'update:currentPage'
])
// 当前页展示条数改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  emits('update:pageSize', val)
  emits('handleSizeChange', {
    currentPage: currentPage.value,
    pageSize: val
  })
}
// 当前页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  emits('update:currentPage', val)
  emits('handleCurrentChange', {
    currentPage: val,
    pageSize: pageSize.value
  })
}
</script>
<style scoped lang="scss">
$card-padding: 20px 16px;

.charts-pagination {
  overflow: auto;
}

.box {
  // margin-bottom: 24px;
  padding: 24px;
  margin: 24px 0;
  margin-top: 0;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.1);
  .box-header {
    padding-bottom: 13px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 18px;
    color: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: space-between;

    .header-item {
      display: flex;
      align-items: center;
    }

    .left-item {
      margin-right: 24px;
    }

    .right-item {
      color: #29cca0;
    }
  }
  .box-body {
    margin-top: 12px;
    display: grid;
    grid: auto / repeat(3, 1fr);
    column-gap: 40px;

    .body-title {
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 25.864px;

      .score {
        color: #e6872e;
        font-weight: bold;

        &.score-warn {
          color: #e62e32;
        }
      }
    }

    .chart-item {
      width: 100%;
      height: 178px;

      &.chart-container {
        display: flex;
        justify-content: center;
        align-items: center;

        .no-data {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.65);
        }
      }

      &.progress-item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &.special-item {
          .label {
            width: 98px;
            text-align: right;
          }

          .special-box {
            display: grid;
            grid: auto / repeat(3, 1fr);
            column-gap: 16px;

            .box-item {
              background: #f6f8fa;
              // box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.1);
              border-radius: 4px;
              padding: 12px 16px 8px;

              .label {
                margin-bottom: 6px;
                text-align: left;
                width: auto;
                color: rgba(0, 0, 0, 0.65);
                line-height: 22px;
              }
              .number {
                color: rgba(0, 0, 0, 0.45);
                line-height: 22px;
                span {
                  font-family: 'Helvetica Neue';
                  margin-right: 8px;
                  font-size: 24px;
                  color: rgba(0, 0, 0, 0.85);
                }
              }
            }
          }
        }

        .item {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);

          .label {
            margin-right: 12px;
          }
          .progress {
            margin-right: 18px;
            background: #ebf0f5;
            height: 8px;
            flex: 1;
            position: relative;
            .bar {
              position: absolute;
              top: 0;
              left: 0;
              width: 50%;
              height: 100%;
              background-color: #2396ff;
            }
          }
        }
      }
    }
  }
}

.icon-question {
  color: rgba(0, 0, 0, 0.25);
}

.el-table__empty-block {
  height: calc(100vh - 300px - 32px);
}
</style>
