<template>
  <div class="ocinfo">
    <div class="page-title">
      {{ pageName }}
    </div>
    <div class="main">
      <!-- 基础信息 -->
      <basic-info
        ref="basicInfoRef"
        :info-detaile="infoDetaile"
        @get-from-data="getFromData"
        @change-select="changeSelect"
      ></basic-info>
      <!-- 需要自动创建数据 -->
      <autoData-info
        ref="autoDataInfoRef"
        :info-detaile="infoDetaile"
        @get-from-data="setPicData"
      ></autoData-info>
    </div>
    <div
      v-if="pageName == '新建计划' || pageName == '编辑计划'"
      class="page-footer"
    >
      <el-button plain @click="onCancel">返回</el-button>
      <el-button type="primary" @click="onSumbit">
        {{ pageName == '新建计划' ? '提交' : '保存' }}
      </el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { planManagement as planManagementAPI } from '@/api/index.ts'
import basicInfo from './components/basicInfo.vue'
import autoDataInfo from './components/autoDataInfo.vue'

const basicInfoRef = ref<any>(null)
const autoDataInfoRef = ref<any>(null)
const router = useRouter()
const route = useRoute()
const pageName = computed(() => {
  if (route.params && route.params.pageStatus == 'add') {
    return '新建计划'
  }
  if (route.params && route.params.pageStatus == 'edit') {
    return '编辑计划'
  }
  if (route.params && route.params.pageStatus == 'look') {
    return '查看计划'
  }
})
const basicInfoData = ref({}) // 基础信息
const autoDataInfoData = ref({}) // 自动创建数据
const getFromData = (val: object) => {
  if (val.valid) {
    let arr = [],
      cycleRules = ''
    val.data.cycleRulesArr.forEach((item: any) => {
      if (
        val.data.cycleRulesArr[0][0] == 2 ||
        val.data.cycleRulesArr[0][0] == 3
      ) {
        arr.push(item[1].split('_')[1])
      }
      if (val.data.cycleRulesArr[0][0] == 4) {
        arr.push(item[2])
      }
    })
    if (val.data.cycleRulesArr[0][0] == 1) {
      // 每日
      cycleRules = ''
    } else {
      cycleRules = arr.join(',')
    }
    basicInfoData.value = {
      ...val.data,
      cycleRulesType: val.data.cycleRulesArr[0][0], // 周期规则类型:1：每日，2：每周，3：每月，4：每年
      cycleRules
    }
  } else {
    basicInfoData.value = {}
  }
}
const setPicData = (val: object) => {
  if (val.checkboxIs_) {
    if (val.valid) {
      // 1: 创建业务数据，0: 不创建业务数据
      autoDataInfoData.value = {
        ...val.data,
        createData: 1
      }
    } else {
      autoDataInfoData.value = {}
    }
  } else {
    autoDataInfoData.value = {
      createData: '0'
    }
  }
}
const infoDetaile = ref({})
const onSumbit = async () => {
  // 触发基础信息
  await basicInfoRef.value.submitForm()
  // 触发自动创建数据
  await autoDataInfoRef.value.submitForm()
  nextTick(async () => {
    if (
      JSON.stringify(basicInfoData.value) == '{}' ||
      JSON.stringify(autoDataInfoData.value) == '{}'
    ) {
      return false
    }
    // ==== 需要自动创建数据处理 ====
    // 1 巡检计划-自动创建数据
    let autoDataInfoDataObj = {
      createData: autoDataInfoData.value.createData
    }
    if (
      basicInfoData.value.type == 1 &&
      autoDataInfoData.value.createData == 1
    ) {
      autoDataInfoDataObj = {
        createData: autoDataInfoData.value.createData,
        stationRangeType: autoDataInfoData.value.stationRangeType, // 电站范围
        stationRange:
          autoDataInfoData.value.stationRangeType == 1
            ? ''
            : autoDataInfoData.value.stationRangeType == 2
            ? autoDataInfoData.value.name4.join(',')
            : autoDataInfoData.value.stationRangeType == 3
            ? autoDataInfoData.value.name7.join(',')
            : '' // stationRange 编码 资产公司
      }
    }
    // 2 培训计划-自动创建数据
    if (
      basicInfoData.value.type == 2 &&
      autoDataInfoData.value.createData == 1
    ) {
      autoDataInfoDataObj = {
        createData: autoDataInfoData.value.createData,
        trainingLevel: autoDataInfoData.value.trainingLevel, // 培训级别
        trainingType: autoDataInfoData.value.trainingType // 培训类别
      }
      if (autoDataInfoDataObj.trainingLevel == 1) {
        // 全员级
        autoDataInfoDataObj.belongsProject = ''
        autoDataInfoDataObj.belongsGroup = ''
      } else if (autoDataInfoDataObj.trainingLevel == 2) {
        // 项目级
        autoDataInfoDataObj.belongsProject =
          autoDataInfoData.value.belongsProject.join(',')
        autoDataInfoDataObj.belongsGroup = ''
      } else if (autoDataInfoDataObj.trainingLevel == 3) {
        // 班组级
        autoDataInfoDataObj.belongsProject = ''
        autoDataInfoDataObj.belongsGroup =
          autoDataInfoData.value.belongsGroup.join(',')
      }
    }
    // 3 演练范围-自动创建数据
    if (
      basicInfoData.value.type == 3 &&
      autoDataInfoData.value.createData == 1
    ) {
      autoDataInfoDataObj = {
        createData: autoDataInfoData.value.createData,
        drillRangeType: autoDataInfoData.value.drillRangeType
      }
      if (autoDataInfoDataObj.drillRangeType == 1) {
        // 公司级
        autoDataInfoDataObj.drillRange = autoDataInfoData.value.YMname.join(',')
        autoDataInfoDataObj.SSname = ''
      } else if (autoDataInfoDataObj.drillRangeType == 2) {
        // 班组级
        autoDataInfoDataObj.drillRange = autoDataInfoData.value.SSname.join(',')
        autoDataInfoDataObj.YMname = ''
      }
    }
    try {
      let { data } = await planManagementAPI.saveOrUpdatePlanManage({
        ...basicInfoData.value,
        ...autoDataInfoDataObj,
        id:
          (route.params && route.params.id && JSON.parse(route.params.id)) ||
          '',
        planNo: infoDetaile.value.planNo
      })
      if (data.code == '200') {
        ElMessage({
          message: '保存成功！',
          type: 'success'
        })
        onCancel()
      } else {
        ElMessage({
          message: data.message,
          type: 'error'
        })
      }
    } catch (e) {
      ElMessage({
        message: e.message,
        type: 'error'
      })
    }
  })
}
// 取消
const onCancel = () => {
  router.push('/plan-management')
}
const changeSelect = (val: any) => {
  autoDataInfoRef.value.isPlanType(val)
}
// 获取详情接口
onMounted(() => {
  getDetailes()
})
const info1 = {
  '1': '每日',
  '2': '每周',
  '3': '每月',
  '4': '每年'
}
const info2 = {
  '1': '周一',
  '2': '周二',
  '3': '周三',
  '4': '周四',
  '5': '周五',
  '6': '周六',
  '7': '周日'
}
let str = ''
const getDetailes = async () => {
  if (
    route.params &&
    (route.params.pageStatus == 'look' || route.params.pageStatus == 'edit')
  ) {
    try {
      let { data } = await planManagementAPI.getPlanManageById({
        id: route.params && route.params.id && JSON.parse(route.params.id)
      })
      if (data.code == '200') {
        if (data.data.cycleRulesType == 1) {
          str = ''
        } else if (data.data.cycleRulesType == 2) {
          let arr = data.data.cycleRules.split(',').map((item: any) => {
            return info2[item]
          })
          str = '(' + arr.join(',') + ')'
        } else if (data.data.cycleRulesType == 3) {
          let arr = data.data.cycleRules.split(',').map((item: any) => {
            return item + '号'
          })
          str = '(' + arr.join(',') + ')'
        } else if (data.data.cycleRulesType == 4) {
          let arr = data.data.cycleRules.split(',').map((item: any) => {
            return item.split('_')[0] + '月' + item.split('_')[1] + '号'
          })
          str = '(' + arr.join(',') + ')'
        }

        data.data.cycleRules_ = data.data.cycleRulesType
          ? info1[data.data.cycleRulesType] + str
          : '--'
        infoDetaile.value = data.data
      } else {
        infoDetaile.value = {}
        ElMessage({
          message: data.message,
          type: 'error'
        })
      }
    } catch (e: any) {
      ElMessage({
        message: e.message,
        type: 'error'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.ocinfo {
  padding-bottom: 24px;
}
.main {
  width: 100%;
  height: 100%;
  padding: 0 24px;
  .tabs-box__ {
    height: calc(100% - 362px);
    background: #fff;
    padding: 24px;
    box-sizing: border-box;
    border-radius: 8px;
  }
}
.page-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
  line-height: 64px;
  margin: 24px;
  height: 64px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  & > .el-button + .el-button {
    margin-left: 16px !important;
  }
  & > .el-button {
    width: 72px !important;
    height: 40px !important;
  }
}
.page-title {
  font-size: 16px;
  margin: 24px 0 24px 24px;
}
.page-header {
  height: 60px;
  display: flex;
  align-items: center;
  background: #fff;
  box-shadow: 0 0 8px 0 rgb(21 102 80 / 10%);
  .header-title {
    padding-left: 24px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    .backIcon {
      position: relative;
      top: 2px;
    }
    .goback {
      color: #333;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;
    }
    .detailLine {
      color: #e4e4e4;
      margin: 0 6px 0 12px;
    }
    a {
      color: #999;
      margin-left: 6px;
    }
    span {
      margin-left: 6px;
    }
  }
}
</style>
<style lang="scss">
.tabs-box__ {
  padding: 24px 24px 0;
  .consultingInfo {
    .mb0 {
      margin-bottom: 0 !important;
    }
  }
  .checkList {
    padding: 9px 24px 0 !important;
  }
  .el-tbas {
    height: 100% !important;
    .el-tbas__content {
      height: calc(100% - 55px);
    }
  }
}
</style>
