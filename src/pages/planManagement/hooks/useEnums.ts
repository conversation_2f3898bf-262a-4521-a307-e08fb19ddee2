export default function () {
  const options = [
    {
      value: '1',
      label: '每日',
      level: 1,
      children: []
    },
    {
      value: '2',
      label: '每周',
      level: 1,
      children: [
        {
          value: 'B_1',
          label: '周一',
          children: []
        },
        {
          value: 'B_2',
          label: '周二',
          children: []
        },
        {
          value: 'B_3',
          label: '周三',
          children: []
        },
        {
          value: 'B_4',
          label: '周四',
          children: []
        },
        {
          value: 'B_5',
          label: '周五',
          children: []
        },
        {
          value: 'B_6',
          label: '周六',
          children: []
        },
        {
          value: 'B_7',
          label: '周日',
          children: []
        }
      ]
    },
    {
      value: '3',
      label: '每月',
      level: 1,
      children: []
    },
    {
      value: '4',
      label: '每年',
      level: 1,
      children: []
    }
  ]
  // 月
  const num1 = 31,
    arr1 = []
  for (let i = 1; i <= num1; i++) {
    arr1.push({
      value: 'C_' + i,
      label: i + '日'
    })
  }
  options[2].children = arr1
  // 年
  const num2 = 12,
    arr2 = []
  let num3 = 0
  for (let i = 1; i <= num2; i++) {
    const arr3 = []

    if (i == 1 || i == 3 || i == 5 || i == 7 || i == 8 || i == 10 || i == 12) {
      num3 = 31
    } else if (i == 2) {
      num3 = 29
    } else {
      num3 = 30
    }

    for (let j = 1; j <= num3; j++) {
      arr3.push({
        value: i + '_' + j,
        label: i + '月' + j + '日'
      })
    }
    arr2.push({
      value: i,
      label: i + '月',
      children: arr3
    })
  }
  options[3].children = arr2
  return {
    options
  }
}
