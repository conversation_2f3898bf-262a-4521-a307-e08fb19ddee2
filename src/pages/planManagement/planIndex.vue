<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      >
      </searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>计划管理列表</p>
        <el-button type="primary" @click="handleAdd"
          ><el-icon m-r-4px> <Plus /> </el-icon>新建计划</el-button
        >
      </div>
      <div class="tables">
        <vis-table-pagination
          :loading="tableLoading"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchData.pageSize"
          :current-page="searchData.pageNum"
          :columns="columns"
          :total="listTotal"
          :data="listData"
          :show-overflow-tooltip="true"
          background
          class="vis-table-pagination"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
        >
          <!-- 计划状态 -->
          <template #status="{ row }">
            <el-tag v-if="row.status === 1" class="tag tag1">生效中</el-tag>
            <el-tag v-else-if="row.status === 0" class="tag tag2"
              >已停用</el-tag
            >
            <span v-else>--</span>
          </template>
          <template #operate="{ row }">
            <div class="table-operate">
              <el-button link @click="handleDetail(row)">查看详情</el-button>
              <el-button link @click="handleEdit(row)">编辑</el-button>

              <el-popconfirm
                :title="'确认' + row.statusName + '?'"
                @confirm="handleStop(row)"
              >
                <template #reference>
                  <el-button link @click.stop>
                    {{ row.status == 1 ? '停用' : '启用' }}
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { planManagement as planManagementAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
const router = useRouter()
onMounted(async () => {
  await getTableData()
})
// 搜索
const searchData = ref({
  name: '',
  planNo: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    prop: 'name',
    label: '计划名称',
    placeholder: '请输入计划名称',
    span: 8,
    width: 70,
    maxlength: 32
  },
  {
    prop: 'planNo',
    label: '计划编码',
    placeholder: '请输入计划编码',
    span: 8,
    width: 100,
    maxlength: 32
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  await getTableData()
}
const columns = [
  {
    prop: 'planNo',
    label: '计划编号'
  },
  {
    prop: 'name',
    label: '计划名称'
  },
  {
    prop: 'cycleRules_',
    label: '周期规则'
  },
  {
    prop: 'status',
    label: '计划状态',
    slotName: 'status'
  },
  {
    prop: 'endTime',
    label: '终止时间'
  },
  {
    prop: 'createUser',
    label: '创建人'
  },
  {
    prop: 'createTime',
    label: '创建时间'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 130,
    fixed: 'right'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await planManagementAPI.getPlanManageList(
      {
        ...searchData.value
      },
      [tableLoading]
    )
    listTotal.value = data.data.total

    const info1 = {
      '1': '每日',
      '2': '每周',
      '3': '每月',
      '4': '每年'
    }
    const info2 = {
      '1': '周一',
      '2': '周二',
      '3': '周三',
      '4': '周四',
      '5': '周五',
      '6': '周六',
      '7': '周日'
    }
    let str = ''
    data.data.records &&
      data.data.records.forEach((item: any) => {
        if (item.cycleRulesType == 1) {
          str = ''
        } else if (item.cycleRulesType == 2) {
          let arr = item.cycleRules.split(',').map((item: any) => {
            return info2[item]
          })
          str = '(' + arr.join(',') + ')'
        } else if (item.cycleRulesType == 3) {
          let arr = item.cycleRules.split(',').map((item: any) => {
            return item + '号'
          })
          str = '(' + arr.join(',') + ')'
        } else if (item.cycleRulesType == 4) {
          let arr = item.cycleRules.split(',').map((item: any) => {
            return item.split('_')[0] + '月' + item.split('_')[1] + '号'
          })
          str = '(' + arr.join(',') + ')'
        }
        item.statusName = item.status == 1 ? '停用' : '启用'
        item.endTime = item.endTime ? item.endTime.split(' ')[0] : '--'
        item.createUser = item.createUser ? item.createUser.split('_')[0] : '--'
        item.cycleRules_ = item.cycleRulesType
          ? info1[item.cycleRulesType] + str
          : '--'
      })
    listData.value = data.data.records || []
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}
//  == 操作 ==
// 新建计划
const handleAdd = () => {
  router.push('/plan-management/planInfo/add')
}
// 查看详情
const handleDetail = (row: Record<string, any>) => {
  router.push(`/plan-management/planInfo/look/${row.id}`)
}
// 编辑
const handleEdit = (row: Record<string, any>) => {
  router.push(`/plan-management/planInfo/edit/${row.id}`)
}
// 停用
const handleStop = async (row: Record<string, any>) => {
  try {
    const { data } = await planManagementAPI.statusPlanManage(
      { id: row.id, status: row.status == 1 ? 0 : 1 },
      true
    )
    if (data.code === '200') {
      ElMessage({
        message: row.status == 1 ? `停用成功!` : '启用成功!',
        type: 'success'
      })
      getTableData()
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
// tag相关
.tag {
  font-size: 14px;
  font-weight: 400;
  padding: 1px 12px;
}

.tag1 {
  background: rgba(41, 204, 160, 0.1);
  color: rgba(41, 204, 160, 1);
}

.tag2 {
  background: rgba(230, 135, 46, 0.15);
  color: rgba(230, 135, 46, 1);
}
</style>
