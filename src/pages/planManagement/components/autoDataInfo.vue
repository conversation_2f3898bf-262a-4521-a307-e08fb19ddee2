<template>
  <div class="basicInfo">
    <div class="title_">
      <span>需要自动创建数据</span>
      <el-checkbox v-model="checkboxIs" :disabled="isType"></el-checkbox>
    </div>
    <el-form
      ref="FormRef"
      :inline="true"
      :model="formData"
      :rules="rules"
      label-suffix=""
      label-width="110px"
      class="elForm__"
      v-if="checkboxIs"
    >
      <!-- 计划类型 2 巡检计划 -->
      <el-row :gutter="24" v-if="planTypeName == 2">
        <el-col :span="12" class="rowTop__">
          <el-form-item label="培训级别" prop="trainingLevel">
            <span v-if="isType">
              {{
                getName(levelArr, formData.trainingLevel, 'value', 'label') ||
                '--'
              }}</span
            >
            <el-select
              v-else
              v-model="formData.trainingLevel"
              placeholder="请选择培训级别"
              clearable
              @change="changeItem(formData.trainingLevel, '', 'change')"
            >
              <template v-for="item in levelArr" :key="item.value">
                <el-option :label="item.label" :value="item.value" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="rowTop__">
          <el-form-item label="培训类别" prop="trainingType">
            <span v-if="isType">
              {{
                getName(typesArr, formData.trainingType, 'value', 'label') ||
                '--'
              }}</span
            >
            <el-select
              v-else
              v-model="formData.trainingType"
              placeholder="请选择培训类别"
              clearable
            >
              <template v-for="item in typesArr" :key="item.value">
                <el-option :label="item.label" :value="item.value" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24" v-if="planTypeName == 2">
        <el-col :span="12" class="rowTop__" v-if="formData.trainingLevel == 2">
          <el-form-item label="所属项目" prop="belongsProject">
            <div v-if="isType" class="valueWord__">
              {{
                getMultipleName(
                  name5Arr,
                  formData.belongsProject,
                  'projectUniqueId',
                  'projectName'
                ) || '--'
              }}
            </div>
            <el-select
              v-else
              v-model="formData.belongsProject"
              placeholder="请选择所属项目"
              clearable
              filterable
              multiple
              collapse-tags-tooltip
              collapse-tags
            >
              <template v-for="item in name5Arr" :key="item.projectUniqueId">
                <el-option
                  :label="item.projectName"
                  :value="item.projectUniqueId"
                />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="rowTop__" v-if="formData.trainingLevel == 3">
          <el-form-item label="所属班组" prop="belongsGroup">
            <div v-if="isType" class="valueWord__">
              {{
                getMultipleName(
                  name6Arr,
                  formData.belongsGroup,
                  'id',
                  'name'
                ) || '--'
              }}
            </div>
            <el-select
              v-model="formData.belongsGroup"
              placeholder="请选择所属班组"
              clearable
              filterable
              multiple
              collapse-tags-tooltip
              collapse-tags
              v-else
            >
              <template v-for="item in name6Arr" :key="item.id">
                <el-option :label="item.name" :value="item.id" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 计划类型 1 培训计划 -->
      <el-row :gutter="24" v-if="planTypeName == 1">
        <el-col :span="12" class="rowTop__">
          <el-form-item label="电站范围" prop="stationRangeType">
            <span v-if="isType">
              {{
                getName(typeArr, formData.stationRangeType, 'value', 'label') ||
                '--'
              }}</span
            >
            <el-select
              v-else
              v-model="formData.stationRangeType"
              placeholder="请选择电站范围"
              clearable
            >
              <el-option
                v-for="item in typeArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col
          v-if="formData.stationRangeType == 2"
          :span="12"
          class="rowTop__"
        >
          <el-form-item label="资产公司" prop="name4">
            <div v-if="isType" class="valueWord__">
              {{
                getMultipleName(
                  companyList1,
                  formData.name4,
                  'companyCode',
                  'companyName'
                ) || '--'
              }}
            </div>
            <el-select
              v-else
              v-model="formData.name4"
              placeholder="请选择资产公司"
              clearable
              multiple
              collapse-tags-tooltip
              collapse-tags
              filterable
            >
              <el-option
                v-for="item in companyList1"
                :key="item.companyCode"
                :label="item.companyName"
                :value="item.companyCode"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col
          :span="12"
          class="rowTop__"
          v-if="formData.stationRangeType == 3"
        >
          <el-form-item label="运维商" prop="name7">
            <div v-if="isType" class="valueWord__">
              {{
                getMultipleName(
                  companyList2,
                  formData.name7,
                  'companyCode',
                  'companyName'
                ) || '--'
              }}
            </div>
            <el-select
              v-else
              v-model="formData.name7"
              placeholder="请选择运维商"
              clearable
              multiple
              collapse-tags-tooltip
              collapse-tags
              filterable
            >
              <el-option
                v-for="item in companyList2"
                :key="item.companyCode"
                :label="item.companyName"
                :value="item.companyCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 计划类型 3 应急演练计划 -->
      <el-row :gutter="24" v-if="planTypeName == 3">
        <el-col :span="12" class="rowTop__">
          <el-form-item label="演练范围" prop="drillRangeType">
            <span v-if="isType">
              {{
                getName(
                  drillRangeTypeArr,
                  formData.drillRangeType,
                  'value',
                  'label'
                ) || '--'
              }}</span
            >
            <el-select
              v-else
              v-model="formData.drillRangeType"
              placeholder="请选择演练范围"
              clearable
              @change="changeYLItem(formData.drillRangeType, '', 'change')"
            >
              <template v-for="item in drillRangeTypeArr" :key="item.value">
                <el-option :label="item.label" :value="item.value" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col
          :span="12"
          class="rowTop__"
          v-if="formData.drillRangeType == '1'"
        >
          <el-form-item label="项目公司" prop="YMname">
            <div v-if="isType" class="valueWord__">
              {{
                getMultipleName(
                  companyList1,
                  formData.YMname,
                  'companyCodeName',
                  'companyName'
                ) || '--'
              }}
            </div>
            <el-select
              v-else
              v-model="formData.YMname"
              placeholder="请选择项目公司"
              clearable
              filterable
              multiple
              collapse-tags-tooltip
              collapse-tags
            >
              <el-option
                v-for="item in companyList1"
                :key="item.companyCodeName"
                :label="item.companyName"
                :value="item.companyCodeName"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col
          v-if="formData.drillRangeType == '2'"
          :span="12"
          class="rowTop__"
        >
          <el-form-item label="所属班组" prop="SSname">
            <div v-if="isType" class="valueWord__">
              {{
                getMultipleName(name6Arr, formData.SSname, 'idName', 'name') ||
                '--'
              }}
            </div>
            <el-select
              v-else
              v-model="formData.SSname"
              placeholder="请选择所属班组"
              clearable
              filterable
              multiple
              collapse-tags-tooltip
              collapse-tags
            >
              <el-option
                v-for="item in name6Arr"
                :key="item.idName"
                :value="item.idName"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { educate as educateAPI, post } from '@/api/index.ts'
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'
import useEnums from '../../educate/train/hooks/useEnums'
const { typesArr, levelArr } = useEnums()
const planTypeName = ref(1)
// 查找name
const getName = (arr: any, val: string, key: string, label: string) => {
  let str = ''
  arr.length &&
    arr.forEach((item: any) => {
      if (item[key] == val) {
        str = item[label]
      }
    })
  return str
}
const getMultipleName = (arr: any, val: any, key: string, label: string) => {
  let str = ''
  val.length &&
    val.forEach((item: any) => {
      arr.length &&
        arr.forEach((item_: any) => {
          if (item_[key] == item) {
            str += item_[label] + ','
          }
        })
    })
  return str.slice(0, str.length - 1)
}
const typeArr = ref([
  {
    label: '全部',
    value: '1'
  },
  {
    label: '资产公司',
    value: '2'
  },
  {
    label: '运维商',
    value: '3'
  }
])
const drillRangeTypeArr = ref([
  {
    label: '公司级',
    value: '1'
  },
  {
    label: '班组级',
    value: '2'
  }
])
// 判断页面状态
const isType = computed(() => {
  if (route.params && route.params.pageStatus == 'look') {
    return true
  }
})
const name5Arr = ref<any[]>([]) // 所属项目
const name6Arr = ref<any[]>([]) // 所属班组

const checkboxIs = ref(false)
const route = useRoute()
const props = defineProps({
  infoDetaile: {
    type: Object,
    default: () => {
      return {
        trainingLevel: '',
        stationRangeType: '',
        trainingType: '',
        name4: [],
        belongsProject: [],
        belongsGroup: [],
        name7: []
      }
    }
  }
})
const FormRef = ref<FormInstance>()
const formData = ref<any>({
  trainingLevel: '',
  stationRangeType: '',
  trainingType: '',
  name4: [],
  name7: [],
  belongsProject: [],
  belongsGroup: [],
  drillRangeType: '', // 演练范围
  YMname: [], // 项目公司
  SSname: [] // 所属班组
})
const rules = reactive<any>({
  trainingLevel: [
    { required: true, message: '请选择培训级别', trigger: 'change' }
  ],
  stationRangeType: [
    { required: true, message: '请选择电站范围', trigger: 'change' }
  ],
  trainingType: [
    { required: true, message: '请选择培训类型', trigger: 'change' }
  ],
  belongsProject: [
    { required: true, message: '请选择所属项目', trigger: 'change' }
  ],
  belongsGroup: [
    { required: true, message: '请选择所属班组', trigger: 'change' }
  ],
  name4: [{ required: true, message: '请选择资产公司', trigger: 'change' }],
  name7: [{ required: true, message: '请选择运维商', trigger: 'change' }],
  // 演练范围
  drillRangeType: [
    { required: true, message: '请选择演练范围', trigger: 'change' }
  ],
  // 项目公司
  YMname: [{ required: true, message: '请选择项目公司', trigger: 'change' }],
  // 所属班组
  SSname: [{ required: true, message: '请选择所属班组', trigger: 'change' }]
})

// 提交
const emit = defineEmits(['getFromData'])
const submitForm = async () => {
  if (checkboxIs.value) {
    await FormRef.value?.validate(async (valid) => {
      emit('getFromData', {
        valid,
        checkboxIs_: true,
        data: { ...formData.value }
      })
    })
  } else {
    emit('getFromData', {
      valid: false,
      checkboxIs_: false,
      data: {}
    })
  }
}
// 重置
const resetForm = () => {
  if (!FormRef.value) return
  FormRef.value.resetFields()
}
watch(
  () => props.infoDetaile,
  async (val) => {
    // await getCompany()
    // await getPropertyCompanyList()
    await setformData(val)
  },
  {
    deep: true
  }
)
const changeItem = async (val: string, _str: string, type: string) => {
  if (val == '2') {
    try {
      // 项目级
      const { data } = await post(
        '/operate/operation-project/queryAllAssetProject',
        []
      )
      name5Arr.value = data || []
    } catch (e) {
      name5Arr.value = []
    }
  }
  if (val == '3') {
    try {
      let { data } = await educateAPI.getOperationTeamsGroup_({}) // 班组级
      name6Arr.value =
        data.data?.map((e: any) => ({ ...e, name: e.groupname })) || []
    } catch (e) {
      name6Arr.value = []
      console.log(e)
    }
  }
  if (type == 'change') {
    formData.value.belongsProject = []
    formData.value.belongsGroup = []
  }
}

const changeYLItem = async (val: string, str: string, type: string) => {
  if (val == '1') {
    try {
      try {
        const { data } = await post(
          '/operate/operation-project/queryAllAssetCompanyList',
          []
        )
        data.length &&
          data.forEach((item: any) => {
            item.companyCodeName = item.companyCode + '_' + item.companyName
          })
        companyList1.value = data || []
      } catch (e: any) {
        companyList1.value = []
      }
    } catch (e) {
      companyList1.value = []
    }
  }
  if (val == '2') {
    try {
      let { data } = await educateAPI.getOperationTeamsGroup_({}) // 班组级
      data.data.length &&
        data.data.forEach((item: any) => {
          item.idName = item.id + '_' + item.groupname
        })
      name6Arr.value =
        data.data?.map((e: any) => ({ ...e, name: e.groupname })) || []
    } catch (e) {
      name6Arr.value = []
      console.log(e)
    }
  }
  if (type == 'change') {
    formData.value.YMname = []
    formData.value.SSname = []
  }
}
const setformData = async (val: any) => {
  if (val) {
    checkboxIs.value = val.createData == 0 ? false : true
    // 巡检计划
    if (val.stationRangeType) {
      formData.value.stationRangeType = val.stationRangeType
        ? String(val.stationRangeType)
        : ''
      if (val.stationRangeType == 2) {
        formData.value.name4 = val.stationRange
          ? val.stationRange.split(',')
          : []
      }
      if (val.stationRangeType == 3) {
        formData.value.name7 = val.stationRange
          ? val.stationRange.split(',')
          : []
      }
    }
    // 培训计划
    if (val.trainingLevel) {
      changeItem(String(val.trainingLevel), '', '')
      formData.value.trainingLevel = Number(val.trainingLevel)
      formData.value.trainingType = Number(val.trainingType)

      if (formData.value.trainingLevel == 2) {
        formData.value.belongsProject = val.belongsProject
          ? val.belongsProject.split(',').map((item: any) => {
              return Number(item)
            })
          : []
      }

      if (formData.value.trainingLevel == 3) {
        formData.value.belongsGroup = val.belongsGroup
          ? val.belongsGroup.split(',').map((item: any) => {
              return Number(item)
            })
          : []
      }
    }
    // 应急演练计划
    if (val.drillRangeType) {
      changeYLItem(String(val.drillRangeType), '', '')
      formData.value.drillRangeType = val.drillRangeType

      if (formData.value.drillRangeType == 1) {
        formData.value.YMname = val.drillRange ? val.drillRange.split(',') : []
      }

      if (formData.value.drillRangeType == 2) {
        formData.value.SSname = val.drillRange ? val.drillRange.split(',') : []
      }
    }
  }
}

// 判断计划类型
const isPlanType = (val: any) => {
  planTypeName.value = val
}
defineExpose({
  submitForm,
  resetForm,
  isPlanType
})
// 运维商列表
onMounted(async () => {
  getCompany()
  getPropertyCompanyList()
})
const companyList1 = ref<any[]>([]) // 资产公司
const companyList2 = ref<any[]>([]) // 运维商
const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    companyList2.value = data.data
  } catch (e: any) {
    companyList2.value = []
  }
}
const getPropertyCompanyList = async () => {
  try {
    const { data } = await post(
      '/operate/operation-project/queryAllAssetCompanyList',
      []
    )
    companyList1.value = data
  } catch (e: any) {
    companyList1.value = []
  }
}
</script>
<style lang="scss" scoped>
.basicInfo {
  padding: 24px;
  box-sizing: border-box;
  border-radius: 8px;
  background: #fff;
  margin-bottom: 24px;

  .title_ {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    span {
      font-size: 16px;
      font-weight: 600;
      margin-right: 20px;
    }
  }
}
</style>
<style lang="scss">
.elForm__ {
  .el-form-item--default {
    margin: 0 !important;
  }
  .el-form-item--default.cardPhotoClass {
    margin-bottom: 20px !important;
  }
}
.rowTop__ {
  margin-bottom: 24px;
}
.valueWord__ {
  vertical-align: middle;
  word-wrap: break-word;
  position: relative;
  line-height: 40px;
  word-break: break-all;
}
</style>
