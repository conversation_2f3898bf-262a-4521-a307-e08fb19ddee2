<template>
  <div class="basicInfo">
    <el-form
      ref="FormRef"
      :inline="true"
      :model="formData"
      :rules="rules"
      label-suffix=""
      label-width="110px"
      class="elForm__"
    >
      <el-row
        v-if="route.params && route.params.pageStatus != 'add'"
        :gutter="24"
      >
        <el-col :span="12" class="rowTop__">
          <el-form-item label="计划编号" prop="planNo">
            <span v-if="isType">{{ formData.planNo || '--' }}</span>
            <el-input
              v-else
              v-model.trim="formData.planNo"
              placeholder="请输入计划编号"
              clearable
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12" class="rowTop__">
          <el-form-item label="计划名称" prop="name">
            <span v-if="isType">{{ formData.name || '--' }}</span>
            <el-input
              v-else
              v-model.trim="formData.name"
              placeholder="请输入计划名称"
              clearable
              maxlength="64"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" class="rowTop__">
          <el-form-item label="计划类型" prop="type">
            <span v-if="isType">
              {{
                getName(typeArr, formData.type, 'value', 'label') || '--'
              }}</span
            >
            <el-select
              v-else
              v-model="formData.type"
              placeholder="请选择计划类型"
              clearable
              :disabled="route.params && route.params.pageStatus == 'edit'"
              @change="changeSelect"
            >
              <el-option
                v-for="item in typeArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12" class="rowTop__">
          <el-form-item label="首次开始时间" prop="startTime">
            <span v-if="isType">{{ formData.startTime || '--' }}</span>
            <el-date-picker
              v-else
              v-model="formData.startTime"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择首次开始时间"
              style="width: 100%"
              clearable
              :disabled-date="
                (date: Date) => now.getTime() - 86400000 > date.getTime()
              "
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="rowTop__">
          <el-form-item label="周期规则" prop="cycleRulesArr">
            <div v-if="isType" class="valueWord__">
              {{ formData.cycleRules_ || '--' }}
            </div>
            <el-cascader
              v-else
              v-model="formData.cycleRulesArr"
              :popper-class="'first-no-check-cascader'"
              :options="options"
              multiple
              :props="propss"
              clearable
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="3"
              :show-all-levels="false"
            >
              <template #default="{ node, data }">
                <div @click="clickNode(node, data)">{{ data.label }}</div>
              </template>
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12" class="rowTop__">
          <el-form-item label="计划终止时间" prop="endTime">
            <span v-if="isType">{{ formData.endTime || '--' }}</span>
            <el-date-picker
              v-else
              v-model="formData.endTime"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择计划终止时间"
              style="width: 100%"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="rowTop__">
          <el-form-item label="负责人" prop="headUser">
            <span v-if="isType">{{
              formData.headUser.split('_')[0] || '--'
            }}</span>
            <el-select
              v-else
              v-model="formData.headUser"
              placeholder="请选择负责人"
              clearable
              filterable
            >
              <el-option
                v-for="item in dataList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24" class="rowTop__">
          <el-form-item
            label="计划详情内容"
            prop="detail"
            class="trainingContente__ valueWord__"
          >
            <div v-if="isType" class="valueWord__">
              {{ formData.detail || '--' }}
            </div>
            <el-input
              v-else
              v-model="formData.detail"
              :autosize="{ minRows: 5, maxRows: 7 }"
              type="textarea"
              placeholder="请输入计划详情内容"
              maxlength="1024"
              :show-word-limit="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import useEnums from '../hooks/useEnums.ts'
import { educate as educateAPI } from '@/api/index.ts'
const { options } = useEnums()
import type { FormInstance } from 'element-plus'

const typeArr = ref([
  {
    label: '巡检计划',
    value: '1'
  },
  {
    label: '培训计划',
    value: '2'
  },
  {
    label: '应急演练计划',
    value: '3'
  }
])
// 查找name
const getName = (arr: any, val: string, key: string, label: string) => {
  let str = ''
  arr.length &&
    arr.forEach((item: any) => {
      if (item[key] == val) {
        str = item[label]
      }
    })
  return str
}
// 判断页面状态
const clickNode = (_node: any, data: any) => {
  if (data.level == 1) {
    if (data.label == '每日') {
      formData.value.cycleRulesArr = [[data.value]]
      return false
    }
    formData.value.cycleRulesArr = []
  }
}
const propss = {
  multiple: true
}

const route = useRoute()
const now = new Date()
const isType = computed(() => {
  if (route.params && route.params.pageStatus == 'look') {
    return true
  }
})
const props = defineProps({
  infoDetaile: {
    type: Object,
    default: () => {
      return {
        planNo: '创建后自动生成',
        name: '',
        type: '',
        startTime: '',
        cycleRulesArr: [],
        endTime: '',
        headUser: '',
        detail: '',
        cycleRules_: '',
        cycleRulesType: ''
      }
    }
  }
})
const FormRef = ref<FormInstance>()
const formData = ref<any>({
  planNo: '创建后自动生成',
  name: '',
  type: '1',
  startTime: '',
  cycleRulesArr: [],
  endTime: '',
  headUser: '',
  detail: '',
  cycleRules_: '',
  cycleRulesType: ''
})
const formRulesheadUser = (_rule: any, value: any, callback: any) => {
  if (value.length == 0) {
    callback(new Error('请选择周期规则'))
  } else {
    if (value[0][0] == 4 && value.length > 20) {
      callback(new Error('最多可选择20个日期'))
    } else {
      callback()
    }
  }
}
// 计划终止时间
const formRulesheadEndTime = (_rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请选择计划终止时间'))
  } else {
    if (formData.value.startTime) {
      if (value >= formData.value.startTime) {
        callback()
      } else {
        callback(new Error('计划终止时间不能小于首次开始时间'))
      }
    } else {
      callback()
    }
  }
}
const rules = reactive<any>({
  planNo: [{ required: true, message: '请输入计划编号', trigger: 'blur' }],
  name: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择计划类型', trigger: 'change' }],
  startTime: [
    { required: true, message: '请选择首次开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, trigger: 'change', validator: formRulesheadEndTime }
  ],
  headUser: [{ required: false, message: '请选择负责人', trigger: 'change' }],
  detail: [{ required: true, message: '请输入计划详情内容', trigger: 'blur' }],
  cycleRulesArr: [
    { required: true, validator: formRulesheadUser, trigger: 'change' }
  ]
})

// 提交
const is2DArray = (arr: any) => {
  return (
    Array.isArray(arr[0]) &&
    arr.every((subArray: any) => Array.isArray(subArray))
  )
}
const emit = defineEmits(['getFromData', 'changeSelect'])
const submitForm = async () => {
  await FormRef.value?.validate(async (valid) => {
    // 周期规则-数据特殊处理转化为二维数组
    if (
      route.params &&
      route.params.pageStatus == 'edit' &&
      !is2DArray(formData.value.cycleRulesArr)
    ) {
      let arr =
        formData.value.cycleRulesArr &&
        formData.value.cycleRulesArr.map((item: any) => {
          if (formData.value.cycleRulesType == 1) {
            return ['1']
          } else if (
            formData.value.cycleRulesType == 2 ||
            formData.value.cycleRulesType == 3
          ) {
            return [formData.value.cycleRulesType, item]
          } else {
            return [formData.value.cycleRulesType, item.split('_')[0], item]
          }
        })
      formData.value.cycleRulesArr = arr
    }
    emit('getFromData', {
      valid,
      data: {
        ...formData.value,
        startTime: formData.value.startTime
          ? formData.value.startTime + ' 00:00:00'
          : '',
        endTime: formData.value.endTime
          ? formData.value.endTime + ' 00:00:00'
          : ''
      }
    })
  })
}
// 重置
const resetForm = () => {
  if (!FormRef.value) return
  FormRef.value.resetFields()
}
// 切换计划类型
const changeSelect = (val: any) => {
  emit('changeSelect', val)
}
defineExpose({
  submitForm,
  resetForm
})
watch(
  () => props.infoDetaile,
  async (val) => {
    await setformData(val)
  },
  {
    deep: true
  }
)
const setformData = async (val: any) => {
  if (val) {
    formData.value.planNo = val.planNo
    formData.value.name = val.name
    formData.value.type = String(val.type)
    changeSelect(String(val.type))

    formData.value.startTime = val.startTime
      ? val.startTime.split(' ')[0]
      : '--'
    formData.value.endTime = val.endTime ? val.endTime.split(' ')[0] : '--'
    formData.value.headUser = val.headUser
    formData.value.detail = val.detail

    if (val.cycleRulesType == 1) {
      // 每日
      formData.value.cycleRulesArr = ['1']
    } else if (val.cycleRulesType == 2) {
      // 每周
      formData.value.cycleRulesArr = val.cycleRules
        .split(',')
        .map((item: any) => {
          return 'B_' + item
        })
    } else if (val.cycleRulesType == 3) {
      // 每月
      formData.value.cycleRulesArr = val.cycleRules
        .split(',')
        .map((item: any) => {
          return 'C_' + item
        })
    } else {
      // 每年
      formData.value.cycleRulesArr = val.cycleRules.split(',')
    }

    formData.value.cycleRules_ = val.cycleRules_
    formData.value.cycleRulesType = val.cycleRulesType
  }
}

const dataList = ref<any[]>([])
const generateData = async (operatorsId: string) => {
  try {
    let { data } = await educateAPI.getOperationUserAllList({
      operatorsId: operatorsId ? operatorsId : ''
    })
    if (data.code == 200) {
      data.data.forEach((item: any) => {
        item.value = item.userName + '_' + item.id
        item.label = item.userName
      })
      dataList.value = data.data
    }
  } catch (e: any) {
    dataList.value = []
  }
}

onMounted(() => {
  generateData('')
  emit('changeSelect', formData.value.type)
})
</script>
<style lang="scss" scoped>
.basicInfo {
  padding: 24px;
  box-sizing: border-box;
  border-radius: 8px;
  background: #fff;
  margin-bottom: 24px;

  .title {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    margin-bottom: 24px;
  }
}
</style>
<style lang="scss">
.trainingContente__ {
  .el-textarea.el-input--default {
    position: relative;
    .el-input__count {
      position: absolute;
      bottom: 8px !important;
      right: 26px !important;
    }
  }
}
.valueWord__ {
  vertical-align: middle;
  word-wrap: break-word;
  position: relative;
  line-height: 40px;
  word-break: break-all;
}

.elForm__ {
  .el-form-item--default {
    margin: 0 !important;
  }
  .el-form-item--default.cardPhotoClass {
    margin-bottom: 20px !important;
  }
}

.rowTop__ {
  margin-bottom: 24px;
}

// 级联选择器，第一级不需要复选框
.first-no-check-cascader {
  .el-cascader-panel {
    .el-cascader-menu:first-child {
      .el-cascader-node {
        .el-checkbox {
          display: none !important;
        }
      }
    }
    .el-cascader-menu {
      position: relative !important;
      .el-checkbox {
        position: absolute !important;
        z-index: 99 !important;
      }
      .el-cascader-node__label {
        position: absolute !important;
        width: 86% !important;
        z-index: 9 !important;
        padding-left: 20px !important;
      }
      .el-cascader-node__postfix {
        position: absolute !important;
        z-index: 1 !important;
      }
    }
  }
}
</style>
