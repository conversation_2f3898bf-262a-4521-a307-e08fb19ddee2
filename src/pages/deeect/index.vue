<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>缺陷列表</p>
      </div>
      <div class="tables">
        <vis-table-pagination
          :loading="tableLoading"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchData.pageSize"
          :current-page="searchData.pageNum"
          :columns="columns"
          :total="listTotal"
          :data="listData"
          :show-overflow-tooltip="true"
          background
          class="vis-table-pagination"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
        >
          <template #defectFinder="{ row }">
            {{ row.defectFinder ? row.defectFinder.split('_')[0] : '--' }}
          </template>

          <template #defectDuty="{ row }">
            {{ row.defectDuty ? row.defectDuty.split('_')[0] : '--' }}
          </template>

          <template #defectVerifyUser="{ row }">
            {{
              row.defectVerifyUser ? row.defectVerifyUser.split('_')[0] : '--'
            }}
          </template>
          <template #defectState="{ row }">
            <el-tag v-if="row.defectState == 2" class="tag_ tag_2"
              >处理中</el-tag
            >
            <el-tag v-if="row.defectState == 3" class="tag_ tag_3"
              >已处理</el-tag
            >
            <el-tag v-if="row.defectState == 4" class="tag_ tag_4"
              >已取消</el-tag
            >
          </template>

          <template #operate="{ row }">
            <div class="table-operate">
              <el-button link @click="handleBtn(row)">查看详情</el-button>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import { deeect as deeectAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import useEnums from './hooks/useEnums.ts'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
let { defectTypeArr, defectLevelArr } = useEnums()
const router = useRouter()

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  companyCode.data && (await getTableData())
  startWatch = true
})

// 搜索
const searchData = ref({
  stationCode: '',
  defectType: '',
  defectLevel: '',
  defectState: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    label: '电站名称',
    prop: 'stationCode',
    type: 'stationSelect',
    width: '86px',
    placeholder: '请选择电站',
    span: 8
  },
  {
    type: 'select',
    label: '缺陷类型',
    prop: 'defectType',
    span: 7,
    width: '80px',
    options: defectTypeArr,
    filterable: true
  },
  {
    type: 'select',
    label: '缺陷级别',
    prop: 'defectLevel',
    span: 7,
    width: '80px',
    options: defectLevelArr,
    filterable: true
  },
  {
    type: 'select',
    label: '处理状态',
    prop: 'defectState',
    span: 7,
    width: '80px',
    options: [
      {
        label: '处理中',
        value: '2'
      },
      {
        label: '已处理',
        value: '3'
      },
      {
        label: '已取消',
        value: '4'
      }
    ],
    filterable: true
  }
])
const handleSearch = (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  getTableData()
}

const columns = [
  {
    prop: 'defectNo',
    label: '编号'
  },
  {
    prop: 'stationName',
    label: '所属电站'
  },
  {
    prop: 'defectLevel',
    label: '缺陷级别'
  },
  {
    prop: 'defectFinder',
    label: '缺陷发现人',
    slotName: 'defectFinder'
  },
  {
    prop: 'defectDuty',
    label: '缺陷责任人',
    slotName: 'defectDuty'
  },
  {
    prop: 'defectVerifyUser',
    label: '缺陷确认人',
    slotName: 'defectVerifyUser'
  },
  {
    prop: 'defectState',
    slotName: 'defectState',
    label: '处理状态'
  },
  {
    prop: 'createTime',
    label: '缺陷发现时间'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 130,
    fixed: 'right'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await deeectAPI.getDefectMsgList(
      { ...searchData.value, code: localStorage.getItem('PVOM_COMPANY_CODE') },
      [tableLoading]
    )
    console.log(data)
    listTotal.value = data?.data?.total || 0
    listData.value = data?.data?.records || []
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}
const handleBtn = (row: Record<string, any>) => {
  router.push('/deeect/deeectInfo/look/' + row.id)
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="css" scoped>
/* tag_相关 */
.tag_ {
  font-size: 14px;
  font-weight: 400;
  width: 66px;
  height: 22px;
  line-height: 22px;
  text-align: center;
}
.tag_1 {
  background: rgba(41, 204, 160, 0.1);
  color: rgba(41, 204, 160, 1);
}

.tag_2 {
  background: rgba(92, 66, 255, 0.1);
  color: rgba(92, 66, 255, 1);
}

.tag_3 {
  background: rgba(16, 140, 255, 0.1);
  color: rgba(16, 140, 255, 1);
}
.tag_4 {
  background: rgba(255, 0, 0, 0.1);
  color: rgba(255, 0, 0, 1);
}
</style>
