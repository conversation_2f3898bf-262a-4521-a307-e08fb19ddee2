import { deeect as deeectAPI } from '@/api/index.ts'
import { workorder as workorderAPI } from '@/api/index.ts'

export default function () {
  // 缺陷类型
  const defectTypeArr = ref<any[]>([
    {
      label: '部件损坏',
      value: '1'
    },
    {
      label: '通讯中断',
      value: '2'
    },
    {
      label: '数据错误',
      value: '3'
    },
    {
      label: '其他',
      value: '4'
    },
    {
      label: '原因不明',
      value: '5'
    }
  ])
  // 缺陷级别
  const defectLevelArr = ref<any[]>([
    {
      label: '一般缺陷',
      value: '一般缺陷'
    },
    {
      label: '重大缺陷',
      value: '一般缺陷'
    }
  ])
  // 缺陷原因
  const defectSourceArr = ref<any[]>([
    {
      label: '运行维护处理不当',
      value: '1'
    },
    {
      label: '设备质量不良',
      value: '2'
    },
    {
      label: '施工及安全不良',
      value: '3'
    },
    {
      label: '天气因素及外力',
      value: '4'
    },
    {
      label: '检修质量不良',
      value: '5'
    },
    {
      label: '原因不明',
      value: '6'
    }
  ])
  // 处理状态
  const defectStateArr = ref<any[]>([
    {
      label: '待处理',
      value: '1'
    },
    {
      label: '处理中',
      value: '2'
    },
    {
      label: '已处理',
      value: '3'
    },
    {
      label: '驳回',
      value: '4'
    }
  ])
  // 工单类型
  const workTypeArr = ref<any[]>([
    {
      label: '运维工单',
      value: 1
    },
    {
      label: '巡检工单',
      value: 2
    },
    {
      label: '清洗工单',
      value: 3
    }
  ])
  // 工单状态
  const workStateArr = ref<any[]>([
    {
      label: '待指派',
      value: 1
    },
    {
      label: '待接单',
      value: 2
    },
    {
      label: '待开始',
      value: 3
    },
    {
      label: '处理中',
      value: 4
    },
    {
      label: '待验证',
      value: 5
    },
    {
      label: '已完成',
      value: 6
    },
    {
      label: '已取消',
      value: 7
    }
  ])
  // 缺陷大类 | 缺陷小类
  const defectClassIdArr = ref<any[]>([])
  const defectSubclassIdArr = ref<any[]>([])
  const defectSubclassIdArrClone = ref<any[]>([])
  const getDefectClassToSubList = async () => {
    try {
      let { data } = await deeectAPI.getDefectClassToSubClass({})
      defectClassIdArr.value = data.data.defectClassList // 大类
      defectSubclassIdArr.value = data.data.defectSubclassList // 小类
      defectSubclassIdArrClone.value = data.data.defectSubclassList // 小类2
    } catch (e) {
      defectClassIdArr.value = []
      defectSubclassIdArr.value = []
      defectSubclassIdArrClone.value = []
    }
  }
  // 巡检计划
  const inspectionPlanIdArr = ref<any[]>([])
  const getAllPlanManageListFn = async () => {
    try {
      let { data } = await workorderAPI.getAllPlanManageList({})
      inspectionPlanIdArr.value = data.data
    } catch (e) {
      inspectionPlanIdArr.value = []
    }
  }
  onMounted(() => {
    getDefectClassToSubList()
    getAllPlanManageListFn()
  })

  return {
    defectTypeArr,
    defectLevelArr,
    defectStateArr,
    defectSourceArr,
    workTypeArr,
    workStateArr,
    defectClassIdArr,
    defectSubclassIdArr,
    defectSubclassIdArrClone,
    inspectionPlanIdArr
  }
}
