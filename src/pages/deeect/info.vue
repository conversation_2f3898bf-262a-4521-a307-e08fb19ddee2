<template>
  <div class="page-operate">
    <div class="operate-title">查看详情</div>
  </div>

  <el-form
    ref="formRef"
    :rules="formRules"
    :model="formData"
    label-suffix=""
    label-position="right"
    :inline="true"
    class="w-full"
    label-width="150px"
  >
    <div class="info-deeect-base">
      <div class="deeectInfo">
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="缺陷大类" prop="defectClassName">
              <div>
                {{ formData.defectClassName || '--' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="2">
            <el-form-item label="缺陷小类" prop="defectSubclassName">
              <div>
                {{ formData.defectSubclassName || '--' }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="10">
            <el-form-item label="缺陷级别" prop="defectLevel">
              <div>
                {{ formData.defectLevel || '--' }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="10" :offset="2">
            <el-form-item label="缺陷描述" prop="defectMsg">
              <div>
                {{ formData.defectMsg || '--' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="发现时间" prop="createTime">
              <div>
                {{ formData.createTime || '--' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="2">
            <el-form-item label="所属电站" prop="stationName">
              <div>
                {{ formData.stationName || '--' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="设备名称" prop="deviceName">
              <div>
                {{
                  (formData.deviceVo && formData.deviceVo.deviceName) || '--'
                }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="2">
            <el-form-item label="设备类型" prop="deviceTypesName">
              <div>
                {{
                  (formData.deviceVo && formData.deviceVo.deviceTypesName) ||
                  '--'
                }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="设备品牌" prop="deviceBrandName">
              <div>
                {{
                  (formData.deviceVo && formData.deviceVo.deviceBrandName) ||
                  '--'
                }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="10" :offset="2">
            <el-form-item label="设备型号" prop="deviceModeName">
              <div>
                {{
                  (formData.deviceVo && formData.deviceVo.deviceModeName) ||
                  '--'
                }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="10">
            <el-form-item label="关联工单" prop="workTitle">
              <div
                v-if="formData.workTitle"
                style="
                  display: flex;
                  color: rgba(41, 204, 160, 1);
                  cursor: pointer;
                "
                @click="toWorkTitle()"
              >
                <img :src="link" class="linkSvg" />
                {{ formData.workTitle || '--' }}
              </div>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="2">
            <el-form-item label="SN码" prop="deviceSn">
              <div>
                {{ (formData.deviceVo && formData.deviceVo.deviceSn) || '--' }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="info-deeect-base">
      <div class="deeectInfo">
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="缺陷发现人" prop="defectFinder">
              <div>
                {{
                  formData.defectFinder
                    ? formData.defectFinder.split('_')[0]
                    : '--'
                }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="10" :offset="2">
            <el-form-item label="发现人联系方式" prop="defectFinderPhone">
              <div>
                {{ formData.defectFinderPhone || '--' }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="10">
            <el-form-item label="缺陷责任人" prop="defectDuty">
              <div>
                {{
                  formData.defectDuty ? formData.defectDuty.split('_')[0] : '--'
                }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="10" :offset="2">
            <el-form-item label="责任人联系方式" prop="defectDutyPhone">
              <div>
                {{ formData.defectDutyPhone || '--' }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="10">
            <el-form-item label="缺陷确认人" prop="defectVerifyUser">
              <div>
                {{
                  formData.defectVerifyUser
                    ? formData.defectVerifyUser.split('_')[0]
                    : '--'
                }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="10" :offset="2">
            <el-form-item label="确认人联系方式" prop="defectVerifyUserPhone">
              <div>
                {{ formData.defectVerifyUserPhone || '--' }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-form>
</template>
<script setup lang="ts">
import link from '@/assets/svgs/link.svg'
import type { FormRules, FormInstance } from 'element-plus'
import { deeect as deeectAPI } from '@/api/index.ts'
// import useEnums from './hooks/useEnums.ts'

// let { defectTypeArr, defectLevelArr, defectSourceArr } = useEnums()
const router = useRouter()
const route = useRoute()
// 表单
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  stationName: '',
  createTime: '',
  defectTypeName: '',
  defectLevel: '',
  deviceVo: {
    deviceName: '',
    deviceTypesName: '',
    deviceBrandName: '',
    deviceModeName: '',
    deviceSn: ''
  },
  defectSourceName: '',
  defectMsg: '',
  workTitle: '',
  defectFinder: '',
  defectFinderPhone: '',
  defectDuty: '',
  defectDutyPhone: '',
  defectVerifyUser: '',
  defectVerifyUserPhone: '',
  defectClassName: '',
  defectSubclassName: ''
})
const formRules = reactive<FormRules<Record<string, any>>>({})
const getDefectMsgByDefectIdFn = async () => {
  try {
    let { data } = await deeectAPI.getDefectMsgByDefectId({
      defectId: route.params && route.params.id
    })
    formData.value = data.data
  } catch (e) {
    console.log(e)
  }
}
const toWorkTitle = () => {
  router.push(route.path + `/lookorder/${formData.value.workOrderId}`)
}
onMounted(() => {
  getDefectMsgByDefectIdFn()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
<style lang="scss">
.trainingContente__ {
  .el-textarea.el-input--default {
    position: relative;
    .el-input__count {
      position: absolute;
      bottom: 8px !important;
      right: 26px !important;
    }
  }
}
</style>
