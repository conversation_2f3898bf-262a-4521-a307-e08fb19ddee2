.info-deeect-base {
  margin: 16px 24px 24px !important;
  padding: 0 !important;

  .deeectInfo {
    padding-top: 24px;
    background-color: #fff;
    .operate_ {
      margin: 24px;
      margin-top: 0;
      p {
        display: flex;
        align-items: center;
        span:first-child {
          width: 4px;
          height: 16px;
          background-color: rgba(42, 203, 160, 1);
          margin-right: 8px;
        }
        span:last-child {
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: normal;
        }
      }
    }
    .info-item {
      padding-bottom: 24px;
    }
    .label {
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
.page-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
  line-height: 64px;
  margin: 24px;
  height: 64px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  & > .el-button + .el-button {
    margin-left: 16px !important;
  }
  & > .el-button {
    min-width: 72px !important;
    height: 40px !important;
  }
}
