<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import GroupUsers from './components/groupUsers.vue'
import GroupStations from './components/groupStations.vue'
import request from '@/utils/request'
import ProjectSelect from '@/components/project-select.vue'

const groupUsersRef = ref()
const groupStationsRef = ref()
const radioValue = ref(1)
const treeKey = ref(1)
const defaultExpandedKeys = ref<number[]>([1])
const changeRadioValue = async (val: any) => {
  if (val === 3) {
    treeProps.value = {
      children: 'treeChild',
      label: 'name'
    }
  } else if (val === 2) {
    treeProps.value = {
      children: 'children',
      label: 'name'
    }
  } else {
    treeProps.value = {
      children: 'treeChild',
      label: 'name'
    }
  }
  searchProps.value = searchPropsObj[val]
  searchData.value = {}
  await getTreeDataObj[val]()
  nextTick(() => {
    treeKey.value = radioValue.value
  })
}

const searchPropsObj = reactive<any>({
  1: [
    {
      prop: 'name',
      label: '查询',
      placeholder: '请输入组织名称/运维项目名称',
      span: 16,
      width: '50px'
    }
  ],
  2: [
    {
      prop: 'name',
      label: '安全区名称',
      placeholder: '请输入安全区名称',
      span: 16,
      width: '90px'
    }
  ],
  3: [
    {
      prop: 'name',
      label: '查询',
      placeholder: '请输入运维商名称/运维项目名称',
      span: 16,
      width: '50px'
    }
  ]
})
const searchProps = ref(searchPropsObj[radioValue.value])
let searchData = ref<Partial<Record<string, any>>>({
  name: ''
})
const treeRef = ref()
const first = ref(true)
const num = ref(0)
const handleSearch = async (val: any) => {
  first.value = true
  num.value = 0
  searchData.value = { ...val }
  if (!searchData.value.name) {
    treeKey.value = Date.now()
  }
  treeRef.value!.filter(searchData.value.name)
  if (num.value === 0) {
    if (searchData.value.name) {
      changeTreeNode()
    } else {
      changeTreeNode(treeData.value?.[0] || {})
    }
  }
  const node = treeRef.value.getNode(selectedValue.value)
  for (var i = 0; i < node.store._getAllNodes().length; i++) {
    node.store._getAllNodes()[i].expanded =
      node.store._getAllNodes()[i].data.id === selectedValue.value
  }
}
const filterNode = (value: any, data: any, node: any) => {
  if (!value) return true
  let parentNode = node.parent
  let labels = [node.label]
  let level = 1
  while (level < node.level) {
    labels = [...labels, parentNode.label]
    parentNode = parentNode.parent
    level++
  }
  const res = labels.some((label) => label.indexOf(value) !== -1)
  if (res) {
    num.value = num.value + 1
    if (first.value && value) {
      changeTreeNode(data)
      first.value = false
    }
    return true
  }
  return false
}

const treeData = ref<any[]>([])
const getTreeDataObj: any = {
  1: async () => {
    defaultExpandedKeys.value = [1]
    try {
      const { data } = await request({
        url: '/operate/org-project/getOrgProjectTree',
        method: 'get',
        loading: true
      })
      treeData.value = data?.data || []
      changeTreeNode(treeData.value?.[0] || {})
    } catch (e: any) {
      treeData.value = []
      changeTreeNode()
      ElMessage({
        message: e.message,
        type: 'error'
      })
    }
  },
  2: async (type?: any) => {
    defaultExpandedKeys.value = [1]
    try {
      const { data } = await request({
        url: '/operate/safeZoneController/getSafeZoneTreeVO',
        method: 'get',
        loading: true
      })
      treeData.value = data?.data || []
      changeTreeNode(treeData.value?.[0] || {}, type)
    } catch (e: any) {
      treeData.value = []
      changeTreeNode()
      ElMessage({
        message: e.message,
        type: 'error'
      })
    }
  },
  3: async () => {
    defaultExpandedKeys.value = [0]
    try {
      const { data } = await request({
        url: '/operate/operation-project/getOperationCompanyTree',
        method: 'get',
        params: {
          type: 2
        },
        loading: true
      })
      treeData.value = data?.data || []
      console.log(treeData.value)
      changeTreeNode(treeData.value?.[0] || {})
    } catch (e: any) {
      treeData.value = []
      changeTreeNode()
      ElMessage({
        message: e.message,
        type: 'error'
      })
    }
  }
}
const treeProps = ref({
  children: 'treeChild',
  label: 'name'
})

const selectedValue = ref<any>(null)
const selectedCode = ref<string>('')
const depth = ref<number | null>(null)
const isLeaf = ref<number>(0)

const changeTreeNode = (data: any = {}, type: any = false) => {
  if (type !== 'edit') {
    selectedValue.value = data.id ?? null
    selectedCode.value = data.code ?? ''
    depth.value = data.depth || null
    isLeaf.value = data.isLeaf || 0
  }
  defaultExpandedKeys.value = [selectedValue.value]
  nextTick(() => {
    treeRef.value.setCurrentKey(selectedValue.value, false)
  })
  tabValue.value === 2 ? (tabValue.value = 1) : changeTabValue(tabValue.value)
}

const tabValue = ref(1)
const changeTabValue = (val: any) => {
  tabValue.value = val
  nextTick(() => {
    getTabData()
  })
}

const reload = async () => {
  if (radioValue.value === 1) {
    try {
      const { data } = await request({
        url: '/operate/org-project/addOrgProjectTree',
        method: 'get',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        loading: true
      })
      ElMessage({
        message: data.message,
        type: data.code === '200' ? 'success' : 'error'
      })
    } catch (e: any) {
      ElMessage({
        message: e.message,
        type: 'error'
      })
    }
  }
  if (radioValue.value === 3) {
    try {
      const { data } = await request({
        url: '/operate/operatorsManage/addOperateCompanyTree',
        method: 'get',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        loading: true
      })
      ElMessage({
        message: data.message || data.data,
        type: data.code === '200' ? 'success' : 'error'
      })
    } catch (e: any) {
      ElMessage({
        message: e.message,
        type: 'error'
      })
    }
  }
  getTreeDataObj[radioValue.value]()
}

onMounted(async () => {
  getTreeDataObj[radioValue.value]()
})

const selectRef = ref()
let dialogVisible = ref(false)
const dialogOpen = async () => {
  const { data } = await request({
    url: '/operate/safeZoneController/getExistNode',
    method: 'post',
    data: {
      code:
        radioValue.value === 2 ? selectedValue.value || '' : selectedCode.value,
      type: radioValue.value === 1 ? 2 : radioValue.value === 2 ? 1 : 3
    },
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    loading: false
  })
  if (!data.data) {
    ElMessage({
      message: '当前选择节点权限不足，请在左上方重新选择下级节点。！',
      type: 'warning'
    })
    return
  }
  dialogVisible.value = true
}
const dialogClose = () => {
  dialogVisible.value = false
}
const handleSave = async () => {
  if (selectRef.value.selectedData.length === 0) {
    ElMessage({
      message: '请选择成员',
      type: 'error'
    })
    return
  }
  const arr = selectRef.value.selectedData.map((e: any) => {
    return {
      userId: e.id,
      code: radioValue.value === 2 ? selectedValue.value : selectedCode.value,
      depth: depth.value,
      type: radioValue.value === 1 ? 2 : radioValue.value === 2 ? 1 : 3
    }
  })
  try {
    const { data } = await request({
      url: '/operate/operation-project/addOperationCompanyRelation',
      method: 'post',
      data: {
        list: JSON.stringify(arr)
      },
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      loading: true
    })
    if (data.code === '200') {
      ElMessage({
        message: '添加成员成功！',
        type: 'success'
      })
      getTabData()
      dialogClose()
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

let dialogVisibleAq = ref(false)
const dialogOpenAq = async (node: any = null) => {
  const { data } = await request({
    url: '/operate/safeZoneController/getExistNode',
    method: 'post',
    data: {
      code:
        radioValue.value === 2 ? selectedValue.value || '' : selectedCode.value,
      type: radioValue.value === 1 ? 2 : radioValue.value === 2 ? 1 : 3
    },
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    loading: false
  })
  if (!data.data) {
    ElMessage({
      message: '当前选择节点权限不足，请在左上方重新选择下级节点。！',
      type: 'warning'
    })
    return
  }
  dialogStatus.value = node ? 'edit' : 'add'
  dialogVisibleAq.value = true
  formDataAq.value = {
    id: node?.data?.id || null,
    name: node?.data?.name || '',
    parentId: node?.data?.parentId || null,
    isLeaf: node?.data?.isLeaf ?? 0
  }
  getTreeDataOptions()
}
const dialogCloseAq = () => {
  formRefAq.value.resetFields()
  dialogVisibleAq.value = false
}
const handleSaveAq = () => {
  formRefAq.value.validate(async (valid: any) => {
    if (valid) {
      try {
        const { data } = await request({
          url: '/operate/safeZoneController/addUpdateSafeZone',
          method: 'post',
          data: formDataAq.value,
          loading: true
        })
        if (data.code === '200') {
          ElMessage({
            message: `${
              dialogStatus.value === 'edit' ? '修改' : '添加'
            }安全区域成功!`,
            type: 'success'
          })
          if (dialogStatus.value === 'add' && data.data) {
            selectedValue.value = data?.data?.id || selectedValue.value
            selectedCode.value = data?.data?.code || selectedCode.value
            depth.value = data?.data?.depth || depth.value
            isLeaf.value = data?.data?.isLeaf ?? isLeaf.value
          }
          dialogCloseAq()
          getTreeDataObj[radioValue.value]('edit')
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}
const handleDeleteAq = async (node: any) => {
  const { data } = await request({
    url: '/operate/safeZoneController/getExistNode',
    method: 'post',
    data: {
      code:
        radioValue.value === 2 ? selectedValue.value || '' : selectedCode.value,
      type: radioValue.value === 1 ? 2 : radioValue.value === 2 ? 1 : 3
    },
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    loading: false
  })
  if (!data.data) {
    ElMessage({
      message: '当前选择节点权限不足，请在左上方重新选择下级节点。！',
      type: 'warning'
    })
    return
  }
  try {
    const { data } = await request({
      url: '/operate/safeZoneController/deleteSafeZoneById',
      method: 'post',
      data: {
        id: node?.data?.id || null
      },
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      loading: true
    })
    if (data.code === '200') {
      ElMessage({
        message: '删除安全区域成功!',
        type: 'success'
      })
      getTreeDataObj[radioValue.value]()
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

const formRefAq = ref()
const dialogStatus = ref('add')
let formDataAq = ref<Record<string, any>>({
  id: null,
  name: '',
  parentId: null,
  isLeaf: 0
})
const formRulesAq = reactive({
  name: [{ required: true, message: '请输入全区域名称', trigger: 'blur' }],
  parentId: [{ required: true, message: '请选择上级区域', trigger: 'change' }],
  isLeaf: [{ required: true, message: '请选择是否叶子节点', trigger: 'change' }]
})
const treeDataOptions = ref<any[]>([])
const getTreeDataOptions = async () => {
  try {
    const { data } = await request({
      url: 'operate/safeZoneController/getAddSafeZoneTreeVO',
      method: 'get',
      loading: true
    })
    const obj = data?.data || []
    const filterTreeData = (obj: any) => {
      obj.forEach((item: any) => {
        if (item.children && item.children.length > 0 && item.depth < 4) {
          filterTreeData(item.children)
        } else {
          item.children = []
        }
      })
    }
    filterTreeData(obj)
    treeDataOptions.value = obj
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
    treeDataOptions.value = []
  }
}
const getTabData = async () => {
  const { data } = await request({
    url: '/operate/safeZoneController/getExistNode',
    method: 'post',
    data: {
      code:
        radioValue.value === 2 ? selectedValue.value || '' : selectedCode.value,
      type: radioValue.value === 1 ? 2 : radioValue.value === 2 ? 1 : 3
    },
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    loading: false
  })
  if (!data.data) {
    if (groupUsersRef.value) {
      groupUsersRef.value.tableData.total = 0
      groupUsersRef.value.tableData.data = []
    }
    if (groupStationsRef.value) {
      groupStationsRef.value.tableData.total = 0
      groupStationsRef.value.tableData.data = []
    }
    return false
  }
  const obj = {
    selectedValue: selectedValue.value,
    selectedCode: selectedCode.value,
    depth: depth.value,
    radioValue: radioValue.value
  }
  groupUsersRef.value && groupUsersRef.value.getTableData(obj)
  groupStationsRef.value && groupStationsRef.value.getTableData(obj)
}

const selectRefXm = ref()
let dialogVisibleXm = ref(false)
const dialogOpenDz = async () => {
  const { data } = await request({
    url: '/operate/safeZoneController/getExistNode',
    method: 'post',
    data: {
      code:
        radioValue.value === 2 ? selectedValue.value || '' : selectedCode.value,
      type: radioValue.value === 1 ? 2 : radioValue.value === 2 ? 1 : 3
    },
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    loading: false
  })
  if (!data.data) {
    ElMessage({
      message: '当前选择节点权限不足，请在左上方重新选择下级节点。！',
      type: 'warning'
    })
    return
  }
  dialogVisibleXm.value = true
}
const dialogCloseXm = () => {
  dialogVisibleXm.value = false
}
const handleSaveXm = async () => {
  if (selectRefXm.value.selectedData.length === 0) {
    ElMessage({
      message: '请选择项目！',
      type: 'error'
    })
    return
  }
  try {
    const { data } = await request({
      url: '/operate/safeZoneController/saveSafeZoneStation',
      method: 'post',
      data: {
        safeZoneId: selectedValue.value,
        operationProjectList: selectRefXm.value.selectedData.map((e: any) => {
          return {
            code: e.operationProjectCode,
            name: e.operationProjectName
          }
        })
      },
      loading: true
    })
    if (data.code === '200') {
      ElMessage({
        message: '关联电站成功',
        type: 'success'
      })
      dialogCloseXm()
      getTreeDataObj[radioValue.value]('edit')
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
</script>

<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="main-left">
        <el-radio-group v-model="radioValue" @change="changeRadioValue">
          <el-radio :label="1">按组织项目</el-radio>
          <el-radio :label="2">按安全管理区</el-radio>
          <el-radio :label="3">按运维商</el-radio>
        </el-radio-group>
        <div class="operating" mt-10px mb-5px>
          <el-button
            v-if="radioValue === 1 || radioValue === 3"
            type="primary"
            size="small"
            @click="reload"
            >手动同步</el-button
          >
          <el-button
            v-if="radioValue === 2"
            type="primary"
            size="small"
            @click="dialogOpenAq(null)"
            >新增安全区</el-button
          >
          <el-button type="primary" size="small" @click="dialogOpen"
            >关联人员</el-button
          >
          <el-button
            v-if="isLeaf === 1 && radioValue === 2"
            type="primary"
            size="small"
            @click="dialogOpenDz"
            >关联运维项目</el-button
          >
        </div>
        <el-scrollbar max-height="calc(100% - 72px)">
          <el-tree
            ref="treeRef"
            :key="treeKey"
            :data="treeData"
            :props="treeProps"
            mt-10px
            node-key="id"
            :accordion="true"
            :filter-node-method="filterNode"
            :current-node-key="selectedValue"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="changeTreeNode"
          >
            <template #default="{ node }">
              <div class="custom-tree-node">
                <div class="label">{{ node.label }}</div>
                <div
                  v-if="radioValue === 2 && node.data.id !== 1"
                  class="buttons"
                >
                  <el-button
                    v-if="!node.data.code"
                    type="primary"
                    size="small"
                    @click.stop="dialogOpenAq(node)"
                    >编辑</el-button
                  >
                  <el-popconfirm
                    title="确认删除？"
                    @confirm="handleDeleteAq(node)"
                  >
                    <template #reference>
                      <el-button type="primary" size="small" @click.stop
                        >删除</el-button
                      >
                    </template>
                  </el-popconfirm>
                </div>
              </div>
            </template>
          </el-tree>
        </el-scrollbar>
      </div>
      <div class="main-right">
        <el-tabs v-model="tabValue" type="card" @tab-change="changeTabValue">
          <el-tab-pane label="人员" :name="1">
            <GroupUsers
              v-if="tabValue === 1"
              ref="groupUsersRef"
              :radio-value="radioValue"
              :selected-value="selectedValue"
              :selected-code="selectedCode"
              :depth="depth"
            ></GroupUsers>
          </el-tab-pane>
          <el-tab-pane label="电站" :name="2">
            <GroupStations
              v-if="tabValue === 2"
              ref="groupStationsRef"
              :radio-value="radioValue"
              :selected-value="selectedValue"
              :selected-code="selectedCode"
              :depth="depth"
              :is-leaf="isLeaf"
            ></GroupStations>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible"
    title="添加成员"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="800px"
    class="vis-dialog"
  >
    <div mb-15px>
      <GroupUsers
        v-if="dialogVisible"
        ref="selectRef"
        type="select"
      ></GroupUsers>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="handleSave">确认关联</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="dialogVisibleAq"
    :title="(dialogStatus === 'edit' ? '编辑' : '新增') + '安全区域'"
    align-center
    :before-close="dialogCloseAq"
    :close-on-click-modal="false"
    width="500px"
    class="vis-dialog"
  >
    <el-form
      ref="formRefAq"
      :model="formDataAq"
      label-suffix=""
      label-width="150px"
      :rules="formRulesAq"
    >
      <el-form-item label="安全区域名称" prop="name">
        <el-input
          v-model="formDataAq.name"
          :maxlength="50"
          autocomplete="off"
        />
      </el-form-item>
      <el-form-item
        v-if="dialogStatus === 'add'"
        label="上级区域"
        prop="parentId"
      >
        <el-cascader
          v-model="formDataAq.parentId"
          placeholder="请选择上级区域"
          :options="treeDataOptions"
          clearable
          filterable
          :props="{
            value: 'id',
            label: 'name',
            children: 'children',
            expandTrigger: 'hover',
            checkStrictly: true,
            emitPath: false,
            disabled: (data: any) => {
              return data?.id === formDataAq?.id || !!data?.isLeaf
            }
          }"
        />
      </el-form-item>
      <el-form-item label="是否最后一级节点" prop="isLeaf">
        <el-switch
          v-model="formDataAq.isLeaf"
          :disabled="dialogStatus === 'edit'"
          :inactive-value="0"
          :active-value="1"
          inline-prompt
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogCloseAq">取消</el-button>
        <el-button type="primary" @click="handleSaveAq">保存</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="dialogVisibleXm"
    title="运维项目选择"
    align-center
    :before-close="dialogCloseXm"
    :close-on-click-modal="false"
    width="800px"
    class="vis-dialog"
  >
    <div v-if="dialogVisibleXm" mt--10px mb-15px>
      <ProjectSelect ref="selectRefXm"></ProjectSelect>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogCloseXm">取消</el-button>
        <el-button type="primary" @click="handleSaveXm">确认关联</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.main {
  display: flex;
  .main-left {
    width: 330px;
    flex: none;
    margin-right: 24px;
    .el-radio {
      margin-right: 15px;
    }
  }
  .main-right {
    width: calc(100% - 354px);
    flex: auto;
  }
}
.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: top;
  .label {
    flex: auto;
  }
  .buttons {
    display: none;
    flex: none;
    padding-right: 5px;
    margin-top: -1.5px;
    .el-button {
      padding: 2px 5px;
      height: 17px;
      font-size: 12px;
    }
    .el-button + .el-button {
      margin-left: 5px;
    }
  }
}
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background: #ebeff2;
  .custom-tree-node .buttons {
    display: block;
  }
}
.el-tabs :deep(.el-tabs__item) {
  padding-left: 20px !important;
  padding-right: 20px !important;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  padding-left: 20px !important;
}
</style>
