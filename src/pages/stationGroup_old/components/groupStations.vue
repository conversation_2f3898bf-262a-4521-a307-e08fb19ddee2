<template>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="searchData.pageSize"
    :current-page="searchData.pageNum"
    :columns="tableColumns"
    :total="tableData.total"
    :data="tableData.data"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    row-key="id"
    @handle-selection-change="handleSelectionChange"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import request from '@/utils/request'
// import StationSelect from '@/components/station-select.vue'

const props = withDefaults(
  defineProps<{
    radioValue?: number
    selectedValue?: number | string | null
    selectedCode?: string
    depth?: number | null
    isLeaf?: number
  }>(),
  {
    radioValue: 1,
    selectedValue: null,
    selectedCode: '',
    depth: null,
    isLeaf: 0
  }
)

const tableColumns = ref<any[]>([
  {
    prop: 'stationName',
    label: '电站名称'
  },
  {
    prop: 'stationCode',
    label: '电站编号'
  },
  {
    prop: 'area',
    label: '行政区域'
  },
  {
    prop: 'capins',
    label: '装机容量(kW)',
    minWidth: 120
  },
  {
    prop: 'createTime',
    label: '创建时间'
  }
])

const selectedData = ref<Record<string, any>[]>([])
const handleSelectionChange = (rows: Record<string, any>[]) => {
  selectedData.value = [...rows]
}

let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})

let searchData = ref({
  safeZoneId: props.selectedValue,
  pageSize: 10,
  pageNum: 1
})

let tableLoading = ref(false)
const getTableData = async ({
  useProps = false,
  selectedValue = null,
  selectedCode = '',
  depth = null
}: any = {}) => {
  if (useProps) {
    selectedValue = selectedValue || props.selectedValue
    selectedCode = selectedCode || props.selectedCode
    depth = depth || props.depth
  } else {
    searchData.value.pageSize = 10
    searchData.value.pageNum = 1
  }
  if (props.radioValue === 2) {
    try {
      const { data } = await request({
        url: '/operate/safeZoneController/getSafeZoneStation',
        method: 'post',
        loading: [tableLoading],
        data: {
          ...searchData.value,
          safeZoneId: selectedValue
        }
      })
      tableData.data = data?.data?.records || []
      tableData.total = data?.data?.total
    } catch (e: any) {
      tableData.data = []
      tableData.total = 0
    }
  } else {
    let url
    if (props.radioValue === 1) {
      url = '/operate/org-project/getOrgProjectTree/stations'
    }
    if (props.radioValue === 3) {
      url = '/operate/operation-project/getOperationCompanyTree/stations'
    }
    try {
      const { data } = await request({
        url,
        method: 'get',
        loading: [tableLoading],
        params: {
          pageSize: searchData.value.pageSize,
          pageNum: searchData.value.pageNum,
          code: selectedCode,
          depth: props.radioValue === 3 ? depth : null
        }
      })
      tableData.data = data?.data?.records || data?.data?.data || []
      tableData.total = data?.data?.total
    } catch (e: any) {
      tableData.data = []
      tableData.total = 0
    }
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData({ useProps: true })
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData({ useProps: true })
}

defineExpose({
  getTableData,
  tableData
})
</script>
