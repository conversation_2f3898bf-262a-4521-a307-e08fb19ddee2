<template>
  <searchForm
    v-if="type === 'select'"
    :search-props="searchProps"
    :search-data="searchData"
    label-width="60px"
    @submit-emits="handleSearch"
  ></searchForm>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="searchData.pageSize"
    :current-page="searchData.pageNum"
    :columns="tableColumns"
    :total="tableData.total"
    :data="tableData.data"
    :show-overflow-tooltip="true"
    background
    class="vis-table-pagination"
    row-key="username"
    @handle-selection-change="handleSelectionChange"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
    <template #operate="{ row }">
      <div class="table-operate">
        <el-popconfirm title="确认删除？" @confirm="handleDelete(row)">
          <template #reference>
            <el-button link type="danger">删除</el-button>
          </template>
        </el-popconfirm>
      </div>
    </template>
  </vis-table-pagination>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import request from '@/utils/request'

const props = withDefaults(
  defineProps<{
    type?: string
    radioValue?: number
    selectedValue?: number | string | null
    selectedCode?: string
    depth?: number | null
  }>(),
  {
    type: undefined,
    radioValue: 1,
    selectedValue: null,
    selectedCode: '',
    depth: null
  }
)

const tableColumns = ref<any[]>([])
if (props.type === 'select') {
  tableColumns.value = [
    {
      prop: 'selection',
      label: '选择',
      fixed: 'left',
      reserve: true,
      selectable: (row: { isSelect: string }) => {
        return row.isSelect !== '1'
      }
    },
    {
      prop: 'employeeName',
      label: '姓名'
    },
    {
      prop: 'username',
      label: '用户名'
    },
    {
      prop: 'employeePhone',
      label: '手机号'
    }
  ]
} else {
  tableColumns.value = [
    {
      prop: 'nickname',
      label: '姓名'
    },
    {
      prop: 'username',
      label: '用户名'
    },
    {
      prop: 'mobile',
      label: '手机号'
    },
    {
      prop: 'operate',
      slotName: 'operate',
      label: '操作',
      minWidth: '60',
      fixed: 'right'
    }
  ]
}
const selectedData = ref<Record<string, any>[]>([])
const handleSelectionChange = (rows: Record<string, any>[]) => {
  selectedData.value = [...rows]
}

let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})

const searchProps = ref([
  {
    prop: 'keyWord',
    label: '关键字',
    placeholder: '请输入姓名/用户名/手机号',
    span: 16
  }
])
let searchData = ref({
  employeeName: '',
  username: '',
  employeePhone: '',
  keyWord: '',
  pageSize: 10,
  pageNum: 1
})
const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageSize = 10
  searchData.value.pageNum = 1
  getTableData({ useProps: true })
}

let tableLoading = ref(false)
const getTableData = async ({
  useProps = false,
  selectedValue = null,
  selectedCode = '',
  radioValue = null
}: any = {}) => {
  if (useProps) {
    selectedValue = selectedValue || props.selectedValue
    selectedCode = selectedCode || props.selectedCode
    radioValue = radioValue || props.radioValue
  } else {
    searchData.value.pageSize = 10
    searchData.value.pageNum = 1
  }
  if (props.type === 'select') {
    try {
      const { data } = await request({
        url: '/operate/uc/getUserInfoList',
        method: 'get',
        loading: [tableLoading],
        params: {
          pageSize: searchData.value.pageSize,
          pageNo: searchData.value.pageNum,
          keyWord: searchData.value.keyWord
        }
      })
      tableData.data = data?.data?.list || []
      tableData.total = data?.data?.total || 0
    } catch (e: any) {
      tableData.data = []
      tableData.total = 0
    }
  } else {
    try {
      let url = '/operate/org-project/getOrgProjectTree/peoples'
      const { data } = await request({
        url,
        method: 'get',
        loading: [tableLoading],
        params: {
          pageSize: searchData.value.pageSize,
          pageNum: searchData.value.pageNum,
          code: props.radioValue === 2 ? selectedValue : selectedCode,
          type: radioValue === 1 ? 2 : radioValue === 2 ? 1 : 3
        },
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      })
      if (typeof data.data === 'string') {
        ElMessage({
          message: data.data,
          type: 'error'
        })
        tableData.data = []
        tableData.total = 0
      } else {
        tableData.data = data?.data?.data || []
        tableData.total = data?.data?.total || 0
      }
    } catch (e: any) {
      tableData.data = []
      tableData.total = 0
    }
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData({ useProps: true })
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData({ useProps: true })
}

onMounted(async () => {
  if (props.type === 'select') {
    getTableData({ useProps: true })
  }
})

const handleDelete = async (row: Record<string, any>) => {
  try {
    const { data } = await request({
      url: '/operate/operation-project/deleteOperationCompanyRelation',
      method: 'post',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      data: {
        idsList: row.id,
        type: props.radioValue === 1 ? 2 : props.radioValue === 2 ? 1 : 3,
        depth: props.depth || null,
        code: props.radioValue === 2 ? props.selectedValue : props.selectedCode
      },
      loading: true
    })
    if (data.code === '200') {
      ElMessage({
        message: '删除成功！',
        type: 'success'
      })
      searchData.value.pageSize = 10
      searchData.value.pageNum = 1
      getTableData({ useProps: true })
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
defineExpose({
  selectedData,
  getTableData,
  tableData
})
</script>
