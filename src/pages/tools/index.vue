<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>工器具列表</p>
        <el-button type="primary" @click="editDetail(row, 'add')"
          ><el-icon><Plus /></el-icon>新建工器具</el-button
        >
      </div>

      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :loading="loading"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.size"
        :columns="columns"
        :total="list.total"
        :data="list.records"
        :show-overflow-tooltip="true"
        :current-page="searchData.current"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #toolsState="{ row }">
          <el-tag :type="row.toolsState === '1' ? 'success' : 'danger'">
            {{ getStateName(row.toolsState) }}
          </el-tag>
        </template>
        <!-- 操作 -->
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click.stop="editDetail(row, 'details')"
              >查看详情</el-button
            >
            <el-button link @click.stop="editDetail(row, 'edit')"
              >编辑</el-button
            >
            <el-button link @click.stop="onCheckInShow(row)">登记</el-button>
            <el-popconfirm title="确定要删除吗？" @confirm="handleDelete(row)">
              <template #reference>
                <el-button link @click.stop>删除</el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
  <!-- 新建/编辑弹窗 -->
  <tools-add
    :is-show="isShow"
    :type-val="typeVal"
    :rows="rows"
    :tools-status="toolsStatus"
    @closeDialog="closeDialog"
    @updateList="getListFun"
  ></tools-add>

  <!-- 登记弹窗 -->
  <tools-check-in
    :is-show="isShowCheckIn"
    :rows="rows"
    :register-type-list="registerTypeList"
    @updateList="getListFun"
    @closeDialog="() => (isShowCheckIn = false)"
  ></tools-check-in>

  <!-- 详情弹窗 -->
  <tools-details
    :is-show="isShowDetails"
    :rows="rows"
    :tools-status="toolsStatus"
    :register-type-list="registerTypeList"
    @closeDialog="() => (isShowDetails = false)"
  ></tools-details>
</template>
<script setup lang="ts">
import {
  queryByPage,
  delToolsManage,
  getToolsManageStateList,
  getToolsRegisterTypeList
} from '@/api/module/tools.ts'
import visTablePagination from '@/components/table-pagination.vue'
import toolsAdd from './components/toolsAdd.vue'
import toolsCheckIn from './components/toolsCheckIn.vue'
import toolsDetails from './components/toolsDetails.vue'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
// 搜索
const searchProps = ref([
  {
    prop: 'toolsName',
    label: '工器具名称或编码',
    span: 8,
    width: '140px'
  },
  {
    prop: 'toolsState',
    label: '状态',
    width: '100px',
    type: 'select'
  }
])
const searchData = ref({
  toolsName: '', // 名称或编码
  toolsState: '', // 状态
  current: 1, // 当前页
  size: 10 // 每页条数
})
// 表格
let loading = ref(false)
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    current: 1, // 当前页
    size: 10 // 每页条数
  }
  getListFun()
}
const columns = reactive([
  {
    prop: 'stationName',
    label: '所属电站',
    minWidth: 160
  },
  {
    prop: 'toolsName',
    label: '工器具名称',
    minWidth: 160
  },
  {
    prop: 'toolsCode',
    label: '编码',
    minWidth: 160
  },
  {
    prop: 'toolsType',
    label: '型号',
    minWidth: 160
  },
  {
    prop: 'inspectionCycle',
    label: '检验周期',
    minWidth: 160
  },
  {
    prop: 'recentlyCheckTime',
    label: '最近检验时间',
    minWidth: 200
  },
  {
    prop: 'toolsState',
    label: '状态',
    slotName: 'toolsState',
    minWidth: 100
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 210,
    fixed: 'right'
  }
])
const list = reactive({
  records: [],
  total: 0
})
const handleSizeChange = (params: any) => {
  searchData.value.current = 1
  searchData.value.size = params.pageSize
  getListFun()
}
const handleCurrentChange = (params: any) => {
  searchData.value.current = params.currentPage
  getListFun()
}
const handleDelete = async (row: any) => {
  await delToolsManage({
    id: row.id
  })
    .then(({ data }) => {
      if (data.code == '200') {
        if (data.data) {
          ElMessage({
            message: '删除成功',
            type: 'success'
          })
          getListFun()
        } else {
          ElMessage({
            message: data.message,
            type: 'warning'
          })
        }
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getListFun()
  }
)
onMounted(async () => {
  getStateList()
  getRegisterTypeList()
  companyCode.data && (await getListFun())
  startWatch = true
})

const toolsStatus = ref(null)

/**
 * 获取状态名称
 */
const getStateName = (toolsState: string) => {
  return (
    (toolsState &&
      toolsStatus?.value?.find(
        (item: { value: string }) => item.value == toolsState
      )?.label) ||
    '--'
  )
}

/**
 * 获取状态列表
 */
const getStateList = () => {
  getToolsManageStateList()
    .then(({ data }) => {
      if (data.code === '200') {
        toolsStatus.value = data.data.map((item: { name: any; value: any }) => {
          return {
            label: item.name,
            value: item.value
          }
        })
        searchProps.value[1].options = toolsStatus.value
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

const registerTypeList = ref(null)
/**
 * 获取登记类型列表
 */
const getRegisterTypeList = () => {
  getToolsRegisterTypeList().then(({ data }) => {
    if (data.code === '200') {
      registerTypeList.value = data.data.map(
        (item: { name: any; value: any }) => {
          return {
            label: item.name,
            value: item.value
          }
        }
      )
    }
  })
}
/**
 * 获取列表
 */
const getListFun = async () => {
  await queryByPage(
    {
      ...searchData.value
    },
    [loading]
  )
    .then(({ data }) => {
      if (data.code == 200) {
        list.records = data.data.records || []
        list.total = data.data.total
      } else {
        list.records = []
        list.total = 0
      }
    })
    .catch((err) => {
      list.records = []
      list.total = 0
      console.log(err)
    })
}

const isShowCheckIn = ref(false) // 登记弹窗
const isShowDetails = ref(false) // 详情弹窗
// 弹窗
const isShow = ref(false)
const typeVal = ref('')
const rows = ref({})
const editDetail = (ag1: any, ag2: string) => {
  switch (ag2) {
    case 'add': // 新增
      isShow.value = true
      rows.value = {}
      typeVal.value = 'add'
      break
    case 'edit': // 编辑
      isShow.value = true
      rows.value = ag1
      typeVal.value = 'edit'
      break
    case 'details': // 详情
      rows.value = ag1
      isShowDetails.value = true
      break
    default:
      return
  }
}
/**
 * 显示登记弹窗
 */
const onCheckInShow = (row: any) => {
  rows.value = row
  isShowCheckIn.value = true
}
/**
 * 关闭弹窗
 */
const closeDialog = () => {
  isShow.value = false
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.main {
  :deep .el-table {
    margin-top: 10px;
  }
  &-btn {
    display: flex;
    justify-content: space-between;
  }
}
.page-container {
  :deep(.vis-dialog) {
    padding: 0 !important;
  }
  .el-button:focus-visible {
    outline: none;
  }
}
</style>
