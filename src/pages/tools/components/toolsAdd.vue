<!--
 * @Description: 工器具添加
 * @Author: zwcong
 * @Date: 2024-04-02 15:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-04-12 16:26:58
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="titleVal"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="500px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-suffix=""
        label-width="140px"
        :rules="formRules"
      >
        <el-form-item label="工器具名称" prop="toolsName">
          <el-input
            v-model="formData.toolsName"
            :maxlength="64"
            autocomplete="off"
            placeholder="请输入工器具名称"
          />
        </el-form-item>
        <el-form-item label="所属电站" prop="stationName">
          <StationSelect
            v-model="formData.stationCode"
            v-model:label="formData.stationName"
            @change="(data: any) => (stationInfo = data)"
          />
        </el-form-item>
        <el-form-item label="编码" prop="toolsCode">
          <el-input
            v-model="formData.toolsCode"
            :maxlength="64"
            autocomplete="off"
            placeholder="请输入编码"
          />
        </el-form-item>
        <el-form-item label="型号" prop="toolsType">
          <el-input
            v-model="formData.toolsType"
            :maxlength="64"
            autocomplete="off"
            placeholder="请输入型号"
          />
        </el-form-item>
        <el-form-item label="检验周期" prop="inspectionCycle">
          <el-input
            v-model="formData.inspectionCycle"
            :maxlength="64"
            autocomplete="off"
            placeholder="请输入检验周期"
          />
        </el-form-item>
        <el-form-item label="状态" prop="toolsState">
          <el-select v-model="formData.toolsState" placeholder="请选择状态">
            <el-option
              v-for="item in toolsStatus"
              :key="item.key"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="存放位置" prop="toolsPosition">
          <el-input
            v-model="formData.toolsPosition"
            :maxlength="1000"
            autocomplete="off"
            type="textarea"
            placeholder="请输入存放位置"
            show-word-limit
            :rows="5"
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="submitForm()"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import {
  addToolsManage,
  editToolsManage,
  queryDetailById
} from '@/api/module/tools.ts'
import type { FormInstance, FormRules } from 'element-plus'
import StationSelect from '@/components/spic-station'
import { loadingOpen, loadingClose } from '@/utils/common.ts'
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  toolsName: '', // 工器具名称
  stationCode: '', // 所属电站
  stationName: '', // 所属电站
  toolsCode: '', // 编码
  toolsType: '', // 型号
  inspectionCycle: '', //检验周期
  toolsState: '', // 状态
  toolsPosition: '' //存放位置
})
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  typeVal: {
    type: String,
    default: '',
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  },
  toolsStatus: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const stationInfo = ref({
  stationCode: '',
  stationName: ''
})

const dialogVisible = ref(false)
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      setFormData()
    }
  },
  {
    deep: true
  }
)

const titleVal = ref('标题')
watch(
  () => props.typeVal,
  (val: string) => {
    if (val === 'add') {
      titleVal.value = '新建工器具'
    } else if (val === 'edit') {
      titleVal.value = '编辑工器具'
    }
  }
)

const setFormData = () => {
  if (props.typeVal == 'edit') {
    onDetails()
  }
}
const formRules = reactive<FormRules<Record<string, any>>>({
  toolsName: [{ required: true, message: '请输入工器具名称', trigger: 'blur' }],
  toolsType: [{ required: true, message: '请输入型号', trigger: 'blur' }],
  stationName: [
    { required: true, message: '请选择所属电站', trigger: 'change' }
  ],
  inspectionCycle: [
    { required: true, message: '请输入检验周期', trigger: 'blur' }
  ],
  toolsState: [{ required: true, message: '请选择状态', trigger: 'change' }],
  toolsPosition: [
    { required: true, message: '请输入存放位置', trigger: 'blur' }
  ]
})

const btnLoading = ref(false)

// 点击确定
const emit = defineEmits(['closeDialog', 'updateList'])
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      btnLoading.value = true

      const stationData = {
        stationCode: stationInfo?.value?.stationCode,
        stationName: stationInfo?.value?.stationName
      }
      if (props.typeVal == 'add') {
        await addToolsManage(
          {
            ...formData.value,
            ...stationData
          },
          false
        )
          .then(({ data }) => {
            if (data.code == 200) {
              ElMessage({
                message: '新建成功',
                type: 'success'
              })
              dialogClose()
              emit('updateList')
            } else {
              ElMessage({
                message: data.message,
                type: 'error'
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            btnLoading.value = false
          })
      } else {
        await editToolsManage(
          {
            ...formData.value,
            ...stationData,
            id: props.rows.id
          },
          false
        )
          .then(({ data }) => {
            if (data.code == 200) {
              ElMessage({
                message: '编辑成功',
                type: 'success'
              })
              dialogClose()
              emit('updateList')
            } else {
              ElMessage({
                message: data.message,
                type: 'error'
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            btnLoading.value = false
          })
      }
    }
  })
}
// 关闭弹窗
const dialogClose = () => {
  formRef.value && formRef.value.resetFields()
  formData.value.stationCode = ''
  emit('closeDialog', false)
}

/**
 * 获取工器具详情
 */
const onDetails = () => {
  loadingOpen()
  queryDetailById({ id: props.rows.id })
    .then(({ data }) => {
      if (data.code == 200) {
        formData.value.toolsName = data?.data?.toolsName
        formData.value.stationCode = data?.data?.stationCode
        formData.value.stationName = data?.data?.stationName
        formData.value.toolsCode = data?.data?.toolsCode
        formData.value.toolsType = data?.data?.toolsType
        formData.value.inspectionCycle = data?.data?.inspectionCycle
        formData.value.toolsState = Number(data?.data?.toolsState)
        formData.value.toolsPosition = data?.data?.toolsPosition

        stationInfo.value.stationCode = data?.data?.stationCode
        stationInfo.value.stationName = data?.data?.stationName
      }
    })
    .finally(() => {
      loadingClose()
    })
}
</script>
<style lang="scss" scoped></style>
