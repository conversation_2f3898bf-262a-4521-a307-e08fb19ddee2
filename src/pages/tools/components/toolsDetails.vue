<!--
 * @Description: 工器具详情弹窗
 * @Author: zwcong
 * @Date: 2024-04-03 15:21:19
 * @LastEditors: zwcong
 * @LastEditTime: 2024-04-12 15:46:04
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="titleVal"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="1000px"
    class="vis-dialog tools-details-dialog"
  >
    <div class="mb-10px">
      <el-row class="mb-10px" :gutter="20">
        <el-col :span="8" class="ellipsis" :title="detailsData.toolsCode">
          编码：{{ detailsData.toolsCode || '--' }}
        </el-col>
        <el-col :span="8" class="ellipsis" :title="detailsData.toolsType">
          型号：{{ detailsData.toolsType }}
        </el-col>
        <el-col
          :span="8"
          class="ellipsis"
          :title="getStateName(detailsData.toolsState)"
        >
          状态：{{ getStateName(detailsData.toolsState) }}
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="ellipsis" :title="detailsData.stationName">
          厂站：{{ detailsData.stationName }}
        </el-col>
        <el-col :span="8" class="ellipsis" :title="detailsData.inspectionCycle">
          检验周期：{{ detailsData.inspectionCycle }}
        </el-col>
        <el-col :span="8" class="ellipsis" :title="detailsData.toolsPosition">
          存放位置：{{ detailsData.toolsPosition }}
        </el-col>
      </el-row>
    </div>
    <el-tabs v-model="activeTabs" @tab-change="tabChange">
      <el-tab-pane name="jh" label="借还记录"></el-tab-pane>
      <el-tab-pane name="jy" label="检验记录"></el-tab-pane>
    </el-tabs>

    <el-scrollbar style="padding: 0 20px 0 10px">
      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :loading="loading"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="tableData.size"
        :columns="columns"
        :total="list.total"
        :data="list.records"
        :show-overflow-tooltip="true"
        :current-page="tableData.current"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
      </vis-table-pagination>
    </el-scrollbar>
  </el-dialog>
</template>
<script lang="ts" setup>
import visTablePagination from '@/components/table-pagination.vue'
import { queryDetailById, queryToolsRecordsByPage } from '@/api/module/tools.ts'

let titleVal = ref('工器具详情')

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  },
  toolsStatus: {
    type: Object,
    default: () => {
      return {}
    }
  },
  registerTypeList: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const list = reactive({
  records: [],
  total: 0
})

//tabs
let activeTabs = ref('jh')

// 表格
let loading = ref(false)
const columnsJh = reactive([
  {
    prop: 'toolsRecordsTime',
    label: '时间'
  },
  {
    prop: 'toolsRecordsType',
    label: '类型',
    formatter: (val) => {
      if (!val.toolsRecordsType) return '--'
      else {
        return props.registerTypeList.find((item) => {
          return item.value === Number(val.toolsRecordsType)
        })?.label
      }
    }
  },
  {
    prop: 'person',
    label: '人员'
  },
  {
    prop: 'toolsRecordsExplain',
    label: '说明'
  }
])
const columnsJy = reactive([
  {
    prop: 'toolsRecordsTime',
    label: '检验时间'
  },
  {
    prop: 'checkUnit',
    label: '检验单位'
  },
  {
    prop: 'person',
    label: '检验人员'
  },
  {
    prop: 'toolsRecordsExplain',
    label: '检验说明'
  }
])

const tableData = ref({
  current: 1, // 当前页
  size: 10 // 每页条数
})

const columns = computed(() => {
  if (activeTabs.value === 'jh') {
    return columnsJh
  } else {
    return columnsJy
  }
})

const detailsData = ref({})
const dialogVisible = ref(false)

watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    onDetails()
    getListFun()
  },
  {
    deep: true
  }
)

/**
 * 获取状态名称
 */
const getStateName = (toolsState: number) => {
  return (
    (toolsState &&
      props.toolsStatus.find(
        (item: { value: number }) => item.value == toolsState
      )?.label) ||
    '--'
  )
}

/**
 * 获取工器具详情
 */
const onDetails = () => {
  queryDetailById({ id: props.rows.id }).then(({ data }) => {
    if (data.code == 200) {
      detailsData.value = { ...data.data }
    }
  })
}

// 点击确定
const emit = defineEmits(['closeDialog'])
// 关闭弹窗
const dialogClose = () => {
  activeTabs.value = 'jh'
  emit('closeDialog', false)
}

/**
 * 获取列表
 */
const getListFun = async () => {
  let toolsRecordsType = 1
  activeTabs.value === 'jh' ? (toolsRecordsType = 1) : (toolsRecordsType = 3)

  list.records = []
  // list.total = 0

  let res = await queryToolsRecordsByPage(
    {
      toolsRecordsType,
      toolsId: props.rows.id,
      ...tableData.value
    },
    [loading]
  )
  if (res.data.code === '200') {
    list.records = res.data.data.records
    list.total = res.data.data.total
  }
}

const handleSizeChange = (params: any) => {
  tableData.value.current = 1
  tableData.value.size = params.pageSize
  getListFun()
}
const handleCurrentChange = (params: any) => {
  tableData.value.current = params.currentPage
  getListFun()
}

const tabChange = () => {
  tableData.value = {
    current: 1, // 当前页
    size: 10 // 每页条数
  }
  getListFun()
}
</script>
<style lang="scss">
.tools-details-dialog.vis-dialog .el-dialog__body {
  padding: 15px;
}
</style>
