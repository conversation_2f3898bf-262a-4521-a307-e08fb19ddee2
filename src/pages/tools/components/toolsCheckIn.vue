<!--
 * @Description: 工器具登记
 * @Author: zwcong
 * @Date: 2024-04-02 15:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-04-12 10:42:11
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="titleVal"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="500px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-suffix=""
        label-width="140px"
        :rules="formRules"
        :validate-on-rule-change="false"
      >
        <el-form-item label="登记类型" prop="toolsRecordsType" class="mb24">
          <el-select
            v-model="formData.toolsRecordsType"
            placeholder="请选择登记类型"
            @change="onTypeChange"
          >
            <el-option
              v-for="item in registerTypeList"
              :key="item.key"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <template v-if="formData.toolsRecordsType">
          <el-form-item
            :label="getTypeName + `时间`"
            prop="toolsRecordsTime"
            class="mb24"
          >
            <el-date-picker
              v-model="formData.toolsRecordsTime"
              type="datetime"
              :placeholder="`请选择${getTypeName}时间`"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-value="new Date()"
              :disabled-date="toolsRecordsTimeDisabled"
              :disabled-hours="() => disabledHours(formData.toolsRecordsTime)"
              :disabled-minutes="
                (hour) => disabledMinutes(hour, formData.toolsRecordsTime)
              "
              :disabled-seconds="
                (hour, minute) =>
                  disabledSeconds(hour, minute, formData.toolsRecordsTime)
              "
            />
          </el-form-item>
          <el-form-item
            v-if="formData.toolsRecordsType === 3"
            label="检验单位"
            prop="checkUnit"
            class="mb24"
          >
            <el-input
              v-model="formData.checkUnit"
              autocomplete="off"
              placeholder="请输入检验单位"
              :maxlength="64"
            />
          </el-form-item>
          <el-form-item
            :label="getTypeName + `人员`"
            prop="person"
            class="mb24"
          >
            <el-input
              v-model="formData.person"
              autocomplete="off"
              :placeholder="`请输入${getTypeName}人员`"
              :maxlength="64"
            />
          </el-form-item>
          <el-form-item
            :label="getDescName + '说明'"
            prop="toolsRecordsExplain"
            class="mb24"
          >
            <el-input
              v-model="formData.toolsRecordsExplain"
              :maxlength="1000"
              autocomplete="off"
              type="textarea"
              :placeholder="`请输入${getDescName}说明`"
              :rows="5"
              show-word-limit
            />
          </el-form-item>
        </template>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="submitForm()"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { registerToolsManage } from '@/api/module/tools.ts'
import { disabledHours, disabledMinutes, disabledSeconds } from '@/utils/index'
import type { FormInstance, FormRules } from 'element-plus'
import dayjs from 'dayjs'

let titleVal = ref('登记')

const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  toolsRecordsType: '', // 登记类型
  toolsRecordsTime: '', // 借出/归还/检验时间
  person: '', // 借出/归还/检验人员
  toolsRecordsExplain: '', // 说明
  checkUnit: '' //检验单位
})
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  },
  registerTypeList: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

let dialogVisible = ref(false)
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
  },
  {
    deep: true
  }
)

/**
 * 获取登记类型名称
 */
const getTypeName = computed(() => {
  const result = props.registerTypeList.find((item) => {
    return item.value === formData.value.toolsRecordsType
  })
  return result ? result.label : ''
})

/**
 * 获取说明名称
 */
const getDescName = computed(() =>
  formData.value.toolsRecordsType === 3 ? getTypeName.value : ''
)

const formRules = computed<FormRules<Record<string, any>>>(() => {
  return {
    toolsRecordsType: [
      { required: true, message: '请选择登记类型', trigger: 'change' }
    ],
    toolsRecordsTime: [
      {
        required: true,
        message: `请选择${getTypeName.value}时间`,
        trigger: 'blur'
      }
    ],
    person: [
      {
        required: true,
        message: `请输入${getTypeName.value}人员`,
        trigger: 'blur'
      }
    ],
    toolsRecordsExplain: [
      {
        required: true,
        message: `请输入${getDescName.value}说明`,
        trigger: 'blur'
      }
    ],
    checkUnit: [{ required: true, message: '请输入检验单位', trigger: 'blur' }]
  }
})

const btnLoading = ref(false)

// 点击确定
const emit = defineEmits(['closeDialog', 'updateList'])
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      btnLoading.value = true

      registerToolsManage({
        toolsId: props.rows.id,
        ...formData.value
      })
        .then(({ data }) => {
          if (data.code == 200) {
            ElMessage({
              message: `登记成功`,
              type: 'success'
            })
            dialogClose()
            emit('updateList')
          } else {
            ElMessage({
              message: data.message,
              type: 'error'
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          btnLoading.value = false
        })
    }
  })
}
// 关闭弹窗
const dialogClose = () => {
  formRef.value && formRef.value.resetFields()
  emit('closeDialog', false)
}
/**
 * 禁用时间
 */
const toolsRecordsTimeDisabled = (date: Date) => {
  return (
    date.getTime() < Date.parse('1900-01-01 00:00:00') ||
    date.getTime() > Date.now()
  )
}

const onTypeChange = () => {
  formRef.value && formRef.value!.clearValidate()

  formData.value.toolsRecordsTime = dayjs(new Date()).format(
    'YYYY-MM-DD HH:mm:ss'
  )
  formData.value.person = ''
  formData.value.toolsRecordsExplain = ''
  formData.value.checkUnit = ''
}
</script>
<style lang="scss">
.mb24 {
  margin-bottom: 24px !important;
}
</style>
