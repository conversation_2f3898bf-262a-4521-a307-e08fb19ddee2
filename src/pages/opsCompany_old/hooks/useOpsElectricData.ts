import { opsCompany as opsCompanyAPI } from '@/api/index.ts'

export default function (companyCode: string, loading?: any) {
  const opsElectricColumns: Record<string, any>[] = [
    {
      prop: 'stationName',
      label: '电站名称'
    },
    {
      prop: 'stationUniqueId',
      label: '电站编号'
    },
    {
      prop: 'districtAddress',
      label: '行政区划',
      minWidth: '160'
    },
    {
      prop: 'capins',
      label: '装机容量(kW)',
      minWidth: '120'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      minWidth: '120'
    }
  ]
  const opsElectricTotal = ref<number>(0)
  const opsElectricData = ref<Record<string, any>[]>([])
  const opsElectricPage = reactive({
    pageNum: 1,
    pageSize: 10
  })
  onMounted(async () => {
    getOpsElectricData()
  })
  const getOpsElectricData = async () => {
    try {
      const { data } = await opsCompanyAPI.getMaintainStationtVOList({
        companyCode,
        ...opsElectricPage
      })
      opsElectricTotal.value = data?.data?.total || 0
      opsElectricData.value = [...(data?.data?.records || [])]
    } catch (e) {
      opsElectricTotal.value = 0
      opsElectricData.value = []
    }
  }
  const opsElectricSizeChange = (params: Record<string, any>) => {
    opsElectricPage.pageNum = 1
    opsElectricPage.pageSize = params.pageSize
    getOpsElectricData()
  }
  const opsElectricCurrentChange = (params: Record<string, any>) => {
    opsElectricPage.pageNum = params.currentPage
    opsElectricPage.pageSize = params.pageSize
    getOpsElectricData()
  }
  return {
    opsElectricColumns,
    opsElectricTotal,
    opsElectricData,
    getOpsElectricData,
    opsElectricPage,
    opsElectricSizeChange,
    opsElectricCurrentChange
  }
}
