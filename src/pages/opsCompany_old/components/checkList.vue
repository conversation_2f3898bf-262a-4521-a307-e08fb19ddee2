<template>
  <div class="checkList">
    <div class="title">
      <div class="left-title">核查列表</div>
      <el-button type="primary" @click="onSumbitEdit()">新建</el-button>
    </div>
    <div class="tables">
      <vis-table-pagination
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="columns"
        :total="listTotal"
        :data="listData"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <!-- 核查报告 -->
        <template #fileReport="{ row }">
          <SpicUpload
            v-model="row.fileReport"
            type="image"
            :limit="5"
            :disabled="true"
          />

          <!-- <el-image
            v-for="item in row.files"
            style="width: 50px; height: 50px; margin-left: 10px"
            :src="item"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :z-index="9999"
            fit="cover"
            @click="clickPicItem(item)"
          /> -->
        </template>

        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="handleDetail(row)">编辑</el-button>

            <el-popconfirm title="确认删除？" @confirm="handleDelete(row)">
              <template #reference>
                <el-button link @click.stop>删除</el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </vis-table-pagination>
    </div>

    <!-- <el-image-viewer
      v-if="showViewElement"
      :url-list="[fileUrl]"
      :initial-index="0"
      infinite
      :z-index="3000"
      @close="() => (showViewElement = false)"
    /> -->

    <!-- 核查记录弹窗 -->
    <dialog-form
      :isShow="isShow"
      :typeVal="typeVal"
      :rows="rows_"
      @closeDialog="
        () => {
          isShow = false
        }
      "
      @updateList="getTableData"
    ></dialog-form>
  </div>
</template>
<script setup lang="ts">
import { opsCompany as opsCompanyAPI } from '@/api/index.ts'
import dialogForm from './dialogForm.vue'
import visTablePagination from '@/components/table-pagination.vue'
import type { PageObject } from '../types'
const route = useRoute()

// 表格
const columns = [
  {
    prop: 'content',
    label: '核查内容',
    minWidth: '240'
  },
  {
    prop: 'verifyTime',
    label: '核查时间',
    minWidth: '100'
  },
  {
    prop: 'verifyCompany',
    label: '核查公司',
    minWidth: '190'
  },
  {
    prop: 'fileReport',
    label: '核查报告',
    minWidth: '170',
    slotName: 'fileReport'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: '100',
    fixed: 'right'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
onMounted(async () => {
  await getTableData()
})
const tableLoading = ref(false)
const searchData = ref({
  pageNum: 1,
  pageSize: 10
})
const handleSizeChange = async (params: PageObject) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: PageObject) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}
const getTableData = async () => {
  try {
    let { data } = await opsCompanyAPI.getVerifyRecordList(
      {
        ...searchData.value,
        companyCode: route.query && route.query.companyCode
      },
      [tableLoading]
    )
    listTotal.value = data.data.total
    data.data.records.forEach((item: any) => {
      item.fileReport = item.fileReport ? item.fileReport.split(',') : []
    })
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
// 新建 - 新建核查记录弹窗
const isShow = ref(false)
const typeVal = ref('add')
const rows_ = ref({})
const onSumbitEdit = () => {
  isShow.value = true
  typeVal.value = 'add'
  rows_.value = {}
}
const handleDetail = (row: Record<string, any>) => {
  isShow.value = true
  typeVal.value = 'edit'
  rows_.value = row
}
const handleDelete = async (row: Record<string, any>) => {
  try {
    let { data } = await opsCompanyAPI.delVerifyRecord({
      id: row.id
    })
    if (data.code == '200') {
      ElMessage({
        message: '删除成功！',
        type: 'success'
      })
      getTableData()
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
// 点击预览图片
// const showViewElement = ref(false)
// const fileUrl = ref('')
// const clickPicItem = (item: any) => {
//   showViewElement.value = true
//   fileUrl.value = item
// }
</script>
<style lang="scss" scoped>
.checkList {
  height: calc(100% - 340px);
  padding: 9px 24px 24px;
  box-sizing: border-box;
  border-radius: 8px;
  background: #fff;
  .title {
    font-size: 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-title {
      display: flex;
      align-items: center;
    }
    .waringInfo {
      display: flex;
      align-items: center;
      color: #ff9900;
      font-size: 12px;
      .el-icon {
        margin: 0 4px 0 8px;
        font-size: 16px;
      }
    }
  }
  :deep(.el-table--default) {
    height: 528px !important;
  }
}
</style>
