<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import { opsCompany as opsCompanyAPI } from '@/api/index.ts'

const route = useRoute()

let searchData = ref({
  companyCode: route.query.companyCode,
  pageNum: 1,
  pageSize: 10
})

const tableColumns = ref([
  {
    prop: 'operationProjectCode',
    label: '运维项目编码',
    minWidth: 120
  },
  {
    prop: 'operationProjectName',
    label: '运维项目名称',
    minWidth: '120'
  },
  {
    prop: 'assetSaleCompanyName',
    label: '资产公司'
  },
  {
    prop: 'operationCompanyName',
    label: '运维商'
  },
  {
    prop: 'serviceType',
    label: '服务类型',
    formatter: (row: any) => {
      return (
        { 1: '保发电量', 2: '不保发电量' }?.[row.serviceType as number] ||
        row.serviceType ||
        '--'
      )
    }
  },
  {
    prop: 'serviceStatus',
    label: '服务状态',
    formatter: (row: any) => {
      return (
        { 1: '待开始', 2: '服务中', 3: '已结束' }?.[
          row.serviceStatus as number
        ] ||
        row.serviceStatus ||
        '--'
      )
    }
  },
  {
    prop: 'totalCapins',
    label: '装机容量(kW)',
    minWidth: '120'
  },
  {
    prop: 'relatedStation',
    label: '关联电站数量',
    minWidth: '120'
  }
])

let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})

let tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await opsCompanyAPI.getMaintainProjectVOList({
      ...searchData.value
    })
    if (data.code === '200') {
      tableData.total = data?.data?.total || 0
      tableData.data = data?.data?.records || []
    }
  } catch (e) {
    tableData.total = 0
    tableData.data = []
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

onMounted(async () => {
  getTableData()
})
</script>
<template>
  <div class="project-page">
    <vis-table-pagination
      :loading="tableLoading"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchData.pageSize"
      :current-page="searchData.pageNum"
      :columns="tableColumns"
      :total="tableData.total"
      :data="tableData.data"
      :show-overflow-tooltip="true"
      background
      class="vis-table-pagination"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    >
    </vis-table-pagination>
  </div>
</template>
<style lang="scss" scoped>
.project-page {
  :deep(.el-table--default) {
    height: 528px !important;
  }
}
</style>
