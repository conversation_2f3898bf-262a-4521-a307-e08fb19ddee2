<template>
  <div class="consultingInfo" :class="isTypeHandleShow ? 'cls-detail' : ''">
    <div class="title">
      <div class="left-title">
        资质信息
        <div class="waringInfo" v-if="!isTypeHandleShow">
          <el-icon color="#FF9900">
            <WarningFilled />
          </el-icon>
          提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件
        </div>
      </div>
      <div>
        <el-button
          v-if="
            route.query && route.query.pageType == 'handle' && isTypeHandleShow
          "
          type="primary"
          @click="onSumbitEdit"
        >
          编辑
        </el-button>
        <div
          v-if="
            route.query && route.query.pageType == 'handle' && !isTypeHandleShow
          "
        >
          <el-button plain @click="cancelBtn">取消</el-button>
          <el-button type="primary" @click="submitForm">保存</el-button>
        </div>
      </div>
    </div>
    <el-form
      ref="consultingInfoFormRef"
      :inline="true"
      :model="consulSearchData"
      :rules="isTypeHandleShow ? {} : consulRules"
      label-suffix=""
      label-width="124px"
      class="consulelForm__"
    >
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="营业执照" prop="license">
            <SpicUpload
              :key="uploadKey"
              v-model="licenseArr"
              type="image"
              :handle-remove-fn="handleRemoveLicenseFn"
              :limit="5"
              :disabled="
                route.query &&
                route.query.pageType == 'handle' &&
                isTypeHandleShow
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="营业执照有效期" prop="licenseTimeRange">
            <span v-if="isTypeHandleShow">
              {{
                consulSearchData.licenseStartDate &&
                consulSearchData.licenseEndDate
                  ? `${consulSearchData.licenseStartDate.slice(
                      0,
                      10
                    )}至${consulSearchData.licenseEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="licenseTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="企业资质" prop="enterpriseQualification">
            <SpicUpload
              :key="uploadKey"
              v-model="enterpriseQualificationArr"
              type="image"
              :handle-remove-fn="handleRemoveEnterpriseQualificationFn"
              :limit="5"
              :disabled="
                route.query &&
                route.query.pageType == 'handle' &&
                isTypeHandleShow
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item
            label="企业资质有效期"
            prop="enterpriseQualificationTimeRange"
          >
            <span v-if="isTypeHandleShow">
              {{
                consulSearchData.enterpriseQualificationStartDate &&
                consulSearchData.enterpriseQualificationEndDate
                  ? `${consulSearchData.enterpriseQualificationStartDate.slice(
                      0,
                      10
                    )}至${consulSearchData.enterpriseQualificationEndDate.slice(
                      0,
                      10
                    )}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="enterpriseQualificationTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="安全许可证" prop="securityLicense">
            <SpicUpload
              :key="uploadKey"
              v-model="securityLicenseArr"
              type="image"
              :handle-remove-fn="handleRemoveSecurityLicenseFn"
              :limit="5"
              :disabled="
                route.query &&
                route.query.pageType == 'handle' &&
                isTypeHandleShow
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item
            label="安全许可证有效期"
            prop="securityLicenseTimeRange"
          >
            <span v-if="isTypeHandleShow">
              {{
                consulSearchData.securityLicenseStartDate &&
                consulSearchData.securityLicenseEndDate
                  ? `${consulSearchData.securityLicenseStartDate.slice(
                      0,
                      10
                    )}至${consulSearchData.securityLicenseEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="securityLicenseTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="安全承诺书" prop="safePromise" class="mb0">
            <SpicUpload
              :key="uploadKey"
              v-model="safePromiseArr"
              type="image"
              :handle-remove-fn="handleRemoveSafePromiseFn"
              :limit="5"
              :disabled="
                route.query &&
                route.query.pageType == 'handle' &&
                isTypeHandleShow
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item
            label="安全承诺书有效期"
            prop="safePromiseTimeRange"
            class="mb0"
          >
            <span v-if="isTypeHandleShow">
              {{
                consulSearchData.safePromiseStartDate &&
                consulSearchData.safePromiseEndDate
                  ? `${consulSearchData.safePromiseStartDate.slice(
                      0,
                      10
                    )}至${consulSearchData.safePromiseEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="safePromiseTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { opsCompany as opsCompanyAPI } from '@/api/index.ts'
import SpicUpload from '@/components/spic-upload'
import type { FormInstance, FormRules } from 'element-plus'
interface RuleForm {
  license: string
  enterpriseQualification: string
  securityLicense: string
  safePromise: string
}
const route = useRoute()
const consultingInfoFormRef = ref<FormInstance>()
const props = defineProps({
  infoDetaile: {
    type: Object,
    default: () => {
      return {
        license: '',
        licenseStartDate: '',
        licenseEndDate: '',
        enterpriseQualification: '',
        enterpriseQualificationStartDate: '',
        enterpriseQualificationEndDate: '',
        securityLicense: '',
        securityLicenseStartDate: '',
        securityLicenseEndDate: '',
        safePromise: '',
        safePromiseStartDate: '',
        safePromiseEndDate: ''
      }
    }
  }
})

const consulSearchData = ref<Record<string, any>>({
  license: '',
  licenseStartDate: '',
  licenseEndDate: '',
  enterpriseQualification: '',
  enterpriseQualificationStartDate: '',
  enterpriseQualificationEndDate: '',
  securityLicense: '',
  securityLicenseStartDate: '',
  securityLicenseEndDate: '',
  safePromise: '',
  safePromiseStartDate: '',
  safePromiseEndDate: ''
})

const consulRules = reactive<FormRules<RuleForm>>({
  license: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
  enterpriseQualification: [
    {
      required: true,
      message: '请上传企业资质',
      trigger: 'change'
    }
  ],
  securityLicense: [
    { required: true, message: '请上传安全许可证', trigger: 'change' }
  ],
  safePromise: [
    {
      required: true,
      message: '请上传安全承诺书',
      trigger: 'change'
    }
  ]
})
// 上传图片
const licenseArr = ref<any[]>([])

watch(
  () => licenseArr.value,
  (val) => {
    if (val.length) {
      consulSearchData.value.license = val.join(',')
      consultingInfoFormRef.value?.validateField('license')
    } else {
      consulSearchData.value.license = ''
    }
  },
  {
    deep: true
  }
)

const enterpriseQualificationArr = ref<any[]>([])
watch(
  () => enterpriseQualificationArr.value,
  (val) => {
    if (val.length) {
      consulSearchData.value.enterpriseQualification = val.join(',')
      consultingInfoFormRef.value?.validateField('enterpriseQualification')
    } else {
      consulSearchData.value.enterpriseQualification = ''
    }
  },
  {
    deep: true
  }
)

const securityLicenseArr = ref<any[]>([])

watch(
  () => securityLicenseArr.value,
  (val) => {
    if (val.length) {
      consulSearchData.value.securityLicense = val.join(',')
      consultingInfoFormRef.value?.validateField('securityLicense')
    } else {
      consulSearchData.value.securityLicense = ''
    }
  },
  {
    deep: true
  }
)
const safePromiseArr = ref<any[]>([])

watch(
  () => safePromiseArr.value,
  (val) => {
    if (val.length) {
      consulSearchData.value.safePromise = val.join(',')
      consultingInfoFormRef.value?.validateField('safePromise')
    } else {
      consulSearchData.value.safePromise = ''
    }
  },
  {
    deep: true
  }
)

const handleRemoveLicenseFn = () => {
  if (licenseArr.value.length <= 0) {
    consulSearchData.value.licenseStartDate = ''
    consulSearchData.value.licenseEndDate = ''
    licenseTimeRange.value = []
  }
}
const handleRemoveEnterpriseQualificationFn = () => {
  if (enterpriseQualificationArr.value.length <= 0) {
    consulSearchData.value.enterpriseQualificationStartDate = ''
    consulSearchData.value.enterpriseQualificationEndDate = ''
    enterpriseQualificationTimeRange.value = []
  }
}
const handleRemoveSecurityLicenseFn = () => {
  if (securityLicenseArr.value.length <= 0) {
    consulSearchData.value.securityLicenseStartDate = ''
    consulSearchData.value.securityLicenseEndDate = ''
    securityLicenseTimeRange.value = []
  }
}
const handleRemoveSafePromiseFn = () => {
  if (safePromiseArr.value.length <= 0) {
    consulSearchData.value.safePromiseStartDate = ''
    consulSearchData.value.safePromiseEndDate = ''
    safePromiseTimeRange.value = []
  }
}

// 提交
const emit = defineEmits(['setPicData'])
const isTypeHandleShow = ref(false) // 判断页面状态
const isDisable = ref(false) // 判断是否禁用
const id_ = ref('') // 页面id
const oldObjSearchData = ref({})
const submitForm = async () => {
  // if (!consultingInfoFormRef.value) return
  await consultingInfoFormRef.value?.validate(async (valid) => {
    if (route.query && route.query.pageType == 'handle') {
      if (valid) {
        dealEffecteTime()
        try {
          let { data } = await opsCompanyAPI.addOperatorsInfo({
            ...consulSearchData.value,
            id: id_.value,
            menuType: 2
          })

          if (data.code == '200') {
            ElMessage({
              message: '保存成功！',
              type: 'success'
            })
            isTypeHandleShow.value = true
            oldObjSearchData.value = JSON.parse(
              JSON.stringify(consulSearchData.value)
            )
          } else {
            isTypeHandleShow.value = false
            ElMessage({
              message: '保存失败！',
              type: 'error'
            })
          }
        } catch (e) {
          isTypeHandleShow.value = false
          isDisable.value = true
          ElMessage({
            message: '保存失败！',
            type: 'error'
          })
        }
      } else {
        isTypeHandleShow.value = false
        isDisable.value = true
      }
    } else {
      dealEffecteTime()
      emit('setPicData', {
        valid,
        data: { ...consulSearchData.value }
      })
    }
  })
}
// 处理有效期开始时间和结束时间
const dealEffecteTime = () => {
  if (licenseTimeRange.value?.length) {
    consulSearchData.value.licenseStartDate =
      licenseTimeRange.value[0] + ' 00:00:00'
    consulSearchData.value.licenseEndDate =
      licenseTimeRange.value[1] + ' 23:59:59'
  } else {
    consulSearchData.value.licenseStartDate = ''
    consulSearchData.value.licenseEndDate = ''
  }

  if (enterpriseQualificationTimeRange.value?.length) {
    consulSearchData.value.enterpriseQualificationStartDate =
      enterpriseQualificationTimeRange.value[0] + ' 00:00:00'
    consulSearchData.value.enterpriseQualificationEndDate =
      enterpriseQualificationTimeRange.value[1] + ' 23:59:59'
  } else {
    consulSearchData.value.enterpriseQualificationStartDate = ''
    consulSearchData.value.enterpriseQualificationEndDate = ''
  }

  if (securityLicenseTimeRange.value?.length) {
    consulSearchData.value.securityLicenseStartDate =
      securityLicenseTimeRange.value[0] + ' 00:00:00'
    consulSearchData.value.securityLicenseEndDate =
      securityLicenseTimeRange.value[1] + ' 23:59:59'
  } else {
    consulSearchData.value.securityLicenseStartDate = ''
    consulSearchData.value.securityLicenseEndDate = ''
  }

  if (safePromiseTimeRange.value?.length) {
    consulSearchData.value.safePromiseStartDate =
      safePromiseTimeRange.value[0] + ' 00:00:00'
    consulSearchData.value.safePromiseEndDate =
      safePromiseTimeRange.value[1] + ' 23:59:59'
  } else {
    consulSearchData.value.safePromiseStartDate = ''
    consulSearchData.value.safePromiseEndDate = ''
  }
}
// 重置
const resetForm = () => {
  if (!consultingInfoFormRef.value) return
  consultingInfoFormRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})

// 编辑按钮
onMounted(async () => {
  if (route.query && route.query.pageType == 'handle') {
    isTypeHandleShow.value = true
  }
})
watch(
  () => props.infoDetaile,
  async (val) => {
    await setSearchData(val)
  },
  {
    deep: true
  }
)
const licenseTimeRange = ref<any[]>([])
const enterpriseQualificationTimeRange = ref<any[]>([])
const securityLicenseTimeRange = ref<any[]>([])
const safePromiseTimeRange = ref<any[]>([])
const setSearchData = (val: any) => {
  if (val) {
    consulSearchData.value.license = val.license
    consulSearchData.value.enterpriseQualification = val.enterpriseQualification
    consulSearchData.value.securityLicense = val.securityLicense
    consulSearchData.value.safePromise = val.safePromise
    id_.value = val.id

    // 营业执照
    licenseArr.value = val.license && val.license.split(',')

    // 企业执照
    enterpriseQualificationArr.value =
      val.enterpriseQualification && val.enterpriseQualification.split(',')

    // 安全许可证
    securityLicenseArr.value =
      val.securityLicense && val.securityLicense.split(',')

    // 安全承诺书
    safePromiseArr.value = val.safePromise && val.safePromise.split(',')

    // 4个证书的有效期
    if (val.licenseStartDate && val.licenseEndDate) {
      consulSearchData.value.licenseStartDate = val.licenseStartDate
      consulSearchData.value.licenseEndDate = val.licenseEndDate
      licenseTimeRange.value = [
        props.infoDetaile.licenseStartDate.slice(0, 10),
        props.infoDetaile.licenseEndDate.slice(0, 10)
      ]
    }
    if (
      val.enterpriseQualificationStartDate &&
      val.enterpriseQualificationEndDate
    ) {
      consulSearchData.value.enterpriseQualificationStartDate =
        val.enterpriseQualificationStartDate
      consulSearchData.value.enterpriseQualificationEndDate =
        val.enterpriseQualificationEndDate
      enterpriseQualificationTimeRange.value = [
        props.infoDetaile.enterpriseQualificationStartDate.slice(0, 10),
        props.infoDetaile.enterpriseQualificationEndDate.slice(0, 10)
      ]
    }
    if (val.securityLicenseStartDate && val.securityLicenseEndDate) {
      consulSearchData.value.securityLicenseStartDate =
        val.securityLicenseStartDate
      consulSearchData.value.securityLicenseEndDate = val.securityLicenseEndDate
      securityLicenseTimeRange.value = [
        props.infoDetaile.securityLicenseStartDate.slice(0, 10),
        props.infoDetaile.securityLicenseEndDate.slice(0, 10)
      ]
    }
    if (val.safePromiseStartDate && val.safePromiseEndDate) {
      consulSearchData.value.safePromiseStartDate = val.safePromiseStartDate
      consulSearchData.value.safePromiseEndDate = val.safePromiseEndDate
      safePromiseTimeRange.value = [
        props.infoDetaile.safePromiseStartDate.slice(0, 10),
        props.infoDetaile.safePromiseEndDate.slice(0, 10)
      ]
    }
  }
}
const onSumbitEdit = async () => {
  isTypeHandleShow.value = !isTypeHandleShow.value
  if (!isTypeHandleShow.value) {
    isDisable.value = true
    oldObjSearchData.value = JSON.parse(
      JSON.stringify({ ...consulSearchData.value })
    )
  }
}
const uploadKey = ref<any>('')
const cancelBtn = () => {
  consulSearchData.value = JSON.parse(JSON.stringify(oldObjSearchData.value))
  isTypeHandleShow.value = true

  for (let i in consulRules) {
    consulRules[i][0].required = false
  }
  setTimeout(() => {
    for (let i in consulRules) {
      consulRules[i][0].required = true
    }
  }, 0)

  // 营业执照
  licenseArr.value =
    oldObjSearchData.value.license && oldObjSearchData.value.license.split(',')
  // 企业执照
  enterpriseQualificationArr.value =
    oldObjSearchData.value.enterpriseQualification &&
    oldObjSearchData.value.enterpriseQualification.split(',')
  // 安全许可证
  securityLicenseArr.value =
    oldObjSearchData.value.securityLicense &&
    oldObjSearchData.value.securityLicense.split(',')
  // 安全承诺书
  safePromiseArr.value =
    oldObjSearchData.value.safePromise &&
    oldObjSearchData.value.safePromise.split(',')
  uploadKey.value = Date.now()
}
</script>
<style lang="scss">
.consulelForm__ {
  .el-form-item--default {
    margin: 0 !important;
    margin-bottom: 24px !important;
  }
}
</style>
<style lang="scss" scoped>
.consultingInfo {
  height: calc(100% - 340px);
  padding: 24px 24px 0;
  box-sizing: border-box;
  border-radius: 8px;
  background: #fff;

  .title {
    font-size: 16px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left-title {
      display: flex;
      align-items: center;
    }

    .waringInfo {
      display: flex;
      align-items: center;
      color: #ff9900;
      font-size: 12px;

      .el-icon {
        margin: 0 4px 0 8px;
        font-size: 16px;
      }
    }
  }
}

.cls-detail {
  word-wrap: break-word;
  word-break: break-all;
}
.cls-detail :deep(.el-form-item__label) {
  color: rgba(0, 0, 0, 0.45) !important;
  line-height: 22px !important;
  height: 22px !important;
}
.cls-detail :deep(.el-form-item__content) {
  line-height: 22px !important;
}
</style>
