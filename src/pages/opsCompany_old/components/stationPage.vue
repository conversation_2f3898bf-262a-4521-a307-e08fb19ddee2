<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import useOpsElectricData from '../hooks/useOpsElectricData'

const route = useRoute()
const companyCode = route.query.companyCode as string
const tableLoading = ref(false)
const {
  opsElectricColumns,
  opsElectricTotal,
  opsElectricData,
  opsElectricSizeChange,
  opsElectricCurrentChange,
  opsElectricPage
} = useOpsElectricData(companyCode, [tableLoading])
</script>
<template>
  <div class="station-page">
    <vis-table-pagination
      :loading="tableLoading"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="opsElectricPage.pageSize"
      :current-page="opsElectricPage.pageNum"
      :columns="opsElectricColumns"
      :total="opsElectricTotal"
      :data="opsElectricData"
      :show-overflow-tooltip="true"
      background
      class="vis-table-pagination"
      @handle-size-change="opsElectricSizeChange"
      @handle-current-change="opsElectricCurrentChange"
    >
    </vis-table-pagination>
  </div>
</template>
<style lang="scss" scoped>
.station-page {
  :deep(.el-table--default) {
    height: 528px !important;
  }
}
</style>
