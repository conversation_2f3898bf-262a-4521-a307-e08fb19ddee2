<template>
  <div class="basicInfo" :class="isTypeHandleShow ? 'cls-detail' : ''">
    <div class="title">
      基础信息
      <div>
        <el-button
          v-if="
            route.query && route.query.pageType == 'handle' && isTypeHandleShow
          "
          type="primary"
          @click="onSumbitEdit"
        >
          编辑
        </el-button>
        <div
          v-if="
            route.query && route.query.pageType == 'handle' && !isTypeHandleShow
          "
        >
          <el-button plain @click="cancelBtn">取消</el-button>
          <el-button type="primary" @click="submitForm">保存</el-button>
        </div>
      </div>
    </div>
    <el-form
      ref="searchFormRef"
      :inline="true"
      :model="searchData"
      :rules="isTypeHandleShow ? {} : rules"
      label-suffix=""
      label-width="110px"
      class="elForm__"
    >
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="运维单位类型" prop="operatorType">
            <span v-if="isTypeHandleShow">{{
              searchData.operatorType == 2
                ? '运维实施单位'
                : searchData.operatorType == 1
                ? '运维管理单位'
                : ''
            }}</span>
            <el-select
              v-else
              v-model="searchData.operatorType"
              placeholder="请选择运维单位类型"
              :disabled="disabledParentCompany"
              clearable
            >
              <el-option label="运维管理单位" :value="1" />
              <el-option label="运维实施单位" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="searchData.operatorType == '2'" :span="10" :offset="2">
          <el-form-item label="上级管理单位" prop="parentCompanyId">
            <span v-if="isTypeHandleShow">{{ searchData.parentCompany }}</span>
            <el-select
              v-else
              v-model="searchData.parentCompanyId"
              placeholder="请选择上级管理单位"
              clearable
              filterable
              @change="changeParentItem"
            >
              <template v-for="item in parentCompanys" :key="item.id">
                <el-option :label="item.companyName" :value="item.id" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="searchData.operatorType != '2' ? 2 : 0">
          <el-form-item label="公司名称" prop="companyCode">
            <span v-if="isTypeHandleShow">{{ searchData.companyName }}</span>
            <el-select
              v-else
              v-model="searchData.companyCode"
              placeholder="请选择公司"
              clearable
              :disabled="isDisable"
              filterable
              @change="changeItem"
            >
              <template v-for="item in companyTypesArr" :key="item.companyCode">
                <el-option
                  :label="item.companyName"
                  :value="item.companyCode"
                  :disabled="item.isSelect == 1 ? true : false"
                />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="searchData.operatorType == '2' ? 2 : 0">
          <el-form-item label="公司地址" prop="address">
            <span v-if="isTypeHandleShow">{{ searchData.address }}</span>
            <el-input
              v-else
              v-model.trim="searchData.address"
              placeholder="请输入公司地址"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="searchData.operatorType != '2' ? 2 : 0">
          <el-form-item label="公司负责人" prop="headUser">
            <span v-if="isTypeHandleShow">{{ searchData.headUser }}</span>
            <el-input
              v-else
              v-model="searchData.headUser"
              placeholder="请输入公司负责人"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="searchData.operatorType == '2' ? 2 : 0">
          <el-form-item label="联系电话" prop="phone">
            <span v-if="isTypeHandleShow">{{ searchData.phone }}</span>
            <el-input
              v-else
              v-model.trim="searchData.phone"
              placeholder="请输入联系电话"
              clearable
              maxlength="11"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="searchData.operatorType != '2' ? 2 : 0">
          <el-form-item label="身份证号" prop="identityCard">
            <span v-if="isTypeHandleShow">{{ searchData.identityCard }}</span>
            <el-input
              v-else
              v-model="searchData.identityCard"
              placeholder="请输入身份证号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="searchData.operatorType == '2'"
          :span="10"
          :offset="searchData.operatorType == '2' ? 2 : 0"
        >
          <el-form-item label="身份证有效期" prop="identityCardTimeRange">
            <span v-if="isTypeHandleShow">
              {{
                searchData.identityCardStartDate &&
                searchData.identityCardEndDate
                  ? `${searchData.identityCardStartDate.slice(
                      0,
                      10
                    )}至${searchData.identityCardEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="identityCardTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item
            label="身份证照片"
            prop="cardPhoto"
            class="cardPhotoClass"
          >
            <SpicUpload
              :key="uploadKey"
              v-model="files"
              type="image"
              :handle-remove-fn="handleRemoveFn"
              :limit="5"
              :disabled="
                route.query &&
                route.query.pageType == 'handle' &&
                isTypeHandleShow
              "
            />
          </el-form-item>
          <div v-if="!isTypeHandleShow" class="waringInfoBas__">
            <el-icon color="#FF9900">
              <WarningFilled />
            </el-icon>
            提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件
          </div>
        </el-col>
        <el-col
          v-if="searchData.operatorType != '2'"
          :span="10"
          :offset="searchData.operatorType != '2' ? 2 : 0"
        >
          <el-form-item label="身份证有效期" prop="identityCardTimeRange">
            <span v-if="isTypeHandleShow">
              {{
                searchData.identityCardStartDate &&
                searchData.identityCardEndDate
                  ? `${searchData.identityCardStartDate.slice(
                      0,
                      10
                    )}至${searchData.identityCardEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="identityCardTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import SpicUpload from '@/components/spic-upload'
import { opsCompany as opsCompanyAPI } from '@/api/index.ts'
import request from '@/utils/request.ts'

const route = useRoute()

const formRulesheadUser = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入公司负责人'))
  } else {
    if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
      callback(new Error('请输入汉字'))
    } else {
      callback()
    }
  }
}
const formRulesphone = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入联系电话'))
  } else {
    if (value.trim().length < 11) {
      callback(new Error('请输入11位联系电话'))
    } else {
      callback()
    }
  }
}
const formRulesidentityCard = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入身份证号'))
  } else {
    // !/^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[Xx\d]$/
    if (!/^[0-9A-Za-z]+$/.test(value)) {
      callback(new Error('请输入正确身份证号'))
    } else {
      callback()
    }
  }
}
const props = defineProps({
  infoDetaile: {
    type: Object,
    default: () => {
      return {
        operatorType: '',
        parentCompany: '',
        parentCompanyId: null,
        companyCode: '',
        companyName: '',
        address: '',
        headUser: '',
        phone: '',
        cardPhoto: '',
        identityCard: ''
      }
    }
  }
})

const searchFormRef = ref<FormInstance>()
const searchData = ref<any>({
  operatorType: null, // 运维单位类型
  parentCompanyId: null,
  parentCompany: '', // 公司名称

  companyCode: '',
  companyName: '',
  address: '',
  headUser: '',
  phone: '',
  cardPhoto: '',
  identityCard: '',
  identityCardStartDate: '',
  identityCardEndDate: ''
})
const companyTypesArr = ref<any[]>([])
const parentCompanys = ref<any[]>([])
const rules = reactive<any>({
  operatorType: [
    { required: true, message: '请选择运维单位类型', trigger: 'change' }
  ],
  parentCompanyId: [
    { required: true, message: '请选择上级管理单位', trigger: 'blur' }
  ],
  companyCode: [
    { required: true, message: '请选择公司名称', trigger: 'change' }
  ],
  address: [
    {
      required: true,
      message: '请输入公司地址',
      trigger: 'blur'
    }
  ],
  headUser: [
    {
      required: true,
      trigger: 'blur',
      validator: formRulesheadUser
    }
  ],
  phone: [
    {
      required: true,
      trigger: 'blur',
      validator: formRulesphone
    }
  ],
  cardPhoto: [
    { required: true, message: '请上传身份证照片', trigger: 'change' }
  ],
  identityCard: [
    {
      required: true,
      trigger: 'blur',
      validator: formRulesidentityCard
    }
  ]
})
const identityCardTimeRange = ref<any[]>([])
// 上传图片
let files = ref<any[]>([])

watch(
  () => files.value,
  (val) => {
    if (val.length) {
      searchData.value.cardPhoto = val.join(',')
      searchFormRef.value?.validateField('cardPhoto')
    } else {
      searchData.value.cardPhoto = ''
    }
  },
  {
    deep: true
  }
)

const handleRemoveFn = () => {
  if (files.value.length <= 0) {
    searchData.value.identityCardStartDate = ''
    searchData.value.identityCardEndDate = ''
    identityCardTimeRange.value = []
  }
}

// 提交
const isTypeHandleShow = ref(false) // 判断页面状态
const isDisable = ref(true) // 判断是否禁用
const id_ = ref('') // 页面id
const emit = defineEmits(['getFromData'])
const oldObjSearchData = ref<any>({})

const submitForm = async () => {
  // if (!searchFormRef.value) return
  await searchFormRef.value?.validate(async (valid) => {
    if (route.query && route.query.pageType == 'handle') {
      if (valid) {
        dealEffecteTime()
        try {
          let { data } = await opsCompanyAPI.addOperatorsInfo({
            ...searchData.value,
            id: id_.value,
            menuType: 1
          })
          if (data.code == '200') {
            ElMessage({
              message: '保存成功！',
              type: 'success'
            })
            isTypeHandleShow.value = true
            oldObjSearchData.value = JSON.parse(
              JSON.stringify(searchData.value)
            )
          } else {
            isTypeHandleShow.value = false
            ElMessage({
              message: '保存失败！',
              type: 'error'
            })
          }
        } catch (e) {
          isTypeHandleShow.value = false
          isDisable.value = true
          ElMessage({
            message: '保存失败！',
            type: 'error'
          })
        }
      } else {
        isTypeHandleShow.value = false
        isDisable.value = true
      }
    } else {
      dealEffecteTime()
      emit('getFromData', {
        valid,
        data: { ...searchData.value }
      })
    }
  })
}
// 处理有效期开始时间和结束时间
const dealEffecteTime = () => {
  if (identityCardTimeRange.value?.length) {
    searchData.value.identityCardStartDate =
      identityCardTimeRange.value[0] + ' 00:00:00'
    searchData.value.identityCardEndDate =
      identityCardTimeRange.value[1] + ' 23:59:59'
  } else {
    searchData.value.identityCardStartDate = ''
    searchData.value.identityCardEndDate = ''
  }
}
// 重置
const resetForm = () => {
  if (!searchFormRef.value) return
  searchFormRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})
// 编辑按钮
onMounted(async () => {
  if (route.query && route.query.pageType == 'handle') {
    isTypeHandleShow.value = true
  } else {
    isDisable.value = false
    getApiCompanyList()
    getparentCompanys()
  }
})
watch(
  () => props.infoDetaile,
  async (val) => {
    getApiCompanyList()
    getparentCompanys()
    await setSearchData(val)
  },
  {
    deep: true
  }
)
const disabledParentCompany = ref(false)
const setSearchData = async (val: any) => {
  if (val) {
    searchData.value.operatorType = val.operatorType
    if (searchData.value.operatorType == '1') {
      disabledParentCompany.value = true
    }
    searchData.value.parentCompanyId = val.parentCompanyId
    searchData.value.parentCompany = val.parentCompany

    searchData.value.companyCode = val.companyCode
    searchData.value.companyName = val.companyName
    searchData.value.address = val.address
    searchData.value.headUser = val.headUser
    searchData.value.phone = val.phone
    searchData.value.cardPhoto = val.cardPhoto
    searchData.value.identityCard = val.identityCard
    id_.value = val.id
    if (val.identityCardStartDate && val.identityCardEndDate) {
      searchData.value.identityCardStartDate = val.identityCardStartDate
      searchData.value.identityCardEndDate = val.identityCardEndDate
      identityCardTimeRange.value = [
        props.infoDetaile.identityCardStartDate.slice(0, 10),
        props.infoDetaile.identityCardEndDate.slice(0, 10)
      ]
    }
    // 身份证照片
    files.value = val.cardPhoto.split(',')
  }
}
const getApiCompanyList = async () => {
  try {
    let { data } = await opsCompanyAPI.getCompanyList(
      {
        companyCode: '',
        companyName: '',
        companyTypeId: '',
        socialUniformCreditCode: '',
        pageNum: 1,
        pageSize: 20000
      },
      false
    )
    if (data.code == '200') {
      companyTypesArr.value = data.data
    } else {
      companyTypesArr.value = []
    }
  } catch (e) {
    companyTypesArr.value = []
    console.log(e)
  }
}
const getparentCompanys = async () => {
  try {
    let { data } = await request({
      url: '/operate/operatorsManage/queryOperateCompanyByManage',
      method: 'get'
    })
    if (data.code == '200') {
      parentCompanys.value = data.data || []
    } else {
      parentCompanys.value = []
    }
  } catch (e) {
    parentCompanys.value = []
  }
}
const changeItem = (val: string) => {
  companyTypesArr.value.length &&
    companyTypesArr.value.forEach((item: any) => {
      if (item.companyCode == val) {
        searchData.value.companyName = item.companyName
      }
    })
}
const changeParentItem = (val: string) => {
  parentCompanys.value.length &&
    parentCompanys.value.forEach((item: any) => {
      if (item.id == val) {
        searchData.value.parentCompany = item.companyName
      }
    })
}

const onSumbitEdit = async () => {
  isTypeHandleShow.value = !isTypeHandleShow.value
  if (!isTypeHandleShow.value) {
    isDisable.value = true
    oldObjSearchData.value = JSON.parse(JSON.stringify({ ...searchData.value }))
  }
}
const uploadKey = ref<any>('')
const cancelBtn = () => {
  searchData.value = JSON.parse(JSON.stringify(oldObjSearchData.value))
  isTypeHandleShow.value = true

  for (let i in rules) {
    rules[i][0].required = false
  }
  setTimeout(() => {
    for (let i in rules) {
      rules[i][0].required = true
    }
  }, 0)
  files.value = oldObjSearchData.value.cardPhoto.split(',')
  uploadKey.value = Date.now()
}
</script>

<style lang="scss" scoped>
.basicInfo {
  padding: 24px;
  box-sizing: border-box;
  border-radius: 8px;
  background: #fff;
  margin-bottom: 24px;

  .title {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    margin-bottom: 24px;
  }
}
.cls-detail {
  word-wrap: break-word;
  word-break: break-all;
}
.cls-detail :deep(.el-form-item__label) {
  color: rgba(0, 0, 0, 0.45) !important;
  line-height: 22px !important;
  height: 22px !important;
}
.cls-detail :deep(.el-form-item__content) {
  line-height: 22px !important;
}
</style>
<style lang="scss">
.elForm__ {
  .el-form-item--default {
    // margin: 0 !important;
  }
  .el-form-item--default.cardPhotoClass {
    margin-bottom: 20px !important;
  }
}

.rowRig__ {
  padding-left: 10px !important;
  box-sizing: border-box;
}

.waringInfoBas__ {
  padding-left: 100px;
  display: flex;
  align-items: center;
  color: #ff9900;
  font-size: 12px;

  .el-icon {
    margin: 0 4px 0 8px;
    font-size: 16px;
  }
}
</style>
