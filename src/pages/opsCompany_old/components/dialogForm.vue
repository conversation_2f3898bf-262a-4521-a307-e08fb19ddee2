<!--
 * @Author: 赵鹏鹏
 * @Date: 2024-03-20 15:14:02
 * @LastEditors: 赵鹏鹏
 * @Description: 新建核查记录
-->
<template>
  <el-dialog
    align-center
    width="812px"
    :title="props.typeVal == 'add' ? '新建核查记录' : '编辑核查记录'"
    class="vis-dialog dialog2__"
    v-model="dialogVisible"
    :before-close="dialogClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      :rules="formRules"
    >
      <div class="topClass">
        <div class="titleR__"><span></span><span>核查记录</span></div>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="核查内容" prop="content" class="mb24">
              <el-input
                v-model="formData.content"
                autocomplete="off"
                placeholder="请输入核查内容"
                maxLength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="核查时间" prop="verifyTime" class="mb24">
              <el-date-picker
                v-model="formData.verifyTime"
                type="datetime"
                placeholder="请选择核查时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="核查公司" prop="verifyCompany" class="mb32">
              <el-input
                v-model="formData.verifyCompany"
                :maxlength="6"
                autocomplete="off"
                placeholder="请输入核查公司"
                maxLength="20"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="botClass" style="margin-top: 20px">
        <div class="titleR__">
          <span></span><span>核查报告</span>
          <div class="waringInfo_">
            <el-icon color="#FF9900">
              <WarningFilled />
            </el-icon>
            提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件
          </div>
        </div>
        <el-form-item label="核查报告" prop="fileReport" class="mb32">
          <SpicUpload
            v-model="files"
            type="image"
            :limit="5"
            :key="uploadKey"
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="submitForm()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { opsCompany as opsCompanyAPI } from '@/api/index.ts'
import type { FormInstance, FormRules } from 'element-plus'
import SpicUpload from '@/components/spic-upload'
const formRef = ref<FormInstance>()
const formRulesContent = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入核查内容'))
  } else {
    if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
      callback(new Error('请输入汉字'))
    } else {
      callback()
    }
  }
}
const formRulesVerifyCompany = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入核查公司'))
  } else {
    if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
      callback(new Error('请输入汉字'))
    } else {
      callback()
    }
  }
}

const formRules = reactive<FormRules<Record<string, any>>>({
  content: [{ required: true, trigger: 'blur', validator: formRulesContent }],
  verifyTime: [
    {
      required: true,
      message: '请选择核查时间',
      trigger: 'change'
    }
  ],
  verifyCompany: [
    { required: true, trigger: 'blur', validator: formRulesVerifyCompany }
  ],
  fileReport: [{ required: true, message: '请上传核查报告', trigger: 'change' }]
})

const route = useRoute()
const formData = ref<Record<string, any>>({
  content: '',
  verifyTime: '',
  verifyCompany: '',
  fileReport: ''
})
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  typeVal: {
    type: String,
    default: 'add',
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

let dialogVisible = ref(false)
watch(
  () => props.isShow,
  (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      setFormData()
    }
  },
  {
    deep: true
  }
)
// 上传报告
let files = ref<any[]>([])
watch(
  () => files.value,
  (val) => {
    if (val.length) {
      formData.value.fileReport = val.join(',')
      formRef.value?.validateField('fileReport')
    } else {
      formData.value.fileReport = ''
    }
  },
  {
    deep: true
  }
)
const id_ = ref('')
const setFormData = () => {
  if (props.typeVal == 'add') {
    formData.value.content = ''
    formData.value.verifyTime = ''
    formData.value.verifyCompany = ''
    formData.value.fileReport = ''
    files.value = []
  } else {
    formData.value.content = props.rows.content
    formData.value.verifyTime = props.rows.verifyTime
    formData.value.verifyCompany = props.rows.verifyCompany
    formData.value.fileReport = props.rows.fileReport
    id_.vlaue = props.rows.id

    // 核查报告
    files.value = props.rows.fileReport
  }
}
// 点击确定
const emit = defineEmits(['closeDialog', 'updateList'])
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      let params
      if (props.typeVal == 'add') {
        params = {
          ...formData.value,
          companyCode: route.query && route.query.companyCode
        }
      } else {
        params = {
          ...formData.value,
          companyCode: route.query && route.query.companyCode,
          id: id_.vlaue
        }
      }

      try {
        let { data } = await opsCompanyAPI.addUpdateVerifyRecord(params)
        if (data.code === '200') {
          ElMessage({
            message: props.typeVal == 'add' ? '新增成功！' : '编辑成功！',
            type: 'success'
          })
          dialogClose()
          emit('updateList')
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}
// 关闭弹窗
const uploadKey = ref<any>('')
const dialogClose = () => {
  formRef.value && formRef.value.resetFields()
  files.value = []
  uploadKey.value = Date.now()
  emit('closeDialog', false)
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.titleR__ {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  span:first-child {
    width: 4px;
    height: 16px;
    background: #2acba0;
    margin-right: 10px;
  }

  span:nth-child(2) {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85) !important;
  }

  .waringInfo_ {
    display: flex;
    align-items: center;
    color: #ff9900;
    font-size: 12px;

    .el-icon {
      margin: 0 4px 0 8px;
      font-size: 16px;
    }
  }
}
.mb32 {
  margin-bottom: 32px !important;
}
.mb24 {
  margin-bottom: 24px !important;
}
// .el-dialog.vis-dialog.dialog2__ .el-dialog__header .el-dialog__headerbtn {
//   top: 16px;
// }
</style>
