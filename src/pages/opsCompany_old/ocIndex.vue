<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>运维商列表</p>
        <el-button type="primary" @click="handleAdd"
          ><el-icon m-r-4px> <Plus /> </el-icon>新建运维商</el-button
        >
      </div>
      <div class="tables">
        <vis-table-pagination
          :loading="tableLoading"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchData.pageSize"
          :current-page="searchData.pageNum"
          :columns="columns"
          :total="listTotal"
          :data="listData"
          :show-overflow-tooltip="true"
          background
          class="vis-table-pagination"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
        >
          <template #headUser="{ row }">
            {{ row.headUser ? row.headUser.split('_')[0] : '--' }}
          </template>
          <template #operate="{ row }">
            <div class="table-operate">
              <el-button link @click="handleDetail(row)">查看详情</el-button>

              <el-popconfirm title="确认删除？" @confirm="handleDelete(row)">
                <template #reference>
                  <el-button link @click.stop>删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { opsCompany as opsCompanyAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import type { PageObject } from './types'

const router = useRouter()

onMounted(async () => {
  await getTableData()
})

// 搜索
const searchData = ref({
  companyName: '',
  headUser: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    prop: 'companyName',
    label: '公司名称',
    placeholder: '请输入公司名称',
    span: 8,
    width: '70px',
    maxlength: 32
  },
  {
    prop: 'headUser',
    label: '区域监盘人',
    placeholder: '请输入区域监盘人',
    span: 8,
    width: '110px'
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  await getTableData()
}

const columns = [
  {
    prop: 'companyName',
    label: '公司名称',
    minWidth: '240'
  },
  {
    prop: 'headUser',
    label: '区域监盘人',
    slotName: 'headUser',
    minWidth: '100'
  },
  {
    prop: 'phone',
    label: '联系电话',
    minWidth: '190'
  },
  {
    prop: 'identityCard',
    label: '身份证号',
    minWidth: '170'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: '170'
  },
  {
    prop: 'updateTime',
    label: '更新时间',
    minWidth: '170'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 130,
    fixed: 'right'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await opsCompanyAPI.getOperatorsList(
      { ...searchData.value },
      [tableLoading]
    )
    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: PageObject) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: PageObject) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}

// 操作
const handleAdd = () => {
  router.push('/ops-company-old/ocInfo-old?pageType=add')
}
const handleDetail = (row: Record<string, any>) => {
  router.push(
    `/ops-company-old/ocInfo-old?pageType=handle&companyCode=${
      row.companyCode
    }&id=${JSON.stringify(row.id)}`
  )
}
const handleDelete = async (row: Record<string, any>) => {
  // 1. 删除时需要判断，如果该运营商下存在运维人员，则不允许删除。
  // 2. 点击删除，删除本条运维商数据，只做逻辑删除，不清除数据。
  // 3. 删除时，需弹窗进行二次确认。
  // 用这个文案吧：“该运维商已启用，无法删除！”包含存在运维人员和运维项目两种情况
  try {
    // let { data } = await opsCompanyAPI.delOperatorsInfoCheck({
    //   id: row.id
    // })
    // if (data.code == '200' && data.data) {
    //   handleDelete2(row.id)
    // } else {
    //   ElMessage({
    //     message: '该运维商已启用，无法删除！',
    //     type: 'error'
    //   })
    // }
    handleDelete2(row.id)
  } catch (e: any) {
    ElMessage({
      message: e,
      type: 'error'
    })
  }
}
const handleDelete2 = async (id: string) => {
  try {
    let { data } = await opsCompanyAPI.delFaultKnowledgeById({
      id
    })
    if (data.code == '200') {
      ElMessage({
        message: '删除成功！',
        type: 'success'
      })
      searchData.value.pageNum = 1
      searchData.value.pageSize = 10
      getTableData()
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e,
      type: 'error'
    })
  }
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
