<template>
  <div class="ocinfo">
    <div class="page-title">
      {{ pageName }}
    </div>
    <div class="main">
      <!-- 基础信息 -->
      <basic-info
        ref="basicInfoRef"
        @getFromData="getFromData"
        :infoDetaile="infoDetaile"
      ></basic-info>
      <!-- 资质信息 -->
      <consulting-info
        ref="consultingInfoRef"
        @setPicData="setPicData"
        v-if="route.query && route.query.pageType == 'add'"
      ></consulting-info>
      <!-- tabs菜单 -->
      <div class="tabs-box__" v-else>
        <el-tabs v-model="activeTabName" @tab-change="tabChange">
          <!-- 资质信息 -->
          <el-tab-pane label="资质信息" name="tabsOne">
            <consulting-info
              ref="consultingInfoRef"
              @setPicData="setPicData"
              :infoDetaile="infoDetaile"
              v-if="activeTabName == 'tabsOne'"
            ></consulting-info>
          </el-tab-pane>
          <!-- 核查记录 -->
          <el-tab-pane label="核查记录" name="tabsTwo">
            <check-list v-if="activeTabName == 'tabsTwo'"></check-list>
          </el-tab-pane>
          <!-- 联系人列表 -->
          <el-tab-pane label="联系人列表" name="tabsThree">
            <ContactsPage v-if="activeTabName == 'tabsThree'"></ContactsPage>
          </el-tab-pane>
          <!-- 运维项目 -->
          <el-tab-pane label="运维项目" name="tabsFour">
            <ProjectPage v-if="activeTabName == 'tabsFour'"></ProjectPage>
          </el-tab-pane>
          <!-- 运维电站列表 -->
          <el-tab-pane label="运维电站列表" name="tabsFive">
            <StationPage v-if="activeTabName == 'tabsFive'"></StationPage>
          </el-tab-pane>
          <!-- 运维人员列表 -->
          <el-tab-pane label="运维人员列表" name="tabsSix">
            <PersonnelList v-if="activeTabName == 'tabsSix'"></PersonnelList>
          </el-tab-pane>
          <!-- 班组列表 -->
          <el-tab-pane label="班组列表" name="tabsSeven">
            <TeamList v-if="activeTabName == 'tabsSeven'"></TeamList>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div
      class="page-footer"
      v-if="route.query && route.query.pageType == 'add'"
    >
      <el-button plain @click="onCancel">返回</el-button>
      <el-button type="primary" @click="onSumbit">提交</el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { opsCompany as opsCompanyAPI } from '@/api/index.ts'
import basicInfo from './components/basicInfo.vue'
import consultingInfo from './components/consultingInfo.vue'
import checkList from './components/checkList.vue'
import ContactsPage from './components/contactsPage.vue'
import ProjectPage from './components/projectPage.vue'
import StationPage from './components/stationPage.vue'
import PersonnelList from './components/personnelList.vue'
import TeamList from './components/teamList.vue'

const basicInfoRef = ref<any>(null)
const consultingInfoRef = ref<any>(null)
const router = useRouter()
const route = useRoute()
const pageName = computed(() => {
  if (route.query && route.query.pageType == 'add') {
    return '新建运维商'
  }
  if (route.query && route.query.pageType == 'handle') {
    return '查看详情'
  }
})

// 取消
const onCancel = () => {
  router.push('/ops-company')
}
const basicInfoData = ref({}) // 基础信息
const consultingInfoData = ref({}) // 资质信息

const onSumbit = async () => {
  // 触发基础信息
  await basicInfoRef.value.submitForm()
  // 触发资质信息
  await consultingInfoRef.value.submitForm()

  nextTick(async () => {
    if (
      JSON.stringify(basicInfoData.value) == '{}' ||
      JSON.stringify(consultingInfoData.value) == '{}'
    ) {
      return false
    }
    try {
      let { data } = await opsCompanyAPI.addOperatorsInfo({
        ...basicInfoData.value,
        ...consultingInfoData.value
      })
      if (data.code == '200') {
        ElMessage({
          message: '新增成功！',
          type: 'success'
        })
        onCancel()
      } else {
        ElMessage({
          message: data.message,
          type: 'error'
        })
      }
    } catch (e) {
      ElMessage({
        message: e.message,
        type: 'error'
      })
    }
  })
}
const getFromData = (val: object) => {
  if (val.valid) {
    basicInfoData.value = val.data
  } else {
    basicInfoData.value = {}
  }
}
const setPicData = (val: object) => {
  if (val.valid) {
    consultingInfoData.value = val.data
  } else {
    consultingInfoData.value = {}
  }
}
// tab切换
const activeTabName = ref('tabsOne')
const infoDetaile = ref({})
// 获取详情接口
onMounted(() => {
  getDetailes()
})
const tabChange = (val: string) => {
  if (val === 'tabsOne') {
    getDetailes()
  }
}
const getDetailes = async () => {
  if (route.query && route.query.pageType == 'handle') {
    try {
      let { data } = await opsCompanyAPI.getOperatorsInfoById({
        id: route.query && route.query.id
      })
      if (data.code == '200') {
        infoDetaile.value = data.data
      } else {
        infoDetaile.value = {}
        ElMessage({
          message: data.message,
          type: 'error'
        })
      }
    } catch (e: any) {
      ElMessage({
        message: e.message,
        type: 'error'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.ocinfo {
  padding-bottom: 24px;
}
.main {
  width: 100%;
  height: 100%;
  padding: 0 24px;
  .tabs-box__ {
    height: calc(100% - 362px);
    background: #fff;
    padding: 24px;
    box-sizing: border-box;
    border-radius: 8px;
  }
}
.page-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
  line-height: 64px;
  margin: 24px;
  height: 64px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  & > .el-button + .el-button {
    margin-left: 16px !important;
  }
  & > .el-button {
    width: 72px !important;
    height: 40px !important;
  }
}
.page-title {
  font-size: 16px;
  margin: 24px 0 24px 24px;
}
.page-header {
  height: 60px;
  display: flex;
  align-items: center;
  background: #fff;
  box-shadow: 0 0 8px 0 rgb(21 102 80 / 10%);
  .header-title {
    padding-left: 24px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    .backIcon {
      position: relative;
      top: 2px;
    }
    .goback {
      color: #333;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;
    }
    .detailLine {
      color: #e4e4e4;
      margin: 0 6px 0 12px;
    }
    a {
      color: #999;
      margin-left: 6px;
    }
    span {
      margin-left: 6px;
    }
  }
}
</style>
<style lang="scss">
.tabs-box__ {
  padding: 24px 24px 0;
  .consultingInfo {
    .mb0 {
      margin-bottom: 0 !important;
    }
  }
  .checkList {
    padding: 9px 24px 0 !important;
  }
  .el-tbas {
    height: 100% !important;
    .el-tbas__content {
      height: calc(100% - 55px);
    }
  }
}
</style>
