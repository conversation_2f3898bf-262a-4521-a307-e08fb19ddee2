<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="100px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>运维人员列表</p>
        <el-button type="primary" @click="handleAdd(null)">
          <el-icon><Plus /></el-icon>
          新建运维人员
        </el-button>
      </div>
      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :loading="tableLoading"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="tableColumns"
        :total="tableData.total"
        :data="tableData.data"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #userSex="{ row }">
          <span v-if="row.userSex === 1">男</span>
          <span v-if="row.userSex === 2">女</span>
        </template>
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="handleAdd(row)">查看详情</el-button>
            <el-popconfirm title="确认删除？" @confirm="handleDelete(row)">
              <template #reference>
                <el-button link @click.stop>删除</el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'

const router = useRouter()

let searchData = ref({
  companyName: '',
  userName: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    label: '所属运维公司',
    prop: 'companyName',
    type: 'select',
    filterable: true,
    options: []
  },
  { label: '人员姓名', prop: 'userName' }
])
const tableColumns = [
  { prop: 'companyName', label: '所属运维公司', minWidth: 120 },
  {
    prop: 'companyLeader',
    label: '公司负责人',
    minWidth: 100,
    formatter: (e: any) =>
      e.companyLeader ? e.companyLeader.split('_')[0] : '--'
  },
  { prop: 'userName', label: '运维人员姓名', minWidth: 120 },
  // { prop: 'positionLevel', label: '级别' },
  { prop: 'userSex', label: '性别', slotName: 'userSex' },
  { prop: 'userAge', label: '年龄' },
  { prop: 'userPhone', label: '联系电话' },
  { prop: 'createTime', label: '创建时间' },
  { prop: 'updateTime', label: '更新时间' },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 130,
    fixed: 'right'
  }
]
const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageNum = 1
  searchData.value.pageSize = 10
  getTableData()
}
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await opsPersonnelAPI.getOperationUserList(
      searchData.value,
      [tableLoading]
    )
    tableData.data = data?.data?.records || []
    tableData.total = data?.data?.total || 0
  } catch (e: any) {
    tableData.data = []
    tableData.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

onMounted(async () => {
  getTableData()
  getCompany()
})

const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const handleAdd = async (row: any = null) => {
  if (row) {
    router.push(
      `/ops-personnel/detail?pageType=detail&personnelCode=${row.id}&pageStatus=detail`
    )
  } else {
    router.push(`/ops-personnel/detail?pageType=add&pageStatus=add`)
  }
  // router.push(`/ops-personnel/detail?pageType=add&pageStatus=add`)
  // router.push({
  //   name: 'OpsPersonnelDetail',
  //   query: {
  //     pageType: 'add',
  //     personnelCode: row ? row.id : '',
  //     pageStatus: row ? 'detail' : 'add'
  //   }
  // })
}
const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    searchProps.value[0].options = data.data.map((item: any) => {
      return {
        label: item.companyName || '',
        value: item.companyName || ''
      }
    })
  } catch (e: any) {
    searchProps.value[0].options = []
  }
}
const handleDelete = async (item: any) => {
  try {
    const { data } = await opsPersonnelAPI.deleteOperationUser(
      { id: item.id },
      true
    )
    if (data.code === '200') {
      ElMessage({
        message: `删除成功!`,
        type: 'success'
      })
      getTableData()
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
