<template>
  <div class="ops-page">
    <div class="page-operate">
      <div class="operate-title">{{ computedTitle }}</div>
      <el-button v-if="pageStatus === 'detail'" type="primary" @click="edit">
        编辑
      </el-button>
      <div v-if="pageStatus === 'edit'">
        <el-button type="default" plain @click="cancelEdit"> 取消 </el-button>
        <el-button type="primary" :loading="btnLoading" @click="save">
          提交
        </el-button>
      </div>
    </div>
    <div v-if="computedShowCom" class="info-base">
      <company
        ref="companyRef"
        :page-status="pageStatus"
        :detail-data="detailData"
        @get-company-data="getCompanyData"
      />
    </div>
    <div v-if="computedShowCom" class="info-base">
      <base-info
        ref="baseInfoRef"
        :page-status="pageStatus"
        :detail-data="detailData"
        @get-base-info-data="getBaseInfoData"
      />
    </div>
    <div v-if="computedShowCom && pageStatus != 'add'" class="info-base">
      <person-cre :detail-data="detailData" />
    </div>
    <div v-if="computedShowCom" class="info-base">
      <insurance
        ref="insuranceRef"
        :page-status="pageStatus"
        :detail-data="detailData"
        @get-insurance-data="getInsuranceData"
      />
    </div>
    <div v-if="computedShowCom" class="info-base">
      <credentials
        ref="credentialsRef"
        :page-status="pageStatus"
        :detail-data="detailData"
        @get-credentials-data="getCredentialsData"
      />
    </div>
    <div
      v-if="computedShowCom && pageStatus != 'add'"
      class="info-base train-list"
      style="height: 678px"
    >
      <el-tabs v-model="activeTabName">
        <el-tab-pane label="培训记录" name="tabsOne">
          <train-list v-if="activeTabName == 'tabsOne'" />
        </el-tab-pane>
        <el-tab-pane label="考试记录" name="tabsTwo">
          <exam-list v-if="activeTabName == 'tabsTwo'" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="pageStatus !== 'detail'" class="info-base page-footer">
      <el-button type="default" plain @click="goback"> 返回 </el-button>
      <el-button type="primary" :loading="btnLoading" @click="save">
        提交
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import company from './components/company.vue'
import baseInfo from './components/baseInfo.vue'
import personCre from './components/personCre.vue'
import insurance from './components/insurance.vue'
import credentials from './components/credentials.vue'
import trainList from './components/trainList.vue'
import examList from './components/examList.vue'
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'

const router = useRouter()
const route = useRoute()
const pageStatus = ref(route.query.pageStatus as string)

const computedTitle = computed(() => {
  let txt = '新建运维人员'
  switch (pageStatus.value) {
    case 'add':
      txt = '新建运维人员'
      break
    case 'detail':
      txt = '查看详情'
      break
    case 'edit':
      txt = '查看详情'
      break
  }
  return txt
})
const computedShowCom = computed(() => {
  return (
    (pageStatus.value == 'detail' && detailData.value) ||
    pageStatus.value != 'detail'
  )
})
onMounted(async () => {
  if (route.query.personnelCode) {
    getDetail(route.query.personnelCode)
  }
})
// tab切换
const activeTabName = ref('tabsOne')

const detailData = ref()
const getDetail = async (id: any) => {
  try {
    const { data } = await opsPersonnelAPI.getOperationUser(
      { id: Number(id) },
      true
    )
    detailData.value = data.data
  } catch (e: any) {}
}
const edit = () => {
  pageStatus.value = 'edit'
}
const goback = () => {
  router.go(-1)
}
const cancelEdit = () => {
  companyRef.value.resetForm()
  baseInfoRef.value.resetForm()
  insuranceRef.value.resetForm()
  credentialsRef.value.resetForm()
  location.reload()
}
// 所属运维公司
const companyData = ref({})
const companyValid = ref(false)
const companyRef = ref<any>(null)
const getCompanyData = (val: any) => {
  companyValid.value = val.valid
  if (val.valid) {
    companyData.value = val.data
  }
}
// 基础信息
const baseInfoData = ref({})
const baseInfoValid = ref(false)
const baseInfoRef = ref<any>(null)
const getBaseInfoData = (val: any) => {
  baseInfoValid.value = val.valid
  if (val.valid) {
    baseInfoData.value = val.data
  }
}
// 保险及其他材料
const insuranceData = ref({})
const insuranceValid = ref(false)
const insuranceRef = ref<any>(null)
const getInsuranceData = (val: any) => {
  insuranceValid.value = val.valid
  if (val.valid) {
    insuranceData.value = val.data
  }
}
// 持有证件
const credentialsData = ref({})
const credentialsValid = ref(false)
const credentialsRef = ref<any>(null)
const getCredentialsData = (val: any) => {
  credentialsValid.value = val.valid
  if (val.valid) {
    credentialsData.value = val.data
  }
}

const btnLoading = ref(false)
const save = async () => {
  await companyRef.value.submitForm()
  await baseInfoRef.value.submitForm()
  await insuranceRef.value.submitForm()
  await credentialsRef.value.submitForm()
  if (
    !companyValid.value ||
    !baseInfoValid.value ||
    !insuranceValid.value ||
    !credentialsValid.value
  ) {
    return
  }

  try {
    let result: any = {}
    btnLoading.value = true
    const params = {
      ...companyData.value,
      ...baseInfoData.value,
      ...insuranceData.value,
      ...credentialsData.value,
      id: detailData.value?.id || null
    }
    if (detailData.value?.id) {
      result = await opsPersonnelAPI.updateOperationUser(params, true)
    } else {
      result = await opsPersonnelAPI.saveOperationUser(params, true)
    }
    const data = result.data
    if (data.code === '200') {
      ElMessage({
        message: `${pageStatus.value == 'edit' ? '编辑' : '保存'}成功!`,
        type: 'success'
      })
      router.go(-1)
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
  btnLoading.value = false
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.ops-page {
  padding-bottom: 24px;
}
.page-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
  line-height: 64px;
  margin: 24px;
  height: 64px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  & > .el-button + .el-button {
    margin-left: 16px !important;
  }
}
.el-button {
  width: 72px !important;
  height: 40px !important;
}
.info-base {
  padding-bottom: 0 !important;
  margin-bottom: 0px !important;
}
.train-list {
  padding-bottom: 24px !important;
}
</style>
