<template>
  <div :class="pageStatus === 'detail' ? 'cls-detail' : ''">
    <div class="base">
      保险及其他材料
      <div v-if="pageStatus !== 'detail'" class="tip">
        <el-icon color="#FF9900" size="16px"><WarningFilled /></el-icon>
        <div class="txt">提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件</div>
      </div>
    </div>

    <el-form
      ref="insuranceRef"
      :model="formData"
      label-suffix=""
      label-width="150px"
    >
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="社保参保证明" prop="socialSecurityCertificate">
            <SpicUpload
              v-model="socialSecurityFiles"
              type="image"
              :limit="5"
              :disabled="pageStatus === 'detail'"
            />
            <span
              v-if="
                (!socialSecurityFiles || !socialSecurityFiles.length) &&
                pageStatus === 'detail'
              "
            >
              --
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="工伤证明" prop="workInjuryCertificate">
            <SpicUpload
              v-model="workInjuryFiles"
              type="image"
              :limit="5"
              :disabled="pageStatus === 'detail'"
            />
            <span
              v-if="
                (!workInjuryFiles || !workInjuryFiles.length) &&
                pageStatus === 'detail'
              "
            >
              --
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="体检报告" prop="physicalExamination">
            <SpicUpload
              v-model="physicalFiles"
              type="image"
              :limit="5"
              :disabled="pageStatus === 'detail'"
            />
            <span
              v-if="
                (!physicalFiles || !physicalFiles.length) &&
                pageStatus === 'detail'
              "
            >
              --
            </span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import SpicUpload from '@/components/spic-upload'
import type { FormInstance } from 'element-plus'
const props = defineProps({
  pageStatus: {
    type: String,
    require: true,
    default: 'add'
  },
  detailData: {
    type: Object,
    require: false,
    default: null
  }
})
const formData = ref({
  socialSecurityCertificate: props?.detailData?.socialSecurityCertificate || '',
  workInjuryCertificate: props?.detailData?.workInjuryCertificate || '',
  physicalExamination: props?.detailData?.physicalExamination || ''
})

const socialSecurityFiles = ref<any[]>([])
const workInjuryFiles = ref<any[]>([])
const physicalFiles = ref<any[]>([])

if (props?.detailData?.socialSecurityCertificate) {
  socialSecurityFiles.value =
    props.detailData.socialSecurityCertificate.split(',')
}
if (props?.detailData?.workInjuryCertificate) {
  workInjuryFiles.value = props.detailData.workInjuryCertificate.split(',')
}
if (props?.detailData?.physicalExamination) {
  physicalFiles.value = props.detailData.physicalExamination.split(',')
}
// 提交
const insuranceRef = ref<FormInstance>()

watch(
  [
    () => socialSecurityFiles.value,
    () => workInjuryFiles.value,
    () => physicalFiles.value
  ],
  ([val1, val2, val3]) => {
    formData.value.socialSecurityCertificate = val1.length ? val1.join(',') : ''
    formData.value.workInjuryCertificate = val2.length ? val2.join(',') : ''
    formData.value.physicalExamination = val3.length ? val3.join(',') : ''
  },
  {
    deep: true
  }
)

const emit = defineEmits(['getInsuranceData'])
const submitForm = async () => {
  if (!insuranceRef.value) return
  await insuranceRef.value.validate((valid) => {
    emit('getInsuranceData', {
      valid,
      data: { ...formData.value }
    })
    return valid
  })
}
// 重置
const resetForm = () => {
  if (!insuranceRef.value) return
  insuranceRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})
</script>

<style scoped src="../style.scss"></style>
