<template>
  <div class="tables">
    <vis-table-pagination
      :loading="tableLoading"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchData.pageSize"
      :current-page="searchData.pageNum"
      :columns="columns"
      :total="listTotal"
      :data="listData"
      :show-overflow-tooltip="true"
      background
      height="528px"
      class="vis-table-pagination"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    >
      <template #examType="{ row }">
        {{ filters.examType(row.examType) }}
      </template>
      <template #examGrade="{ row }">
        {{ getGrade(row) }}
      </template>
      <template #examResult="{ row }">
        {{ filters.examResult(row.examResult) }}
      </template>
    </vis-table-pagination>
  </div>
</template>
<script setup lang="ts">
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import filters from '@/utils/filter.js'
import { formatTime } from '../personnel.ts'

const route = useRoute()

const columns = [
  {
    prop: 'examType',
    label: '考试类型',
    slotName: 'examType',
    minWidth: '118'
  },
  {
    prop: 'examTitle',
    label: '考试主题'
  },
  {
    prop: 'examStartTime',
    label: '考试开始时间',
    minWidth: '110'
  },
  {
    prop: 'examEndTime',
    label: '考试结束时间',
    minWidth: '110'
  },
  {
    prop: 'examGrade',
    label: '考试成绩',
    slotName: 'examGrade'
  },
  {
    prop: 'examResult',
    label: '考试结果',
    slotName: 'examResult'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
onMounted(async () => {
  await getTableData()
})
const tableLoading = ref(false)
const searchData = ref({
  id: route.query.personnelCode,
  pageNum: 1,
  pageSize: 10
})
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}
const getTableData = async () => {
  try {
    let { data } = await opsPersonnelAPI.getExamRecord(searchData.value, [
      tableLoading
    ])
    listTotal.value = data.data.total
    data.data.records.forEach((item: any) => {
      item.trainingRecordUrl = item.trainingRecordUrl
        ? item.trainingRecordUrl.split(',')
        : []
      item.examStartTime = formatTime(item.examStartTime)
      item.examEndTime = formatTime(item.examEndTime)
    })
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const getGrade = (row: any) => {
  if (!row.makeUpExamGrade) return row.examGrade
  return Number(row.makeUpExamGrade) > Number(row.examGrade)
    ? row.makeUpExamGrade
    : row.examGrade
}
</script>
