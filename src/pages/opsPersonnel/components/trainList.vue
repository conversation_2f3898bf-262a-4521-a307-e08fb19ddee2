<template>
  <div class="tables">
    <vis-table-pagination
      :loading="tableLoading"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchData.pageSize"
      :current-page="searchData.pageNum"
      :columns="columns"
      :total="listTotal"
      :data="listData"
      :show-overflow-tooltip="true"
      background
      height="528px"
      class="vis-table-pagination"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    >
      <template #trainingType="{ row }">
        {{ filters.trainType(row.trainingType) }}
      </template>
      <template #trainingLevel="{ row }">
        {{ filters.trainLevel(row.trainingLevel) }}
      </template>

      <template #trainingRecordUrl="{ row }">
        <template v-if="row.trainingRecordUrl?.length">
          <SpicUpload
            v-model="row.trainingRecordUrl"
            type="image"
            :limit="5"
            :disabled="true"
          />
        </template>
        <template v-else> -- </template>
      </template>
    </vis-table-pagination>
  </div>
</template>
<script setup lang="ts">
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import filters from '@/utils/filter.js'
import { formatTime } from '../personnel.ts'

const route = useRoute()

const columns = [
  {
    prop: 'trainingType',
    label: '培训类型',
    slotName: 'trainingType',
    minWidth: '130'
  },
  {
    prop: 'trainingLevel',
    label: '培训级别',
    slotName: 'trainingLevel'
  },
  {
    prop: 'trainingTitle',
    label: '培训主题'
  },
  {
    prop: 'beginTime',
    label: '培训开始时间',
    minWidth: '110'
  },
  {
    prop: 'endTime',
    label: '培训结束时间',
    minWidth: '110'
  },
  {
    prop: 'trainingRecordUrl',
    label: '培训记录',
    minWidth: '170',
    slotName: 'trainingRecordUrl'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
onMounted(async () => {
  await getTableData()
})
const tableLoading = ref(false)
const searchData = ref({
  id: route.query.personnelCode,
  pageNum: 1,
  pageSize: 10
})
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}
const getTableData = async () => {
  try {
    let { data } = await opsPersonnelAPI.getTrainRecord(searchData.value, [
      tableLoading
    ])
    listTotal.value = data.data.total
    data.data.records.forEach((item: any) => {
      item.trainingRecordUrl = item.trainingRecordUrl
        ? item.trainingRecordUrl.split(',')
        : []
      item.beginTime = formatTime(item.beginTime)
      item.endTime = formatTime(item.endTime)
    })
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
</script>
