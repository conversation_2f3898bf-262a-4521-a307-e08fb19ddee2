<template>
  <div class="credential" :class="pageStatus === 'detail' ? 'cls-detail' : ''">
    <div class="base">
      持有证件
      <div v-if="pageStatus !== 'detail'" class="tip">
        <el-icon color="#FF9900" size="16px"><WarningFilled /></el-icon>
        <div class="txt">提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件</div>
      </div>
    </div>

    <el-form
      ref="credentialsRef"
      :model="formData"
      label-suffix=""
      label-width="154px"
    >
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="安全员证" prop="securityMemberCard">
            <SpicUpload
              v-model="securityMemberFiles"
              type="image"
              :handle-remove-fn="handleRemovesecurityMemberFn"
              :limit="5"
              :disabled="pageStatus === 'detail'"
            />
            <span
              v-if="
                (!securityMemberFiles || !securityMemberFiles.length) &&
                pageStatus === 'detail'
              "
            >
              --
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item
            label="安全员证有效期"
            prop="securityMemberCardTimeRange"
          >
            <span v-if="pageStatus === 'detail'">
              {{
                formData.securityMemberCardStartDate &&
                formData.securityMemberCardEndDate
                  ? `${formData.securityMemberCardStartDate.slice(
                      0,
                      10
                    )}至${formData.securityMemberCardEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="securityMemberCardTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled="!securityMemberFiles.length || !securityMemberFiles"
            />
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="低压工作证" prop="lowPressureWorkCard">
            <SpicUpload
              v-model="lowPressureWorkFiles"
              type="image"
              :handle-remove-fn="handleRemoveLowPressureWorkFn"
              :limit="5"
              :disabled="pageStatus === 'detail'"
            />
            <span
              v-if="
                (!lowPressureWorkFiles || !lowPressureWorkFiles.length) &&
                pageStatus === 'detail'
              "
            >
              --
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item
            label="低压工作证有效期"
            prop="lowPressureWorkCardTimeRange"
          >
            <span v-if="pageStatus === 'detail'">
              {{
                formData.lowPressureWorkCardStartDate &&
                formData.lowPressureWorkCardEndDate
                  ? `${formData.lowPressureWorkCardStartDate.slice(
                      0,
                      10
                    )}至${formData.lowPressureWorkCardEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="lowPressureWorkCardTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled="!lowPressureWorkFiles.length || !lowPressureWorkFiles"
            />
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="高压工作证" prop="highElectricianCard">
            <SpicUpload
              v-model="highElectricianFiles"
              type="image"
              :handle-remove-fn="handleRemoveHighElectricianFn"
              :limit="5"
              :disabled="pageStatus === 'detail'"
            />
            <span
              v-if="
                (!highElectricianFiles || !highElectricianFiles.length) &&
                pageStatus === 'detail'
              "
            >
              --
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item
            label="高压工作证有效期"
            prop="highElectricianCardTimeRange"
          >
            <span v-if="pageStatus === 'detail'">
              {{
                formData.highElectricianCardStartDate &&
                formData.highElectricianCardEndDate
                  ? `${formData.highElectricianCardStartDate.slice(
                      0,
                      10
                    )}至${formData.highElectricianCardEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="highElectricianCardTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled="!highElectricianFiles.length || !highElectricianFiles"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="高处作业操作证" prop="highPressureWorkCard">
            <SpicUpload
              v-model="highPressureWorkFiles"
              type="image"
              :handle-remove-fn="handleRemoveHighPressureWorkFn"
              :limit="5"
              :disabled="pageStatus === 'detail'"
            />
            <span
              v-if="
                (!highPressureWorkFiles || !highPressureWorkFiles.length) &&
                pageStatus === 'detail'
              "
            >
              --
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item
            label="高处作业操作证有效期"
            prop="highPressureWorkCardTimeRange"
          >
            <span v-if="pageStatus === 'detail'">
              {{
                formData.highPressureWorkCardStartDate &&
                formData.highPressureWorkCardEndDate
                  ? `${formData.highPressureWorkCardStartDate.slice(
                      0,
                      10
                    )}至${formData.highPressureWorkCardEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="highPressureWorkCardTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled="
                !highPressureWorkFiles.length || !highPressureWorkFiles
              "
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import SpicUpload from '@/components/spic-upload'
import type { FormInstance } from 'element-plus'

const props = defineProps({
  pageStatus: {
    type: String,
    require: true,
    default: 'add'
  },
  detailData: {
    type: Object,
    require: false,
    default: null
  }
})

const formData = ref<Record<string, any>>({
  securityMemberCard: props?.detailData?.securityMemberCard || '', // 安全员证
  securityMemberCardStartDate:
    props?.detailData?.securityMemberCardStartDate || '',
  securityMemberCardEndDate: props?.detailData?.securityMemberCardEndDate || '',
  lowPressureWorkCard: props?.detailData?.lowPressureWorkCard || '', // 低压
  lowPressureWorkCardStartDate:
    props?.detailData?.lowPressureWorkCardStartDate || '',
  lowPressureWorkCardEndDate:
    props?.detailData?.lowPressureWorkCardEndDate || '',
  highElectricianCard: props?.detailData?.highElectricianCard || '', // 高压
  highElectricianCardStartDate:
    props?.detailData?.highElectricianCardStartDate || '',
  highElectricianCardEndDate:
    props?.detailData?.highElectricianCardEndDate || '',
  highPressureWorkCard: props?.detailData?.highPressureWorkCard || '', //  高处操作工作证
  highPressureWorkCardStartDate:
    props?.detailData?.highPressureWorkCardStartDate || '',
  highPressureWorkCardEndDate:
    props?.detailData?.highPressureWorkCardEndDate || ''
})

const securityMemberFiles = ref<any[]>([])
const securityMemberCardTimeRange = ref<any[]>([])
const lowPressureWorkFiles = ref<any[]>([])
const lowPressureWorkCardTimeRange = ref<any[]>([])
const highElectricianFiles = ref<any[]>([])
const highElectricianCardTimeRange = ref<any[]>([])
const highPressureWorkFiles = ref<any[]>([])
const highPressureWorkCardTimeRange = ref<any[]>([])

if (props?.detailData?.securityMemberCard) {
  securityMemberFiles.value = props.detailData.securityMemberCard.split(',')
}
if (
  props?.detailData?.securityMemberCardStartDate &&
  props?.detailData?.securityMemberCardEndDate
) {
  securityMemberCardTimeRange.value = [
    props.detailData.securityMemberCardStartDate.slice(0, 10),
    props.detailData.securityMemberCardEndDate.slice(0, 10)
  ]
}
if (props?.detailData?.lowPressureWorkCard) {
  lowPressureWorkFiles.value = props.detailData.lowPressureWorkCard.split(',')
}
if (
  props?.detailData?.lowPressureWorkCardStartDate &&
  props?.detailData?.lowPressureWorkCardEndDate
) {
  lowPressureWorkCardTimeRange.value = [
    props.detailData.lowPressureWorkCardStartDate.slice(0, 10),
    props.detailData.lowPressureWorkCardEndDate.slice(0, 10)
  ]
}

if (props?.detailData?.highElectricianCard) {
  highElectricianFiles.value = props.detailData.highElectricianCard.split(',')
}
if (
  props?.detailData?.highElectricianCardStartDate &&
  props?.detailData?.highElectricianCardEndDate
) {
  highElectricianCardTimeRange.value = [
    props.detailData.highElectricianCardStartDate.slice(0, 10),
    props.detailData.highElectricianCardEndDate.slice(0, 10)
  ]
}

if (props?.detailData?.highPressureWorkCard) {
  highPressureWorkFiles.value = props.detailData.highPressureWorkCard.split(',')
}
if (
  props?.detailData?.highPressureWorkCardStartDate &&
  props?.detailData?.highPressureWorkCardEndDate
) {
  highPressureWorkCardTimeRange.value = [
    props.detailData.highPressureWorkCardStartDate.slice(0, 10),
    props.detailData.highPressureWorkCardEndDate.slice(0, 10)
  ]
}

// 提交
const credentialsRef = ref<FormInstance>()
watch(
  [
    () => securityMemberFiles.value,
    () => lowPressureWorkFiles.value,
    () => highElectricianFiles.value,
    () => highPressureWorkFiles.value
  ],
  ([val1, val2, val3, val4]) => {
    formData.value.securityMemberCard = val1.length ? val1.join(',') : ''
    formData.value.lowPressureWorkCard = val2.length ? val2.join(',') : ''
    formData.value.highElectricianCard = val3.length ? val3.join(',') : ''
    formData.value.highPressureWorkCard = val4.length ? val4.join(',') : ''
  },
  {
    deep: true
  }
)

const handleRemovesecurityMemberFn = () => {
  if (securityMemberFiles.value.length <= 0) {
    formData.value.securityMemberCardStartDate = ''
    formData.value.securityMemberCardEndDate = ''
    securityMemberCardTimeRange.value = []
  }
}
const handleRemoveLowPressureWorkFn = () => {
  if (lowPressureWorkFiles.value.length <= 0) {
    formData.value.lowPressureWorkCardStartDate = ''
    formData.value.lowPressureWorkCardEndDate = ''
    lowPressureWorkCardTimeRange.value = []
  }
}
const handleRemoveHighElectricianFn = () => {
  if (highElectricianFiles.value.length <= 0) {
    formData.value.highElectricianCardStartDate = ''
    formData.value.highElectricianCardEndDate = ''
    highElectricianCardTimeRange.value = []
  }
}
const handleRemoveHighPressureWorkFn = () => {
  if (highPressureWorkFiles.value.length <= 0) {
    formData.value.highPressureWorkCardStartDate = ''
    formData.value.highPressureWorkCardEndDate = ''
    highPressureWorkCardTimeRange.value = []
  }
}

const emit = defineEmits(['getCredentialsData'])
const submitForm = async () => {
  if (!credentialsRef.value) return

  await credentialsRef.value.validate((valid) => {
    dealEffecteTime()
    emit('getCredentialsData', {
      valid,
      data: { ...formData.value }
    })
    return valid
  })
}
// 处理有效期开始时间和结束时间
const dealEffecteTime = () => {
  if (
    securityMemberFiles.value?.length &&
    securityMemberCardTimeRange.value?.length
  ) {
    formData.value.securityMemberCardStartDate =
      securityMemberCardTimeRange.value[0] + ' 00:00:00'
    formData.value.securityMemberCardEndDate =
      securityMemberCardTimeRange.value[1] + ' 23:59:59'
  } else {
    formData.value.securityMemberCardStartDate = ''
    formData.value.securityMemberCardEndDate = ''
  }
  if (
    lowPressureWorkFiles.value?.length &&
    lowPressureWorkCardTimeRange.value?.length
  ) {
    formData.value.lowPressureWorkCardStartDate =
      lowPressureWorkCardTimeRange.value[0] + ' 00:00:00'
    formData.value.lowPressureWorkCardEndDate =
      lowPressureWorkCardTimeRange.value[1] + ' 23:59:59'
  } else {
    formData.value.lowPressureWorkCardStartDate = ''
    formData.value.lowPressureWorkCardEndDate = ''
  }
  if (
    highElectricianFiles.value?.length &&
    highElectricianCardTimeRange.value?.length
  ) {
    formData.value.highElectricianCardStartDate =
      highElectricianCardTimeRange.value[0] + ' 00:00:00'
    formData.value.highElectricianCardEndDate =
      highElectricianCardTimeRange.value[1] + ' 23:59:59'
  } else {
    formData.value.highElectricianCardStartDate = ''
    formData.value.highElectricianCardEndDate = ''
  }
  if (
    highPressureWorkFiles.value?.length &&
    highPressureWorkCardTimeRange.value?.length
  ) {
    formData.value.highPressureWorkCardStartDate =
      highPressureWorkCardTimeRange.value[0] + ' 00:00:00'
    formData.value.highPressureWorkCardEndDate =
      highPressureWorkCardTimeRange.value[1] + ' 23:59:59'
  } else {
    formData.value.highPressureWorkCardStartDate = ''
    formData.value.highPressureWorkCardEndDate = ''
  }
}

// 重置
const resetForm = () => {
  if (!credentialsRef.value) return
  credentialsRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})
</script>
<style scoped src="../style.scss"></style>
