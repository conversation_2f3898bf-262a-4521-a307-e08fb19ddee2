<template>
  <div :class="pageStatus === 'detail' ? 'cls-detail' : ''">
    <div class="base">所属运维公司</div>
    <el-form
      ref="companyFormRef"
      :model="formData"
      label-suffix=""
      label-width="150px"
      :rules="pageStatus === 'detail' ? companyRulesNo : companyRules"
    >
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="公司名称" prop="companyName">
            <span v-if="pageStatus === 'detail'">
              {{ formData.companyName || '--' }}
            </span>
            <el-input
              v-else-if="pageStatus === 'edit'"
              v-model="formData.companyName"
              placeholder="请选择公司名称"
              disabled
            />
            <el-select
              v-else
              v-model="formData.companyName"
              placeholder="请选择公司名称"
              filterable
              @change="changeCompany"
            >
              <el-option
                v-for="item in companyList"
                :key="item.id"
                :label="item.companyName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="公司负责人" prop="companyLeader">
            <span v-if="pageStatus === 'detail'">
              {{ companyLeaderName }}
            </span>
            <el-input
              v-else
              v-model="companyLeaderName"
              placeholder="请输入公司负责人"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'

const companyLeaderName = computed(() => {
  return formData.value.companyLeader
    ? formData.value.companyLeader?.split('_')?.[0]
    : '--'
})
const props = defineProps({
  pageStatus: {
    type: String,
    require: true,
    default: 'add'
  },
  detailData: {
    type: Object,
    require: false,
    default: null
  }
})

const params = ref({
  operatorsId: '' // 运维商id
})

const formData = ref({
  companyName: props?.detailData?.companyName || '', // 公司名称
  companyLeader: props?.detailData?.companyLeader || '' // 公司负责人
})
const companyRules = reactive<FormRules>({
  companyName: [
    { required: true, message: '请选择公司名称', trigger: 'change' }
  ],
  companyLeader: [
    { required: true, message: '请输入公司负责人', trigger: 'change' }
  ]
})
const companyRulesNo = reactive<FormRules>({})

onMounted(async () => {
  getCompany()
})
const companyList = ref()
const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    companyList.value = data.data
  } catch (e: any) {
    companyList.value = []
  }
}
// 修改公司名称
const changeCompany = (val: any) => {
  for (let i = 0; i < companyList.value.length; i++) {
    if (companyList.value[i].id == val) {
      formData.value.companyLeader = companyList.value[i].companyLeader
      params.value.operatorsId = companyList.value[i].id
      break
    }
  }
}
// 提交
const companyFormRef = ref<FormInstance>()
const emit = defineEmits(['getCompanyData'])
const submitForm = async () => {
  if (!companyFormRef.value) return
  await companyFormRef.value.validate((valid) => {
    emit('getCompanyData', {
      valid,
      data: { ...params.value }
    })
    return valid
  })
}
// 重置
const resetForm = () => {
  if (!companyFormRef.value) return
  companyFormRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})
</script>

<style scoped src="../style.scss"></style>
