<template>
  <div :class="pageStatus === 'detail' ? 'cls-detail' : ''">
    <div class="base">基础信息</div>
    <el-form
      ref="baseInfoRef"
      :model="formData"
      label-suffix=""
      label-width="150px"
      :rules="pageStatus === 'detail' ? formRulesNo : formRules"
    >
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="姓名" prop="ucUserId">
            <span v-if="pageStatus === 'detail'">
              {{ formData.userName || '--' }}
            </span>
            <el-select
              v-else
              v-model="formData.ucUserId"
              placeholder="请选择姓名"
              filterable
              @change="changeUser"
            >
              <el-option
                v-for="item in personnelList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="身份证号" prop="identityCard">
            <span v-if="pageStatus === 'detail'">
              {{ formData.identityCard || '--' }}
            </span>
            <el-input
              v-else
              v-model.trim="formData.identityCard"
              placeholder="请输入身份证号"
              maxlength="100"
            />
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="身份证照片" prop="cardPhoto">
            <SpicUpload
              v-model="files"
              type="image"
              :handle-remove-fn="handleRemoveFn"
              :limit="5"
              :disabled="pageStatus === 'detail'"
            />
            <div v-if="pageStatus !== 'detail'" class="tip tip-img">
              <el-icon color="#FF9900" size="16px"><WarningFilled /></el-icon>
              <div class="txt">
                提示：图片大小不超过5MB，格式为png/jpg/jpeg的文件
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="身份证有效期" prop="identityCardTimeRange">
            <span v-if="pageStatus === 'detail'">
              {{
                formData.identityCardStartDate && formData.identityCardEndDate
                  ? `${formData.identityCardStartDate.slice(
                      0,
                      10
                    )}至${formData.identityCardEndDate.slice(0, 10)}`
                  : '--'
              }}
            </span>
            <el-date-picker
              v-else
              v-model="identityCardTimeRange"
              type="daterange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="联系电话" prop="userPhone">
            <span v-if="pageStatus === 'detail'">
              {{ formData.userPhone || '--' }}
            </span>
            <el-input
              v-else
              v-model.trim="formData.userPhone"
              placeholder="请输入联系电话"
              maxlength="11"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="年龄" prop="userAge">
            <span v-if="pageStatus === 'detail'">
              {{ formData.userAge || '--' }}
            </span>
            <el-input
              v-else
              v-model.trim="formData.userAge"
              placeholder="请输入年龄"
              maxlength="2"
              clearable
              @input="() => limitGroupNum(formData.userAge)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="性别" prop="userSex">
            <span v-if="pageStatus === 'detail'">
              <span v-if="formData.userSex === 1">男</span>
              <span v-if="formData.userSex === 2">女</span>
            </span>
            <el-radio-group v-else v-model="formData.userSex" class="ml-4">
              <el-radio :label="1" size="large">男</el-radio>
              <el-radio :label="2" size="large">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="运维岗位级别" prop="positionLevel">
            <span v-if="pageStatus === 'detail'">
              {{ formData.positionLevel || '--' }}
            </span>
            <el-input
              v-else
              v-model.trim="formData.positionLevel"
              placeholder="请输入运维岗位级别"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="户口所在地" prop="householdRegistration">
            <span v-if="pageStatus === 'detail'">
              {{ formData.householdRegistration || '--' }}
            </span>
            <el-input
              v-else
              v-model.trim="formData.householdRegistration"
              placeholder="请输入户口所在地"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="劳动合同签署公司" prop="contractCompany">
            <span v-if="pageStatus === 'detail'">
              {{ formData.contractCompany || '--' }}
            </span>
            <el-input
              v-else
              v-model.trim="formData.contractCompany"
              placeholder="请输入劳动合同签署公司"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="运维省份" prop="operationProvince">
            <span v-if="pageStatus === 'detail'">
              {{ formData.operationProvince || '--' }}
            </span>
            <el-input
              v-else
              v-model.trim="formData.operationProvince"
              placeholder="请输入运维省份"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="主要负责区域" prop="undertakeRegion">
            <span v-if="pageStatus === 'detail'">
              {{ formData.undertakeRegion || '--' }}
            </span>
            <el-input
              v-else
              v-model.trim="formData.undertakeRegion"
              placeholder="请输入主要负责区域"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import SpicUpload from '@/components/spic-upload'
import type { FormInstance, FormRules } from 'element-plus'
import { approval as approvalAPI } from '@/api/index.ts'

const props = defineProps({
  pageStatus: {
    type: String,
    require: true,
    default: 'add'
  },
  detailData: {
    type: Object,
    require: false,
    default: null
  }
})

const formData = ref<Record<string, any>>({
  ucUserId: props?.detailData?.ucUserId || '', // 姓名
  userName: props?.detailData?.userName || '', // 姓名
  identityCard: props?.detailData?.identityCard || '', // 身份证号
  cardPhoto: props?.detailData?.cardPhoto || '', // 身份证照片
  identityCardStartDate: props?.detailData?.identityCardStartDate || '', //
  identityCardEndDate: props?.detailData?.identityCardEndDate || '', //
  userPhone: props?.detailData?.userPhone || '', // 联系电话
  userAge: props?.detailData?.userAge || '', // 年龄
  userSex: props?.detailData?.userSex || '', // 性别
  positionLevel: props?.detailData?.positionLevel || '', // 运维岗位级别
  householdRegistration: props?.detailData?.householdRegistration || '', // 户口所在地
  contractCompany: props?.detailData?.contractCompany || '', // 劳动合同签署公司
  operationProvince: props?.detailData?.operationProvince || '', // 运维省份
  undertakeRegion: props?.detailData?.undertakeRegion || '' // 主要负责区域
})

const formRulesidentityCard = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入身份证号'))
  } else {
    if (!/^[0-9A-Za-z]+$/.test(value)) {
      callback(new Error('请输入正确身份证号'))
    } else {
      callback()
    }
  }
}
const formRulesphone = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入联系电话'))
  } else {
    if (!/^1\d{10}$/.test(value)) {
      callback(new Error('请输入11位联系电话'))
    } else {
      callback()
    }
  }
}
const formRulesage = (_rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入年龄，年龄最大为99'))
  } else {
    if (!/^(?:[1-9]\d*|0)$/.test(value)) {
      callback(new Error('输入有误，年龄最大为99'))
    } else {
      callback()
    }
  }
}
const formRulesNo = reactive<FormRules>({})
const formRules = reactive<FormRules>({
  ucUserId: [
    {
      required: true,
      message: '请选择姓名',
      trigger: 'change'
    }
  ],
  identityCard: [
    {
      required: true,
      trigger: 'blur',
      validator: formRulesidentityCard
    }
  ],
  cardPhoto: [
    { required: true, message: '请上传身份证照片', trigger: 'change' }
  ],
  userPhone: [
    {
      required: true,
      trigger: 'blur',
      validator: formRulesphone
    }
  ],
  userAge: [
    {
      required: true,
      trigger: 'blur',
      validator: formRulesage
    }
  ],
  userSex: [
    {
      required: true,
      message: '请选择性别',
      trigger: 'change'
    }
  ]
})

const limitGroupNum = (val: any) => {
  if (val === '0') {
    formData.value.userAge = ''
  } else {
    formData.value.userAge = String(val).replace(/^(0)|\D/g, '') || ''
  }
}

const identityCardTimeRange = ref<any[]>([])
if (
  props?.detailData?.identityCardStartDate &&
  props?.detailData?.identityCardEndDate
) {
  identityCardTimeRange.value = [
    props.detailData.identityCardStartDate.slice(0, 10),
    props.detailData.identityCardEndDate.slice(0, 10)
  ]
}
onMounted(async () => {
  getAllPersonnel()
})

// 提交
const baseInfoRef = ref<FormInstance>()
let files = ref<any[]>([])

if (props?.detailData?.cardPhoto) {
  files.value = props?.detailData?.cardPhoto.split(',')
}

watch(
  () => files.value,
  (val) => {
    if (val.length) {
      formData.value.cardPhoto = val.join(',')
      baseInfoRef.value?.validateField('cardPhoto')
    } else {
      formData.value.cardPhoto = ''
    }
  },
  {
    deep: true
  }
)
const handleRemoveFn = () => {
  if (files.value.length <= 0) {
    formData.value.identityCardStartDate = ''
    formData.value.identityCardEndDate = ''
    identityCardTimeRange.value = []
  }
}

const emit = defineEmits(['getBaseInfoData'])
const submitForm = async () => {
  if (!baseInfoRef.value) return
  await baseInfoRef.value.validate((valid) => {
    dealEffecteTime()
    emit('getBaseInfoData', {
      valid,
      data: { ...formData.value }
    })
    return valid
  })
}
// 处理有效期开始时间和结束时间
const dealEffecteTime = () => {
  if (identityCardTimeRange.value?.length) {
    formData.value.identityCardStartDate =
      identityCardTimeRange.value[0] + ' 00:00:00'
    formData.value.identityCardEndDate =
      identityCardTimeRange.value[1] + ' 23:59:59'
  } else {
    formData.value.identityCardStartDate = ''
    formData.value.identityCardEndDate = ''
  }
}
const personnelList = ref<any[]>([])
const getAllPersonnel = async () => {
  let { data } = await approvalAPI.getUserInfoList({
    pageSize: 10000,
    pageNo: 1
  })
  if (data && data.data) {
    personnelList.value = data.data.list
      .filter((item: any) => item.id != null)
      .map(({ employeeName, id, employeePhone }: any) => ({
        value: Number(id),
        label: employeeName,
        phone: employeePhone
      }))
  }
}

// 修改姓名，关联联系电话
const changeUser = (val: any) => {
  if (val) {
    const targetItem = personnelList.value?.find((item) => item.value == val)
    formData.value.userPhone = targetItem?.phone || ''
    formData.value.userName = targetItem?.label || ''
    formData.value.ucUserId = targetItem?.value || ''
  }
}
// 重置
const resetForm = () => {
  if (!baseInfoRef.value) return
  baseInfoRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})
</script>

<style scoped src="../style.scss"></style>
