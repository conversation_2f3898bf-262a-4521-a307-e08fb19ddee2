<template>
  <div class="base">人员资质</div>
  <el-row :gutter="24" justify="center">
    <el-col :span="8" justify="center">
      <img
        v-if="detailData.isWorkPermit"
        src="@/assets/images/opsPersonnel1_active.png"
        alt=""
      />
      <img v-else src="@/assets/images/opsPersonnel1.png" alt="" />
    </el-col>
    <el-col :span="8">
      <img
        v-if="detailData.isWorkLeader"
        src="@/assets/images/opsPersonnel2_active.png"
        alt=""
      />
      <img v-else src="@/assets/images/opsPersonnel2.png" alt="" />
    </el-col>
    <el-col :span="8">
      <img
        v-if="detailData.isWorkTicketIssuer"
        src="@/assets/images/opsPersonnel3_active.png"
        alt=""
      />
      <img v-else src="@/assets/images/opsPersonnel3.png" alt="" />
    </el-col>
  </el-row>
</template>
<script setup lang="ts">
defineProps({
  detailData: {
    type: Object,
    require: false,
    default: null
  }
})
</script>

<style scoped src="../style.scss"></style>
<style scoped lang="scss">
.el-row {
  padding-bottom: 24px;
  .el-col-8 {
    display: flex;
    justify-content: center;
  }
  img {
    width: 320px;
  }
}
</style>
