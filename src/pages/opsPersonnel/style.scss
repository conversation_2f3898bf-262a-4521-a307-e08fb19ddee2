.base {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  // line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
}
.tip {
  font-size: 12px;
  font-weight: 400;
  color: #ff9900;
  line-height: 16px;
  display: flex;
  align-items: center;
  margin-left: 8px;
  &.tip-img {
    margin-top: 8px;
    margin-left: 0;
    flex-basis: 100%;
  }
  .txt {
    margin-left: 4px;
  }
}
.cls-detail {
  word-wrap: break-word;
  word-break: break-all;
}
.cls-detail :deep(.el-form-item__label) {
  color: rgba(0, 0, 0, 0.45) !important;
  line-height: 22px !important;
  height: 22px !important;
}
.cls-detail :deep(.el-form-item__content) {
  line-height: 22px !important;
}

.el-form-item__content > span {
  color: rgba(0, 0, 0, 0.85) !important;
}
