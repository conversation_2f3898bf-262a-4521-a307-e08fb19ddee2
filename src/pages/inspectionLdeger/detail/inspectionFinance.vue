<template>
  <!-- <p class="detailTitle">财务结算列表</p> -->
  <vis-table-pagination
    :columns="inspectionColumns"
    :data="detail.inspectionData"
    background
    :border="true"
    class="vis-table-pagination"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="10"
    :total="detail.total"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
</template>
<script setup>
import visTablePagination from '@/components/table-pagination.vue'
import { overhaul as api } from '@/api/index.ts'
let route = useRoute()

// 巡检记录1
const inspectionColumns = ref([
  {
    prop: 'settlementRules',
    label: '结算规则',
    formatter: (v) => {
      if (!v.settlementRules) return '--'
      if (v.settlementRules == 4) return '本单总价'
      return v.settlementRules
    }
  },
  {
    prop: 'unitPrice',
    label: '单价',
    formatter: (v) => {
      if (v.unitPrice == null || v.unitPrice == undefined) return '--'
      return v.unitPrice + '元'
    }
  },
  {
    prop: 'quantity',
    label: '数量'
  },
  {
    prop: 'unit',
    label: '单位'
  },
  {
    prop: 'price',
    label: '总价',
    formatter: (v) => {
      if (v.price == null || v.price == undefined) return '--'
      return v.price + '元'
    }
  }
])
// 获取详情信息接口
let detail = reactive({
  total: 0,
  inspectionData: [] // 维保任务
})
// 巡检记录
let params = ref({
  workOrderNo: route.params.id,
  pageNum: 1,
  pageSize: 10
})
const getFinancialSettlementInfoFun = async () => {
  try {
    let result = await api.getFinancialSettlementInfo(params.value)
    if (result.data.code == 200) {
      detail.inspectionData = result.data.data
      detail.total = result.data.total
    }
  } catch (e) {
    detail.inspectionData = []
    detail.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
const handleSizeChange = (pm) => {
  params.value.pageNum = 1
  params.value.pageSize = pm.pageSize
  getFinancialSettlementInfoFun()
}
const handleCurrentChange = (pm) => {
  params.value.pageNum = pm.currentPage
  getFinancialSettlementInfoFun()
}
onMounted(async () => {
  await getFinancialSettlementInfoFun() // 维保任务
})
</script>
