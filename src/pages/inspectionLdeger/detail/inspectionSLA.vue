<template>
  <vis-table-pagination
    :columns="inspectionColumns"
    :data="detail.inspectionData"
    background
    :border="true"
    class="vis-table-pagination"
    layout="total, sizes, prev, pager, next, jumper"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="10"
    :total="detail.total"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
</template>
<script setup>
import visTablePagination from '@/components/table-pagination.vue'
import { overhaul as api } from '@/api/index.ts'
import filters from '@/utils/filter.js'
let route = useRoute()
const inspectionColumns = ref([
  {
    prop: 'type',
    label: '统计分类'
  },
  {
    prop: 'state',
    label: '统计状态'
  },
  {
    prop: 'statisticsDuration',
    label: '统计时长'
  }
  // {
  //   prop: 'effectiveDuration',
  //   label: '有效时长'
  // }
])
let detail = reactive({
  total: 0,
  inspectionData: [] // 维保任务
})
let params = ref({
  workOrderNo: route.params.id,
  pageNum: 1,
  pageSize: 10
})
const getData = async () => {
  try {
    let result = await api.getSlaStatisticsInfo(params.value)
    if (result.data.code == 200) {
      if (result.data.data.length > 0) {
        result.data.data.forEach((value) => {
          value.type = filters.typeFilter(value.type)
          value.state = filters.stateFilter(value.state)
        })
      }
      detail.inspectionData = result.data.data
      detail.total = result.data.total
    }
  } catch (error) {
    console.info(error)
  }
}
const handleSizeChange = (pm) => {
  params.value.pageNum = 1
  params.value.pageSize = pm.pageSize
  getData()
}
const handleCurrentChange = (pm) => {
  params.value.pageNum = pm.currentPage
  getData()
}
onMounted(async () => {
  await getData()
})
</script>
