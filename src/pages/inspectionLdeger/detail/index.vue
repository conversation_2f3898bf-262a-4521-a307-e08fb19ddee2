<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-title">
        <el-icon class="backIcon"><ArrowLeft /></el-icon>
        <span class="goback" @click="() => router.go(-1)">返回上一级</span>
        <span class="detailLine">|</span>
        <router-link to="/inspection">巡检工单台账</router-link>
        <a>></a>
        <span>巡检工单详情</span>
      </div>
    </div>
    <div class="info-base">
      <div class="operate">
        <p>工单基本信息</p>
      </div>
      <el-descriptions>
        <el-descriptions-item label="工单编号：">{{
          detail.baseInfo.workOrderNo || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="工单标题：">
          {{ detail.baseInfo.workOrderName || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="工单模板：">
          {{ detail.baseInfo.orderModel || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="优先级：">
          {{ detail.baseInfo.priority || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="关联电站：">
          {{ detail.baseInfo.powerStation || '--' }}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="故障部件类型：">
          {{ detail.baseInfo.faultType || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="故障部件SN：">
          {{ detail.baseInfo.faultTypeSn || '--' }}
        </el-descriptions-item> -->
        <el-descriptions-item label="创建人：">
          {{ detail.baseInfo.createUser || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="处理人：">
          {{ detail.baseInfo.handlers || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="协同人：">
          {{ detail.baseInfo.xieTongRen || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="结束时间：">
          {{ detail.baseInfo.endTime || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="关联巡检计划：">{{
          detail.baseInfo.inspectionPlan || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="行政区划：">{{
          detail.baseInfo.administrativeDifferences ||
          detail.baseInfo.province +
            detail.baseInfo.city +
            detail.baseInfo.area ||
          '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="详细地址：">{{
          detail.baseInfo.detailAddress || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="经度：">{{
          detail.baseInfo.longitude || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="纬度：">{{
          detail.baseInfo.latitude || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="联系人：">{{
          detail.baseInfo.contactsName || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="联系电话：">{{
          detail.baseInfo.contactsPhone || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="备注：">{{
          detail.baseInfo.remark || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="工单类型：">{{
          detail.baseInfo.workType || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="工单状态：">{{
          detail.baseInfo.workState || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间：">{{
          detail.baseInfo.createTime || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="更新时间：">{{
          detail.baseInfo.updateTime || '--'
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div v-auto-height class="info-tab">
      <el-tabs v-model="activeTabName" class="demo-tabs">
        <el-tab-pane label="巡检记录" name="record">
          <el-scrollbar height="calc(100vh - 545px)" class="p-l-10px p-r-10px">
            <inspection-record></inspection-record>
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="工时统计" name="sla">
          <el-scrollbar height="calc(100vh - 545px)" class="p-l-10px p-r-10px">
            <inspection-sla class="mb-20px"></inspection-sla>
            <inspection-regist></inspection-regist>
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="款项结算" name="finance">
          <el-scrollbar height="calc(100vh - 545px)" class="p-l-10px p-r-10px">
            <inspection-finance></inspection-finance>
          </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="工单评价" name="evaluate">
          <el-scrollbar height="calc(100vh - 545px)" class="p-l-10px p-r-10px">
            <inspection-evaluate></inspection-evaluate>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script setup>
import filters from '@/utils/filter.js'
import inspectionRecord from './inspectionRecord.vue'
import inspectionSla from './inspectionSLA.vue'
import inspectionRegist from './inspectionRegist.vue'
import inspectionFinance from './inspectionFinance.vue'
import inspectionEvaluate from './inspectionEvaluate.vue'
const router = useRouter()
const activeTabName = ref('record')

// 获取详情信息接口
let detail = reactive({
  baseInfo: {}
})
// 基本信息
const getDetail = async () => {
  detail.baseInfo = JSON.parse(sessionStorage.getItem('itemInfo')) || {}
  detail.baseInfo.workType = detail.baseInfo?.workType
    ? filters.workTypeFilter(detail.baseInfo.workType)
    : null
}
onMounted(async () => {
  // 获取详情
  getDetail()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.info-base {
  padding: 24px 24px 12px 24px;
  margin: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
}

.info-tab {
  overflow: hidden;
  padding: 12px 24px;
  margin: 0 24px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
}
.file-list {
  .el-row {
    width: 100%;
  }
  .el-col {
    margin-bottom: 10px;
  }

  .file-wrapper {
    position: relative;
    z-index: 1;
    padding: 10px;
    padding-bottom: 7px;
    width: auto;
    background: #f6f8fa;
    border-radius: 8px;
    cursor: pointer;

    .file-wrapper-hover {
      position: absolute;
      inset: 0;
      z-index: 10;
      display: none;
      justify-content: center;
      align-items: center;
      background: rgb(0 0 0 / 68%);
      border-radius: 8px;

      .download-wrapper {
        position: relative;
        z-index: 11;

        img {
          display: block;
          margin-bottom: 0;
          width: 20px;
          height: 20px;
        }

        span {
          height: 18px;
          font-size: 13px;
          color: #fff;
          line-height: 18px;
        }
      }

      .close {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 12;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 20px;
        height: 20px;

        img {
          display: block;
          width: 16px;
          height: 16px;
        }
      }
    }

    &:hover .file-wrapper-hover {
      display: flex;
    }

    img {
      display: block;
      margin: 0 auto;
      margin-bottom: 10px;
      height: 70px;
    }

    .title {
      overflow: hidden;
      height: 24px;
      font-size: 14px;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #333;
      line-height: 24px;
    }

    .note {
      display: flex;
      justify-content: space-between;
      height: 18px;
      font-size: 12px;
      color: #999;
      line-height: 18px;
    }
  }
}
.operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  p {
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    color: #333;
    span {
      margin-left: 8px;
      color: #2acba0;
      font-weight: 500;
    }
  }
  &.end {
    justify-content: flex-end;
  }
}
</style>
