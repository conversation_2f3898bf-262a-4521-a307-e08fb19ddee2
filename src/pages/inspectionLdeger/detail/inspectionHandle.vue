<template>
  <!-- <p class="detailTitle">签到信息</p>
  <vis-table-pagination
    :columns="inspectionColumns"
    :data="detail.inspectionData"
    background
    class="vis-table-pagination"
  >
  </vis-table-pagination>
  <p class="detailTitle">现场处理信息</p>
  <vis-table-pagination
    :columns="inspectionTwoColumns"
    :data="detail.inspectionData"
    background
    class="vis-table-pagination"
  >
  </vis-table-pagination> -->
  <div class="detailFlex">
    <div class="flexLeft">
      <table v-if="detail.inspectionData.length > 0" class="detailTable">
        <tr>
          <th colspan="2">签到信息</th>
        </tr>
        <tr>
          <td style="width: 140px">出发地址</td>
          <td>{{ detail.inspectionData[0].embarkAddress }}</td>
        </tr>
        <tr>
          <td>出发时间</td>
          <td>{{ detail.inspectionData[0].embarkTime }}</td>
        </tr>
        <tr>
          <td>上门签到地址</td>
          <td>{{ detail.inspectionData[0].signAddress }}</td>
        </tr>
        <tr>
          <td>签到时间</td>
          <td>{{ detail.inspectionData[0].signTime }}</td>
        </tr>
        <tr>
          <td>离场签退地址</td>
          <td>{{ detail.inspectionData[0].signOutAddress }}</td>
        </tr>
        <tr>
          <td>签退时间</td>
          <td>{{ detail.inspectionData[0].signOutTime }}</td>
        </tr>
        <tr>
          <td>总里程</td>
          <td>{{ detail.inspectionData[0].totalMileage }}</td>
        </tr>
        <tr>
          <td>现场处理时间</td>
          <td>{{ detail.inspectionData[0].sceneDisposeTime }}</td>
        </tr>
        <tr>
          <td>工时</td>
          <td>{{ detail.inspectionData[0].manHour }}</td>
        </tr>
      </table>
    </div>
    <div style="width: 20px"></div>
    <div class="flexRight">
      <table v-if="detail.inspectionData.length > 0" class="detailTable">
        <tr>
          <th colspan="2">远程处理信息</th>
        </tr>
        <tr>
          <td style="width: 140px">远程工程师</td>
          <td>{{ detail.inspectionData[0].remoteEngineer }}</td>
        </tr>
        <tr>
          <td>远程沟通说明</td>
          <td>{{ detail.inspectionData[0].communicateIllustrate }}</td>
        </tr>
        <tr>
          <td>远程沟通结果</td>
          <td>{{ detail.inspectionData[0].communicateResults }}</td>
        </tr>
      </table>
    </div>
  </div>
</template>
<script setup>
// import visTablePagination from '@/components/table-pagination.vue'
import { overhaul as api } from '@/api/index.ts'
import filters from '@/utils/filter.js'
let route = useRoute()
// 现场维修1
// const inspectionColumns = ref([
//   {
//     prop: 'embarkAddress',
//     label: '出发地址'
//   },
//   {
//     prop: 'embarkTime',
//     label: '出发时间'
//   },
//   {
//     prop: 'signAddress',
//     label: '上门签到地址',
//     minWidth: 120
//   },
//   {
//     prop: 'signTime',
//     label: '签到时间'
//   },
//   {
//     prop: 'signOutAddress',
//     label: '离场签退地址',
//     minWidth: 120
//   },
//   {
//     prop: 'signOutTime',
//     label: '签退时间'
//   },
//   {
//     prop: 'totalMileage',
//     label: '总里程'
//   },
//   {
//     prop: 'sceneDisposeTime',
//     label: '现场处理时间',
//     minWidth: 120
//   },
//   {
//     prop: 'manHour',
//     label: '工时'
//   }
// ])
// 现场维修2
// const inspectionTwoColumns = ref([
//   {
//     prop: 'remoteEngineer',
//     label: '远程工程师',
//     minWidth: 120
//   },
//   {
//     prop: 'communicateIllustrate',
//     label: '远程沟通说明',
//     minWidth: 120
//   },
//   {
//     prop: 'communicateResults',
//     label: '远程沟通结果'
//   }
// ])
// 获取详情信息接口
let detail = reactive({
  inspectionData: [] // 维保任务
})
// 巡检记录
const getSignHandleInfoFun = async () => {
  let params = {
    workOrderNo: route.params.id
  }
  try {
    let result = await api.getSignHandleInfo(params)
    if (result.data.code == 200) {
      if (result.data.data.length > 0) {
        result.data.data.forEach((value) => {
          value.embarkTime = filters.datetimeFilter(
            new Date(value.embarkTime),
            'YYYY-MM-DD hh:mm:ss'
          )
          value.sceneDisposeTime = filters.datetimeFilter(
            new Date(value.sceneDisposeTime),
            'YYYY-MM-DD hh:mm:ss'
          )
          value.signTime = filters.datetimeFilter(
            new Date(value.signTime),
            'YYYY-MM-DD hh:mm:ss'
          )
          value.signOutTime = filters.datetimeFilter(
            new Date(value.signOutTime),
            'YYYY-MM-DD hh:mm:ss'
          )
        })
      }
      detail.inspectionData = result.data.data
    }
  } catch (error) {
    console.info(error)
  }
}
onMounted(async () => {
  await getSignHandleInfoFun() // 维保任务
})
</script>
<style lang="scss" scoped>
.detailTable {
  width: 100%;
  border: none;
  border-collapse: collapse;
  tr {
    height: 22px;
    line-height: 22px;
    color: #333;
    font-size: 14px;
    th {
      background: #f8f8f8;
      padding: 16px 24px;
      font-weight: 600;
      border: 1px solid #e6e9ec;
    }
    td {
      border: 1px solid #e6e9ec;
      padding: 16px 24px;
      font-weight: 300;
    }
  }
}
.detailFlex {
  display: flex;
  .flexLeft,
  .flexRight {
    flex: 1;
  }
}
</style>
