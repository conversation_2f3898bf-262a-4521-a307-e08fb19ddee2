<template>
  <el-descriptions :column="1">
    <el-descriptions-item label="安全措施项">
      <pre class="pre8978">{{
        detail.inspectionData?.[0]?.safetyMeasureItem || '--'
      }}</pre>
    </el-descriptions-item>
    <el-descriptions-item label="处理描述">
      <pre class="pre8978">{{
        detail.inspectionData?.[0]?.processDescription || '--'
      }}</pre>
    </el-descriptions-item>
    <el-descriptions-item label="图片">
      <el-image
        v-if="detail.inspectionData?.[0]?.imageUrlOne"
        :src="`${WF_PATH}${detail.inspectionData?.[0]?.imageUrlOne}`"
        :preview-src-list="[WF_PATH + detail.inspectionData?.[0]?.imageUrlOne]"
        fit="cover"
        class="w-100px h-100px"
      >
      </el-image>
      <el-image
        v-if="detail.inspectionData?.[0]?.imageUrlTwo"
        :src="`${WF_PATH}${detail.inspectionData?.[0]?.imageUrlTwo}`"
        fit="cover"
        :preview-src-list="[WF_PATH + detail.inspectionData?.[0]?.imageUrlTwo]"
        class="ml-12px w-100px h-100px"
      >
      </el-image>
      <el-image
        v-if="detail.inspectionData?.[0]?.imageUrlThree"
        :src="`${WF_PATH}${detail.inspectionData?.[0]?.imageUrlThree}`"
        fit="cover"
        :preview-src-list="[
          WF_PATH + detail.inspectionData?.[0]?.imageUrlThree
        ]"
        class="ml-12px w-100px h-100px"
      >
      </el-image>
      <el-image
        v-if="detail.inspectionData?.[0]?.imageUrlFour"
        :lazy="true"
        :src="`${WF_PATH}${detail.inspectionData?.[0]?.imageUrlFour}`"
        fit="cover"
        :preview-src-list="[WF_PATH + detail.inspectionData?.[0]?.imageUrlFour]"
        class="ml-12px w-100px h-100px"
      ></el-image>
      <template
        v-if="
          !detail.inspectionData?.[0]?.imageUrlOne &&
          !detail.inspectionData?.[0]?.imageUrlTwo &&
          !detail.inspectionData?.[0]?.imageUrlThree &&
          !detail.inspectionData?.[0]?.imageUrlFour
        "
        >--</template
      >
    </el-descriptions-item>
    <el-descriptions-item label="附件">
      <a
        v-if="detail.inspectionData?.[0]?.attachment"
        :href="`${WF_PATH}${detail.inspectionData?.[0]?.attachment}`"
        target="_blank"
        title="下载文件"
      >
        <span
          class="download-file"
          :style="`background: url(${fileBgSvg}) no-repeat; background-size: 100%`"
        >
          {{ detail.inspectionData?.[0]?.attachment.split('.').at(-1) }}
        </span>
        <span class="file-name">
          {{ detail.inspectionData?.[0]?.attachment.split('&').at(-1) }}
        </span>
      </a>
      <template v-else>--</template>
    </el-descriptions-item>
  </el-descriptions>
</template>
<script setup>
import request from '@/utils/request'
import filters from '@/utils/filter.js'
import fileBgSvg from '@/assets/svgs/fileBg.svg'

const WF_PATH = import.meta.env.VITE_APP_WF_PATH
let route = useRoute()

let detail = reactive({
  total: 0,
  inspectionData: []
})
let params = ref({
  workOrderNo: route.params.id,
  pageNum: 1,
  pageSize: 10
})
const getData = async () => {
  try {
    let result = await request({
      url: '/operate/work-inspection-order/getServiceProgress',
      method: 'get',
      params: params.value
    })
    if (result.data.code == 200) {
      if (result.data.data.length > 0) {
        result.data.data.forEach((value) => {
          value.type = filters.typeFilter(value.type)
          value.state = filters.stateFilter(value.state)
        })
      }
      detail.inspectionData = result.data.data
      detail.total = result.data.total
    }
  } catch (error) {
    console.info(error)
  }
}
onMounted(async () => {
  await getData()
})
</script>
<style lang="scss" scoped>
:deep(
    .el-descriptions__body
      .el-descriptions__table:not(.is-bordered)
      .el-descriptions__cell
  ) {
  padding-bottom: 12px !important;
  display: inline-flex;
}
:deep(.el-descriptions__label) {
  width: 90px !important;
  color: #666 !important;
  font-weight: 600 !important;
}
:deep(.el-descriptions__content) {
  width: calc(100% - 90px) !important;
}
pre.pre8978 {
  all: initial;
  width: auto;
  display: inline-block;
  white-space: pre-wrap;
  color: #333;
  line-height: 1.5;
}

.download-file {
  display: block;
  width: 80px;
  height: 80px;
  overflow: hidden;
  line-height: 80px;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  margin: 0 auto;
}
.file-name {
  line-height: 42px;
}
</style>
