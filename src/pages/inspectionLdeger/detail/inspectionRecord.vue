<template>
  <p class="detailTitle" style="margin-top: 0">巡检报告</p>
  <el-descriptions :column="1" style="margin-bottom: 10px">
    <el-descriptions-item label="巡检报告：">
      <template v-if="detail.inspectionData?.[0]?.inspectionAttachment">
        <a
          v-for="item in detail.inspectionData[0].inspectionAttachment.split(
            ','
          )"
          :key="item"
          href="javascript: void(0)"
          :title="item"
          class="download-file"
          @click="
            downloadFile(`${WF_PATH}${item}`, `${item.split('&').at(-1)}`)
          "
        >
          <span
            class="file-image"
            :style="`background: url(${fileBgSvg}) no-repeat; background-size: 100%`"
          >
            {{ item.split('.').at(-1) }}
          </span>
          <span class="file-name">
            {{ item.split('&').at(-1) }}
          </span>
        </a>
      </template>
      <template v-else>--</template>
    </el-descriptions-item>
  </el-descriptions>
  <p class="detailTitle" style="margin-top: 0">光伏阵列</p>
  <el-descriptions :column="2" class="s50hu">
    <el-descriptions-item label="光伏组件无损伤划痕污物遮挡：">
      {{ detail.inspectionData?.[0]?.attributeOne || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="光伏组件固定牢靠、无松动：">{{
      detail.inspectionData?.[0]?.attributeFour || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="光伏阵列金属支架无腐蚀、无变形：">{{
      detail.inspectionData?.[0]?.attributeTwo || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="光伏阵列线缆布置规整、无脱落现象：">{{
      detail.inspectionData?.[0]?.attributeFive || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="光伏阵列线缆接头无松动烧毁现象：">{{
      detail.inspectionData?.[0]?.attributeThree || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="光伏阵列接地连接正常：">{{
      detail.inspectionData?.[0]?.attributeSix || '--'
    }}</el-descriptions-item>

    <el-descriptions-item label="光伏阵列抽查组件接线盒温度(°C)：">
      {{ detail.inspectionData?.[0]?.temperatureOne || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="光伏阵列抽查组件接线盒温度：">{{
      detail.inspectionData?.[0]?.temperatureOneResult || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="光伏阵列抽查组件背板温度(°C)：">{{
      detail.inspectionData?.[0]?.temperatureTwo || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="光伏阵列抽查组件背板温度：">{{
      detail.inspectionData?.[0]?.temperatureTwoResult || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="光伏阵列抽查电缆接头温度(℃)：">{{
      detail.inspectionData?.[0]?.temperatureThree || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="光伏阵列抽查电缆接头温度：">{{
      detail.inspectionData?.[0]?.temperatureThreeResult || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="光伏阵列备注：">{{
      detail.inspectionData?.[0]?.remark || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="光伏阵列图片：">
      <template v-if="detail.inspectionData?.[0]?.image">
        <el-image
          v-for="(item, index) in detail.inspectionData?.[0]?.image.split(',')"
          :key="'one' + index"
          :src="`${WF_PATH}${item}`"
          :preview-src-list="
            detail.inspectionData?.[0]?.image.split(',').map((v) => WF_PATH + v)
          "
          fit="cover"
          class="w-80px h-80px ml-12px"
        >
        </el-image>
      </template>
      <template v-else>--</template>
    </el-descriptions-item>
  </el-descriptions>
  <p class="detailTitle" style="margin-top: 0">汇流箱</p>
  <el-descriptions :column="2" class="s50hu">
    <el-descriptions-item
      label="汇流箱本体完整无损坏变形，箱门闭锁良好、标示齐全："
    >
      {{ detail.inspectionData?.[1]?.attributeOne || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="汇流箱母排无发热变色、放电闪络、异味：">{{
      detail.inspectionData?.[1]?.attributeSix || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="汇流箱接线端子无松动、烧坏现象：">{{
      detail.inspectionData?.[1]?.attributeTwo || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="汇流箱断路器无发热、烧坏现象：">{{
      detail.inspectionData?.[1]?.attributeSeven || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="汇流箱防雷器正常无动作：">{{
      detail.inspectionData?.[1]?.attributeThree || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="汇流箱熔断器正常：">{{
      detail.inspectionData?.[1]?.attributeEight || '--'
    }}</el-descriptions-item>

    <el-descriptions-item label="汇流箱设备通信正常：">{{
      detail.inspectionData?.[1]?.attributeFour || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="汇流箱接地连接正常：">{{
      detail.inspectionData?.[1]?.attributeNine || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="汇流箱防火封堵封堵完好：">{{
      detail.inspectionData?.[1]?.attributeFive || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="汇流箱警示标识齐全、牢固、清晰：">{{
      detail.inspectionData?.[1]?.attributeTen || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="汇流箱一次接线端子温度：">{{
      detail.inspectionData?.[1]?.temperatureOneResult || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="汇流箱备注：">{{
      detail.inspectionData?.[1]?.remark || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="汇流箱图片：">
      <template v-if="detail.inspectionData?.[1]?.image">
        <el-image
          v-for="(item, index) in detail.inspectionData?.[1]?.image.split(',')"
          :key="'one' + index"
          :src="`${WF_PATH}${item}`"
          :preview-src-list="
            detail.inspectionData?.[1]?.image.split(',').map((v) => WF_PATH + v)
          "
          fit="cover"
          class="w-80px h-80px ml-12px"
        >
        </el-image>
      </template>
      <template v-else>--</template>
    </el-descriptions-item>
  </el-descriptions>
  <p class="detailTitle" style="margin-top: 0">逆变器</p>
  <el-descriptions :column="2" class="s50hu">
    <el-descriptions-item label="逆变器柜体密封良好：">
      {{ detail.inspectionData?.[2]?.attributeOne || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="逆变器标识牌警示牌齐全、牢固、清晰：">{{
      detail.inspectionData?.[2]?.attributeEight || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器通讯正常：">{{
      detail.inspectionData?.[2]?.attributeTwo || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器防小动物设施齐全、完好：">{{
      detail.inspectionData?.[2]?.attributeNine || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器设备无报警、异音、异味：">{{
      detail.inspectionData?.[2]?.attributeThree || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器柜门密封良好、闭锁正常：">{{
      detail.inspectionData?.[2]?.attributeTen || '--'
    }}</el-descriptions-item>

    <el-descriptions-item label="逆变器显示屏示正常：">
      {{ detail.inspectionData?.[2]?.attributeFour || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="逆变器开关运行正常、温度正常：">{{
      detail.inspectionData?.[2]?.attributeEleven || '--'
    }}</el-descriptions-item>
    <el-descriptions-item
      label="逆变器线缆插接牢固、端子排无锈蚀、标识清楚、温度正常："
      >{{
        detail.inspectionData?.[2]?.attributeFive || '--'
      }}</el-descriptions-item
    >
    <el-descriptions-item label="逆变器线缆接头无发热变色、无放电闪络现象：">{{
      detail.inspectionData?.[2]?.attributeTwelve || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器防尘网清洁：">{{
      detail.inspectionData?.[2]?.attributeSix || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器防火封堵完好：">{{
      detail.inspectionData?.[2]?.attributeThirteen || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器风机运行正常：">{{
      detail.inspectionData?.[2]?.attributeSeven || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器接地连接正常：">{{
      detail.inspectionData?.[2]?.attributeFourteen || '--'
    }}</el-descriptions-item>

    <el-descriptions-item label="逆变器风机出口温度(°C)：">
      {{ detail.inspectionData?.[2]?.temperatureOne || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="逆变器风机出口温度：">{{
      detail.inspectionData?.[2]?.temperatureOneResult || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器一次接线端子温度(°C)：">{{
      detail.inspectionData?.[2]?.temperatureTwo || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器一次接线端子温度：">{{
      detail.inspectionData?.[2]?.temperatureTwoResult || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器电容温度(℃)：">{{
      detail.inspectionData?.[2]?.temperatureThree || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器电容温度：">{{
      detail.inspectionData?.[2]?.temperatureThreeResult || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器IGBT温度(℃)：">{{
      detail.inspectionData?.[2]?.temperatureFour || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器IGBT温度：">{{
      detail.inspectionData?.[2]?.temperatureFourResult || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器备注：">{{
      detail.inspectionData?.[2]?.remark || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="逆变器图片：">
      <template v-if="detail.inspectionData?.[2]?.image">
        <el-image
          v-for="(item, index) in detail.inspectionData?.[2]?.image.split(',')"
          :key="'one' + index"
          :src="`${WF_PATH}${item}`"
          :preview-src-list="
            detail.inspectionData?.[2]?.image.split(',').map((v) => WF_PATH + v)
          "
          fit="cover"
          class="w-80px h-80px ml-12px"
        >
        </el-image>
      </template>
      <template v-else>--</template>
    </el-descriptions-item>
  </el-descriptions>
  <p class="detailTitle" style="margin-top: 0">并网柜</p>
  <el-descriptions :column="2" class="s50hu">
    <el-descriptions-item label="并网柜断路器无发热、烧坏现象：">
      {{ detail.inspectionData?.[3]?.attributeOne || '--' }}
    </el-descriptions-item>
    <el-descriptions-item
      label="并网柜刀闸(开关)接触良好，无腐蚀、放电、变形情况："
      >{{
        detail.inspectionData?.[3]?.attributeFive || '--'
      }}</el-descriptions-item
    >
    <el-descriptions-item label="并网柜刀闸(开关)无发热、烧坏现象：">{{
      detail.inspectionData?.[3]?.attributeTwo || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="并网柜接线端子无松动、烧坏现象：">{{
      detail.inspectionData?.[3]?.attributeSix || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="并网柜接地连接正常：">{{
      detail.inspectionData?.[3]?.attributeThree || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="并网柜防火封堵完好：">{{
      detail.inspectionData?.[3]?.attributeSeven || '--'
    }}</el-descriptions-item>

    <el-descriptions-item label="并网柜警示标识齐全、牢固、清晰：">
      {{ detail.inspectionData?.[3]?.attributeFour || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="并网柜防雷器正常无动作：">{{
      detail.inspectionData?.[3]?.attributeEight || '--'
    }}</el-descriptions-item>

    <el-descriptions-item label="并网柜刀闸触点温度(℃)：">
      {{ detail.inspectionData?.[3]?.temperatureOne || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="并网柜刀闸触点温度：">{{
      detail.inspectionData?.[3]?.temperatureOneResult || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="并网柜一次接线端子温度(℃)：">{{
      detail.inspectionData?.[3]?.temperatureTwo || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="并网柜一次接线端子温度：">{{
      detail.inspectionData?.[3]?.temperatureTwoResult || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="并网柜备注：">{{
      detail.inspectionData?.[3]?.remark || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="并网柜图片：">
      <template v-if="detail.inspectionData?.[3]?.image">
        <el-image
          v-for="(item, index) in detail.inspectionData?.[3]?.image.split(',')"
          :key="'one' + index"
          :src="`${WF_PATH}${item}`"
          :preview-src-list="
            detail.inspectionData?.[3]?.image.split(',').map((v) => WF_PATH + v)
          "
          fit="cover"
          class="w-80px h-80px ml-12px"
        >
        </el-image>
      </template>
      <template v-else>--</template>
    </el-descriptions-item>
  </el-descriptions>
  <p class="detailTitle" style="margin-top: 0">周围环境及构建筑物</p>
  <el-descriptions :column="2" class="s50hu">
    <el-descriptions-item label="周围环境环境清洁无杂物：">
      {{ detail.inspectionData?.[4]?.attributeOne || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="周围环境组件周围无遮挡：">{{
      detail.inspectionData?.[4]?.attributeSix || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="周围环境各警示标识齐全、牢固、清晰：">{{
      detail.inspectionData?.[4]?.attributeTwo || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="周围环境栏杆、孔洞牢固无生锈情况：">{{
      detail.inspectionData?.[4]?.attributeSeven || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="周围环境爬梯、平台牢固无生锈情况：">{{
      detail.inspectionData?.[4]?.attributeThree || '--'
    }}</el-descriptions-item>
    <el-descriptions-item
      label="周围环境屋顶防水良好，落水管完好，无积水情况："
      >{{
        detail.inspectionData?.[4]?.attributeEight || '--'
      }}</el-descriptions-item
    >

    <el-descriptions-item label="构建筑物建筑物业主无变动：">
      {{ detail.inspectionData?.[4]?.attributeFour || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="构建筑物建筑物基础完好，无沉降、开裂：">{{
      detail.inspectionData?.[4]?.attributeNine || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="构建筑物各设备基础完好，无沉降、开裂：">
      {{ detail.inspectionData?.[4]?.attributeFive || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="周围环境及构建筑物备注：">{{
      detail.inspectionData?.[4]?.remark || '--'
    }}</el-descriptions-item>
  </el-descriptions>
  <p class="detailTitle" style="margin-top: 0">应急、防火检查</p>
  <el-descriptions :column="2" class="s50hu">
    <el-descriptions-item label="周围环境环境清洁无杂物：">
      {{ detail.inspectionData?.[5]?.attributeOne || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="周围环境组件周围无遮挡：">{{
      detail.inspectionData?.[5]?.attributeSix || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="周围环境各警示标识齐全、牢固、清晰：">{{
      detail.inspectionData?.[5]?.attributeTwo || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="周围环境栏杆、孔洞牢固无生锈情况：">{{
      detail.inspectionData?.[5]?.attributeSeven || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="周围环境爬梯、平台牢固无生锈情况：">{{
      detail.inspectionData?.[5]?.attributeThree || '--'
    }}</el-descriptions-item>
    <el-descriptions-item
      label="周围环境屋顶防水良好，落水管完好，无积水情况："
      >{{
        detail.inspectionData?.[5]?.attributeEight || '--'
      }}</el-descriptions-item
    >

    <el-descriptions-item label="构建筑物建筑物业主无变动：">
      {{ detail.inspectionData?.[5]?.attributeFour || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="构建筑物建筑物基础完好，无沉降、开裂：">{{
      detail.inspectionData?.[5]?.attributeNine || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="构建筑物各设备基础完好，无沉降、开裂：">
      {{ detail.inspectionData?.[5]?.attributeFive || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="应急、防火检查备注：">{{
      detail.inspectionData?.[5]?.remark || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="应急、防火检查图片：">
      <template v-if="detail.inspectionData?.[5]?.image">
        <el-image
          v-for="(item, index) in detail.inspectionData?.[5]?.image.split(',')"
          :key="'one' + index"
          :src="`${WF_PATH}${item}`"
          :preview-src-list="
            detail.inspectionData?.[5]?.image.split(',').map((v) => WF_PATH + v)
          "
          fit="cover"
          class="w-80px h-80px ml-12px"
        >
        </el-image>
      </template>
      <template v-else>--</template>
    </el-descriptions-item>
  </el-descriptions>
  <p class="detailTitle" style="margin-top: 0">其他检查情况说明</p>
  <el-descriptions :column="2" class="s50hu">
    <el-descriptions-item label="其他检查情况说明：">{{
      detail.inspectionData?.[6]?.remark || '--'
    }}</el-descriptions-item>
    <el-descriptions-item label="其他检查情况图片：">
      <template v-if="detail.inspectionData?.[6]?.image">
        <el-image
          v-for="(item, index) in detail.inspectionData?.[6]?.image.split(',')"
          :key="'one' + index"
          :src="`${WF_PATH}${item}`"
          :preview-src-list="
            detail.inspectionData?.[6]?.image.split(',').map((v) => WF_PATH + v)
          "
          fit="cover"
          class="w-80px h-80px ml-12px"
        >
        </el-image>
      </template>
      <template v-else>--</template>
    </el-descriptions-item>
  </el-descriptions>
  <p class="detailTitle" style="margin-top: 0">巡检结论</p>
  <el-descriptions :column="1">
    <el-descriptions-item label="巡检结论：">
      {{ detail.inspectionData?.[0]?.inspectionResult || '--' }}
    </el-descriptions-item>
  </el-descriptions>
  <p class="detailTitle">验收情况</p>
  <el-descriptions :column="2" class="s50hu">
    <el-descriptions-item label="验收人：">
      {{ detail.inspectionData?.[0]?.attributeThirteen || '--' }}
    </el-descriptions-item>
    <el-descriptions-item label="验收状态：">
      {{ detail.inspectionData?.[0]?.attributeFourteen || '--' }}
    </el-descriptions-item>
  </el-descriptions>

  <!-- <el-descriptions :column="1">
    <el-descriptions-item label="图片">
      <template v-if="detail.imageOnes.length">
        <el-image
          v-for="(item, index) in detail.imageOnes"
          :key="'one' + index"
          :src="`${WF_PATH}${item}`"
          :preview-src-list="detail.imageOnes.map((v) => WF_PATH + v)"
          fit="cover"
          class="w-100px h-100px mr-12px"
        >
        </el-image>
      </template>
      <template v-if="detail.imageTwos.length">
        <el-image
          v-for="(item, index) in detail.imageTwos"
          :key="'two' + index"
          :src="`${WF_PATH}${item}`"
          fit="cover"
          :preview-src-list="detail.imageTwos.map((v) => WF_PATH + v)"
          class="mr-12px w-100px h-100px"
        >
        </el-image>
      </template>
      <template v-if="!detail.imageOnes.length && !detail.imageTwos.length"
        >--</template
      >
    </el-descriptions-item>
  </el-descriptions> -->
</template>
<script setup>
import request from '@/utils/request'
import fileBgSvg from '@/assets/svgs/fileBg.svg'
let route = useRoute()

const WF_PATH = import.meta.env.VITE_APP_WF_PATH

let detail = reactive({
  inspectionData: []
})

let params = ref({
  workOrderNo: route.params.id
})

const downloadFile = async (path, name) => {
  // window.open(`${path}?response-content-type=application%2Foctet-stream`)
  const link = document.createElement('a')
  link.href = path
  link.download = name
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
onMounted(async () => {
  try {
    let result = await request({
      url: '/operate/work-inspection-order/getInspectionData',
      method: 'get',
      params: params.value
    })
    if (result.data.code == 200) {
      detail.inspectionData = result.data.data
    }
  } catch (error) {
    detail.inspectionData = []
  }
})
</script>
<style lang="scss" scoped>
:deep(
    .el-descriptions__body
      .el-descriptions__table:not(.is-bordered)
      .el-descriptions__cell
  ) {
  padding-bottom: 5px !important;
  display: inline-flex;
}
.s50hu {
  margin-bottom: 15px;
  :deep(
      .el-descriptions__body
        .el-descriptions__table:not(.is-bordered)
        .el-descriptions__cell
    ) {
    width: 50%;
  }
  :deep(tr) {
    vertical-align: top;
  }
}
:deep(.el-descriptions__label) {
  color: #333 !important;
  font-weight: 500 !important;
  flex: none;
}
:deep(.el-descriptions__content) {
  margin-left: 0px;
  color: #888;
  padding-right: 15px;
}
pre.pre8978 {
  all: initial;
  width: auto;
  display: block;
  white-space: pre-wrap;
  color: #333;
  line-height: 1.5;
}

.download-file {
  display: flex;
  margin-bottom: 3px;
  .file-image {
    display: block;
    width: 38px;
    height: 38px;
    overflow: hidden;
    line-height: 38px;
    text-align: center;
    font-size: 12px;
    font-weight: 600;
    color: #fff;
    margin: 0 auto;
    flex: none;
  }
  .file-name {
    line-height: 42px;
    margin-left: 10px;
    flex: auto;
  }
}
</style>
