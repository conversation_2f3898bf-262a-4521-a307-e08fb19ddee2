<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchData"
        :search-data="productionlineForm"
        :btn-info="btnInfo"
        @submit-emits="submitEmitsFn"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :loading="loading"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="productionlineForm.pageSize"
        :columns="columns"
        :total="list.total"
        :data="list.data"
        :show-overflow-tooltip="true"
        :current-page="productionlineForm.pageNum"
        background
        :border="true"
        class="vis-table-pagination border-table-noborder"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="viewDetail(row)">查看</el-button>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>
<script setup>
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import address from '@/api/data/area.json'
import { overhaul as api } from '@/api/index.ts'
import filters from '@/utils/filter.js'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const route = useRoute()
const columns = [
  {
    prop: 'workOrderNo',
    label: '工单编号',
    minWidth: 170
  },
  {
    prop: 'workOrderName',
    label: '工单标题',
    minWidth: 150,
    type: 'html',
    formatter: (v) => {
      return v.workOrderName
        ? `<a href="https://servicego.tsy.spic.com.cn/site/custom-object/28898/records/view/${v.dataId}" target="_blank" class="link">${v.workOrderName}<a>`
        : '' // 28422 28898
    }
  },
  {
    prop: 'workState',
    label: '工单状态',
    minWidth: 80
  },
  {
    prop: 'priority',
    label: '优先级',
    minWidth: 60
  },
  {
    prop: 'powerStation',
    label: '电站',
    type: 'html',
    minWidth: 150,
    formatter: (v) => {
      return v.powerStation
        ? `<a href="/pvom/station/all/detail/${v.powerStationCode}?hideBack=true&path=${route.path}&pathName=巡检工单台账" class="link">${v.powerStation}<a>`
        : ''
    }
  },
  {
    prop: 'administrativeDifferences',
    label: '行政区划',
    minWidth: 160,
    formatter: (v) => {
      if (v.administrativeDifferences) return v.administrativeDifferences
      if (!v.province && !v.city && !v.area) return '--'
      return v.province + (v.city || '') + (v.area || '')
    }
  },
  {
    prop: 'detailAddress',
    label: '详细地址',
    minWidth: 160
  },
  {
    prop: 'operationSquare',
    label: '运维公司',
    minWidth: 150
  },
  {
    prop: 'contactsName',
    label: '联系人',
    minWidth: 80
  },
  {
    prop: 'contactsPhone',
    label: '联系电话',
    minWidth: 120
  },
  {
    prop: 'startTime',
    label: '开始时间',
    minWidth: 170
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: 170
  },
  {
    prop: 'updateTime',
    label: '更新时间',
    minWidth: 170
  },
  {
    prop: 'createUser',
    label: '创建人',
    minWidth: 180
  },
  {
    prop: 'handlers',
    label: '处理人',
    minWidth: 120
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 80,
    fixed: 'right'
  }
]

const router = useRouter()
// 查询参数
let productionlineForm = ref({
  workOrderNo: '', // 工单编号
  workOrderName: '', // 工单标题
  workState: '', //工单状态
  priority: '', // 优先级
  code: '', // 行政区划
  contactsName: '', // 联系人
  contactsPhone: '', // 联系方式
  createUser: '', // 创建人
  handlers: '', // 处理人
  pageNum: 1, // 当前页
  pageSize: 10, // 每页条数
  workType: 0, // 工单类型
  operationSquare: '', // 运维公司
  powerStation: '' // 电站名称
})
// 查询条件数据
const searchData = ref([
  {
    label: '工单编号',
    placeholder: '请输入工单编号',
    type: 'input',
    prop: 'workOrderNo',
    span: 8
  },
  {
    label: '工单标题',
    placeholder: '请输入工单标题',
    type: 'input',
    prop: 'workOrderName',
    span: 8
  },
  {
    label: '工单状态',
    placeholder: '请选择工单状态',
    type: 'select',
    prop: 'workState',
    span: 8,
    options: [
      {
        label: '草稿',
        value: 1
      },
      {
        label: '待处理',
        value: 2
      },
      {
        label: '处理中',
        value: 3
      },
      {
        label: '等待中',
        value: 4
      },
      {
        label: '已取消',
        value: 5
      },
      {
        label: '已完成',
        value: 6
      }
    ]
  },
  {
    label: '优先级',
    placeholder: '请选择优先级',
    type: 'select',
    prop: 'priority',
    span: 8,
    options: [
      {
        label: '低',
        value: '低'
      },
      {
        label: '中',
        value: '中'
      },
      {
        label: '高',
        value: '高'
      }
    ]
  },
  {
    label: '行政区划',
    placeholder: '请选择行政区划',
    type: 'cascader',
    prop: 'code',
    options: address,
    span: 8,
    props: {
      value: 'label',
      label: 'label',
      children: 'children',
      expandTrigger: 'hover',
      checkStrictly: true,
      emitPath: true
    }
  },
  {
    label: '运维公司',
    placeholder: '请输入运维公司',
    type: 'input',
    prop: 'operationSquare',
    span: 8
  },
  // {
  //   label: '电站名称',
  //   placeholder: '请输入电站名称',
  //   type: 'input',
  //   prop: 'powerStation',
  //   span: 8,
  //   width: 100
  // },
  {
    label: '联系人',
    placeholder: '请输入联系人',
    type: 'input',
    prop: 'contactsName',
    span: 8
  },
  {
    label: '联系电话',
    placeholder: '请输入联系电话',
    type: 'input',
    prop: 'contactsPhone',
    span: 8
  },
  {
    label: '创建人',
    placeholder: '请输入创建人',
    type: 'input',
    prop: 'createUser',
    span: 8
  },
  {
    label: '处理人',
    placeholder: '请输入处理人',
    type: 'input',
    prop: 'handlers',
    span: 8
  }
])
const btnInfo = ref({
  span: 16
})
// 提交查询按钮
const list = reactive({
  data: [],
  total: 0
})
// 查询按钮
const submitEmitsFn = async (val) => {
  if (Object.keys(val).length) {
    productionlineForm.value = val
    productionlineForm.value.pageNum = 1
    productionlineForm.value.pageSize = 10
    getListFun()
  } else {
    productionlineForm.value = {
      workOrderNo: '', // 工单编号
      workOrderName: '', // 工单标题
      workState: '', //工单状态
      priority: '', // 优先级
      code: '', // 行政区划
      contactsName: '', // 联系人
      contactsPhone: '', // 联系方式
      createUser: '', // 创建人
      handlers: '', // 处理人
      pageNum: 1, // 当前页
      pageSize: 10, // 每页条数
      workType: 0, // 工单类型
      operationSquare: '', // 运维公司
      powerStation: '' // 电站名称
    }
    getListFun()
  }
}

const loading = ref(false)
// 接口获取数据列表
const getListFun = async () => {
  if (loading.value) return
  try {
    let pm = JSON.parse(JSON.stringify(productionlineForm.value))
    if (pm.code) {
      pm.code = pm.code.join(',')
    }
    let result = await api.inspectionList(pm, [loading])
    if (result.data.code == 200) {
      if (result.data.data.records.length > 0) {
        result.data.data.records.forEach((value) => {
          value.workState = filters.workStateFilter(value.workState)
          value.urgency = filters.priorityFilter(value.urgency)
        })
        list.data = result.data.data.records
        list.total = result.data.data.total
      } else {
        list.data = []
        list.total = 0
      }
    }
  } catch (e) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

watch(
  () => companyCode.data,
  () => {
    companyCode.data && getListFun()
  }
)
onMounted(async () => {
  companyCode.data && getListFun()
})

const viewDetail = (row) => {
  router.push({ name: 'inspectionDetail', params: { id: row.workOrderNo } })
  sessionStorage.setItem('itemInfo', JSON.stringify(row))
}
const handleSizeChange = (params) => {
  productionlineForm.value.pageNum = 1
  productionlineForm.value.pageSize = params.pageSize
  getListFun()
}
const handleCurrentChange = (params) => {
  productionlineForm.value.pageNum = params.currentPage
  getListFun()
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss">
.el-table.border-table-noborder {
  --el-table-border-color: none;
  .el-table__cell,
  th.el-table__cell.is-leaf {
    border-bottom: solid 1px var(--el-border-color-lighter);
  }
}
</style>
