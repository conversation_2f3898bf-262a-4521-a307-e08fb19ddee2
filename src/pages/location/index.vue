<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import * as api from '@/api/index.ts'
import useTableData from '@/hooks/useTableData'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const reportTypes = [
  {
    label: '位置校准',
    value: 1
  }
]
const reportStates = [
  {
    label: '审批通过',
    value: 0
  },
  {
    label: '待审批',
    value: 1
  },
  {
    label: '审批驳回',
    value: 2
  }
]
const searchProps = ref<Obj[]>([
  {
    prop: 'reportManageType',
    label: '事件类型',
    width: '80px',
    type: 'select',
    options: reportTypes
  },
  {
    prop: 'stationName',
    label: '电站名称',
    width: '80px',
    type: 'input'
  },
  {
    prop: 'reportManageState',
    label: '审批状态',
    width: '80px',
    type: 'select',
    options: reportStates
  }
])
let searchData = reactive<Obj>({
  reportManageType: 1,
  stationName: '',
  reportManageState: '',
  pageSize: 10,
  pageNum: 1
})
const columns = [
  {
    prop: 'stationCode',
    label: '电站编码'
  },
  {
    prop: 'stationName',
    label: '电站名称'
  },
  {
    prop: 'stationUserPhone',
    label: '联系方式'
  },
  {
    prop: 'reportManageType',
    label: '事件类型',
    formatter: (row: any) =>
      reportTypes.find((item) => item.value == row.reportManageType)?.label ||
      '--'
  },
  {
    prop: 'informantName',
    label: '上报人',
    formatter: (row: any) => row.informantName?.split('_')?.at(0) || '--'
  },
  {
    prop: 'submitTime',
    label: '提交时间'
  },
  {
    prop: 'reportManageState',
    label: '状态',
    type: 'tag',
    formatter: (row: any) =>
      reportStates.find((item) => item.value == row.reportManageState)?.label ||
      '--'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 110,
    fixed: 'right'
  }
]
const getTableData = async (data: Obj, loading: Loading) => {
  return await api.post(
    '/operate/reportManage/getReportManageList',
    { ...data, code: localStorage.getItem('PVOM_COMPANY_CODE') },
    loading
  )
}
const {
  tableData,
  tablePage,
  tableLoading,
  changeCurrent,
  changeSize,
  changeData
} = useTableData(getTableData, searchData, { immediate: false })

const route = useRoute()
watch(
  () => [companyCode.data, route],
  () => {
    companyCode.data && changeData()
  },
  { immediate: true }
)

const handleSearch = async (val: any) => {
  Object.keys(searchData).forEach((key) => {
    searchData[key] = val[key]
  })
  changeData(true)
}
const detailDialog = ref<boolean>(false)
const detailData = ref<Obj>({})
const router = useRouter()
const handleView = async (row: Obj) => {
  router.push('/location/detail/' + row.id)
}
</script>

<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>上报管理列表</p>
      </div>
      <TablePagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :show-overflow-tooltip="true"
        background
        :columns="columns"
        :data="tableData.data"
        :total="tableData.total"
        :loading="tableLoading"
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        class="table-pagination"
        @handle-size-change="changeSize"
        @handle-current-change="changeCurrent"
      >
        <template #reportManageState="{ row }">
          <template
            v-if="
              reportStates.find((item) => item.value == row.reportManageState)
                ?.label
            "
          >
            <el-tag
              :type="
                row.reportManageState === 1
                  ? 'info'
                  : row.reportManageState === 2
                  ? 'danger'
                  : 'success'
              "
              >{{
                reportStates.find((item) => item.value == row.reportManageState)
                  ?.label
              }}</el-tag
            >
          </template>
          <template v-else>--</template>
        </template>
        <template #operate="{ row }">
          <el-button type="primary" link @click="() => handleView(row)">
            查看详情
          </el-button>
        </template>
      </TablePagination>
    </div>
  </div>
  <el-dialog
    v-model="detailDialog"
    title="查看详情"
    align-center
    :before-close="() => ((detailData = {}), (detailDialog = false))"
    :close-on-click-modal="false"
    width="500px"
    class="vis-dialog"
  >
    <el-descriptions :column="1" style="padding-bottom: 12px">
      <el-descriptions-item label="电站编码：">
        {{ detailData.stationCode || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="电站名称：">
        {{ detailData.stationName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="联系方式：">
        {{ detailData.stationUserPhone || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="上报人：">
        {{ detailData.informantName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="上报时间：">
        {{ detailData.submitTime || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="审批人：">
        {{ detailData.approveUserName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="审批时间：">
        {{ detailData.approveDate || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="审批驳回原因：">
        {{ detailData.turnDownMsg || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="电站原地址：">
        {{ detailData.beforeAddress || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="校准地址：">
        {{ detailData.calibrateAddress || '--' }}
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
