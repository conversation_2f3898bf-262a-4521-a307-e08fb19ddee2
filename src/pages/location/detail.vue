<script setup lang="ts">
import * as api from '@/api/index.ts'

const detailDialog = ref<boolean>(false)
const detailData = ref<Obj>({})

const route = useRoute()
onMounted(() => {
  const id = route.params.id as string
  if (id) {
    getDetailData({ id })
  }
})
const getDetailData = async (row: Obj) => {
  try {
    const { data } = await api.get(
      '/operate/reportManage/getReportManageDetail',
      {
        id: row.id
      }
    )
    detailData.value = data || {}
    detailData.value.reportManage = detailData.value?.reportManage
      ? detailData.value.reportManage?.split(',')
      : []
    detailDialog.value = true
  } catch (e: any) {
    detailData.value = {}
    ElMessage.error(e.message)
  }
}
</script>

<template>
  <div class="info-base">
    <el-descriptions :column="2">
      <el-descriptions-item label="电站编码：">
        {{ detailData.stationCode || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="电站名称：">
        {{ detailData.stationName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="联系方式：">
        {{ detailData.stationUserPhone || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="上报人：">
        {{ detailData.informantName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="上报时间：">
        {{ detailData.submitTime || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="审批人：">
        {{ detailData.approveUserName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="审批时间：">
        {{ detailData.approveDate || '--' }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions :column="1">
      <el-descriptions-item :rowspan="2" :span="2" label="审批驳回原因：">
        {{ detailData.turnDownMsg || '--' }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <div class="info-base image-wrapper6787">
    <el-descriptions :column="1">
      <el-descriptions-item label="位置图片：" style="color: red">
        <SpicUpload
          v-model="detailData.reportManage"
          type="image"
          :disabled="true"
        />
      </el-descriptions-item>
    </el-descriptions>
  </div>
  <div class="info-base">
    <el-descriptions :column="2">
      <el-descriptions-item label="电站原地址：">
        {{ detailData.beforeAddress || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="校准地址：">
        {{ detailData.calibrateAddress || '--' }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss">
.image-wrapper6787 {
  .el-descriptions__label {
    vertical-align: top !important;
  }
  .spic-upload {
    display: inline-block;
  }
}
</style>
