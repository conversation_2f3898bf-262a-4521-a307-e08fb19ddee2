<!--
 * @Author: 赵鹏鹏
 * @Date: 2024-03-20 09:19:35
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @Description: 故障知识
-->
<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-title">{{ $route.meta.title }}</div>
    </div>
    <div class="page-main">
      <div class="page-search">
        <searchForm
          :search-props="searchProps"
          :search-data="searchData"
          label-width="60px"
          @submit-emits="handleSearch"
        ></searchForm>
      </div>
      <div v-auto-height class="main">
        <div class="operate">
          <p>故障知识列表</p>
          <el-button type="primary" @click="editDetail(row, 'add')">
            <el-icon><Plus /></el-icon>新建故障知识</el-button
          >
        </div>

        <vis-table-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :loading="loading"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchData.pageSize"
          :columns="columns"
          :total="list.total"
          :data="list.data"
          :show-overflow-tooltip="true"
          :current-page="searchData.pageNum"
          background
          class="vis-table-pagination"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
        >
          <!-- 品牌型号 -->
          <template #deviceModelsName="{ row }">
            {{
              row.deviceModelsName ? row.brandName + row.deviceModelsName : '--'
            }}
          </template>
          <!-- 操作 -->
          <template #operate="{ row }">
            <div class="table-operate">
              <el-button link @click.stop="editDetail(row, 'edit')"
                >编辑</el-button
              >

              <el-popconfirm title="确认删除？" @confirm="handleDelete(row)">
                <template #reference>
                  <el-button link @click.stop>删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
    <!-- 弹窗 -->
    <dialogForm
      :isShow="isShow"
      :typeVal="typeVal"
      :rows="rows"
      @closeDialog="closeDialog"
      @updateList="getListFun"
    ></dialogForm>
  </div>
</template>
<script setup lang="ts">
import {
  getFaultKnowledgeVOList,
  delFaultKnowledgeById
} from '@/api/module/fault.ts'
import visTablePagination from '@/components/table-pagination.vue'
import dialogForm from './components/dialog-form.vue'
// 搜索
const searchProps = ref([
  {
    prop: 'faultTypeName',
    label: '故障类型名称',
    placeholder: '请输入故障类型名称',
    span: 8,
    width: '110px',
    maxlength: 32
  },
  {
    prop: 'faultCode',
    label: '故障类型编码',
    placeholder: '请输入故障类型编码',
    span: 8,
    width: '110px'
  },
  {
    prop: 'keyword',
    label: '关键词',
    placeholder: '请输入关键词',
    span: 8,
    width: '110px'
  }
])
const searchData = ref({
  faultTypeName: '',
  faultCode: '',
  keyword: '',
  pageNum: 1, // 当前页
  pageSize: 10 // 每页条数
})
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1, // 当前页
    pageSize: 10 // 每页条数
  }
  getListFun()
}
// 表格
let loading = ref(false)
const columns = [
  {
    prop: 'faultTypeName',
    label: '关联故障类型'
  },
  {
    prop: 'faultCode',
    label: '故障类型编码'
  },
  {
    prop: 'deviceModelsName',
    label: '所属设备型号',
    slotName: 'deviceModelsName'
  },
  {
    prop: 'faultTraits',
    label: '故障特征'
  },
  {
    prop: 'faultCause',
    label: '根本原因'
  },
  {
    prop: 'measures',
    label: '处理措施'
  },
  {
    prop: 'keyword',
    label: '关键词'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 120,
    fixed: 'right'
  }
]
const list = reactive({
  data: [],
  total: 0
})
const handleSizeChange = (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getListFun()
}
const handleCurrentChange = (params: any) => {
  searchData.value.pageNum = params.currentPage
  getListFun()
}
const handleDelete = async (row: any) => {
  await delFaultKnowledgeById({
    id: row.id
  })
    .then(({ data }) => {
      if (data.code == '200') {
        if (data.data) {
          ElMessage({
            message: '删除成功',
            type: 'success'
          })
          getListFun()
        } else {
          ElMessage({
            message: data.message,
            type: 'warning'
          })
        }
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
onMounted(() => {
  getListFun()
})
const getListFun = async () => {
  await getFaultKnowledgeVOList({
    ...searchData.value
  })
    .then(({ data }) => {
      if (data.code == 200) {
        list.data = data.data.records || []
        list.total = data.data.total
      } else {
        list.data = []
        list.total = 0
      }
    })
    .catch((err) => {
      list.data = []
      list.total = 0
      console.log(err)
    })
}

// 弹窗
let isShow = ref(false)
let typeVal = ref('')
const rows = ref({})

const editDetail = (ag1: any, ag2: String) => {
  switch (ag2) {
    case 'add': // 新增
      isShow.value = true
      rows.value = {}
      typeVal.value = 'add'
      break
    case 'edit': // 编辑
      isShow.value = true
      rows.value = ag1
      typeVal.value = 'edit'
      break
    default:
      return
  }
}
const closeDialog = () => {
  isShow.value = false
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.main {
  :deep(.el-table) {
    margin-top: 10px;
  }
  &-btn {
    display: flex;
    justify-content: space-between;
  }
}
.page-container {
  :deep(.vis-dialog) {
    padding: 0 !important;
  }
}
</style>
