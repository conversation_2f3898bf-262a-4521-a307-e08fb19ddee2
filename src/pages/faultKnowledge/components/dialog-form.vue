<!--
 * @Author: 赵鹏鹏
 * @Date: 2024-03-20 15:14:02
 * @LastEditors: 赵鹏鹏
 * @Description: 页面名称
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="titleVal"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="680"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-suffix=""
        label-width="140px"
        :rules="formRules"
        class="elFrom2"
      >
        <!-- 关联故障类型 -->
        <el-form-item label="关联故障类型" prop="faultDatabaseId">
          <el-select
            v-model="formData.faultDatabaseId"
            placeholder="请选择关联故障类型"
            filterable
          >
            <el-option
              v-for="(item, index) in faultDatabaseArr"
              :key="index"
              :label="item.faultTypeName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 故障特征 -->
        <el-form-item label="故障特征" prop="faultTraits">
          <el-input
            v-model="formData.faultTraits"
            :rows="2"
            type="textarea"
            placeholder="请输入故障特征"
            show-word-limit
            :autosize="{ minRows: 3, maxRows: 3 }"
          />
        </el-form-item>
        <!-- 故障根本原因 -->
        <el-form-item label="故障根本原因" prop="faultCause">
          <el-input
            v-model="formData.faultCause"
            :rows="2"
            type="textarea"
            placeholder="请输入故障根本原因"
            show-word-limit
            :autosize="{ minRows: 3, maxRows: 3 }"
          />
        </el-form-item>

        <!-- 处理措施 -->
        <el-form-item label="处理措施" prop="measures">
          <el-input
            v-model="formData.measures"
            :rows="2"
            type="textarea"
            placeholder="请输入处理措施"
            show-word-limit
            :autosize="{ minRows: 3, maxRows: 3 }"
          />
        </el-form-item>

        <!-- 关键词 -->
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="formData.keyword"
            placeholder="请输入关键词"
            maxlength="300"
          />
        </el-form-item>

        <!-- 设备品牌 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备品牌" prop="brandCode">
              <el-select
                v-model="formData.brandCode"
                placeholder="请选择设备品牌"
                @change="changeItem"
                filterable
              >
                <el-option
                  v-for="(item, index) in brandNameArr"
                  :key="index"
                  :label="item.brandName"
                  :value="item.brandCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 设备型号 -->
          <el-col :span="12">
            <el-form-item label="设备型号" prop="deviceModelsCode">
              <el-select
                v-model="formData.deviceModelsCode"
                placeholder="请选择设备型号"
                filterable
              >
                <el-option
                  v-for="(item, index) in deviceModelsCodeArr"
                  :key="index"
                  :label="item.deviceModelsName"
                  :value="item.deviceModelsCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 附件 -->
        <el-form-item label="附件" prop="fileName">
          <div class="upload-box">
            <div
              class="mask-box"
              v-if="fileNameObj.fileName"
              @click="showInfo"
            ></div>

            <el-upload
              class="upload-demo"
              drag
              :limit="1"
              :action="actionUrl"
              :on-exceed="handleExceed"
              :before-upload="beforeAvatarUpload"
              :on-success="handleAvatarSuccess"
              :on-change="handleChange"
              :headers="customHeaders"
              ref="upload"
            >
              <div class="el-upload__text">
                <p>
                  <el-icon size="22" color="#29CCA0"><FolderAdd /></el-icon
                  >点击或将文件拖拽到这里上传
                </p>
                <p>（限制上传1个文件，单个文件不超过50MB）</p>
              </div>
            </el-upload>

            <div class="fileBox__" v-if="fileNameObj.fileName">
              <el-icon size="16" color="#29CCA0"><Link /></el-icon>
              <span class="fileText">
                {{
                  fileNameObj.originalFileName &&
                  fileNameObj.originalFileName.length > 8
                    ? fileNameObj.originalFileName.slice(0, 6) +
                      '...' +
                      getFileExtension(fileNameObj.originalFileName)
                    : fileNameObj.originalFileName
                }}
              </span>

              <el-icon
                size="16"
                color="#29CCA0"
                @click="handleDownload(fileNameObj)"
                ><Download
              /></el-icon>
              &nbsp;&nbsp;
              <el-icon size="16" color="#29CCA0" @click="deleteClick()"
                ><Delete
              /></el-icon>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="submitForm()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import {
  getFaultDatabaseList,
  getDeviceBrandList,
  getDeviceModelList,
  saveOrUpdateFaultKnowledge,
  getFaultKnowledgeById
} from '@/api/module/fault.ts'
import { getCookieValue } from '@/utils'
import { genFileId } from 'element-plus'
import type {
  UploadInstance,
  UploadProps,
  UploadRawFile,
  FormInstance,
  FormRules
} from 'element-plus'
import { loadingOpen, loadingClose } from '@/utils/common.ts'

import request from '@/utils/request'
let fileNameObj = ref({
  fileName: '',
  originalFileName: ''
})
const upload = ref<UploadInstance>()
const formRef = ref<FormInstance>()
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  typeVal: {
    type: String,
    default: '',
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {
        faultDatabaseId: '',
        faultTraits: '',
        faultCause: '',
        measures: '',
        brandName: '',
        brandCode: '',
        deviceModelsName: '', // 设备型号名称
        deviceModelsCode: '', // 设备型号编码
        keyword: '',
        fileName: '',
        id: ''
      }
    }
  }
})

let dialogVisible = ref(false)
let id_ = ref('')
const formData = ref<Record<string, any>>({
  faultDatabaseId: '', // 关联故障类型
  faultTraits: '', // 故障特征
  faultCause: '', // 故障根本原因
  measures: '', // 处理措施
  brandName: '', // 设备品牌名称
  brandCode: '', // 设备品牌名code
  deviceModelsName: '', // 设备型号名称
  deviceModelsCode: '', // 设备型号编码
  keyword: '', // 关键词
  fileName: '' // 附件
})

const DEFAULT_PATH: string = import.meta.env.VITE_APP_DEFAULT_PATH
let actionUrl =
  DEFAULT_PATH + '/pvom/operate/operatorsManage/uploadContractInfoFile'

const brandNameArr = ref<Record<string, any>[]>([]) // 设备品牌
const faultDatabaseArr = ref<Record<string, any>[]>([]) // 故障类型
const deviceModelsCodeArr = ref<Record<string, any>[]>([]) // 设备型号
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      await getGetFaultDatabaseList_()
      await getGetDeviceBrandList_()

      await (() => {
        if (props.typeVal == 'edit') {
          id_.value = props.rows.id || ''
          getGetFaultKnowledgeById()
        } else {
          upload && upload.value && upload.value!.clearFiles()
          // upload && upload.value!.resetFields()
          fileNameObj.value.fileName = ''
          fileNameObj.value.originalFileName = ''
          formData.value = {
            faultDatabaseId: '', // 关联故障类型
            faultTraits: '', // 故障特征
            faultCause: '', // 故障根本原因
            measures: '', // 处理措施
            brandName: '', // 设备品牌名称
            brandCode: '', // 设备品牌名code
            deviceModelsName: '', // 设备型号名称
            deviceModelsCode: '', // 设备型号编码
            keyword: '', // 关键词
            fileName: '' // 附件
          }
        }
      })()
    }
  },
  {
    deep: true
  }
)
// 获取编辑参数
const getGetFaultKnowledgeById = async () => {
  await getFaultKnowledgeById({
    id: props.rows.id || ''
  })
    .then(({ data }) => {
      if (data.code == 200) {
        formData.value = {
          ...data.data
        }
        fileNameObj.value = {
          fileName: data.data.fileName ? data.data.fileName.split('@')[0] : '',
          originalFileName: data.data.fileName
            ? data.data.fileName.split('@')[1]
            : ''
        }
        getGetDeviceModelList_(data.data.brandCode)
      } else {
        formData.value = {}
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
let titleVal = ref('标题')
watch(
  () => props.typeVal,
  (val) => {
    if (val === 'add') {
      titleVal.value = '新建故障知识'
    } else if (val === 'edit') {
      titleVal.value = '编辑故障知识'
    }
  }
)
// 上传文件
const showInfo = async () => {
  ElMessage({
    message: '请先删除已上传的文件！',
    type: 'warning'
  })
}
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload && upload.value && upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.size / 1024 / 1024 > 50) {
    ElMessage.error('单个文件不超过50MB!')
    return false
  }
  return true
}
const handleAvatarSuccess: UploadProps['onSuccess'] = (response) => {
  if (response.code == 200) {
    fileNameObj.value = {
      ...response.data
    }
  } else {
    fileNameObj.value = {
      fileName: '',
      originalFileName: ''
    }
  }
}
const handleChange: UploadProps['onChange'] = (uploadFile) => {
  if (uploadFile.status == 'ready') {
    loadingOpen()
  }
  if (uploadFile.status == 'success' || uploadFile.status == 'fail') {
    if (uploadFile.status == 'fail') {
      ElMessage({
        message: '上传发生错误！',
        type: 'error'
      })
    }
    if (uploadFile.status == 'success') {
      ElMessage({
        message: '上传成功！',
        type: 'success'
      })
    }
    loadingClose()
  }
}
const customHeaders = ref({
  Authorization: getCookieValue('tsydev_spic_identify_uat')
})
const getFileExtension = (filename: any) => {
  return /[.]/.exec(filename) ? /[^.]+$/.exec(filename)[0] : undefined
}
// 下载
const handleDownload = async (file: any) => {
  try {
    const { data } = await request({
      url: '/operate/operatorsManage/downloadFaultKnowledgeFile',
      headers: { 'Content-Type': 'text/plan' },
      responseType: 'blob',
      method: 'post',
      data: `${file.fileName}@${fileNameObj.value.originalFileName}`,
      loading: true
    })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(data)
    link.download = file.originalFileName
    link.click()
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
// 表单信息
const getGetFaultDatabaseList_ = async () => {
  await getFaultDatabaseList()
    .then(({ data }) => {
      if (data.code == 200) {
        faultDatabaseArr.value = data.data
      } else {
        faultDatabaseArr.value = []
      }
    })
    .catch((err) => {
      faultDatabaseArr.value = []
      console.log(err)
    })
}
const getGetDeviceBrandList_ = async () => {
  await getDeviceBrandList()
    .then(({ data }) => {
      if (data.code == 200) {
        brandNameArr.value = data.data
      } else {
        brandNameArr.value = []
      }
    })
    .catch((err) => {
      brandNameArr.value = []
      console.log(err)
    })
}

const getGetDeviceModelList_ = async (brandCode: string) => {
  await getDeviceModelList({
    brandCode
  })
    .then(({ data }) => {
      if (data.code == 200) {
        deviceModelsCodeArr.value = data.data
      } else {
        deviceModelsCodeArr.value = []
      }
    })
    .catch((err) => {
      deviceModelsCodeArr.value = []
      console.log(err)
    })
}
const changeItem = (val: any) => {
  formData.value.deviceModelsName = ''
  formData.value.deviceModelsCode = ''
  val && getGetDeviceModelList_(val)
}
const formRules = reactive<FormRules<Record<string, any>>>({
  faultDatabaseId: [
    { required: true, message: '请选择关联故障类型', trigger: 'change' }
  ],
  faultTraits: [{ required: true, message: '请输入故障特征', trigger: 'blur' }],
  faultCause: [
    { required: true, message: '请输入故障根本原因', trigger: 'blur' }
  ],
  measures: [{ required: true, message: '请输入处理措施', trigger: 'blur' }]
})
// 查找name
const getName = (arr, val, key, label) => {
  let str = ''
  arr.length &&
    arr.forEach((item) => {
      if (item[key] == val) {
        str = item[label]
      }
    })
  return str
}
// 点击确定
const emit = defineEmits(['closeDialog', 'updateList'])
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      let fileName = ''
      if (fileNameObj.value.fileName && fileNameObj.value.originalFileName) {
        fileName = `${fileNameObj.value.fileName}@${fileNameObj.value.originalFileName}`
      }
      let params = {
        ...formData.value,
        brandCode: formData.value.brandCode || '', // 设备品牌名code
        deviceModelsCode: formData.value.deviceModelsCode || '', // 设备型号编码
        brandName: getName(
          brandNameArr.value,
          formData.value.brandCode,
          'brandCode',
          'brandName'
        ), // 设备品牌名称
        deviceModelsName: getName(
          deviceModelsCodeArr.value,
          formData.value.deviceModelsCode,
          'deviceModelsCode',
          'deviceModelsName'
        ), // 设备型号名称
        fileName
      }
      if (props.typeVal == 'add') {
        await saveOrUpdateFaultKnowledge(params, true)
          .then(({ data }) => {
            if (data.code == 200) {
              ElMessage({
                message: '新建成功',
                type: 'success'
              })
              dialogClose()
              emit('updateList')
            } else {
              ElMessage({
                message: data.message,
                type: 'error'
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
      } else {
        await saveOrUpdateFaultKnowledge(
          {
            ...params,
            id: id_.value
          },
          true
        )
          .then(({ data }) => {
            if (data.code == 200) {
              ElMessage({
                message: '编辑成功',
                type: 'success'
              })
              dialogClose()
              emit('updateList')
            } else {
              ElMessage({
                message: data.message,
                type: 'error'
              })
            }
          })
          .catch((err) => {
            console.log(err)
          })
      }
    }
  })
}
const deleteClick = () => {
  fileNameObj.value = {}
  upload && upload.value && upload.value!.clearFiles()
  ElMessage({
    message: '删除成功！',
    type: 'success'
  })
}
// 关闭弹窗
const dialogClose = () => {
  fileNameObj.value = {}
  deviceModelsCodeArr.value = []
  formRef.value && formRef.value.resetFields()
  upload && upload.value && upload.value!.clearFiles()
  emit('closeDialog', false)
}
</script>
<style lang="scss" scoped>
.upload-box {
  width: 60%;
  height: 180px;
  position: relative;
  .upload-demo {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    :deep(.el-upload-dragger) {
      background: rgba(41, 204, 160, 0.04);
    }
    .el-upload__text {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      p:first-child {
        font-size: 14px;
        display: flex;
        align-items: center;
        i {
          margin-right: 8px;
        }
      }
      p:last-child {
        font-size: 12px;
        color: #ccc;
      }
    }
  }
  .mask-box {
    width: 100%;
    height: 146px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100;
  }
}
</style>
<style lang="scss">
.elFrom2 {
  .el-form-item__content {
    .upload-box {
      .fileBox__ {
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 100%;
        display: flex;
        align-items: center;
        background: rgba(246, 248, 250, 1);
        padding: 0 16px;
        box-sizing: border-box;
        .fileText {
          width: 62%;
          margin: 0 30px 0 8px;
        }
        .el-icon {
          cursor: pointer;
        }
      }
      .el-upload-list {
        display: none;
      }
    }
  }
}
</style>
