<template>
  <div class="list-pagination">
    <el-scrollbar>
      <div
        v-for="(item, index) in data"
        :key="index"
        v-preventReClick="1000"
        class="item"
        @click="viewDetail(item)"
      >
        <div class="levle"></div>
        <div class="levleType">{{ item.stationRate }}</div>
        <div class="title1">
          {{ item.stationName || '--' }}
        </div>
        <div class="item-header">
          <div class="items1">
            <span>地址：</span><span>{{ item.area || '--' }}</span>
          </div>
          <div class="items1">
            <span>运维商：</span
            ><span>{{ item.operationProviderName || '--' }}</span>
          </div>
          <div class="items1">
            <span>并网日期：</span>
            <span>
              {{
                item.connectedDate ? item.connectedDate.split(' ')[0] : '--'
              }}</span
            >
          </div>
        </div>
        <div class="item-content">
          <div class="content-box">
            <span class="sps">装机容量</span>
            <div class="spbs" :title="item.capins + ' kWp'">
              <b>{{ item.capins || '--' }}</b>
              <span>kWp</span>
            </div>
          </div>

          <div class="content-box">
            <span class="sps">发电功率</span>
            <div class="spbs" :title="item.generatingCapacity + ' kW'">
              <b>{{ item.generatingCapacity || '--' }}</b>
              <span>kW</span>
            </div>
          </div>

          <div class="content-box">
            <span class="sps">当日发电量</span>
            <div class="spbs" :title="item.dayPowerGeneration + ' kWh'">
              <b>
                {{ item.dayPowerGeneration || '--' }}
              </b>
              <span>kWh</span>
            </div>
          </div>

          <div class="content-box">
            <span class="sps">累计发电量</span>
            <div class="spbs" :title="item.totalPowerGeneration + ' kWh'">
              <b>{{ item.totalPowerGeneration || '--' }}</b>
              <span>kWh</span>
            </div>
          </div>

          <div class="content-box">
            <span class="sps">日利用小时</span>
            <div class="spbs" :title="item.dayUtilizationHours + ' h'">
              <b>{{ item.dayUtilizationHours || '--' }}</b>
              <span>h</span>
            </div>
          </div>
        </div>
      </div>

      <el-pagination
        style="background-color: #fff; height: 60px; padding-right: 10px"
        v-if="total"
        ref="paginationRef"
        m-t-16px
        justify-end
        :current-page="currentPage"
        :page-size="pageSize"
        v-bind="$attrs"
        :total="total"
        :pager-count="5"
        :page-count="pageCount"
        :prev-text="
          !($attrs.layout as string).includes('pager') ? '上一页' : undefined
        "
        :next-text="
          !($attrs.layout as string).includes('pager') ? '下一页' : undefined
        "
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </el-scrollbar>
  </div>
</template>
<script lang="ts" setup>
import { debounce } from '@/utils'
const router = useRouter()
import useSearchFormStore from '@/store/searchForm'
// const searchFormStore = useSearchFormStore()
// const tablePagination = ref<any>()
const paginationRef = ref<any>()
// const defaultHeight = ref<number | string>('auto')
type PropsType = {
  data: Record<string, any>[]
  total?: number
  pageSize?: number
  currentPage?: number
  multipleSelection?: any[]
  loading?: boolean
  pageCount?: number
  height?: number | string
  employeeIdArr?: any
}
const props = withDefaults(defineProps<PropsType>(), {
  total: 0,
  pageSize: 0,
  currentPage: 1,
  multipleSelection: undefined,
  loading: false,
  pageCount: undefined,
  height: undefined,
  employeeIdArr: undefined
})
let data: any = ref(props.data)
const viewDetail = (item: any) => {
  router.push({
    path: `/station/stationLow/detail/${item.stationCode}`,
    query: {
      hideBack: 'true'
    }
  })
}
const total = ref(props?.total || 0)
const currentPage = ref(props.currentPage || 1)
const pageSize = ref(props.pageSize || 10)

const resizeStatus = ref(Date.now())
const changeResizeStatus = () => {
  resizeStatus.value = Date.now()
}
const debounceFn = debounce(changeResizeStatus)
onMounted(() => {
  window.addEventListener('resize', debounceFn)
})
onUnmounted(() => {
  window.removeEventListener('resize', debounceFn)
})
watch(
  () => props.data,
  () => {
    data.value = props.data
    total.value = props.total || 0
  },
  {
    deep: true,
    immediate: true
  }
)

watch(
  () => props?.pageSize,
  (val: number | undefined) => {
    if (val) pageSize.value = val
  }
)
watch(
  () => props?.currentPage,
  (val: number | undefined) => {
    if (val) currentPage.value = val
  }
)
// const computedHeight = async () => {
//   if (!props.height) {
//     await nextTick()
//     const tableEl = tablePagination.value && tablePagination.value.$el
//     const parentEl = tableEl && tableEl.offsetParent
//     if (!parentEl) return
//     const parentHeight = parentEl.offsetHeight
//     const tableTop = tableEl.offsetTop
//     const paginationHeight = props.total ? 48 : 0
//     const paddingBottomHeight =
//       Number(
//         window.getComputedStyle(parentEl)?.paddingBottom?.replace('px', '')
//       ) || 0
//     defaultHeight.value =
//       parentHeight - tableTop - paginationHeight - paddingBottomHeight
//   }
// }

// watch(
//   [() => props.total, () => resizeStatus.value, () => searchFormStore.height],
//   () => {
//     computedHeight()
//   },
//   { deep: true, immediate: true }
// )
const emits = defineEmits([
  'handleSizeChange',
  'handleCurrentChange',
  'update:pageSize',
  'update:currentPage'
])
// 当前页展示条数改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  emits('update:pageSize', val)
  emits('handleSizeChange', {
    currentPage: currentPage.value,
    pageSize: val
  })
}
// 当前页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  emits('update:currentPage', val)
  emits('handleCurrentChange', {
    currentPage: val,
    pageSize: pageSize.value
  })
}
// defineExpose({ tablePagination, computedHeight })
</script>

<style lang="scss" scoped>
.list-pagination {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .el-scrollbar {
    padding: 0 !important;
  }
  .item {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 24px;
    padding: 24px;
    box-sizing: border-box;
    background-color: #fff;
    position: relative;
    cursor: pointer;
    .levleType {
      color: rgba(255, 255, 255, 1);
      font-size: 12px;
      font-weight: 600;
      position: absolute;
      right: 13px;
      top: 9px;
      transform: rotate(45deg);
    }
    .levle {
      position: absolute;
      right: 0;
      top: 0;
      background: url('../../../assets/images/levles.png') no-repeat center
        center;
      background-size: 100% 100%;
      width: 48px;
      height: 48px;
    }
    .title1 {
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
    }
    .item-header {
      display: flex;
      margin: 24px 0;
      .items1 {
        font-size: 14px;
        flex: 1;
        display: flex;
        span:first-child {
          color: rgba(0, 0, 0, 0.45);
          font-size: 14px;
          font-weight: 400;
        }
        span:nth-child(2) {
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
          font-weight: 400;
          margin-left: 8px;
        }
      }
    }
    .item-content {
      display: flex;
      justify-content: space-between;
      .content-box {
        background: rgba(246, 248, 250, 1);
        padding: 12px 0 12px 16px;
        box-sizing: border-box;
        border-radius: 4px;
        width: 19%;
        .sps {
          font-size: 14px;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.65);
        }
        .spbs {
          margin-top: 12px;
          display: flex;
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          b {
            font-size: 24px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
          }
          span {
            font-size: 14px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
            margin-left: 8px;
            padding-top: 10px;
            box-sizing: border-box;
          }
        }
      }
    }
  }
}
.el-scrollbar {
  padding-right: 12px;
}
</style>
<style lang="scss">
.el-pagination__editor.el-input {
  width: 64px;
}
</style>
