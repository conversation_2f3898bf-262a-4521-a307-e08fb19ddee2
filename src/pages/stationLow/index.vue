<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="92px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-if="isShow" class="chart-box">
      <div class="left-box">
        <div class="zsBox"></div>
        <ECharts :key="chartKey" :option="optionPie" class="flex2"></ECharts>
      </div>
      <div class="right-box">
        <div class="zsBox"></div>
        <div class="box1">
          <ECharts :key="chartKey" :option="optionItem"></ECharts>
        </div>

        <div class="flex3">
          <div class="sp1Box">
            <div class="sp1">A级电站数量</div>
            <div class="sp2">{{ astationsNum }}</div>
          </div>
          <div class="sp2Box">
            <div class="sp1">B级电站数量</div>
            <div class="sp2">{{ bstationsNum }}</div>
          </div>
          <div class="sp3Box">
            <div class="sp1">C级电站数量</div>
            <div class="sp2">{{ cstationsNum }}</div>
          </div>
          <div class="sp4Box">
            <div class="sp1">D级电站数量</div>
            <div class="sp2">{{ dstationsNum }}</div>
          </div>
          <div class="sp5Box">
            <div class="sp1">E级电站数量</div>
            <div class="sp2">{{ estationsNum }}</div>
          </div>
        </div>
      </div>
    </div>
    <div :class="[isShow ? 'main calc324' : 'main calc88']">
      <div v-if="tableData.data.length == 0" class="mainBox">
        <span>暂无数据</span>
      </div>

      <list-pagination
        v-else
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNumber"
        :total="tableData.total"
        :data="tableData.data"
        :show-overflow-tooltip="true"
        background
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
      </list-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import listPagination from './components/list-pagination.vue'
import searchForm from '@/components/search-form.vue'
import { stationLow as stationLowApi } from '@/api'
import ECharts from '@/components/charts.vue'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()

const chartKey = ref(0)
const textTotalNum = ref(0)
const astationsNum = ref(0)
const bstationsNum = ref(0)
const cstationsNum = ref(0)
const dstationsNum = ref(0)
const estationsNum = ref(0)

const isShow = ref(false)
const optionPie = ref<Obj>({
  tooltip: {
    show: false,
    trigger: 'none'
  },
  labelLine: {
    show: false
  },
  legend: {
    show: false
  },
  title: {
    show: true,
    text: textTotalNum.value,
    x: 'center',
    y: '34%',
    textStyle: {
      color: 'rgba(0,0,0,0.85)',
      fontSize: 28,
      fontWeight: 500
    },
    subtext: '电站总数',
    subtextStyle: {
      color: 'rgba(0,0,0,0.85)',
      fontSize: 14,
      fontWeight: 400
    }
  },
  series: [
    {
      name: 'pie',
      type: 'pie',
      radius: ['78%', '100%'],
      itemStyle: {
        borderRadius: 0,
        color: function (params: any) {
          if (params.data.type == 1) {
            return 'rgba(41, 204, 160, 1)'
          }
          if (params.data.type == 2) {
            return 'rgba(24, 144, 255, 1)'
          }
          if (params.data.type == 3) {
            return 'rgba(255, 184, 34, 1)'
          }
          if (params.data.type == 4) {
            return 'rgba(73, 216, 247, 1)'
          }
          if (params.data.type == 5) {
            return 'rgba(247, 136, 73, 1)'
          }
        }
      },
      label: {
        show: false
      },
      data: []
    }
  ]
})

const optionItem = ref<Obj>({
  tooltip: {
    show: false,
    trigger: 'none'
  },
  labelLine: {
    show: false
  },
  legend: {
    show: false
  },
  title: [],
  series: [
    {
      type: 'pie',
      radius: ['80%', '100%'],
      showEmptyCircle: true,
      itemStyle: {
        borderRadius: 0,
        color: function (params: any) {
          if (params.data.type == 1) {
            return 'rgba(41, 204, 160, 1)'
          }
          if (params.data.type == 2) {
            return 'rgba(240, 242, 245, 1)'
          }
        }
      },
      label: {
        show: true,
        fontSize: 20,
        fontWeight: 500,
        color: 'rgba(0, 0, 0, 0.85)',
        position: 'center',
        formatter: (params: any) => {
          return params.data.showValue + '%'
        }
      },
      data: [],
      left: '-80%',
      right: 0,
      top: 0,
      bottom: 0
    },
    {
      left: '-40%',
      right: 0,
      top: 0,
      bottom: 0,
      type: 'pie',
      radius: ['80%', '100%'],
      showEmptyCircle: true,
      itemStyle: {
        borderRadius: 0,
        color: function (params: any) {
          if (params.data.type == 1) {
            return 'rgba(24, 144, 255, 1)'
          }
          if (params.data.type == 2) {
            return 'rgba(240, 242, 245, 1)'
          }
        }
      },
      label: {
        show: true,
        fontSize: 20,
        fontWeight: 500,
        color: 'rgba(0, 0, 0, 0.85)',
        position: 'center',
        formatter: (params: any) => {
          return params.data.showValue + '%'
        }
      },
      data: []
    },
    {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      type: 'pie',
      radius: ['80%', '100%'],
      showEmptyCircle: true,
      itemStyle: {
        borderRadius: 0,
        color: function (params: any) {
          if (params.data.type == 1) {
            return 'rgba(255, 184, 34, 1)'
          }
          if (params.data.type == 2) {
            return 'rgba(240, 242, 245, 1)'
          }
        }
      },
      label: {
        show: true,
        fontSize: 20,
        fontWeight: 500,
        color: 'rgba(0, 0, 0, 0.85)',
        position: 'center',
        formatter: (params: any) => {
          return params.data.showValue + '%'
        }
      },
      data: []
    },
    {
      left: '40%',
      right: 0,
      top: 0,
      bottom: 0,
      type: 'pie',
      radius: ['80%', '100%'],
      showEmptyCircle: true,
      itemStyle: {
        borderRadius: 0,
        color: function (params: any) {
          if (params.data.type == 1) {
            return 'rgba(73, 216, 247, 1)'
          }
          if (params.data.type == 2) {
            return 'rgba(240, 242, 245, 1)'
          }
        }
      },
      label: {
        show: true,
        fontSize: 20,
        fontWeight: 500,
        color: 'rgba(0, 0, 0, 0.85)',
        position: 'center',
        formatter: (params: any) => {
          return params.data.showValue + '%'
        }
      },
      data: []
    },
    {
      left: '80%',
      right: 0,
      top: 0,
      bottom: 0,
      type: 'pie',
      radius: ['80%', '100%'],
      showEmptyCircle: true,
      itemStyle: {
        borderRadius: 0,
        color: function (params: any) {
          if (params.data.type == 1) {
            return 'rgba(247, 136, 73, 1)'
          }
          if (params.data.type == 2) {
            return 'rgba(240, 242, 245, 1)'
          }
        }
      },
      label: {
        show: true,
        fontSize: 20,
        fontWeight: 500,
        color: 'rgba(0, 0, 0, 0.85)',
        position: 'center',
        formatter: (params: any) => {
          return params.data.showValue + '%'
        }
      },
      data: []
    }
  ]
})
let searchData = ref<Obj>({
  companyCode: '',
  stationName: '',
  stationRate: '',
  pageNumber: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    label: '资产所属公司',
    width: '102px',
    prop: 'companyCode',
    type: 'companySelect',
    placeholder: '请选择资产所属公司'
  },
  {
    label: '电站名称',
    width: '74px',
    prop: 'stationName',
    placeholder: '请输入电站名称',
    maxlength: 64,
    type: 'input2'
  },
  {
    label: '电站评级',
    width: '74px',
    prop: 'stationRate',
    type: 'select',
    options: [
      { label: 'D', value: 'D' },
      { label: 'E', value: 'E' }
    ]
  }
])
const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
const handleSearch = async (val: any) => {
  searchData.value.pageNumber = 1
  searchData.value.pageSize = 10
  searchData.value = val
  getTableData()
}

const tableLoading = ref(false)
const getTableData = async () => {
  try {
    const { data } = await stationLowApi.getStatisticsList(
      { ...searchData.value, code: localStorage.getItem('PVOM_COMPANY_CODE') },
      true
    )
    tableData.data = data?.data.stationVoList || []
    tableData.total = data?.data.total || 0

    astationsNum.value = data?.data.astationsNum || 0
    bstationsNum.value = data?.data.bstationsNum || 0
    cstationsNum.value = data?.data.cstationsNum || 0
    dstationsNum.value = data?.data.dstationsNum || 0
    estationsNum.value = data?.data.estationsNum || 0

    // 电站总数
    textTotalNum.value =
      Number(astationsNum.value) +
      Number(bstationsNum.value) +
      Number(cstationsNum.value) +
      Number(dstationsNum.value) +
      Number(estationsNum.value)
    optionPie.value.title.text = textTotalNum.value
    if (textTotalNum.value == 0) {
      optionPie.value.series[0].data = []
    } else {
      optionPie.value.series[0].data = [
        {
          name: 'A级电站数量',
          value: astationsNum.value,
          type: 1
        },
        {
          name: 'B级电站数量',
          value: bstationsNum.value,
          type: 2
        },
        {
          name: 'C级电站数量',
          value: cstationsNum.value,
          type: 3
        },
        {
          name: 'D级电站数量',
          value: dstationsNum.value,
          type: 4
        },
        {
          name: 'E级电站数量',
          value: estationsNum.value,
          type: 5
        }
      ]
    }

    // A级电站数量
    if (astationsNum.value == 0) {
      optionItem.value.series[0].data = []
    } else {
      optionItem.value.series[0].data = [
        {
          name: 'A级电站数量',
          value: astationsNum.value,
          showValue: (
            (Number(astationsNum.value) / Number(textTotalNum.value)) *
            100
          ).toFixed(1),
          type: 1
        },
        {
          name: '',
          showValue: (
            (Number(astationsNum.value) / Number(textTotalNum.value)) *
            100
          ).toFixed(1),
          value: textTotalNum.value - astationsNum.value,
          type: 2
        }
      ]
    }

    // b级电站数量
    if (bstationsNum.value == 0) {
      optionItem.value.series[1].data = []
    } else {
      optionItem.value.series[1].data = [
        {
          name: 'B级电站数量',
          value: bstationsNum.value,
          showValue: (
            (Number(bstationsNum.value) / Number(textTotalNum.value)) *
            100
          ).toFixed(1),
          type: 1
        },
        {
          name: '',
          showValue: (
            (Number(bstationsNum.value) / Number(textTotalNum.value)) *
            100
          ).toFixed(1),
          value: textTotalNum.value - bstationsNum.value,
          type: 2
        }
      ]
    }

    // c级电站数量
    if (cstationsNum.value == 0) {
      optionItem.value.series[2].data = []
    } else {
      optionItem.value.series[2].data = [
        {
          name: 'C级电站数量',
          value: cstationsNum.value,
          showValue: (
            (Number(cstationsNum.value) / Number(textTotalNum.value)) *
            100
          ).toFixed(1),
          type: 1
        },
        {
          name: '',
          showValue: (
            (Number(cstationsNum.value) / Number(textTotalNum.value)) *
            100
          ).toFixed(1),
          value: textTotalNum.value - cstationsNum.value,
          type: 2
        }
      ]
    }
    // D级电站数量
    if (dstationsNum.value == 0) {
      optionItem.value.series[3].data = []
    } else {
      optionItem.value.series[3].data = [
        {
          name: 'D级电站数量',
          value: dstationsNum.value,
          showValue: (
            (Number(dstationsNum.value) / Number(textTotalNum.value)) *
            100
          ).toFixed(1),
          type: 1
        },
        {
          name: '',
          showValue: (
            (Number(dstationsNum.value) / Number(textTotalNum.value)) *
            100
          ).toFixed(1),
          value: textTotalNum.value - dstationsNum.value,
          type: 2
        }
      ]
    }

    //E级电站数量
    if (estationsNum.value == 0) {
      optionItem.value.series[4].data = []
    } else {
      optionItem.value.series[4].data = [
        {
          name: 'E级电站数量',
          value: estationsNum.value,
          showValue: (
            (Number(estationsNum.value) / Number(textTotalNum.value)) *
            100
          ).toFixed(1),
          type: 1
        },
        {
          name: '',
          showValue: (
            (Number(estationsNum.value) / Number(textTotalNum.value)) *
            100
          ).toFixed(1),
          value: textTotalNum.value - estationsNum.value,
          type: 2
        }
      ]
    }
    chartKey.value = Date.now()
    searchData.value.companyCode
      ? (isShow.value = true)
      : (isShow.value = false)
  } catch (e) {
    console.log(e)
  }
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  window.addEventListener('resize', () => {
    chartKey.value = Date.now()
  })
  companyCode.data && (await getTableData())
  startWatch = true
})
const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNumber = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNumber = params.currentPage
  getTableData()
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.page-main {
  box-shadow: none !important;
}
.page-search {
  margin-bottom: 24px !important;
}
.page-main .main {
  padding: 0 !important;
  background-color: transparent !important;
}
.page-main .main.calc88 {
  padding-bottom: 20px !important;
  height: calc(100% - 100px);
}
.page-main .main.calc324 {
  padding-bottom: 20px !important;
  height: calc(100% - 324px);
}
.mainBox {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    font-size: 14px;
    color: #666;
  }
}
.chart-box {
  height: 228px;
  width: 100%;
  background-color: #fff;
  margin-bottom: 24px;
  padding: 24px 40px 24px;
  box-sizing: border-box;
  display: flex;
  .left-box {
    width: 276px;
    height: 100%;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    .zsBox {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;
    }
  }
  .right-box {
    flex: 1;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    .zsBox {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;
    }
    .box1 {
      width: 100%;
      height: calc(100% - 68px);
    }
    .flex3 {
      flex: 1;
      display: flex;
      align-items: center;
      .sp1Box {
        flex: 1;
        text-align: center;
        .sp1 {
          font-weight: 600;
          font-size: 18px;
          color: rgba(0, 0, 0, 0.65);
          margin: 8px 0;
        }
        .sp2 {
          font-size: 16px;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
          font-weight: 400;
        }
      }
      .sp2Box {
        flex: 1;
        text-align: center;
        .sp1 {
          font-weight: 600;
          font-size: 18px;
          color: rgba(0, 0, 0, 0.65);
          margin: 8px 0;
        }
        .sp2 {
          font-size: 16px;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
        }
      }
      .sp3Box {
        flex: 1;
        text-align: center;
        .sp1 {
          font-weight: 600;
          font-size: 18px;
          color: rgba(0, 0, 0, 0.65);
          margin: 8px 0;
        }
        .sp2 {
          font-size: 16px;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
        }
      }

      .sp4Box {
        flex: 1;
        text-align: center;
        .sp1 {
          font-weight: 600;
          font-size: 18px;
          color: rgba(0, 0, 0, 0.65);
          margin: 8px 0;
        }
        .sp2 {
          font-size: 16px;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
        }
      }

      .sp5Box {
        flex: 1;
        text-align: center;
        .sp1 {
          font-weight: 600;
          font-size: 18px;
          color: rgba(0, 0, 0, 0.65);
          margin: 8px 0;
        }
        .sp2 {
          font-size: 16px;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
  }
}
</style>
