<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import type { FormInstance } from 'element-plus'
import * as api from '@/api/index.ts'
import DownloadSmallSvg from '@/assets/svgs/downloadSmall.svg'
import LinkSvg from '@/assets/svgs/link.svg'
import request from '@/utils/request'

// 搜索
const searchFormRef = ref<FormInstance>()
const searchData = reactive({
  faultTypeName: '',
  faultCode: '',
  pageNum: 1,
  pageSize: 10
})
const handleReset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  searchData.pageNum = 1
  searchData.pageSize = 10
  handleSearch(formEl)
}
const handleSearch = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  searchData.pageNum = 1
  searchData.pageSize = 10
  await getTableData()
}

const columns = [
  {
    prop: 'expand',
    label: '',
    width: 36
  },
  {
    prop: 'radio-selection',
    label: ''
  },
  {
    prop: 'faultTypeName',
    label: '故障类型名称',
    minWidth: 120
  },
  { prop: 'faultLevelName', label: '故障等级', slotName: 'faultLevelName' },
  {
    prop: 'faultCode',
    label: '故障类型编码',
    minWidth: 120
  },
  {
    prop: 'deviceType',
    label: '所属设备类型',
    minWidth: 120,
    formatter: (row: any) => {
      if (!row.deviceType) return '--'
      return row.deviceType.split('_').at(0)
    }
  }
]
const tableData = reactive<{ total: number; data: any[] }>({
  data: [],
  total: 0
})
const tableLoading = ref(false)
const getTableData = async () => {
  const { data } = await api.post({
    url: '/operate/fault-database/getDeviceListServiceGo',
    data: searchData,
    loading: [tableLoading]
  })
  tableData.total = data.total || 0
  tableData.data = data.records || []
}
const handleSizeChange = async (params: any) => {
  searchData.pageNum = 1
  searchData.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.pageNum = params.currentPage
  searchData.pageSize = params.pageSize
  await getTableData()
}
const selectedData = ref<Record<string, any>>({})
const selectTableCell = async (row: Record<string, any>) => {
  selectedData.value = { ...row }
  selectedData2.value = {}
  await nextTick()
  tableKey2.value = Date.now() + ''
  window.parent.postMessage(
    {
      data1: { ...row },
      data2: {}
    },
    '*'
  )
}

const searchData2 = reactive<{
  pageNum: number
  pageSize: number
  parent: Record<string, any>
}>({
  pageNum: 1,
  pageSize: 10,
  parent: {}
})
const columns2 = [
  {
    prop: 'radio-selection',
    label: ''
  },
  {
    type: 'click',
    prop: 'faultTypeName',
    label: '关联故障类型',
    minWidth: 120,
    click(row: any) {
      detailData.value = { ...row }
      detailDialog.value = true
    }
  },
  {
    prop: 'faultCode',
    label: '故障类型编码',
    minWidth: 120
  },
  {
    prop: 'deviceModelsName',
    label: '所属设备型号',
    minWidth: 120,
    formatter: (row: any) => {
      if (!row.deviceModelsName) return '--'
      return row.brandName + row.deviceModelsName
    }
  }
]
const tableData2 = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
const tableLoading2 = ref(false)
const getTableData2 = async () => {
  const parent = searchData2.parent
  const { data } = await api.post({
    url: '/operate/fault-database/getFaultKnowledgeListServiceGo',
    data: {
      id: parent.id || null
    },
    loading: [tableLoading2]
  })
  tableData2.total = 0
  tableData2.data = data || []

  tableData2.data = tableData2.data.map((e: any) => {
    return {
      ...e,
      parent,
      faultTypeName: parent.faultTypeName,
      faultCode: parent.faultCode
    }
  })
}
const handleSizeChange2 = async (params: any) => {
  searchData2.pageNum = 1
  searchData2.pageSize = params.pageSize
  getTableData2()
}
const handleCurrentChange2 = async (params: any) => {
  searchData2.pageNum = params.currentPage
  searchData2.pageSize = params.pageSize
  getTableData2()
}
const selectedData2 = ref<Record<string, any>>({})
const selectStatus = computed(() => {
  if (tableData2.data.length === 0) {
    return false
  }
  if (!selectedData.value?.id) {
    return false
  }
  return tableData2.data?.[0]?.parent?.id === selectedData.value?.id
})
const tableRef2 = ref()
const selectTableCell2 = (row: Record<string, any>) => {
  if (selectStatus.value) {
    selectedData2.value = { ...row }
  } else {
    tableRef2.value.tablePagination.setCurrentRow()
  }
  const obj = JSON.parse(JSON.stringify(selectedData2.value))
  delete obj.parent
  window.parent.postMessage(
    {
      data1: { ...selectedData.value },
      data2: { ...obj }
    },
    '*'
  )
}
const expandRowKeys = ref<any[]>([])
const tableKey2 = ref<string>('')
const expandTableCell = async (row: Record<string, any>, expandedRows: any) => {
  if (expandedRows.length) {
    expandRowKeys.value = [row.id]
    searchData2.parent = { ...row }
    await getTableData2()
    await nextTick()
    if (selectedData2.value.id) {
      tableKey2.value = Date.now() + ''
    }
  } else {
    expandRowKeys.value = []
  }
}

const detailDialog = ref(false)
const detailData = ref<Record<string, any>>({})

const downloadFile = async (fileName: string) => {
  try {
    const { data } = await request({
      url: '/operate/operatorsManage/downloadFaultKnowledgeFile',
      headers: { 'Content-Type': 'text/plan' },
      responseType: 'blob',
      method: 'post',
      data: fileName,
      loading: true
    })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(data)
    link.download = fileName.split('@')[1]
    link.click()
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
onMounted(async () => {
  await getTableData()
  window.parent.addEventListener('message', (e) => {
    if (e.data === 'error') {
      ElMessage({
        type: 'error',
        message: '请选择故障类型！'
      })
    }
  })
})
</script>

<template>
  <div class="wrapper">
    <el-form
      ref="searchFormRef"
      :inline="true"
      :model="searchData"
      label-suffix=""
    >
      <el-row :gutter="24">
        <el-col :span="8">
          <el-form-item label="故障类型名称" prop="faultTypeName">
            <el-input
              v-model="searchData.faultTypeName"
              placeholder="请输入故障类型名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="故障类型编码" prop="faultCode">
            <el-input
              v-model="searchData.faultCode"
              placeholder="请输入故障类型编码"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" class="search-buttons">
          <el-form-item style="width: auto; margin-right: 0">
            <el-button plain @click="handleReset(searchFormRef)"
              >重置</el-button
            >
            <el-button type="primary" @click="handleSearch(searchFormRef)"
              >查询</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <vis-table-pagination
      :loading="tableLoading"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchData.pageSize"
      :current-page="searchData.pageNum"
      :columns="columns"
      :total="tableData.total"
      :data="tableData.data"
      :show-overflow-tooltip="true"
      row-key="id"
      highlight-current-row
      :expand-row-keys="expandRowKeys"
      background
      class="vis-table-pagination"
      @expand-change="expandTableCell"
      @row-click="selectTableCell"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    >
      <template #faultLevelName="{ row }">
        <el-tag v-if="row.faultLevelName === '重大'" type="danger">{{
          row.faultLevelName
        }}</el-tag>
        <el-tag v-else-if="row.faultLevelName === '严重'" type="warning">{{
          row.faultLevelName
        }}</el-tag>
        <el-tag v-else-if="row.faultLevelName" type="info">{{
          row.faultLevelName
        }}</el-tag>
        <template v-else>--</template>
      </template>
      <template #expand>
        <div ml-60px>
          <vis-table-pagination
            ref="tableRef2"
            :key="'childrenTable' + searchData2.parent.id + tableKey2"
            :loading="tableLoading2"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="searchData2.pageSize"
            :current-page="searchData2.pageNum"
            :columns="columns2"
            :total="tableData2.total"
            :data="tableData2.data"
            :show-overflow-tooltip="true"
            row-key="id"
            :highlight-current-row="selectStatus"
            :current-row-key="selectedData2.id || null"
            background
            class="vis-table-pagination"
            @row-click="selectTableCell2"
            @handle-size-change="handleSizeChange2"
            @handle-current-change="handleCurrentChange2"
          ></vis-table-pagination>
        </div>
      </template>
    </vis-table-pagination>
  </div>
  <el-dialog
    v-model="detailDialog"
    title="故障知识详情"
    align-center
    :before-close="() => ((detailData = {}), (detailDialog = false))"
    :close-on-click-modal="false"
    width="500px"
    class="vis-dialog"
  >
    <el-descriptions :column="1">
      <el-descriptions-item label="故障类型：">{{
        detailData.faultTypeName || '--'
      }}</el-descriptions-item>
      <el-descriptions-item label="故障特征：">{{
        detailData.faultTraits || '--'
      }}</el-descriptions-item>
      <el-descriptions-item label="故障根本原因：">{{
        detailData.faultCause || '--'
      }}</el-descriptions-item>
      <el-descriptions-item label="处理措施：">{{
        detailData.measures || '--'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备品牌：">{{
        detailData.brandName || '--'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备型号：">{{
        detailData.deviceModelsName || '--'
      }}</el-descriptions-item>
      <el-descriptions-item label="关键词：">{{
        detailData.keyword || '--'
      }}</el-descriptions-item>
      <el-descriptions-item label="附件：">
        <template v-if="!detailData.fileName">--</template>
        <div v-else class="file-wrapper">
          <img :src="LinkSvg" class="link" />
          <span class="text">
            {{ detailData.fileName.split('@')[1] }}
          </span>
          <img
            :src="DownloadSmallSvg"
            class="download"
            @click="downloadFile(detailData.fileName)"
          />
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>
<style lang="scss" scoped>
.wrapper {
  padding: 15px;
  height: 100vh;
  overflow: hidden;
}
:deep(.vis-table-pagination) {
  .el-table__row .radio-selection::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    border: solid 1px rgba(0, 0, 0, 0.15);
    border-radius: 50%;
    cursor: pointer;
  }
  .el-table__row.current-row .radio-selection::before {
    border: solid 3px rgba(41, 204, 160, 1);
  }
}
.file-wrapper {
  display: inline-flex;
  align-items: center;
  background: #f6f8fa;
  padding: 3px 10px;
  vertical-align: middle;
  border-radius: 4px;
  .link {
    display: inline-block;
    margin-right: 5px;
    width: 16px;
    height: 16px;
  }
  span {
    display: inline-block;
    max-width: 355px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #2acba0;
    align-items: center;
  }
  .download {
    display: inline-block;
    margin-left: 5px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
}
</style>
