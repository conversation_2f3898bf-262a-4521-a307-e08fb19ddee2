<script setup lang="ts">
import DownloadSmallSvg from '@/assets/svgs/downloadSmall.svg'
import LinkSvg from '@/assets/svgs/link.svg'
import * as api from '@/api/index.ts'
import request from '@/utils/request'

const route = useRoute()
const id = route.query?.id || null

const detailData = ref<Record<string, any>>({})

const getDetailData = async () => {
  const { data } = await api.get({
    url: '/operate/fault-database/getFaultKnowledgeInfo',
    data: { id },
    loading: true
  })
  detailData.value = data || {}
}
const downloadFile = async (fileName: string) => {
  try {
    const { data } = await request({
      url: '/operate/operatorsManage/downloadFaultKnowledgeFile',
      headers: { 'Content-Type': 'text/plan' },
      responseType: 'blob',
      method: 'post',
      data: fileName,
      loading: true
    })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(data)
    link.download = fileName.split('@')[1]
    link.click()
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

onMounted(() => {
  getDetailData()
})
</script>
<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-title">{{ $route.meta.title }}</div>
    </div>
    <div class="page-main">
      <el-descriptions :column="1">
        <el-descriptions-item label="故障类型：">{{
          detailData.faultTypeName || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="故障特征：">{{
          detailData.faultTraits || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="故障根本原因：">{{
          detailData.faultCause || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="处理措施：">{{
          detailData.measures || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="设备品牌：">{{
          detailData.brandName || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="设备型号：">{{
          detailData.deviceModelsName || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="关键词：">{{
          detailData.keyword || '--'
        }}</el-descriptions-item>
        <el-descriptions-item label="附件：">
          <template v-if="!detailData.fileName">--</template>
          <div v-else class="file-wrapper">
            <img :src="LinkSvg" class="link" />
            <span class="text">
              {{ detailData.fileName.split('@')[1] }}
            </span>
            <img
              :src="DownloadSmallSvg"
              class="download"
              @click="downloadFile(detailData.fileName)"
            />
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.page-main {
  background: #fff;
  width: 500px;
  margin-left: auto;
  margin-right: auto;
  padding-top: 5px;
  .el-descriptions {
    margin: 15px 24px;
  }
}
.file-wrapper {
  display: inline-flex;
  align-items: center;
  background: #f6f8fa;
  padding: 3px 10px;
  vertical-align: middle;
  border-radius: 4px;
  .link {
    display: inline-block;
    margin-right: 5px;
    width: 16px;
    height: 16px;
  }
  span {
    display: inline-block;
    max-width: 355px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #2acba0;
    align-items: center;
  }
  .download {
    display: inline-block;
    margin-left: 5px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
}
</style>
