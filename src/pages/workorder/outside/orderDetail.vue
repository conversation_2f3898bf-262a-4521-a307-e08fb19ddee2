<template>
  <el-descriptions title="工单详情" :column="1" pr-10px>
    <el-descriptions-item label="工单编号：">{{
      detailData.workOrderNo
    }}</el-descriptions-item>
    <el-descriptions-item label="工单标题："
      ><a
        :href="`https://servicego.tsy.spic.com.cn/site/custom-object/28422/records/view/${detailData.id}`"
        target="_blank"
        >{{ detailData.workOrderName }}</a
      ></el-descriptions-item
    >
    <el-descriptions-item label="工单状态：">{{
      detailData.orderState
    }}</el-descriptions-item>
    <el-descriptions-item label="创建时间：">
      {{ detailData.createTime }}
    </el-descriptions-item>
    <el-descriptions-item label="创建人：">{{
      detailData.createUser
    }}</el-descriptions-item>
    <el-descriptions-item label="工程师：">{{
      detailData.handlers
    }}</el-descriptions-item>
    <el-descriptions-item label="处理描述：">{{
      detailData.processDescription
    }}</el-descriptions-item>
    <el-descriptions-item label="处理完成图片：">
      <el-image
        v-if="detailData.processedImage"
        :src="WF_PATH + detailData.processedImage"
        :preview-src-list="[WF_PATH + detailData.processedImage]"
        fit="cover"
        class="w-80px h-80px"
      >
      </el-image>
      <span v-else>--</span>
    </el-descriptions-item>
  </el-descriptions>
</template>

<script setup lang="ts">
import request from '@/utils/request'
const route = useRoute()
const WF_PATH = import.meta.env.VITE_APP_WF_PATH

onMounted(() => {
  geStationInfo()
})
const detailData = ref<Record<string, any>>({})
const geStationInfo = async () => {
  try {
    const { data } = await request({
      url: '/operate/remote-access/queryOverhaulOrderDetail',
      method: 'get',
      params: {
        id: route.query.orderId
      }
    })
    detailData.value = data.data || {}
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
    detailData.value = {}
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-descriptions__cell) {
  display: inline-flex;
}
:deep(.el-descriptions__label) {
  margin-right: 12px !important;
  vertical-align: top;
  flex: none;
}
:deep(.el-descriptions__content) {
  flex: auto;
  word-break: break-all;
}
</style>
