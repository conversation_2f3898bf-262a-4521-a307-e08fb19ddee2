<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-suffix=""
    label-width="110px"
    pr-10px
  >
    <el-form-item label="工单标题" prop="title">
      <el-input
        v-model.trim="formData.title"
        placeholder="请输入工单标题"
        :maxlength="64"
        clearable
      />
    </el-form-item>

    <el-form-item label="选择电站" prop="stationCode">
      <StationSelect
        v-model="formData.stationCode"
        v-model:label="formData.stationName"
        @change="changeStation"
      />
    </el-form-item>

    <el-form-item label="运维公司" prop="operationCompany">
      <el-input
        v-model.trim="formData.operationCompany"
        placeholder="请输入运维公司"
        :maxlength="64"
        clearable
        :disabled="true"
      />
    </el-form-item>

    <el-form-item label="工程师" prop="engineer">
      <el-select
        v-model="formData.engineer"
        placeholder="请选择工程师"
        clearable
        filterable
        :disabled="engineerShow"
      >
        <template v-for="item in engineerArr" :key="item.value">
          <el-option :label="item.label" :value="item.value" />
        </template>
      </el-select>
    </el-form-item>

    <template v-if="route.params.pageType === '2'">
      <el-form-item label="巡检模板" prop="inspectionId">
        <el-select
          v-model="formData.inspectionId"
          placeholder="请选择巡检模板"
          filterable
          clearable
        >
          <el-option
            v-for="item in templates"
            :key="item.id"
            :label="item.insTemName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </template>
    <template v-if="isShowOnSiteOrder">
      <el-form-item label="巡检计划" prop="inspectionPlanId">
        <el-select-v2
          v-model="formData.inspectionPlanId"
          :options="inspectionPlanIdArr"
          placeholder="请选择巡检计划"
          filterable
          :props="devicePlanIdNoProps"
          clearable
        />
      </el-form-item>
    </template>
    <el-form-item label="关联其他工单" prop="relevancyWork">
      <el-select-v2
        v-model="formData.relevancyWork"
        :options="relevancyWorkArr"
        placeholder="请选择关联其他工单"
        filterable
        style="width: 100%"
        :props="relevancyWorkProps"
        clearable
      >
      </el-select-v2>
    </el-form-item>

    <el-form-item label="备注" prop="remark">
      <el-input
        v-model.trim="formData.remark"
        placeholder="请输入备注内容"
        clearable
        :rows="3"
        type="textarea"
        :maxlength="1024"
        autocomplete="off"
        show-word-limit
      />
    </el-form-item>
    <el-form-item>
      <el-button plain @click="onCancel">返回</el-button>
      <el-button v-preventReClick="1000" type="primary" @click="onsubmit">
        提交
      </el-button>
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import type { FormRules, FormInstance } from 'element-plus'
import { workorder as workorderAPI } from '@/api/index.ts'
import { educate as educateAPI } from '@/api/index.ts'
import StationSelect from '@/components/spic-station'
import useEnums from '../../deeect/hooks/useEnums.ts'
import { getCookieValue } from '@/utils'
import * as api from '@/api/index.ts'

let { defectSubclassIdArr, inspectionPlanIdArr } = useEnums()
const pageType: any = {
  1: '运维工单',
  2: '巡检工单',
  3: '清洗工单'
}
const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  inspectionId: '',
  stationCode: '',
  stationName: '',
  stationGroupNo: '',
  title: '',
  operationCompany: '',
  operationCompanyCode: '',
  allocated: '',
  engineer: '',
  defectClassId: '',
  defectSubclassId: '',
  defectType: '',
  defectSource: '',
  defectLevel: '',
  defectMsg: '',
  deviceNo: '',
  deviceTypesName: '',
  deviceSn: '',
  deviceModeName: '',
  deviceBrandName: '',
  remark: '',
  relevancyWork: '',
  inspectionTemplate: '',
  inspectionPlanId: ''
})

const templates = ref<any[]>([])
const getTemplates = async (companyId: any) => {
  const { data } = await api.post({
    url: `/operate/inspectionMainTemplateController/getInspectionTemplateList`,
    data: {
      operatorId: companyId
    },
    loading: false
  })
  templates.value = data || []
  formData.value.inspectionId = templates.value[0]?.id || ''
}
const engineerArr = ref<any[]>([]) // 工程师
const relevancyWorkArr = ref<any[]>([]) // 关联其他工单
const devicePlanIdNoProps = {
  label: 'name',
  value: 'id'
}
const formRules = reactive<FormRules<Record<string, any>>>({
  title: [{ required: true, message: '请输入工单标题', trigger: 'blur' }],
  inspectionId: [
    { required: true, message: '请选择工单模板', trigger: 'change' }
  ],
  stationCode: [{ required: true, message: '请选择电站', trigger: 'change' }],
  companyName: [
    { required: true, message: '请输入运维公司', trigger: 'change' }
  ],
  inspectionTemplate: [
    { required: true, message: '请输入巡检模板', trigger: 'change' }
  ]
})

const defectSubclassIdArr_ = ref<any[]>([])
const companyId = ref('')
defectSubclassIdArr_.value = defectSubclassIdArr.value
// 切换运维电站
const changeStation = async (e: any) => {
  formData.value.stationName = e.stationName
  formData.value.stationGroupNo = e.stationGroupNo
  // 接口-运维公司
  try {
    let res = await workorderAPI.getOperatorsInfo({
      stationCode: e.stationCode
    })
    formData.value.operationCompany = res.data.data.companyName || ''
    formData.value.operationCompanyCode = res.data.data.companyCode || ''
    formData.value.allocated = res.data.data.headUser || ''
    companyId.value = res.data.data.id
    getTemplates(companyId.value)
    // 接口-工程师
    generateData(res.data.data.companyCode || '')
    // 接口-判断工程师显示
    getEngineerShow(res.data.data.headUser || '')
  } catch (e) {
    formData.value.operationCompany = ''
  }
}
// 接口-工程师
const generateData = async (companyCode: string) => {
  try {
    let { data } = await educateAPI.getOperationUserAllList({
      companyCode: companyCode
    })
    if (data.code == 200) {
      data.data.forEach((item: any) => {
        item.value = item.userName + '_' + item.userId
        item.label = item.userName
      })
      engineerArr.value = data.data
    }
  } catch (e: any) {
    engineerArr.value = []
  }
}
//  巡检工单
const isShowOnSiteOrder = computed(() => {
  return route.params && route.params.pageType === '2'
})
// 关联其他工单
const relevancyWorkProps = ref({
  label: 'title',
  value: 'id'
})
const getAllWorkOrderList = async () => {
  try {
    let { data } = await workorderAPI.allWorkOrderList()
    relevancyWorkArr.value = data.data || []
  } catch (e) {
    relevancyWorkArr.value = []
  }
}
const engineerShow = ref(true)
const getEngineerShow = async (str: string) => {
  if (
    str &&
    getCookieValue('userNameId') &&
    str
      .split(',')
      ?.map((e: any) => e.split('_')[1])
      .includes(getCookieValue('userNameId')?.split('_')[1] || '')
  ) {
    engineerShow.value = false
  } else {
    engineerShow.value = true
  }
}
onMounted(() => {
  getAllWorkOrderList()
})
// 返回
const onCancel = () => {
  router.push('/workorder/listIndex')
}
// 提交
const onsubmit = async () => {
  await formRef.value?.validate(async (valid: any) => {
    if (valid) {
      try {
        let { data } = await workorderAPI.addWorkOrder({
          ...formData.value,
          workType: route.params && Number(route.params.pageType)
        })
        if (data.code == 200) {
          ElMessage({
            message: '操作成功',
            type: 'success'
          })
          onCancel()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e) {}
    }
  })
}
</script>
<!-- <style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style lang="scss" scoped>
.trainingContente__ {
  .el-textarea.el-input--default {
    position: relative;
    .el-input__count {
      position: absolute;
      bottom: 8px !important;
      right: 26px !important;
    }
  }
}
</style> -->
