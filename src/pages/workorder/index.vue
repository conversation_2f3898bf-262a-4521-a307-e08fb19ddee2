<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm :search-props="searchProps" :search-data="searchData" label-width="60px" @submit-emits="handleSearch"></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>工单列表</p>
        <div>
          <el-button type="primary" @click="handleAdd">
            <el-icon>
              <Plus />
            </el-icon> 新建工单
          </el-button>

          <el-button type="primary" :loading="exportLoading" @click="handleExport"><el-icon>
              <Download />
            </el-icon>导出</el-button>
        </div>
      </div>
      <div class="tables">
        <vis-table-pagination :loading="tableLoading" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100]" :page-size="searchData.pageSize" :current-page="searchData.pageNum" :columns="columns" :total="listTotal" :data="listData" :show-overflow-tooltip="true" background class="vis-table-pagination" @handle-size-change="handleSizeChange" @handle-current-change="handleCurrentChange">
          <template #workType="{ row }">
            <div class="linkBox">
              <img :src="row.workType == '1'
                ? Link1Svg
                : row.workType == '3'
                  ? Link2Svg
                  : row.workType == '2'
                    ? Link3Svg
                    : ''
                " class="linkSvg" />
              <span>{{
                row.workType == 1
                  ? '运维工单'
                  : row.workType == 2
                    ? '巡检工单'
                    : '清洗工单'
              }}</span>
            </div>
          </template>

          <template #stationName="{ row }">
            <span style="color: #29cca0; cursor: pointer" @click="toViewDetail(row)">{{ row.stationName }}</span>
          </template>

          <template #workState="{ row }">
            <el-tag v-if="row.workState == 1" class="tag__ tag__1">待指派</el-tag>
            <el-tag v-if="row.workState == 2" class="tag__ tag__2">待接单</el-tag>
            <el-tag v-if="row.workState == 3" class="tag__ tag__3">待开始</el-tag>
            <el-tag v-if="row.workState == 4" class="tag__ tag__4">处理中</el-tag>
            <el-tag v-if="row.workState == 5" class="tag__ tag__5">待验证</el-tag>
            <el-tag v-if="row.workState == 6" class="tag__ tag__6">已完成</el-tag>
            <el-tag v-if="row.workState == 7" class="tag__ tag__7">已取消</el-tag>
          </template>

          <template #operate="{ row }">
            <div class="table-operate">
              <el-button link @click="handleBtn(row)">查看详情</el-button>
              <el-popconfirm title="确认强制取消？" @confirm="handleCancel(row)" v-if="row.workState != 6 && row.workState != 7 && cancelPermission">
                <template #reference>
                  <el-button link type="danger" @click.stop>强制取消</el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" title="新建工单" width="436" :before-close="() => (dialogVisible = false)" class="vis-dialog">
      <el-radio-group v-model="radioType" class="radio-box">
        <el-radio value="1" size="large">运维工单</el-radio>
        <el-radio value="2" size="large">巡检工单</el-radio>
        <el-radio value="3" size="large">清洗工单</el-radio>
      </el-radio-group>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogSumbit(radioType)">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { workorder as workorderAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import useEnums from '../deeect/hooks/useEnums.ts'
import Link1Svg from '@/assets/svgs/Link1Svg.svg'
import Link2Svg from '@/assets/svgs/Link2Svg.svg'
import Link3Svg from '@/assets/svgs/Link3Svg.svg'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
let { workTypeArr, workStateArr } = useEnums()

const exportLoading = ref(false)
const router = useRouter()
const route = useRoute()

const cancelPermission = ref(false)
const checkCancelPermission = async () => {
  const result: any = await workorderAPI.getCancelButtonPermission()
  if (result.data.code == 200) {
    cancelPermission.value = result.data.data
  }
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  checkCancelPermission()
  companyCode.data && (await getTableData())
  startWatch = true
})

// 搜索
const searchData = ref({
  workType: null,
  stationCode: '',
  workState: null,
  pageNum: Number(route.query.pageNum) || 1,
  pageSize: Number(route.query.pageSize) || 10
})
const searchProps = ref([
  {
    type: 'select',
    label: '工单类型',
    prop: 'workType',
    span: 7,
    width: '80px',
    options: workTypeArr,
    filterable: true
  },
  {
    label: '电站名称',
    prop: 'stationCode',
    type: 'stationSelect',
    width: '86px',
    placeholder: '请选择电站',
    span: 8
  },
  {
    type: 'select',
    label: '工单状态',
    prop: 'workState',
    span: 7,
    width: '80px',
    options: workStateArr,
    filterable: true
  },
  {
    type: 'select',
    label: '来源',
    prop: 'workSource',
    span: 7,
    width: '80px',
    options: [
      { label: '手动创建', value: 1 },
      { label: '告警系统', value: 2 },
      { label: '沃丰迁移', value: 3 },
      { label: '大数据平台', value: 4 }
    ],
    filterable: true
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  await getTableData()
}

const columns = [
  {
    prop: 'workOrderNo',
    label: '工单编号'
  },
  {
    prop: 'title',
    label: '工单标题'
  },
  {
    prop: 'workType',
    label: '工单类型',
    slotName: 'workType'
  },
  {
    prop: 'stationName',
    label: '电站名称',
    width: 180,
    slotName: 'stationName'
  },
  {
    prop: 'stationCode',
    label: '电站编码'
  },
  {
    prop: 'workState',
    label: '工单状态',
    slotName: 'workState',
    width: 108
  },
  {
    prop: 'workSource',
    label: '来源',
    formatter: (row: any) => {
      return row.workSource == 1
        ? '手动创建'
        : row.workSource == 2
          ? '告警系统'
          : row.workSource == 3
            ? '沃丰迁移'
            : row.workSource == 4
              ? '大数据平台'
              : '--'
    }
  },
  {
    prop: 'createTime',
    label: '创建时间'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 130,
    fixed: 'right'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await workorderAPI.getWorkOrderList(
      { ...searchData.value, code: localStorage.getItem('PVOM_COMPANY_CODE') },
      [tableLoading]
    )
    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
  router.replace({
    path: route.path,
    query: {
      pageNum: searchData.value.pageNum,
      pageSize: searchData.value.pageSize
    }
  })
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
  router.replace({
    path: route.path,
    query: {
      pageNum: searchData.value.pageNum,
      pageSize: searchData.value.pageSize
    }
  })
}
const toViewDetail = (row: any) => {
  router.push({
    path: `/station/workorder/detail/${row.stationCode}`,
    query: {
      hideBack: 'true'
    }
  })
}
/**
 * 导出
 */
const handleExport = async () => {
  try {
    exportLoading.value = true
    let res = await workorderAPI.downloadOrder()
    let str = res.headers['content-disposition']
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(res.data)
    link.download = '工单列表.' + str.split('.')[str.split('.').length - 1]
    link.click()
  } catch (e) {
    ElMessage({
      type: 'warning',
      message: '导出失败'
    })
  } finally {
    exportLoading.value = false
  }
}
// 新建工单
const radioType = ref('')
const dialogVisible = ref(false)
const handleAdd = () => {
  dialogVisible.value = true
  radioType.value = '1'
}
const dialogSumbit = (val: string) => {
  router.push(`/workorder/addNewOrder?workType=${val}`) // 工单
}
const handleBtn = (row: Record<string, any>) => {
  router.push(`/workorder/lookorder/${row.id}`)
}
// 强制取消
const handleCancel = async (row: any) => {
  const result: any = await workorderAPI.forceCancelWorkOrder({ id: row.id })
  if (result.data.code == 200) {
    ElMessage({
      type: 'success',
      message: '操作成功'
    })
    getTableData()
  }
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
<style lang="scss" scoped>
.linkSvg {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.linkBox {
  display: flex;
  align-items: center;
}

.radio-box {
  display: flex !important;
  flex-direction: column !important;
  align-items: baseline !important;
  margin-bottom: 24px;
}

.vis-table-pagination {
  .el-button--danger {
    color: #e62e32 !important;
  }
}
</style>
