.page-box {
  .titleBox {
    padding: 24px 24px 0 24px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    .linkBox {
      display: flex;
      align-items: center;
      .bos {
        font-weight: 500;
        font-size: 18px;
        margin-left: 8px;
      }
      .linkSvg {
        width: 60px;
        height: 20px;
      }
    }
  }
  .timeline {
    width: 100%;
    padding: 0 24px 24px 24px;
    box-sizing: border-box;
    display: flex;
    .timelineItem:not(:first-child):after {
      content: '';
      position: absolute;
      width: 100%;
      border: 1px dashed #ccc;
      top: 8%;
      right: 50%;
      z-index: 99;
    }
    .timelineItem {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .words {
        font-size: 14px;
        color: #00000073;
        margin-top: 16px;
      }
      .words-div {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      b {
        font-size: 16px;
      }
      .hc1 {
        width: 20px;
        height: 20px;
        background: rgba(41, 204, 160, 1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        position: relative;
        z-index: 999;
        span {
          color: #fff;
          font-size: 12px;
        }
      }
      .hc2 {
        width: 20px;
        height: 20px;
        border: 1px solid rgba(41, 204, 160, 1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        position: relative;
        z-index: 999;
        span {
          background-color: rgba(41, 204, 160, 1);
          width: 10px;
          height: 10px;
          border-radius: 50%;
        }
      }
      .hc3 {
        width: 20px;
        height: 20px;
        border: 1px solid rgba(204, 204, 204, 1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        position: relative;
        z-index: 999;
        span {
          background-color: rgba(204, 204, 204, 1);
          width: 10px;
          height: 10px;
          border-radius: 50%;
        }
      }
    }
  }
}
