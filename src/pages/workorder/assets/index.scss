.info-deeect-base {
  margin: 16px 24px 24px !important;
  padding: 0 !important;

  .deeectInfo {
    background-color: #fff;
    .operate_ {
      height: 70px;
      padding: 24px;
      box-sizing: border-box;
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      p {
        display: flex;
        align-items: center;
        span:first-child {
          width: 4px;
          height: 16px;
          background-color: rgba(42, 203, 160, 1);
          margin-right: 8px;
        }
        span:last-child {
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: normal;
        }
      }
    }
    .tagBtn {
      position: absolute;
      left: 106px;
      top: 23px;
    }
    .editIcon {
      color: #29cca0;
      cursor: pointer;
      font-size: 14px;
    }
    .info-item {
      padding-bottom: 24px;
    }
    .label {
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
.page-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
  line-height: 64px;
  margin: 24px;
  height: 64px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  & > .el-button + .el-button {
    margin-left: 16px !important;
  }
  & > .el-button {
    min-width: 72px !important;
    height: 40px !important;
  }
}
/* tag_相关 */
.tag__ {
  font-size: 14px;
  font-weight: 400;
  width: 66px;
  height: 22px;
  line-height: 22px;
  text-align: center;
}
.tag__1 {
  background: rgba(255, 0, 0, 0.1);
  color: rgba(255, 0, 0, 1);
}

.tag__2 {
  background: rgba(16, 140, 255, 0.1);
  color: rgba(16, 140, 255, 1);
}

.tag__3 {
  background: rgba(41, 204, 160, 0.1);
  color: rgba(41, 204, 160, 1);
}
.tag__4 {
  background: rgba(92, 66, 255, 0.1);
  color: rgba(92, 66, 255, 1);
}
.tag__5 {
  background: rgba(251, 110, 30, 0.1);
  color: rgba(251, 110, 30, 1);
}
.tag__6 {
  background: rgba(248, 183, 16, 0.1);
  color: rgba(248, 183, 16, 1);
}
.tag__7 {
  background: rgba(122, 138, 153, 0.1);
  color: rgba(122, 138, 153, 1);
}
