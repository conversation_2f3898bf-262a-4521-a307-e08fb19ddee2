<template>
  <div v-if="tabsData.length" class="tabsBox">
    <el-tabs v-model="activeTabName" @tab-change="tabChange">
      <el-tab-pane
        v-for="item in tabsData"
        :key="item.inspectionName"
        :label="item.inspectionName"
        :name="item.inspectionName"
      >
      </el-tab-pane>
    </el-tabs>
    <div class="word-box">
      <template v-if="Object.keys(tabsFields).length">
        <div
          v-for="(fields, name) in tabsFields"
          :key="name"
          class="items-wrapper"
        >
          <div class="items-title">{{ name }}</div>
          <div class="items-content">
            <div
              v-for="item in fields"
              :key="item.inspectionName"
              class="items"
            >
              <div
                v-if="[1, 2, 4, 5, 6].includes(item.resultType)"
                class="displass"
              >
                <span :title="item.standardName">
                  <span
                    v-if="item.isRequired === 0"
                    style="margin-right: 0px; color: red"
                  >
                    *
                  </span>
                  {{ item.standardName }}：
                </span>
                <span>{{ item.textBig || item.textSmall || '--' }}</span>
              </div>
              <div v-if="item.resultType == 3" class="displass2">
                <span :title="item.standardName">
                  <span
                    v-if="item.isRequired === 0"
                    style="margin-right: 0px; color: red"
                  >
                    *
                  </span>
                  {{ item.standardName }}：
                </span>
                <span v-if="item.textBig.length === 0">--</span>
                <SpicUpload
                  v-else
                  v-model="item.textBig"
                  type="image"
                  :limit="3"
                  :disabled="true"
                />
              </div>
            </div>
          </div>
        </div>
      </template>
      <span v-else>--</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { workorder as workorderAPI } from '@/api/index.ts'
import SpicUpload from '@/components/spic-upload'
import { TabPaneName } from 'element-plus'

const props = defineProps({
  workOrderNo: {
    type: String,
    default: ''
  }
})
const activeTabName = ref('')
const tabsData = ref<any[]>([])
const tabsFields = ref<any>({})
const getTabs = async () => {
  try {
    let { data } = await workorderAPI.getInspectionRecode({
      orderNo: props.workOrderNo
    })
    tabsData.value = data.data
    activeTabName.value = data.data[0]?.inspectionName
    let tabsFieldsArr = data.data[0]?.treeChild || []
    tabsFieldsArr = tabsFieldsArr.map((e: any) => {
      return {
        ...e,
        inspectionResult: JSON.parse(e.inspectionResult),
        textBig: (function () {
          if (e.resultType == 3) {
            return e.textBig ? e.textBig.split(',') : []
          }
          return e.textBig
        })()
      }
    })
    tabsFields.value = Object.groupBy(
      tabsFieldsArr,
      (e: any) => e.inspectionName
    )
  } catch (e: any) {
    tabsData.value = []
    tabsFields.value = {}
  }
}
onMounted(() => {
  getTabs()
})

const tabChange = (val: TabPaneName) => {
  let tabsFieldsArr = []
  for (let i = 0; i < tabsData.value.length; i++) {
    let item: any = tabsData.value[i]
    if (item.inspectionName == val) {
      tabsFieldsArr = item.treeChild || []
    }
  }
  tabsFieldsArr = tabsFieldsArr.map((e: any) => {
    return {
      ...e,
      inspectionResult: JSON.parse(e.inspectionResult),
      textBig: (function () {
        if (e.resultType == 3) {
          return e.textBig ? e.textBig.split(',') : []
        }
        return e.textBig
      })()
    }
  })
  tabsFields.value = Object.groupBy(tabsFieldsArr, (e: any) => e.inspectionName)
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
<style scoped lang="scss">
.tabsBox {
  padding: 24px;
  box-sizing: border-box;
  background: #fff;
}
.word-box {
  .items-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
  }
  .items-content {
    display: flex;
    flex-wrap: wrap;
  }
  .items {
    width: 33.3%;
    margin-bottom: 24px;
    .displass {
      width: 100%;
      line-height: 30px;
      display: inline;
      text-align: left;
      span {
        width: 90%;
        margin-right: 10px;
        overflow: hidden; /* 隐藏超出容器的文本 */
        text-overflow: ellipsis; /* 使用省略符号表示文本被截断 */
      }
    }
    .displass2 {
      width: 100%;
      line-height: 30px;
      display: flex;
      flex-direction: column;
      text-align: left;
    }
  }
}
</style>
