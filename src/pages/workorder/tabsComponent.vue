<template>
  <div class="tabsBox">
    <el-tabs v-model="activeTabName" @tab-change="tabChange">
      <el-tab-pane label="打卡记录" name="1">
        <div v-auto-height style="height: 600px">
          <vis-table-pagination
            :loading="tableLoading"
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="searchData.pageSize"
            :current-page="searchData.pageNum"
            :columns="columns"
            :total="listTotal"
            :data="listData"
            :show-overflow-tooltip="true"
            background
            class="vis-table-pagination"
            @handle-size-change="handleSizeChange"
            @handle-current-change="handleCurrentChange"
          >
            <!-- 打卡类型1-签到,2-签退 -->
            <template #signType="{ row }">
              {{
                row.signType == 1 ? '签到' : row.signType == 2 ? '签退 ' : '--'
              }}
            </template>
            <!-- 经纬度 -->
            <template #latitude="{ row }">
              {{ row.longitude }},{{ row.latitude }}
            </template>
          </vis-table-pagination>
        </div>
      </el-tab-pane>
      <el-tab-pane label="处理信息" name="2">
        <div class="info-deeect-base" style="margin: 0 !important">
          <el-form
            ref="formRef"
            :rules="formRules"
            :model="formData"
            label-suffix=""
            label-position="right"
            :inline="true"
            class="w-full"
            label-width="110px"
          >
            <div class="deeectInfo">
              <div class="operate_" style="padding: 0; height: 48px">
                <p><span></span><span>处理时间</span></p>
              </div>

              <el-row :gutter="24">
                <el-col :span="10">
                  <el-form-item label="派单时间" prop="dispatch">
                    <div>{{ formData.dispatch || '--' }}</div>
                  </el-form-item>
                </el-col>

                <el-col :span="10" :offset="2">
                  <el-form-item label="接单时间" prop="takeOrder">
                    <div>{{ formData.takeOrder || '--' }}</div>
                  </el-form-item>
                </el-col>

                <el-col :span="10">
                  <el-form-item label="首次签到时间" prop="firstSignTime">
                    <div>{{ formData.firstSignTime || '--' }}</div>
                  </el-form-item>
                </el-col>

                <el-col :span="10" :offset="2">
                  <el-form-item label="最近签到时间" prop="lastSignTime">
                    <div>{{ formData.lastSignTime || '--' }}</div>
                  </el-form-item>
                </el-col>

                <el-col :span="10">
                  <el-form-item label="最近离场时间" prop="recentOut">
                    <div>{{ formData.recentOut || '--' }}</div>
                  </el-form-item>
                </el-col>

                <el-col :span="10" :offset="2">
                  <el-form-item label="服务结束时间" prop="serviceEnd">
                    <div>{{ formData.serviceEnd || '--' }}</div>
                  </el-form-item>
                </el-col>

                <el-col :span="10">
                  <el-form-item label="工单完成时间" prop="workDcompleted">
                    <div>{{ formData.workDcompleted || '--' }}</div>
                  </el-form-item>
                </el-col>

                <el-col :span="10" :offset="2">
                  <el-form-item label="工单处理时间" prop="disposalDate">
                    <div>
                      {{
                        formData.disposalDate
                          ? formData.disposalDate + '小时'
                          : '--'
                      }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <el-divider style="width: calc(100% - 24px); margin: 0 auto 24px" />
            <div class="deeectInfo">
              <div class="operate_" style="padding: 0; height: 48px">
                <p><span></span><span>过程信息</span></p>
              </div>

              <el-row :gutter="24">
                <el-col :span="10">
                  <el-form-item label="转回原因" prop="backCause">
                    <div style="line-height: 40px">
                      {{ formData.backCause || '--' }}
                    </div>
                  </el-form-item>
                </el-col>

                <el-col :span="10" :offset="2">
                  <el-form-item label="取消工单原因" prop="cancelCause">
                    <div style="line-height: 40px">
                      {{ formData.cancelCause || '--' }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane label="工单日志" name="3">
        <div class="rzBox">
          <div
            v-for="(item, index) in workOrderOperateLogList"
            :key="index"
            class="rz-item"
          >
            <!-- {{ item }} -->
            <span>{{ item.createTime }}</span>
            <span class="hc_ hc_1">
              <span>✓</span>
            </span>
            <b>{{ item.operateDesc }}</b>
            <div class="content">
              <span>操作人：{{ item.operateUserName }}</span>
              <div v-if="item.operateReason">
                {{ item.operateDesc
                }}{{
                  ['签到', '签退'].includes(item.operateDesc) ? '地点' : '原因'
                }}：{{ item.operateReason }}
              </div>
              <div
                v-if="
                  ['签到', '签退'].includes(item.operateDesc) &&
                  item.longitude &&
                  item.latitude
                "
              >
                经纬度：{{ item.longitude }}, {{ item.longitude }}
              </div>
              <div v-if="item.abnormalSignOutReason">
                异常签退原因：{{ item.abnormalSignOutReason }}
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup lang="ts">
import type { FormRules, FormInstance } from 'element-plus'
import visTablePagination from '@/components/table-pagination.vue'
import { workorder as workorderAPI } from '@/api/index.ts'
const route = useRoute()

const props = defineProps({
  detaileInfo: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
onMounted(async () => {
  getTableData()
})

// 打卡记录
const columns = [
  {
    prop: 'signType',
    label: '打卡类型',
    slotName: 'signType'
  },
  {
    prop: 'signTime',
    label: '签到时间'
  },
  {
    prop: 'addressInfo',
    label: '签到地点'
  },
  {
    prop: 'latitude',
    label: '经纬度坐标',
    slotName: 'latitude'
  },
  {
    prop: 'abnormalSignOutReason',
    label: '异常签退原因'
  }
]
const listTotal = ref<number>(0)
const searchData = ref({
  pageNum: 1,
  pageSize: 10
})
// tab切换
const activeTabName = ref('1')
const tabChange = (val: any) => {
  if (val == 1) {
    searchData.value.pageNum = 1
    searchData.value.pageSize = 10
    getTableData()
  }
}
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await workorderAPI.getSginRecords(
      {
        ...searchData.value,
        id: route.params && route.params.ids
      },
      [tableLoading]
    )
    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}
// 处理信息
const formRules = reactive<FormRules<Record<string, any>>>({})
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  dispatch: '',
  takeOrder: '',
  firstSignTime: '',
  lastSignTime: '',
  recentOut: '',
  serviceEnd: '',
  workDcompleted: '',
  disposalDate: '',
  backCause: '',
  cancelCause: ''
})
// 工单日志
const workOrderOperateLogList = ref<any[]>([])
watch(
  () => props.detaileInfo,
  (val) => {
    // 处理信息
    formData.value.dispatch = val?.dispatch
    formData.value.takeOrder = val?.takeOrder
    formData.value.firstSignTime = val?.firstSignTime
    formData.value.lastSignTime = val?.lastSignTime
    formData.value.recentOut = val?.recentOut
    formData.value.serviceEnd = val?.serviceEnd
    formData.value.workDcompleted = val?.workDcompleted
    formData.value.disposalDate = val?.disposalDate
    formData.value.backCause = val?.backCause
    formData.value.cancelCause = val?.cancelCause
    // 工单日志
    workOrderOperateLogList.value = val?.workOrderOperateLogList
  }
)
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
<style scoped lang="scss">
.tabsBox {
  padding: 24px;
  box-sizing: border-box;
}
.rzBox {
  display: flex;
  flex-direction: column;
  padding-bottom: 22px;
  .rz-item {
    height: 110px;
    display: flex;
    align-items: center;
    position: relative;
    b {
      font-size: 16px;
    }
    .content {
      position: absolute;
      left: 200px;
      top: 70px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
    }
  }
  .rz-item:not(:first-child):after {
    content: '';
    position: absolute;
    width: 1px;
    height: 74%;
    border: 1px dashed #ccc;
    top: -32px;
    left: 170px;
    z-index: 99;
  }
}
.hc_ {
  margin: 0 16px 0 24px;
}
.hc_1 {
  width: 20px;
  height: 20px;
  background: rgba(41, 204, 160, 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 999;
  span {
    color: #fff;
    font-size: 12px;
  }
}
.hc_2 {
  width: 20px;
  height: 20px;
  background: rgba(223, 52, 9, 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 999;
  span {
    color: #fff;
    font-size: 12px;
  }
}
.hc_3 {
  width: 20px;
  height: 20px;
  border: 1px solid rgba(41, 204, 160, 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 999;
  span {
    background-color: rgba(41, 204, 160, 1);
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}
.hc_4 {
  width: 20px;
  height: 20px;
  border: 1px solid rgba(204, 204, 204, 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 999;
  span {
    background-color: rgba(204, 204, 204, 1);
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}
</style>
