<template>
  <div class="page-box">
    <div class="titleBox">
      <div class="linkBox">
        <img
          :src="
            detaileInfo.workType == '1'
              ? maintenanceOrderSvg
              : detaileInfo.workType == '2'
                ? onSiteOrderSvg
                : detaileInfo.workType == '3'
                  ? cleanOrderSvg
                  : ''
          "
          class="linkSvg"
        />
        <div class="bos">
          {{ detaileInfo.title + '-' + detaileInfo.workOrderNo || '--' }}
        </div>
      </div>
      <div>
        <el-button v-if="isQxShow" @click="qxOrderFn()">
          <el-icon><CircleClose /></el-icon>
          &nbsp;&nbsp;取消工单
        </el-button>
        <el-button v-if="isZpShow" type="primary" @click="zpOrderFn()">
          <el-icon><FolderRemove /></el-icon>&nbsp;&nbsp;指派工单
        </el-button>
      </div>
    </div>
    <!-- 工单信息 -->
    <div class="info-deeect-base">
      <div class="deeectInfo">
        <div class="operate_">
          <p><span></span><span>工单信息</span></p>

          <div class="tagBtn">
            <el-tag v-if="detaileInfo.workState == 1" class="tag__ tag__1">
              待指派
            </el-tag>
            <el-tag v-if="detaileInfo.workState == 2" class="tag__ tag__2">
              待接单
            </el-tag>
            <el-tag v-if="detaileInfo.workState == 3" class="tag__ tag__3">
              待开始
            </el-tag>
            <el-tag v-if="detaileInfo.workState == 4" class="tag__ tag__4">
              处理中
            </el-tag>
            <el-tag v-if="detaileInfo.workState == 5" class="tag__ tag__5">
              待验证
            </el-tag>
            <el-tag v-if="detaileInfo.workState == 6" class="tag__ tag__6">
              已完成
            </el-tag>
            <el-tag v-if="detaileInfo.workState == 7" class="tag__ tag__7">
              已取消
            </el-tag>
          </div>

          <div class="editIcon" @click="editIconShow = !editIconShow">
            <p v-if="editIconShow">
              展开<el-icon><CaretBottom /></el-icon>
            </p>
            <p v-else>
              收起<el-icon><CaretTop /></el-icon>
            </p>
          </div>
        </div>
        <el-form
          v-show="!editIconShow"
          ref="formRefBase"
          :rules="formRulesBase"
          :model="formDataBase"
          label-suffix=""
          label-position="right"
          :inline="true"
          class="w-full"
          label-width="110px"
          style="padding-right: 50px"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="创建人" prop="createUser">
                <div>
                  {{ formDataBase.createUser || '--' }}
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="分配人" prop="allocated">
                <div>
                  {{ formDataBase.allocated || '--' }}
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="工程师" prop="engineer">
                <div>
                  {{ formDataBase.engineer || '--' }}
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="运维公司" prop="operationCompany">
                <div>
                  {{ formDataBase.operationCompany || '--' }}
                </div>
              </el-form-item>
            </el-col>

            <!-- <el-col v-if="detaileInfo.workType == '2'" :span="12">
              <el-form-item label="巡检模板" prop="inspectionId">
                <el-select
                  v-model="formDataBase.inspectionId"
                  placeholder="请选择巡检模板"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in templates"
                    :key="item.id"
                    :label="item.insTemName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col> -->

            <el-col v-if="detaileInfo.workType == '2'" :span="12">
              <el-form-item label="巡检计划" prop="inspectionPlanId">
                <el-select-v2
                  v-model="formDataBase.inspectionPlanId"
                  :options="inspectionPlanIdArr"
                  placeholder="请选择巡检计划"
                  filterable
                  :props="devicePlanIdNoProps"
                  clearable
                  :disabled="isOverShow"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="来源" prop="workSource">
                <div>
                  {{ formDataBase.workSource }}
                </div>
              </el-form-item>
            </el-col>
            <el-divider style="width: calc(100% - 72px); margin: 0 auto 24px" />
            <el-col :span="12">
              <el-form-item label="关联其他工单" prop="relevancyWork">
                <el-select-v2
                  v-model="formDataBase.relevancyWork"
                  :options="relevancyWorkArr"
                  placeholder="请选择关联其他工单"
                  filterable
                  :props="relevancyWorkProps"
                  clearable
                  :disabled="isOverShow"
                />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model.trim="formDataBase.remark"
                  placeholder="请输入备注"
                  clearable
                  :rows="3"
                  type="textarea"
                  :maxlength="1024"
                  autocomplete="off"
                  show-word-limit
                  :disabled="isSubmitShow"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <!-- 流程进度 -->
    <div class="info-deeect-base">
      <div class="deeectInfo">
        <div class="operate_">
          <p><span></span><span>流程进度</span></p>
        </div>

        <div class="timeline">
          <div
            v-for="(item, index) in workOrderOperateProcessList"
            :Key="index"
            class="timelineItem"
          >
            <span v-if="item.isStep == 1" class="hc1"> <span>✓</span> </span>
            <span v-if="item.isStep == 2" class="hc2"> <span></span> </span>
            <span v-if="item.isStep == 3" class="hc3"> <span></span> </span>
            <b>{{
              item.operateDesc == '取消工单审批通过'
                ? '已取消'
                : item.operateDesc
            }}</b>
            <div v-if="item.operateUserName || item.createTime" class="words">
              <div
                v-if="item.operateDesc != '取消工单审批通过'"
                class="words-div"
              >
                <span>操作人：{{ item.operateUserName || '--' }}</span>
                <span>{{ item.createTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 关联缺陷 -->
    <div v-if="detaileInfo.workType == '1'" class="info-deeect-base">
      <div class="deeectInfo">
        <div class="operate_">
          <p><span></span><span>关联缺陷</span></p>
        </div>
        <el-form
          ref="formRefDefect"
          :rules="formRulesDefect"
          :model="defectMsgForm"
          label-suffix=""
          label-position="right"
          :inline="true"
          class="w-full"
          label-width="110px"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="缺陷大类" prop="defectClassId">
                {{
                  getName(
                    defectClassIdArr,
                    defectMsgForm.defectClassId,
                    'id',
                    'defectClassName'
                  ) || '--'
                }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="缺陷小类" prop="defectSubclassId">
                {{
                  getName(
                    defectSubclassIdArr,
                    defectMsgForm.defectSubclassId,
                    'id',
                    'defectSubclassName'
                  ) || '--'
                }}
              </el-form-item>
            </el-col>

            <!-- <el-col :span="12">
              <el-form-item label="缺陷类型" prop="defectType">
                {{
                  getName(
                    defectTypeArr,
                    defectMsgForm.defectType,
                    'value',
                    'label'
                  ) || '--'
                }}
              </el-form-item>
            </el-col> -->

            <el-col :span="12">
              <el-form-item label="缺陷级别" prop="defectLevel">
                {{
                  getName(
                    defectLevelArr,
                    defectMsgForm.defectLevel,
                    'value',
                    'label'
                  ) || '--'
                }}
              </el-form-item>
            </el-col>

            <!-- <el-col :span="12">
              <el-form-item label="缺陷原因" prop="defectSource">
                {{
                  getName(
                    defectSourceArr,
                    defectMsgForm.defectSource,
                    'value',
                    'label'
                  ) || '--'
                }}
              </el-form-item>
            </el-col> -->

            <el-col :span="24">
              <el-form-item label="缺陷描述" prop="defectMsg">
                {{ defectMsgForm.defectMsg || '--' }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <!-- 工作票信息 -->
    <div
      v-if="
        (detaileInfo.workSource == 3 && detaileInfo.workType != '2') ||
        isShowTwoSeedTicket
      "
      class="info-deeect-base"
    >
      <div class="deeectInfo">
        <div class="operate_">
          <p><span></span><span>工作票信息</span></p>
          <div
            v-if="twoSeedTicket.status || ticketWorkVo.status"
            class="tagBtn"
            style="margin-left: 20px"
          >
            <el-tag class="tag__ tag__2">
              {{
                twoSeedTicketStatus[twoSeedTicket.status || ticketWorkVo.status]
              }}
            </el-tag>
          </div>
          <div v-if="!twoSeedTicket.status && !ticketWorkVo.status">
            <el-button @click="() => (dialogVisibleGlC = true)">
              <el-icon><Plus /></el-icon>
              &nbsp;&nbsp;创建工作票
            </el-button>
            <el-button @click="glOrderFn()">
              <el-icon><Paperclip /></el-icon>
              &nbsp;&nbsp;关联工作票
            </el-button>
          </div>
        </div>
        <el-form
          v-if="detaileInfo.ticketType == 1 || ticketWorkVo.status"
          ref="formRefWork"
          :rules="formRulesWork"
          :model="ticketWorkVo"
          label-suffix=""
          label-position="right"
          :inline="true"
          class="w-full"
          label-width="110px"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="工作票编号" prop="ticketNo">
                <div
                  v-if="ticketWorkVo.ticketWorkNo"
                  style="
                    display: flex;
                    color: rgba(41, 204, 160, 1);
                    cursor: pointer;
                  "
                  @click="
                    toWorkTitle(ticketWorkVo.ticketWorkNo, ticketWorkVo.id)
                  "
                >
                  <img :src="link" class="linkSvg" />
                  {{ ticketWorkVo.ticketWorkNo || '--' }}
                </div>
                <div v-else>--</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作票类型" prop="type">
                <div>电气一种工作票</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联操作票" prop="operateTickets">
                <div>{{ ticketWorkVo.operateTickets || '--' }}</div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="签发人" prop="issuer">
                <div>
                  {{ ticketWorkVo?.signer?.split('_')?.[0] || '--' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="许可人" prop="approval">
                <div>
                  {{ ticketWorkVo?.licensor?.split('_')?.[0] || '--' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="负责人" prop="head">
                <div>
                  {{ ticketWorkVo?.workLeader?.split('_')?.[0] || '--' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="值长" prop="workGrow">
                <div>
                  {{
                    ticketWorkVo?.shiftChiefOperator?.split('_')?.[0] || '--'
                  }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开始时间" prop="workStartTime">
                <div>
                  {{ ticketWorkVo.startTime || '--' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间" prop="workEndTime">
                <div>
                  {{ ticketWorkVo.endTime || '--' }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form
          v-if="detaileInfo.ticketType == 2 || twoSeedTicket.status"
          ref="formRefWork"
          :rules="formRulesWork"
          :model="twoSeedTicket"
          label-suffix=""
          label-position="right"
          :inline="true"
          class="w-full"
          label-width="110px"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="工作票编号" prop="ticketNo">
                <div
                  v-if="twoSeedTicket.ticketNo"
                  style="
                    display: flex;
                    color: rgba(41, 204, 160, 1);
                    cursor: pointer;
                  "
                  @click="toWorkTitle(twoSeedTicket.ticketNo, twoSeedTicket.id)"
                >
                  <img :src="link" class="linkSvg" />
                  {{ twoSeedTicket.ticketNo || '--' }}
                </div>
                <div v-else>--</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作票类型" prop="type">
                <div>电气二种工作票</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联操作票" prop="relevancyOperate">
                <div>{{ twoSeedTicket.relevancyOperate || '--' }}</div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="签发人" prop="issuer">
                <div>
                  {{ twoSeedTicket?.issuer?.split('_')?.[0] || '--' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="许可人" prop="approval">
                <div>
                  {{ twoSeedTicket?.approval?.split('_')?.[0] || '--' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="负责人" prop="head">
                <div>
                  {{ twoSeedTicket?.head?.split('_')?.[0] || '--' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="值长" prop="workGrow">
                <div>
                  {{ twoSeedTicket?.workGrow?.split('_')?.[0] || '--' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开始时间" prop="workStartTime">
                <div>
                  {{ twoSeedTicket.workStartTime || '--' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间" prop="workEndTime">
                <div>
                  {{ twoSeedTicket.workEndTime || '--' }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 设备信息 -->
    <div v-if="detaileInfo.workType == '1'" class="info-deeect-base">
      <div class="deeectInfo">
        <div class="operate_">
          <p><span></span><span>设备信息</span></p>
        </div>
        <el-form
          ref="formRefSb"
          :rules="formRulesSb"
          :model="deviceVO"
          label-suffix=""
          label-position="right"
          :inline="true"
          class="w-full"
          label-width="110px"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="设备名称" prop="deviceNo">
                {{
                  getName(
                    deviceNoArr,
                    deviceVO.deviceNo,
                    'deviceUniqueId',
                    'deviceName'
                  ) || '--'
                }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="设备类型" prop="deviceTypesName">
                {{ deviceVO.deviceTypesName || '--' }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="SN码" prop="deviceSn">
                {{ deviceVO.deviceSn || '--' }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="设备型号" prop="deviceModeName">
                {{ deviceVO.deviceModeName || '--' }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="设备品牌" prop="deviceBrandName">
                {{ deviceVO.deviceBrandName || '--' }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 电站信息 -->
    <div class="info-deeect-base">
      <div class="deeectInfo">
        <div class="operate_">
          <p><span></span><span>电站信息</span></p>
        </div>
        <el-form
          ref="formRefDz"
          :rules="formRulesDz"
          :model="stationInfo"
          label-suffix=""
          label-position="right"
          :inline="true"
          class="w-full"
          label-width="110px"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="电站名称" prop="stationName">
                {{ stationInfo.stationName || '--' }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="电站编码" prop="stationUniqueId">
                {{ stationInfo.stationUniqueId || '--' }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="电站类型" prop="tyPower">
                {{
                  stationInfo.tyPower === 0
                    ? '户用'
                    : stationInfo.tyPower === 1
                      ? '工商业'
                      : '--'
                }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="联系人" prop="nameHoh">
                {{ stationInfo.nameHoh || '--' }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="联系方式" prop="nimMpHoh">
                {{ stationInfo.nimMpHoh || '--' }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="电站经纬度" prop="longitude">
                {{
                  stationInfo.longitude && stationInfo.latitude
                    ? stationInfo.longitude + ',' + stationInfo.latitude
                    : '--'
                }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="行政区划" prop="cityName">
                {{ stationInfo.cityName || '--' }}
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="详细地址" prop="location">
                {{ stationInfo.location || '--' }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 工单处理结果 -->
    <div v-if="detaileInfo.workType != '2'" class="info-deeect-base">
      <div class="deeectInfo">
        <div class="operate_">
          <p><span></span><span>工单处理结果</span></p>
        </div>
        <el-form
          ref="formRefGz"
          :rules="formRulesGz"
          :model="formDataGz"
          label-suffix=""
          label-position="right"
          :inline="true"
          class="w-full"
          label-width="110px"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="现场签到照片" prop="signPhoto">
                <span v-if="formDataGz.signPhoto.length == 0">--</span>
                <SpicUpload
                  v-else
                  v-model="formDataGz.signPhoto"
                  type="image"
                  :limit="5"
                  :disabled="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="安全措施照片" prop="securePhoto">
                <span v-if="formDataGz.securePhoto.length == 0">--</span>
                <SpicUpload
                  v-else
                  v-model="formDataGz.securePhoto"
                  type="image"
                  :limit="5"
                  :disabled="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处理前照片" prop="beforePhoto">
                <span v-if="formDataGz.beforePhoto.length == 0">--</span>
                <SpicUpload
                  v-else
                  v-model="formDataGz.beforePhoto"
                  type="image"
                  :limit="5"
                  :disabled="true"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="处理后照片" prop="afterPhoto">
                <span v-if="formDataGz.afterPhoto.length == 0">--</span>
                <SpicUpload
                  v-else
                  v-model="formDataGz.afterPhoto"
                  type="image"
                  :limit="5"
                  :disabled="true"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="处理结果" prop="disposalResult">
                {{ formDataGz.disposalResult || '--' }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 巡检-光伏矩阵 -->
    <div v-if="detaileInfo.workType == '2'" class="info-deeect-base">
      <TabsOnSiteOrder :work-order-no="workOrderNo"></TabsOnSiteOrder>
    </div>

    <!-- 巡检结论 -->
    <div v-if="detaileInfo.workType == '2'" class="info-deeect-base">
      <div class="deeectInfo">
        <div class="operate_">
          <p><span></span><span>巡检结论</span></p>
        </div>
        <el-form
          ref="formRefXj"
          :rules="formRulesXj"
          :model="formDataXj"
          label-suffix=""
          label-position="right"
          :inline="true"
          class="w-full"
          label-width="110px"
        >
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="巡检结论" prop="concluded">
                {{ formDataXj.concluded || '--' }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="info-deeect-base">
      <div class="deeectInfo">
        <TabsComponent :detaile-info="detaileInfo"></TabsComponent>
      </div>
    </div>
  </div>
  <!-- 底部按钮 -->
  <div class="page-footer" style="margin-top: 0px">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button
      v-if="!isSubmitShow"
      v-preventReClick="1000"
      type="primary"
      @click="onsubmit"
    >
      提交
    </el-button>
  </div>
  <!-- 头部按钮 -->
  <div class="top-footer">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button
      v-if="!isSubmitShow"
      v-preventReClick="1000"
      type="primary"
      @click="onsubmit"
    >
      提交
    </el-button>
  </div>
  <!-- 创建工作票弹框 -->
  <el-dialog
    v-model="dialogVisibleGlC"
    width="500px"
    :title="'创建工作票'"
    class="vis-dialog"
    :before-close="
      () => {
        dialogVisibleGlC = false
      }
    "
  >
    <el-radio-group v-model="radio0" style="margin-bottom: 20px">
      <el-radio-button label="一种工作票" value="1" />
      <el-radio-button label="二种工作票" value="2" />
    </el-radio-group>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisibleGlC = false">取消</el-button>
        <el-button type="primary" @click="addOrderFn">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 关联工作票弹框 -->
  <el-dialog
    v-model="dialogVisibleGl"
    width="500px"
    :title="'关联工作票'"
    class="vis-dialog"
    :before-close="
      () => {
        dialogVisibleGl = false
      }
    "
  >
    <el-radio-group
      v-model="radio1"
      style="margin-bottom: 20px"
      @change="glValue = ''"
    >
      <el-radio-button label="一种工作票" value="1" />
      <el-radio-button label="二种工作票" value="2" />
    </el-radio-group>
    <div
      v-show="radio1 == '1'"
      key="one-ticket"
      style="display: flex; align-items: center; margin-bottom: 20px"
    >
      一种工作票 &nbsp;&nbsp;
      <rolling-select
        v-model="glValue"
        placeholder="请选择一种工作票"
        :query-data="paramsObjOne"
        :query-data2="queryData2"
        style="width: 70%"
        @filterable-options="filterableOptionsFn"
      ></rolling-select>
    </div>
    <div
      v-show="radio1 == '2'"
      key="two-ticket"
      style="display: flex; align-items: center; margin-bottom: 20px"
    >
      二种工作票 &nbsp;&nbsp;
      <rolling-select
        v-model="glValue"
        placeholder="请选择二种工作票"
        :query-data="paramsObj"
        :query-data2="queryData2"
        style="width: 70%"
        @filterable-options="filterableOptionsFn"
      ></rolling-select>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisibleGl = false">取消</el-button>
        <el-button type="primary" @click="dialogSumbitGl">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 取消工单弹框 -->
  <el-dialog
    v-model="dialogVisibleQx"
    width="500px"
    :title="'取消工单'"
    class="vis-dialog"
    :before-close="
      () => {
        dialogVisibleQx = false
      }
    "
  >
    <div style="font-size: 14px; margin-bottom: 8px">
      <span style="color: red">*</span>取消原因
    </div>
    <el-form
      ref="ruleFormRefQx"
      :model="ruleFormQx"
      :rules="rulesQx"
      label-width="auto"
      class="demo-ruleForm"
      status-icon
    >
      <el-form-item label="" prop="nameValue">
        <el-input
          v-model.trim="ruleFormQx.nameValue"
          placeholder="请输入取消原因"
          clearable
          :rows="4"
          type="textarea"
          :maxlength="1024"
          autocomplete="off"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisibleQx = false">取消</el-button>
        <el-button
          v-preventReClick="1000"
          type="primary"
          @click="dialogCloseQx"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 指派工单弹框 -->
  <el-dialog
    v-model="dialogVisibleZp"
    width="500px"
    :title="'运维工程师'"
    class="vis-dialog"
    :before-close="
      () => {
        dialogVisibleZp = false
      }
    "
  >
    <div style="margin-bottom: 60px">
      <el-form ref="gcRuleFormRef" :model="gcRuleForm" :rules="gcRules">
        <el-form-item label="工程师" prop="zpValue">
          <el-select
            v-model="gcRuleForm.zpValue"
            placeholder="请选择工程师"
            clearable
            filterable
          >
            <el-option
              v-for="item in engineerArr"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisibleZp = false">取消</el-button>
        <el-button
          v-preventReClick="1000"
          type="primary"
          @click="dialogSumitZp"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { educate as educateAPI } from '@/api/index.ts'
import { workorder as workorderAPI, ticket as ticketAPI } from '@/api/index.ts'
import type { FormRules, FormInstance } from 'element-plus'
import maintenanceOrderSvg from '@/assets/svgs/maintenanceOrderSvg.svg'
import onSiteOrderSvg from '@/assets/svgs/onSiteOrderSvg.svg'
import cleanOrderSvg from '@/assets/svgs/cleanOrderSvg.svg'
import SpicUpload from '@/components/spic-upload'
import TabsComponent from './tabsComponent.vue'
import useEnums from '../deeect/hooks/useEnums.ts'
import RollingSelect from '@/components/rolling-select.vue'
import TabsOnSiteOrder from './tabsOnSiteOrder.vue'
import { getCookieValue } from '@/utils'
import link from '@/assets/svgs/link.svg'
import useWorkorderStore from '@/store/workorder'

const radio0 = ref('1')
const radio1 = ref('1')
const useWorkorder = useWorkorderStore()

let {
  defectClassIdArr,
  defectSubclassIdArr,
  // defectTypeArr,
  defectLevelArr,
  // defectSourceArr,
  inspectionPlanIdArr
} = useEnums()

const detaileInfo = ref<Obj>({})
const router = useRouter()
const route = useRoute()

// 表单-工单信息
const editIconShow = ref(true)
const formRulesBase = reactive<FormRules<Record<string, any>>>({})
const formRefBase = ref<FormInstance>()
const formDataBase = ref<Record<string, any>>({
  createUser: '',
  allocated: '',
  engineer: '',
  operationCompany: '',
  workSource: '',
  relevancyWork: '',
  remark: '',
  inspectionId: '',
  inspectionPlanId: ''
})
const relevancyWorkArr = ref<any[]>([]) // 关联其他工单
const relevancyWorkProps = {
  label: 'title',
  value: 'id'
}
const getAllWorkOrderList = async () => {
  try {
    let { data } = await workorderAPI.allWorkOrderList({
      id: detaileInfo.value.id
    })
    relevancyWorkArr.value = data.data || []
  } catch (e) {
    relevancyWorkArr.value = []
  }
}
// 流程进度
const workOrderOperateProcessList = ref<any[]>([])
// 表单-关联缺陷
const formRulesDefect = reactive<FormRules<Record<string, any>>>({
  defectClassId: [
    { required: false, message: '请选择缺陷大类', trigger: 'change' }
  ],
  defectSubclassId: [
    { required: false, message: '请选择缺陷小类', trigger: 'change' }
  ],
  defectType: [
    { required: false, message: '请选择缺陷类型', trigger: 'change' }
  ],
  defectLevel: [
    { required: false, message: '请选择缺陷级别', trigger: 'change' }
  ],
  defectSource: [
    { required: false, message: '请选择缺陷原因', trigger: 'change' }
  ],
  defectMsg: [{ required: false, message: '请输入缺陷描述', trigger: 'blur' }]
})
const formRefDefect = ref<FormInstance>()
const defectMsgForm = ref<Record<string, any>>({
  defectClassId: '',
  defectSubclassId: '',
  defectType: '',
  defectLevel: '',
  defectSource: '',
  defectMsg: ''
})
// 表单-工作票信息
const formRulesWork = reactive<FormRules<Record<string, any>>>({})
const formRefWork = ref<FormInstance>()
const twoSeedTicket = ref<Record<string, any>>({
  id: '',
  status: '',
  ticketNo: '',
  type: '',
  relevancyOperate: '',
  issuer: '',
  approval: '',
  head: '',
  workGrow: '',
  workStartTime: '',
  workEndTime: ''
})
const ticketWorkVo = ref<any>({
  createTime: '',
  department: '',
  endTime: '',
  id: '',
  licensor: '',
  operateTickets: '',
  shiftChiefOperator: '',
  signer: '',
  startTime: '',
  stationName: '',
  status: '',
  ticketWorkNo: '',
  workLeader: ''
})
const addOrderFn = () => {
  if (radio0.value == '1') {
    router.push(route.path + `/ticket/work/add?type=add`)
  }
  if (radio0.value == '2') {
    router.push(route.path + `/ticket/two/add`)
  }
}
const twoSeedTicketStatus: any = {
  1: '草稿',
  2: '生效中',
  3: '已许可',
  4: '已终结',
  5: '已作废'
}
const paramsObjOne: any = {
  val_: 'ticketWorkNo',
  api_: 'getTickeFiltrationtList',
  label_: 'ticketWorkNo',
  value_: 'id'
}
const paramsObj: any = {
  val_: 'ticketNo',
  api_: 'getTwoSeedTickeFiltrationtList',
  label_: 'ticketNo',
  value_: 'id'
}
const queryData2 = ref<any>({
  stationCode: '',
  statusList: [1, 2, 3]
})
// 表单-设备信息
const filterableOptions = ref([])
const filterableOptionsFn = (val: any) => {
  filterableOptions.value = val
}
const formRulesSb = reactive<FormRules<Record<string, any>>>({
  deviceNo: [{ required: false, message: '请选择设备名称', trigger: 'change' }],
  deviceTypesName: [
    { required: false, message: '请输入设备类型', trigger: 'blur' }
  ],
  deviceSn: [{ required: false, message: '请输入SN码', trigger: 'blur' }],
  deviceModeName: [
    { required: false, message: '请输入设备型号', trigger: 'blur' }
  ],
  deviceBrandName: [
    { required: false, message: '请输入设备品牌', trigger: 'blur' }
  ]
})
const formRefSb = ref<FormInstance>()
const deviceVO = ref<Record<string, any>>({
  deviceNo: '',
  deviceTypesName: '',
  deviceSn: '',
  deviceModeName: '',
  deviceBrandName: ''
})
const deviceNoArr = ref<any[]>([])
// 接口选择设备
const getDeviceInfoListByStationCode = async (stationCode: string) => {
  try {
    let res = await workorderAPI.getDeviceInfoListByStationCode({
      stationCode
    })
    deviceNoArr.value = res.data.data
  } catch (e) {
    deviceNoArr.value = []
  }
}
// 表单-电站信息
const formRulesDz = reactive<FormRules<Record<string, any>>>({})
const formRefDz = ref<FormInstance>()
const stationInfo = ref<Record<string, any>>({
  stationName: '',
  stationUniqueId: '',
  tyPower: '',
  nameHoh: '',
  nimMpHoh: '',
  longitude: '',
  cityName: '',
  location: ''
})
// 表单-工单处理结果
const formRulesGz = reactive<FormRules<Record<string, any>>>({})
const formRefGz = ref<FormInstance>()
const formDataGz = ref<Record<string, any>>({
  beforePhoto: [],
  afterPhoto: [],
  signPhoto: [],
  securePhoto: [],
  disposalResult: ''
})
// 表单-巡检结论
const formRulesXj = reactive<FormRules<Record<string, any>>>({})
const formRefXj = ref<FormInstance>()
const formDataXj = ref<Record<string, any>>({
  concluded: ''
})
const devicePlanIdNoProps = {
  label: 'name',
  value: 'id'
}
// 弹窗-关联工作票
const dialogVisibleGl = ref(false)
const dialogVisibleGlC = ref(false)
const glValue = ref('')
const glOrderFn = () => {
  dialogVisibleGl.value = true
  glValue.value = ''
}
const dialogSumbitGl = async () => {
  let arr = filterableOptions.value.filter((item: any) => {
    return item.id == glValue.value
  })
  dialogVisibleGl.value = false
  detaileInfo.value.ticketType = radio1.value
  if (radio1.value == '1') {
    ticketWorkVo.value = JSON.parse(JSON.stringify(arr?.[0] || {}))
    twoSeedTicket.value = JSON.parse(JSON.stringify({}))
  }
  if (radio1.value == '2') {
    twoSeedTicket.value = JSON.parse(JSON.stringify(arr?.[0] || {}))
    ticketWorkVo.value = JSON.parse(JSON.stringify({}))
  }
}
// 弹窗-取消工单
interface RuleForm {
  nameValue: string
}
const dialogVisibleQx = ref(false)
const ruleFormRefQx = ref<any>()
const ruleFormQx = ref<RuleForm>({
  nameValue: ''
})
const rulesQx = reactive<FormRules<RuleForm>>({
  nameValue: [{ required: true, message: '请输入取消原因', trigger: 'blur' }]
})
const qxOrderFn = () => {
  dialogVisibleQx.value = true
  ruleFormQx.value.nameValue = ''
}
const dialogCloseQx = async () => {
  await ruleFormRefQx.value.validate(async (valid: any) => {
    if (valid) {
      try {
        let { data } = await workorderAPI.cancelWorkOrderVerifyApprove({
          businessId: route.params && route.params.ids,
          ticketWorkVO: {
            cancellation: ruleFormQx.value.nameValue
          }
        })
        if (data.code == 200) {
          ElMessage({
            message: '操作成功',
            type: 'success'
          })
          dialogVisibleQx.value = false
          getAllApiData()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e) {}
    }
  })
}
// 弹窗-指派工单
interface RuleForm1 {
  zpValue: string
}
const gcRuleFormRef = ref<any>()
const gcRuleForm = ref<any>({
  zpValue: ''
})
const gcRules = reactive<FormRules<RuleForm1>>({
  zpValue: [{ required: true, message: '请选择工程师', trigger: 'change' }]
})
const dialogVisibleZp = ref(false)
const engineerArr = ref<any[]>([])
const zpOrderFn = () => {
  dialogVisibleZp.value = true
  gcRuleForm.value.zpValue = ''
  // 接口-工程师
  generateData(detaileInfo.value.operationCompanyCode || '')
}
// 接口-工程师
const generateData = async (operationCompanyCode: string) => {
  try {
    let { data } = await educateAPI.getOperationUserAllList({
      companyCode: operationCompanyCode
    })
    if (data.code == 200) {
      data.data.forEach((item: any) => {
        item.value = item.userName + '_' + item.userId
        item.label = item.userName
      })
      engineerArr.value = data.data
    }
  } catch (e: any) {
    engineerArr.value = []
  }
}
const dialogSumitZp = async () => {
  await gcRuleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      try {
        let { data } = await workorderAPI.assignWorkOrder({
          workOrderId: Number(route.params && route.params.ids),
          engineer: gcRuleForm.value.zpValue || ''
        })
        if (data.code == 200) {
          ElMessage({
            message: '指派成功',
            type: 'success'
          })
          getAllApiData()
          dialogVisibleZp.value = false
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {}
    }
  })
}
// ============== 页面初始化 ==============
// 详情
onMounted(async () => {
  getAllApiData()
})
const getAllApiData = async () => {
  await getWorkOrderDetailVOFn()
  await getAllWorkOrderList()
}
const isShowTwoSeedTicket = ref(false)
const isOverShow = ref(false)
const isSubmitShow = ref(false)
const workOrderNo = ref('')
const isQxShow = ref(false)
const isZpShow = ref(false)
const getWorkOrderDetailVOFn = async () => {
  // 1-运维工单 2-巡检工单 3-清洗工单
  try {
    let { data } = await workorderAPI.getWorkOrderDetailVO({
      id: route.params && route.params.ids
    })
    detaileInfo.value = data.data
    workOrderNo.value = data?.data?.workOrderNo || ''
    // 工单信息
    formDataBase.value.createUser = data.data.createUser
      ? data.data.createUser.split('_')[0]
      : ''
    let allocated =
      data.data.allocated &&
      data.data.allocated
        .split(',')
        .map((item: any) => {
          return item.split('_')[0]
        })
        .join(',')
    formDataBase.value.allocated = allocated
    formDataBase.value.engineer = data.data.engineer
      ? data.data.engineer.split('_')[0]
      : ''
    formDataBase.value.operationCompany = data.data.operationCompany
    formDataBase.value.workSource =
      data.data.workSource == 1
        ? '手动创建'
        : data.data.workSource == 2
          ? '告警系统'
          : data.data.workSource == 3
            ? '沃丰迁移'
            : data.data.workSource == 4
              ? '大数据平台'
              : '--'
    formDataBase.value.relevancyWork = data.data.relevancyWork
      ? Number(data.data.relevancyWork)
      : ''
    formDataBase.value.remark = data.data.remark
    formDataBase.value.inspectionTemplate = data.data.inspectionTemplate
      ? data.data.inspectionTemplate
      : ''
    formDataBase.value.inspectionPlanId = data.data.inspectionPlanId

    let arr1 = [
      {
        operateDesc: '创建工单',
        operateUserName: '',
        createTime: '',
        isStep: null
      },
      {
        operateDesc: '派发工单',
        operateUserName: '',
        createTime: '',
        isStep: null
      },
      {
        operateDesc: '接收工单',
        operateUserName: '',
        createTime: '',
        isStep: null
      },
      {
        operateDesc: '处理工单',
        operateUserName: '',
        createTime: '',
        isStep: null
      },
      {
        operateDesc: '验收工单',
        operateUserName: '',
        createTime: '',
        isStep: null
      },
      {
        operateDesc: '已完成',
        operateUserName: '',
        createTime: '',
        isStep: null,
        type: '已完成'
      }
    ]
    let arr2 = [
      {
        operateDesc: '创建工单',
        operateUserName: '',
        createTime: '',
        isStep: 3
      },
      {
        operateDesc: '派发工单',
        operateUserName: '',
        createTime: '',
        isStep: 3
      },
      {
        operateDesc: '接收工单',
        operateUserName: '',
        createTime: '',
        isStep: 3
      },
      {
        operateDesc: '处理工单',
        operateUserName: '',
        createTime: '',
        isStep: 3
      },
      {
        operateDesc: '验收工单',
        operateUserName: '',
        createTime: '',
        isStep: 3
      },
      {
        operateDesc: '取消工单审批通过',
        operateUserName: '',
        createTime: '',
        isStep: 3,
        type: '已取消'
      }
    ]

    let newArr: any[] = []
    let workOrderOperateProcessListArr: any[] =
      data.data.workOrderOperateProcessList || []
    if (
      workOrderOperateProcessListArr.some((item: any) => {
        return item.operateDesc == '取消工单审批通过'
      })
    ) {
      newArr = arr2
      workOrderOperateProcessListArr.length &&
        workOrderOperateProcessListArr.forEach((item1) => {
          newArr.forEach((item2) => {
            if (item1.operateDesc == item2.operateDesc) {
              item2.operateUserName = item1.operateUserName || ''
              item2.createTime = item1.createTime || ''
              item2.isStep = 1
            }
          })
        })
    } else {
      newArr = arr1
      workOrderOperateProcessListArr.length &&
        workOrderOperateProcessListArr.forEach((item1) => {
          newArr.forEach((item2) => {
            if (item1.operateDesc == item2.operateDesc) {
              item2.operateUserName = item1.operateUserName || ''
              item2.createTime = item1.createTime || ''
              item2.isStep = 1
            }
          })
        })
      if (workOrderOperateProcessListArr.length == 5) {
        newArr[workOrderOperateProcessListArr.length].isStep = 1
      } else {
        if (workOrderOperateProcessListArr.length == 0) {
          newArr.forEach((item2) => {
            item2.isStep = 3
          })
        } else {
          newArr[workOrderOperateProcessListArr.length].isStep = 2
          newArr.forEach((item2) => {
            if (!item2.isStep) {
              item2.isStep = 3
            }
          })
        }
      }
    }
    workOrderOperateProcessList.value = newArr || []
    // 关联缺陷
    defectMsgForm.value = data.data.defectMsg || {}
    defectMsgForm.value.defectType =
      data.data.defectMsg && data.data.defectMsg.defectType
        ? String(data.data.defectMsg.defectType)
        : ''
    defectMsgForm.value.defectSource =
      data.data.defectMsg && data.data.defectMsg.defectSource
        ? String(data.data.defectMsg.defectSource)
        : ''
    // 工作票信息
    if (
      useWorkorder.ticketWorkVo.ticketWorkNo ||
      useWorkorder.twoSeedTicket.ticketNo
    ) {
      if (radio1.value == '1') {
        ticketWorkVo.value = JSON.parse(
          JSON.stringify(useWorkorder.ticketWorkVo || {})
        )
      }
      if (radio1.value == '2') {
        twoSeedTicket.value = JSON.parse(
          JSON.stringify(useWorkorder.twoSeedTicket || {})
        )
      }
    } else {
      if (data?.data?.ticketType == 1) {
        ticketWorkVo.value = JSON.parse(
          JSON.stringify(data.data?.ticketWorkVo || {})
        )
      }
      if (data?.data?.ticketType == 2) {
        twoSeedTicket.value = JSON.parse(
          JSON.stringify(data.data?.twoSeedTicket || {})
        )
      }
    }
    queryData2.value.stationCode = data.data.stationCode || ''
    if (data.data.workType == 1) {
      isShowTwoSeedTicket.value =
        data.data.defectMsg && data.data.defectMsg.isInvoice == 0 ? true : false // 判断展示工作票信息模块 是否开票：0-是、1-否
    } else if (data.data.workType == 3) {
      isShowTwoSeedTicket.value =
        (data.data.twoSeedTicket && data.data.twoSeedTicket.ticketNo) ||
        (data.data.ticketWorkVO && data.data.ticketWorkVO.ticketWorkNo)
    } else {
      isShowTwoSeedTicket.value = false
    }
    // 设备信息
    getDeviceInfoListByStationCode(data.data.stationCode)
    deviceVO.value.deviceNo =
      (data.data.deviceVO && data.data.deviceVO.deviceUniqueId) || ''
    deviceVO.value.deviceTypesName =
      (data.data.deviceVO && data.data.deviceVO.deviceTypesName) || ''
    deviceVO.value.deviceSn =
      (data.data.deviceVO && data.data.deviceVO.deviceSn) || ''
    deviceVO.value.deviceModeName =
      (data.data.deviceVO && data.data.deviceVO.deviceModeName) || ''
    deviceVO.value.deviceBrandName =
      (data.data.deviceVO && data.data.deviceVO.deviceBrandName) || ''
    // 电站信息
    stationInfo.value = data.data.stationInfo || {}

    // 工单处理结果
    formDataGz.value.signPhoto = data.data.signPhoto
      ? data.data.signPhoto.split(',')
      : []
    formDataGz.value.securePhoto = data.data.securePhoto
      ? data.data.securePhoto?.split(',')
      : []

    formDataGz.value.beforePhoto = data.data.beforePhoto
      ? data.data.beforePhoto.split(',')
      : []
    formDataGz.value.afterPhoto = data.data.afterPhoto
      ? data.data.afterPhoto?.split(',')
      : []
    formDataGz.value.disposalResult = data.data.disposalResult
    // 巡检结论
    formDataXj.value.concluded = data.data.concluded
    // 判断展示缺陷与设备模块-暂时隐藏-需求不稳定
    // 当工单状态为：处理中、待验证、已完成、已取消的时候，PC端展示缺陷跟设备模块相关字段。
    // 工单状态：1:待指派、2:待接单、3:待开始、4:处理中、5:待验证、6：已完成、7：已取消、8：待客服确认
    // 1:待指派、2:待接单、3:待开始、4:处理中、5:待验证、6：已完成、8：待客服确认    展示提交按钮
    // 7：已取消  不展示提交按钮
    // 判断提交按钮显示 || 判断可操作的数据进行禁用
    if (data.data.workState == 6 || data.data.workState == 7) {
      isOverShow.value = true
      if (data.data.workState == 7) {
        isSubmitShow.value = true
      }
    }
    // 判断取消工单按钮
    if (data.data.workState == 1 || data.data.workState == 4) {
      if (
        (data.data.allocated || data.data.createUser) &&
        getCookieValue('userNameId')
      ) {
        if (
          (data.data.allocated &&
            data.data.allocated
              .split(',')
              .includes(getCookieValue('userNameId'))) ||
          (data.data.createUser &&
            data.data.createUser.split('_')[1] ==
              getCookieValue('userNameId')?.split('_')[1])
        ) {
          isQxShow.value = true
        } else {
          isQxShow.value = false
        }
      } else {
        isQxShow.value = false
      }
    } else {
      isQxShow.value = false
    }
    // 指派工程师
    if (data.data.workState == 1) {
      getIsAllocated()
    } else {
      isZpShow.value = false
    }
  } catch (e) {
    detaileInfo.value = {}
  }
}
const getIsAllocated = async () => {
  try {
    let { data } = await workorderAPI.isAllocated({
      orderId: route.params && route.params.ids
    })
    // 判断指派工单按钮显示
    if (data.data && data.data.isAllocated) {
      isZpShow.value = true
    } else {
      isZpShow.value = false
    }
  } catch (e: any) {
    isZpShow.value = false
  }
}
const toWorkTitle = async (workTicketId_: any, id_: string) => {
  if (detaileInfo.value.ticketType == 1) {
    try {
      let { data } = await ticketAPI.checkGroup({
        orderNo: workTicketId_,
        type: 2 // 1-工单，2-一种票，3-二种票，4-操作票
      })
      if (data) {
        router.push(route.path + `/ticket/work/detail/${id_}`)
      } else {
        ElMessage({
          message: '权限不足',
          type: 'warning'
        })
      }
    } catch (e: any) {
      ElMessage({
        message: '权限不足',
        type: 'warning'
      })
    }
  }
  if (detaileInfo.value.ticketType == 2) {
    try {
      let { data } = await ticketAPI.checkGroup({
        orderNo: workTicketId_,
        type: 3 // 1-工单，2-一种票，3-二种票，4-操作票
      })
      if (data) {
        router.push(route.path + `/ticketTwo/detail/${id_}`)
      } else {
        ElMessage({
          message: '权限不足',
          type: 'warning'
        })
      }
    } catch (e: any) {
      ElMessage({
        message: '权限不足',
        type: 'warning'
      })
    }
  }
}
// 返回
const onCancel = () => {
  router.back()
}

// 提交
const onsubmit = async () => {
  try {
    let { data } = await workorderAPI.updateWorkOrder({
      id: Number(route.params && route.params.ids), // 主键
      remark: formDataBase.value.remark, // 备注
      relevancyWork: formDataBase.value.relevancyWork, // 关联其他工单
      ticketId:
        detaileInfo.value.ticketType == 1
          ? ticketWorkVo.value.id
          : detaileInfo.value.ticketType == 2
            ? twoSeedTicket.value.id
            : '',
      inspectionTemplate: formDataBase.value.inspectionTemplate || '', // 巡检模版
      inspectionPlanId: formDataBase.value.inspectionPlanId || '', // 巡检计划
      ticketType: radio1.value
    })
    if (data.code == 200) {
      ElMessage({
        message: '操作成功',
        type: 'success'
      })
      router.back()
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {}
}
// 查找name
const getName = (arr: any, val: any, key: any, label: any) => {
  let str = ''
  arr.length &&
    arr.forEach((item: any) => {
      if (item[key] == val) {
        str = item[label]
      }
    })
  return str
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
<style scoped src="./assets/lookorder.scss"></style>
<style lang="scss" scoped>
.top-footer {
  position: absolute;
  top: 16px;
  right: 24px;
}
</style>
