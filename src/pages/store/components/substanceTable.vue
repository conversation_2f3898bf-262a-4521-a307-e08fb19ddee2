<script setup lang="ts">
import TablePagination from '@/components/table-pagination.vue'
import useTableData from '@/hooks/useTableData.ts'
import { storeManage as storeManageAPI } from '@/api/index.ts'

interface Props {
  type?: 'edit' | 'show'
  serviceType?: 'in' | 'out' | 'check'
  defaultMaxlength?: 5
  getTableDataFn?: (
    data: Obj & { id: string | number },
    loading?: Loading
  ) => Promise<any>
}
const props = withDefaults(defineProps<Props>(), {
  type: 'show',
  serviceType: 'in',
  defaultMaxlength: 5,
  getTableDataFn: undefined
})

const columns: Obj[] = [
  {
    prop: 'goodsTypeName',
    label: '物资类型'
  },
  {
    prop: 'goodsCode',
    label: '物资编码'
  },
  {
    prop: 'goodsName',
    label: '物资名称'
  },
  {
    prop: 'ruleModel',
    label: '规格型号'
  },
  ...(function () {
    if (props.serviceType === 'in') {
      return [
        {
          prop: 'inQuantity',
          label: '入库数量',
          slotName: props.type === 'edit' ? 'inputNum' : null,
          width: 120
        }
      ]
    }
    if (props.serviceType === 'out') {
      if (props.type === 'show') {
        return [
          {
            prop: 'outQuantity',
            label: '出库数量',
            slotName: null,
            width: 100
          }
        ]
      } else {
        return [
          {
            prop: 'inventoryNum',
            label: '库存数量',
            slotName: 'inventoryNum'
          },
          {
            prop: 'outQuantity',
            label: '出库数量',
            slotName: 'inputNum',
            width: 100
          }
        ]
      }
    }
    if (props.serviceType === 'check') {
      return [
        {
          prop: 'inventoryNum',
          label: '库存数量',
          slotName: 'inventoryNum'
        },
        {
          prop: 'checkNum',
          label: '盘点数量',
          slotName: props.type === 'edit' ? 'inputNum' : null,
          width: 100
        },
        {
          prop: 'loseNum',
          label: '损益数量',
          slotName: 'calculateNum'
        }
      ]
    }
    return []
  })()
]
if (props.type === 'edit') {
  columns.push({
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 60,
    fixed: 'right'
  })
}
const formData = reactive(
  (function () {
    return {
      row: [Object.fromEntries(columns.map((e) => [e.prop || e.slotName, '']))]
    }
  })()
)

let getTableData: any = null
const localData = ref<any[]>([])
if (props.type === 'show') {
  getTableData = props.getTableDataFn
}
if (props.type === 'edit') {
  getTableData = async () => {
    const offer = (tablePage.pageNum - 1) * tablePage.pageSize
    const data = {
      total: localData.value.length,
      records: localData.value.slice(offer, offer + tablePage.pageSize)
    }
    return { data }
  }
}
const route = useRoute()
let key: any = route.query.key || 'id'
const {
  tableData,
  tablePage,
  tableLoading,
  changeCurrent,
  changeSize,
  changeData
} = useTableData(getTableData, { [key]: route.query.id })
const emit = defineEmits(['echoData'])

const add = async (arr: Obj[]) => {
  localData.value = arr.map((e: any) => {
    return {
      ...e,
      ...localData.value.find((o: any) => o.id === e.id)
    }
  })
  tablePage.pageNum = 1
  await changeData()
  formData.row = tableData.data
  emit('echoData', localData.value)
}
const clear = async () => {
  localData.value = []
  await changeData()
  formData.row = []
  emit('echoData', localData.value)
}
const checkInNum = async (data: Obj[]) => {
  localData.value = localData.value.map((e: any) => {
    return {
      ...e,
      inQuantity: data.find((o: any) => o.goodsId === e.id) ? '' : e.inQuantity,
      maxNum: data.find((o: any) => o.goodsId === e.id)
        ? 99999 - (data.find((o: any) => o.goodsId === e.id)?.inventoryNum || 0)
        : null
    }
  })
  await changeData()
  formData.row = tableData.data
}
const formRef = ref()
const formKey = ref(0)
const kcArr = ref<any[]>([])
const submit = async () => {
  if (localData.value.length === 0) {
    ElMessage({
      type: 'warning',
      message: '请添加物资！'
    })
    return
  }
  if (
    await formRef.value.validate(async (valid: boolean) => {
      if (!valid) {
        ElMessage({
          type: 'warning',
          message: '您在当前分页有物资数量未填写，请检查！'
        })
      }
    })
  ) {
    const checkall = localData.value.every((e: any) => {
      let bool = false
      if (props.serviceType === 'in') {
        bool = e.inQuantity > 0
      }
      if (props.serviceType === 'out') {
        bool = e.outQuantity > 0
      }
      if (props.serviceType === 'check') {
        if (e.checkNum === '') {
          bool = false
        } else {
          bool = Number(e.checkNum) >= 0
        }
      }
      if (!bool) {
        ElMessage({
          type: 'warning',
          message: `您在其他分页有物资数量未填写，请检查！`
        })
      }
      return bool
    })

    // 核对库存数量
    if (props.serviceType === 'out' || props.serviceType === 'check') {
      try {
        let { data } = await storeManageAPI.checkInventoryNum(localData.value)
        if (data.code == 200) {
          if (data.data.length) {
            let arr: any = []
            // 差异覆值
            data.data.forEach((item: any) => {
              arr.push(item.id)
              localData.value.length &&
                localData.value.forEach((items: any) => {
                  if (item.id == items.id) {
                    items.inventoryNum = item.inventoryNum
                    if (props.serviceType === 'out') {
                      items.outQuantity = ''
                    }
                  }
                })
            })
            kcArr.value = arr
            // 加提示
            ElMessage({
              type: 'warning',
              message: `${
                props.serviceType === 'check'
                  ? '库存数量发生变化，请核对后重新提交'
                  : '库存数量发生变化，请核对后重新输入'
              }`
            })
            return false
          }
        }
      } catch (e: any) {
        return false
        console.log(e)
      }
    }
    return checkall && localData.value
  }
}
const deleteItem = async (row: Obj) => {
  localData.value = localData.value.filter((e) => e.id !== row.id)
  tablePage.pageNum = 1
  await changeData()
  formData.row = tableData.data
  formKey.value = Date.now()
  emit('echoData', localData.value)
}

let oldNum = 0
const limitOutNum = (row: Obj) => {
  if (props.serviceType === 'in' || props.serviceType === 'out') {
    if (props.serviceType === 'out' && row.outQuantity > row.inventoryNum) {
      row.outQuantity = oldNum
      if (row.inventoryNum <= 0) {
        row.outQuantity = ''
      }
      ElMessage({
        type: 'warning',
        message: '出库数量不得大于库存数量！'
      })
    } else {
      oldNum = row.outQuantity
    }
    let strVal = props.serviceType === 'out' ? 'outQuantity' : 'inQuantity'
    // row[strVal] =
    // (row[strVal] && String(row[strVal]).replace(/^(0+)|[^\d]+/g, '')) || ''
    row[strVal] =
      (row[strVal] && String(row[strVal]).replace(/^(0)|\D/g, '')) || ''
  } else {
    // 盘点管理
    if (String(row.checkNum) === '0') {
      row.checkNum = 0
    } else {
      row.checkNum = String(row.checkNum).replace(/^(0)|\D/g, '') || ''
    }
  }
}
defineExpose({
  add,
  submit,
  clear,
  checkInNum
})
</script>
<template>
  <el-form ref="formRef" :key="formKey" :model="formData">
    <TablePagination
      :key="formKey"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :show-overflow-tooltip="true"
      background
      :columns="columns"
      :data="tableData.data"
      :total="tableData.total"
      :loading="tableLoading"
      :current-page="tablePage.pageNum"
      :page-size="tablePage.pageSize"
      class="table-pagination"
      @handle-size-change="
        async (params: Obj) => (
          await changeSize(params),
          (formData.row = tableData.data),
          (formKey = Date.now())
        )
      "
      @handle-current-change="
        async (params: Obj) => (
          await changeCurrent(params),
          (formData.row = tableData.data),
          (formKey = Date.now())
        )
      "
    >
      <!-- 库存数量 -->
      <template #inventoryNum="{ row }">
        <span
          :style="{
            color: kcArr.includes(row.id) ? '#f00' : ''
          }"
          >{{ row.inventoryNum }}</span
        >
      </template>

      <template #operate="{ row }">
        <el-popconfirm title="确认删除？" @confirm="deleteItem(row)">
          <template #reference>
            <el-button link>删除</el-button>
          </template>
        </el-popconfirm>
      </template>
      <!-- @vue-ignore -->
      <template #inputNum="{ row, index }">
        <el-form-item
          v-if="index >= 0"
          :key="row.id"
          :prop="`row.${index}.${
            serviceType === 'check' ? 'checkNum' : `${serviceType}Quantity`
          }`"
          :show-message="false"
          :rules="{
            required: true,
            message: '',
            trigger: 'blur'
          }"
        >
          <el-input
            v-model.trim="
              row[
                serviceType === 'check' ? 'checkNum' : `${serviceType}Quantity`
              ]
            "
            :maxlength="defaultMaxlength"
            :class="row.maxNum || row.maxNum === 0 ? 'input7343' : null"
            @input="() => limitOutNum(row)"
          />
          <div v-if="row.maxNum || row.maxNum === 0" class="tip7343">
            最大入库数量：{{ row.maxNum }}
          </div>
        </el-form-item>
      </template>
      <template #calculateNum="{ row }">
        <span v-if="row.checkNum === ''">--</span>
        <span v-else-if="Number(row.checkNum) - Number(row.inventoryNum) === 0"
          >0</span
        >
        <span
          v-else-if="Number(row.checkNum) - Number(row.inventoryNum) > 0"
          style="color: #29cca0"
          >{{ Number(row.checkNum) - Number(row.inventoryNum) }}</span
        >
        <span
          v-else-if="Number(row.checkNum) - Number(row.inventoryNum) < 0"
          style="color: #ff4444"
          >{{ Number(row.checkNum) - Number(row.inventoryNum) }}</span
        >
      </template>
    </TablePagination>
  </el-form>
</template>
<style scoped lang="scss">
:deep(.el-table .el-form-item) {
  margin-bottom: 0 !important;
  .el-input__wrapper {
    height: inherit !important;
    line-height: inherit !important;
  }
}
.el-table-fixed-column--right {
  .el-button.is-link {
    color: #29cca0 !important;
    &:hover {
      color: var(--el-button-hover-link-text-color) !important;
    }
  }
}
:deep(.el-table .cell) {
  overflow: inherit;
}
.el-input.input7343 {
  margin-top: -7px;
  margin-bottom: 7px;
}
div.tip7343 {
  position: absolute;
  top: 28px;
  left: -8px;
  width: auto;
  z-index: 99999;
  color: #f56c6c;
  font-size: 12px;
  line-height: 12px;
  white-space: nowrap;
  transform: scale(0.9);
}
</style>
