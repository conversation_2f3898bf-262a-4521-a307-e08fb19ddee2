<!--
 * @Description: 物资列表
 * @Author: zwcong
 * @Date: 2024-04-02 15:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-04-18 16:26:40
-->
<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>物资列表</p>
        <el-button type="primary" @click="editDetail(row, 'add')"
          ><el-icon><Plus /></el-icon>新建物资</el-button
        >
      </div>

      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :loading="loading"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.size"
        :columns="columns"
        :total="list.total"
        :data="list.records"
        :show-overflow-tooltip="true"
        :current-page="searchData.current"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #state="{ row }">
          <el-tag :type="row.goodsState === '1' ? 'success' : 'info'">
            {{ getStateName(row.goodsState) }}
          </el-tag>
        </template>
        <!-- 操作 -->
        <template #operate="{ row }">
          <div class="table-operate">
            <el-popconfirm title="是否确认删除？" @confirm="handleDelete(row)">
              <template #reference>
                <el-button link @click.stop>删除</el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
  <!-- 新建/编辑弹窗 -->
  <material-add
    :is-show="isShow"
    :type-val="typeVal"
    :goods-status="goodsStates"
    :goods-cates="goodsCates"
    @closeDialog="closeDialog"
    @updateList="onRefreshList"
  ></material-add>
</template>
<script setup lang="ts">
import {
  queryByPage,
  getGoodsCateList,
  getGoodsStateList,
  deleteById
} from '@/api/module/material.ts'
import visTablePagination from '@/components/table-pagination.vue'
import materialAdd from './components/materialAdd.vue'

// 搜索
const searchProps = ref([
  {
    prop: 'goodsCate',
    label: '物资类别',
    width: '85px',
    type: 'select'
  },
  {
    prop: 'goodsName',
    label: '物资名称',
    width: '90px'
  }
])
const searchData = ref({
  goodsCate: '', // 物资类型
  goodsName: '', // 物资名称
  current: 1, // 当前页
  size: 10 // 每页条数
})
// 表格
let loading = ref(false)
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    current: 1, // 当前页
    size: 10 // 每页条数
  }
  getListFun()
}
const columns = reactive([
  {
    prop: 'goodsTypeName',
    label: '物资类型',
    minWidth: 160
  },
  {
    prop: 'goodsCode',
    label: '物资编码',
    minWidth: 160
  },
  {
    prop: 'goodsName',
    label: '物资名称',
    minWidth: 160
  },
  {
    prop: 'ruleModel',
    label: '规格型号',
    minWidth: 160
  },
  {
    prop: 'goodsState',
    label: '物资状态',
    slotName: 'state',
    minWidth: 100
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    fixed: 'right'
  }
])
const list = reactive({
  records: [],
  total: 0
})
const handleSizeChange = (params: any) => {
  searchData.value.current = 1
  searchData.value.size = params.pageSize
  getListFun()
}
const handleCurrentChange = (params: any) => {
  searchData.value.current = params.currentPage
  getListFun()
}
const handleDelete = async (row: any) => {
  await deleteById({
    id: row.id
  })
    .then(({ data }) => {
      if (data.code == '200') {
        if (data.data) {
          ElMessage({
            message: '删除成功',
            type: 'success'
          })
          getListFun()
        } else {
          ElMessage({
            message: data.message,
            type: 'warning'
          })
        }
      } else {
        ElMessage({
          message: data.message,
          type: 'warning'
        })
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
onMounted(() => {
  getListFun()
  getStateList()
  getGoodsCateLists()
})

const goodsStates = ref(null)

/**
 * 获取状态名称
 */
const getStateName = (state: string) => {
  return (
    (state &&
      goodsStates?.value?.find((item: { value: string }) => item.value == state)
        ?.label) ||
    '--'
  )
}

/**
 * 获取状态列表
 */
const getStateList = () => {
  getGoodsStateList()
    .then(({ data }) => {
      if (data.code === '200') {
        goodsStates.value = data.data.map((item: { name: any; value: any }) => {
          return {
            label: item.name,
            value: item.value
          }
        })
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

const goodsCates = ref(null)

/**
 * 获取物资类别列表
 */
const getGoodsCateLists = () => {
  getGoodsCateList()
    .then(({ data }) => {
      if (data.code === '200') {
        goodsCates.value = data.data.map((item: { name: any; value: any }) => {
          return {
            label: item.name,
            value: item.value
          }
        })
        searchProps.value[0].options = goodsCates.value
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

/**
 * 获取列表
 */
const getListFun = async () => {
  await queryByPage(
    {
      ...searchData.value
    },
    [loading]
  )
    .then(({ data }) => {
      if (data.code == 200) {
        list.records = data.data.records || []
        list.total = data.data.total
      } else {
        list.records = []
        list.total = 0
      }
    })
    .catch((err) => {
      list.records = []
      list.total = 0
      console.log(err)
    })
}

const onRefreshList = () => {
  searchData.value.current = 1
  getListFun()
}

// 弹窗
const isShow = ref(false)
const typeVal = ref('')
const rows = ref({})
const editDetail = (ag1: any, ag2: string) => {
  switch (ag2) {
    case 'add': // 新增
      isShow.value = true
      rows.value = {}
      typeVal.value = 'add'
      break
    case 'edit': // 编辑
      isShow.value = true
      rows.value = ag1
      typeVal.value = 'edit'
      break
    case 'details': // 详情
      rows.value = ag1
      break
    default:
      return
  }
}

const closeDialog = () => {
  isShow.value = false
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.main {
  :deep .el-table {
    margin-top: 10px;
  }
  &-btn {
    display: flex;
    justify-content: space-between;
  }
}
.page-container {
  :deep(.vis-dialog) {
    padding: 0 !important;
  }
  .el-button:focus-visible {
    outline: none;
  }
  .el-tag.el-tag--info {
    --el-tag-text-color: rgba(0, 0, 0, 0.65);
    background-color: #ededed;
    border-color: #ededed;
  }
}
</style>
