<!--
 * @Description: 新建物资类型
 * @Author: zwcong
 * @Date: 2024-04-11 10:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-04-22 15:37:06
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="titleVal"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="446px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-suffix=""
        label-width="140px"
        :rules="formRules"
      >
        <el-form-item label="物资类别" prop="goodsCate">
          <el-select v-model="formData.goodsCate" placeholder="请选择物资类别">
            <el-option
              v-for="item in goodsCates"
              :key="item.key"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="物资类型名称" prop="goodsTypeName">
          <el-input
            v-model.trim="formData.goodsTypeName"
            :maxlength="20"
            autocomplete="off"
            placeholder="请输入物资类型名称"
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="large" @click="dialogClose">取消</el-button>
        <el-button
          type="primary"
          :loading="btnLoading"
          size="large"
          @click="submitForm()"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { goodsTypeAdd } from '@/api/module/material.ts'
import type { FormInstance, FormRules } from 'element-plus'

const addTypeSuccessCall: any = inject('addTypeSuccessCall')

const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  goodsCate: '', // 物资类别
  goodsTypeName: '' // 物资类型名称
})
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  goodsCates: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const dialogVisible = ref(false)
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      setFormData()
    }
  },
  {
    deep: true
  }
)

const titleVal = ref('新建物资类型')

const setFormData = () => {}
const formRules = reactive<FormRules<Record<string, any>>>({
  goodsCate: [{ required: true, message: '请选择物资类别', trigger: 'change' }],
  goodsTypeName: [
    { required: true, message: '请输入物资类型名称', trigger: 'blur' }
  ]
})

const btnLoading = ref(false)

// 点击确定
const emit = defineEmits(['closeDialog'])
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      btnLoading.value = true
      await goodsTypeAdd(
        {
          ...formData.value
        },
        false
      )
        .then(({ data }) => {
          if (data.code == 200) {
            ElMessage({
              message: '新建成功',
              type: 'success'
            })
            dialogClose()
            addTypeSuccessCall(data.data)
          } else {
            ElMessage({
              message: data.message,
              type: 'error'
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          btnLoading.value = false
        })
    }
  })
}
// 关闭弹窗
const dialogClose = () => {
  formRef.value && formRef.value.resetFields()
  emit('closeDialog', false)
}
</script>
<style lang="scss" scoped></style>
