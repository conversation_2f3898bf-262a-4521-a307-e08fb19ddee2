<!--
 * @Description: 物资添加
 * @Author: zwcong
 * @Date: 2024-04-11 10:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-05-14 11:20:26
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="titleVal"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="600px"
    class="vis-dialog"
  >
    <el-scrollbar
      max-height="calc(100vh - 140px)"
      style="padding: 0 20px 0 10px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        label-suffix=""
        label-width="140px"
        :rules="formRules"
      >
        <el-form-item label="物资类型" prop="goodsTypeId">
          <div class="flex-1">
            <el-select
              v-model="formData.goodsTypeId"
              placeholder="请选择物资类型"
              filterable
            >
              <el-option
                v-for="item in materialTypes"
                :key="item.key"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <el-button
            class="btn-special"
            size="large"
            @click="isTypeAddShow = true"
            ><el-icon><Plus /></el-icon>新建物资类型</el-button
          >
        </el-form-item>
        <el-form-item label="物资名称" prop="goodsName">
          <el-input
            v-model="formData.goodsName"
            :maxlength="20"
            autocomplete="off"
            placeholder="请输入物资名称"
          />
        </el-form-item>
        <el-form-item label="规格型号" prop="ruleModel">
          <el-input
            v-model="formData.ruleModel"
            :maxlength="20"
            autocomplete="off"
            placeholder="请输入规格型号"
          />
        </el-form-item>
        <el-form-item label="物资状态" prop="goodsState">
          <el-select v-model="formData.goodsState" placeholder="请选择物资状态">
            <el-option
              v-for="item in goodsStatus"
              :key="item.key"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="large" @click="dialogClose">取消</el-button>
        <el-button
          type="primary"
          :loading="btnLoading"
          size="large"
          @click="submitForm()"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
  <material-type-add
    :is-show="isTypeAddShow"
    :goods-cates="goodsCates"
    @closeDialog="closeTypeAddDialog"
  ></material-type-add>
</template>
<script lang="ts" setup>
import { addGoods, allGoodsTypeList } from '@/api/module/material.ts'
import type { FormInstance, FormRules } from 'element-plus'
import materialTypeAdd from './materialTypeAdd.vue'
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  goodsName: '', // 物资名称
  goodsTypeId: '', // 物资类型
  ruleModel: '', // 规则型号
  goodsState: 1 // 物资状态-默认在用
})
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  typeVal: {
    type: String,
    default: '',
    required: true
  },
  goodsStatus: {
    type: Object,
    default: () => {
      return {}
    }
  },
  goodsCates: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const dialogVisible = ref(false)
const isTypeAddShow = ref(false)

watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      getTypeList()
    }
  },
  {
    deep: true
  }
)

const titleVal = ref('新建物资')

const formRules = reactive<FormRules<Record<string, any>>>({
  goodsName: [{ required: true, message: '请输入物资名称', trigger: 'blur' }],
  goodsTypeId: [
    { required: true, message: '请选择物资类型', trigger: 'change' }
  ],
  ruleModel: [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
  goodsState: [{ required: true, message: '请选择物资状态', trigger: 'change' }]
})

const btnLoading = ref(false)

// 点击确定
const emit = defineEmits(['closeDialog', 'updateList'])
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      btnLoading.value = true
      await addGoods(
        {
          ...formData.value
        },
        false
      )
        .then(({ data }) => {
          if (data.code == 200) {
            ElMessage({
              message: '提交成功',
              type: 'success'
            })
            dialogClose()
            emit('updateList')
          } else {
            ElMessage({
              message: data.message,
              type: 'error'
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          btnLoading.value = false
        })
    }
  })
}
// 关闭弹窗
const dialogClose = () => {
  formRef.value && formRef.value.resetFields()
  emit('closeDialog', false)
}

/**
 * 关闭物资类型弹窗
 */
const closeTypeAddDialog = () => {
  isTypeAddShow.value = false
}

const materialTypes = ref([])

/**
 * 获取物资类型列表
 */
const getTypeList = () => {
  allGoodsTypeList({
    goodsCate: formData.value.goodsCate
  })
    .then(({ data }) => {
      if (data.code === '200') {
        materialTypes.value = data.data.map(
          (item: { goodsTypeName: any; id: any }) => {
            return {
              label: item.goodsTypeName,
              value: item.id
            }
          }
        )
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
const addTypeSuccessCall = (typeId: number) => {
  getTypeList()
  formData.value.goodsTypeId = typeId
}

provide('addTypeSuccessCall', addTypeSuccessCall)
</script>
<style lang="scss" scoped>
.btn-special {
  background-color: #e5fff8;
  color: #29cca0;
  border-color: #e5fff8;
  width: calc(100% - 16px);
  margin-left: 16px;
  font-weight: normal;
  width: 136px;

  &:hover {
    opacity: 0.8;
  }
  &:active {
    background-color: #80f5d6;
    color: #fff;
    border-color: #80f5d6;
  }
}
.el-button:focus-visible {
  outline: none;
}
</style>
