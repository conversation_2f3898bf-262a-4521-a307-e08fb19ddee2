<!--
 * @Description: 库存列表
 * @Author: zwcong
 * @Date: 2024-04-02 15:34:23
 * @LastEditors: zwcong
 * @LastEditTime: 2024-04-18 10:37:45
-->
<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>库存列表</p>
      </div>
      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :loading="loading"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.size"
        :columns="columns"
        :total="list.total"
        :data="list.records"
        :show-overflow-tooltip="true"
        :current-page="searchData.current"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
      </vis-table-pagination>
    </div>
  </div>
</template>
<script setup lang="ts">
import { queryByPage } from '@/api/module/inventory.ts'
import { getGoodsCateList } from '@/api/module/material'
import visTablePagination from '@/components/table-pagination.vue'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
// 搜索
const searchProps = ref([
  {
    prop: 'goodsCate',
    label: '物资类别',
    width: '85px',
    type: 'select'
  },
  {
    prop: 'stationCode',
    label: '电站名称',
    width: '85px',
    type: 'stationSelect'
  },
  {
    prop: 'goodsName',
    label: '物资名称',
    width: '100px'
  }
])
const searchData = ref({
  goodsCate: '', // 物资类别
  stationCode: '', //电站名称
  goodsName: '', // 物资名称
  current: 1, // 当前页
  size: 10 // 每页条数
})
// 表格
let loading = ref(false)
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    current: 1, // 当前页
    size: 10 // 每页条数
  }
  getListFun()
}
const columns = reactive([
  {
    prop: 'stationName',
    label: '电站名称',
    minWidth: 160
  },
  {
    prop: 'goodsTypeName',
    label: '物资类型',
    minWidth: 160
  },
  {
    prop: 'goodsCode',
    label: '物资编码',
    minWidth: 160
  },
  {
    prop: 'goodsName',
    label: '物资名称',
    minWidth: 160
  },
  {
    prop: 'ruleModel',
    label: '规格型号',
    minWidth: 160
  },
  {
    prop: 'inventoryNum',
    label: '库存数量',
    minWidth: 100
  }
])
const list = reactive({
  records: [],
  total: 0
})
const handleSizeChange = (params: any) => {
  searchData.value.current = 1
  searchData.value.size = params.pageSize
  getListFun()
}
const handleCurrentChange = (params: any) => {
  searchData.value.current = params.currentPage
  getListFun()
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getListFun()
  }
)
onMounted(async () => {
  companyCode.data && (await getListFun())
  startWatch = true
  getGoodsCateLists()
})

const goodsCates = ref(null)

/**
 * 获取物资类别列表
 */
const getGoodsCateLists = () => {
  getGoodsCateList()
    .then(({ data }) => {
      if (data.code === '200') {
        goodsCates.value = data.data.map((item: { name: any; value: any }) => {
          return {
            label: item.name,
            value: item.value
          }
        })
        searchProps.value[0].options = goodsCates.value
      }
    })
    .catch((err) => {
      console.log(err)
    })
}

/**
 * 获取列表
 */
const getListFun = async () => {
  await queryByPage(
    {
      ...searchData.value
    },
    [loading]
  )
    .then(({ data }) => {
      if (data.code == 200) {
        list.records = data.data.records || []
        list.total = data.data.total
      } else {
        list.records = []
        list.total = 0
      }
    })
    .catch((err) => {
      list.records = []
      list.total = 0
      console.log(err)
    })
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.main {
  :deep .el-table {
    margin-top: 10px;
  }
  &-btn {
    display: flex;
    justify-content: space-between;
  }
}
.page-container {
  .el-tag.el-tag--info {
    --el-tag-text-color: rgba(0, 0, 0, 0.65);
    background-color: #ededed;
    border-color: #ededed;
  }
}
</style>
