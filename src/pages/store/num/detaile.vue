<!--
 * @Author: zhaopengpeng006 <EMAIL>
 * @Date: 2024-04-15 15:38:37
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-04-16 15:15:35
 * @FilePath: \pv-om-web\src\pages\store\num\detaile.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="page-operate">
    <div class="operate-title">查看详情</div>
  </div>
  <div class="info-base">
    <div class="operate">
      <p>盘点信息</p>
    </div>
    <el-row :gutter="24" class="info">
      <el-col :span="8">
        <div class="info-item">
          <span class="label">电站名称：</span>
          <span class="value">{{ infoDetaile.stationName || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <span class="label">盘点日期：</span>
          <span class="value">
            {{ (infoDetaile.checkTime as string).split(' ')?.at(0) || '--' }}
          </span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <span class="label">盘点人：</span>
          <span class="value">{{ infoDetaile.checkUser || '--' }}</span>
        </div>
      </el-col>
    </el-row>
  </div>

  <div class="info-tab info-tab-height-show">
    <div class="operate">
      <p>盘点物资</p>
    </div>
    <SubstanceTable
      type="show"
      service-type="check"
      :get-table-data-fn="storeManageAPI.getCheckInventoryById"
    />
  </div>
</template>

<script setup lang="ts">
import SubstanceTable from '../components/substanceTable.vue'
import { storeManage as storeManageAPI } from '@/api/index.ts'
const route = useRoute()

const infoDetaile = ref({
  stationName: '',
  checkTime: '',
  checkUser: ''
})
onMounted(async () => {
  let infoArr =
    (route.query.info && (route.query.info as string).split(',')) || []
  infoDetaile.value.stationName = infoArr[0]
  infoDetaile.value.checkTime = infoArr[1]
  infoDetaile.value.checkUser = infoArr[2]
})
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
