<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>盘点列表</p>
        <el-button type="primary" @click="handleAdd"
          ><el-icon> <Plus /> </el-icon>新建盘点单</el-button
        >
      </div>
      <div class="tables">
        <vis-table-pagination
          :loading="tableLoading"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchData.pageSize"
          :current-page="searchData.pageNum"
          :columns="columns"
          :total="listTotal"
          :data="listData"
          :show-overflow-tooltip="true"
          background
          class="vis-table-pagination"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
        >
          <template #checkCode="{ row }">
            <el-tag v-if="row.checkCode == 1" type="success"> 无损益 </el-tag>
            <el-tag v-if="row.checkCode == 2" type="danger">有损益</el-tag>
          </template>

          <template #operate="{ row }">
            <div class="table-operate">
              <el-button link @click="handleDetail(row)">查看详情</el-button>
            </div>
          </template>
        </vis-table-pagination>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeManage as storeManageAPI } from '@/api/index.ts'
import visTablePagination from '@/components/table-pagination.vue'
import type { PageObject } from './types'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const router = useRouter()

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  companyCode.data && (await getTableData())
  startWatch = true
})

// 搜索
const searchData = ref({
  stationCode: '',
  checkNo: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    label: '电站名称',
    prop: 'stationCode',
    type: 'stationSelect',
    width: '86px',
    placeholder: '请选择电站',
    span: 8
  },
  {
    prop: 'checkNo',
    label: '盘点单号',
    placeholder: '请输入盘点单号',
    span: 8,
    width: '110px'
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  await getTableData()
}

const columns = [
  {
    prop: 'checkNo',
    label: '盘点单号'
  },
  {
    prop: 'stationName',
    label: '电站名称'
  },
  {
    prop: 'checkUser',
    label: '盘点人'
  },
  {
    prop: 'checkTime',
    label: '盘点日期',
    formatter: (row: Obj) => row.checkTime?.split(' ')?.at(0) || '--'
  },
  {
    prop: 'checkCode',
    label: '盘点结果',
    slotName: 'checkCode'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 100,
    fixed: 'right'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await storeManageAPI.getCheckInventoryList(
      { ...searchData.value },
      [tableLoading]
    )
    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: PageObject) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getTableData()
}
const handleCurrentChange = async (params: PageObject) => {
  searchData.value.pageNum = params.currentPage
  await getTableData()
}

// 操作
const handleAdd = () => {
  router.push('/store/num/add')
}
const handleDetail = (row: Record<string, any>) => {
  router.push(
    `/store/num/detaile?key=checkId&id=${JSON.stringify(row.id)}&info=${
      row.stationName
    },${row.checkTime},${row.checkUser}`
  )
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
