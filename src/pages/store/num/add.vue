<template>
  <div class="page-operate">
    <div class="operate-title">新建盘点单</div>
  </div>
  <div class="info-base">
    <div class="operate">
      <!-- 盘点信息 -->
      <p>盘点信息</p>
    </div>
    <div class="info">
      <el-form
        ref="formRef"
        :rules="formRules"
        :model="formData"
        label-suffix=""
        label-position="right"
        :inline="true"
        class="w-full"
      >
        <el-row :gutter="24" justify="space-between">
          <el-col :span="8">
            <el-form-item label="电站名称" prop="stationCode">
              <StationSelect
                v-model="formData.stationCode"
                @change="
                  (data: any) => (formData.stationName = data.stationName)
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="盘点日期" prop="checkTime">
              <el-date-picker
                v-model="formData.checkTime"
                type="date"
                placeholder="请选择盘点日期"
                :editable="false"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="盘点人" prop="checkUser">
              <el-input
                v-model.trim="formData.checkUser"
                placeholder="请输入盘点人"
                autocomplete="off"
                maxlength="10"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <div class="info-tab info-tab-height-edit">
    <!-- 盘点物资 -->
    <div class="operate">
      <p>盘点物资</p>
      <el-button type="primary" @click="changeNum">
        <el-icon><Plus /></el-icon>
        添加物资
      </el-button>
    </div>
    <!-- 表格 -->
    <SubstanceTable
      ref="substanceTableRef"
      type="edit"
      service-type="check"
      @echo-data="echoData"
    />
  </div>
  <div class="page-footer">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button type="primary" @click="onSumbit">提交</el-button>
  </div>
  <!-- 添加物资弹窗 -->
  <select-goods
    :is-show="isShow"
    :arr-list="arrList"
    :station-code="formData.stationCode"
    @uplate-data="uplateData"
    @close-dialog="closeDialog"
  ></select-goods>

  <!-- 确定提交弹窗 -->
  <el-dialog v-model="dialogVisible" width="400" :before-close="handleClose">
    <template #header="{ titleId, titleClass }">
      <div class="my-header">
        <span :id="titleId" :class="titleClass">
          <el-icon color="#FF9900" style="margin-right: 8px">
            <WarningFilled /> </el-icon
          >提交
        </span>
      </div>
    </template>
    <span>提交后将修正库存数量，是否确认提交?</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogOnSumbit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import type { FormRules } from 'element-plus'
import SubstanceTable from '../components/substanceTable.vue'
import { storeManage as storeManageAPI } from '@/api/index.ts'
import { getDateTime, getCookieValue } from '@/utils'
import StationSelect from '@/components/spic-station'
import selectGoods from '../components/selectGoods.vue'

const router = useRouter()
const formRef = ref()
const formRules = reactive<FormRules<Record<string, any>>>({
  stationCode: [{ required: true, message: '请选择电站', trigger: 'change' }],
  checkTime: [{ required: true, message: '请选择盘点日期', trigger: 'change' }],
  checkUser: [{ required: true, message: '请输入盘点人', trigger: 'blur' }]
})
const formData = ref<Record<string, any>>({
  stationCode: '',
  stationName: '',
  checkTime: '',
  checkUser: getCookieValue('userName')
})
// 表格
const isShow = ref(false)
const closeDialog = () => {
  isShow.value = false
}

// 返回
const onCancel = () => {
  router.push('/store/num')
}
const changeNum = () => {
  if (formData.value?.stationCode && formData.value?.stationName) {
    isShow.value = true
  } else {
    ElMessage({
      message: '请选择电站',
      type: 'warning'
    })
  }
}
// 提交
const substanceTableRef = ref<any>(null)
const dialogVisible = ref(false)
const handleClose = () => {
  dialogVisible.value = false
}
const checkDetailVoList = ref<any[]>([])
watch(
  () => formData.value.stationCode,
  () => substanceTableRef.value && substanceTableRef.value.clear()
)
const onSumbit = async () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      let data = await substanceTableRef.value.submit()
      if (data && data.length) {
        data.forEach((item: any) => {
          item.loseNum = Number(item.checkNum) - Number(item.inventoryNum)
        })
        checkDetailVoList.value = data
        dialogVisible.value = true
      } else {
        checkDetailVoList.value = []
        dialogVisible.value = false
      }
    }
    // else {
    //   ElMessage({
    //     type: 'warning',
    //     message: '请检查表单！'
    //   })
    // }
  })
}
const uplateData = (arr: Obj[]) => {
  substanceTableRef.value.add(
    arr.map((e: any) => {
      return {
        ...e,
        loseNum: '',
        checkNum: '',
        oldInventoryNum_: e.inventoryNum
      }
    })
  )
}
const arrList = ref([])
const echoData = (val: any) => {
  arrList.value = val
}
const now = new Date()
const dialogOnSumbit = async () => {
  let arr = []
  checkDetailVoList.value.length &&
    checkDetailVoList.value.forEach((item: any) => {
      if (item.loseNum == 0) {
        arr.push(item)
      }
    })
  try {
    let { data } = await storeManageAPI.saveCheckInventory({
      ...formData.value,
      checkTime:
        formData.value?.checkTime + ' ' + getDateTime(now).split(' ').at(1),
      checkCode: arr.length == checkDetailVoList.value.length ? 1 : 2, // 盘点结果：1 - 无损益 2 - 有损益
      checkDetailVoList: checkDetailVoList.value
    })
    if (data.code === '200') {
      ElMessage({
        message: '提交成功',
        type: 'success'
      })
      dialogVisible.value = false
      router.push('/store/num')
    } else {
      dialogVisible.value = true
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    dialogVisible.value = true
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
onMounted(() => {
  formData.value.checkTime = getDateTime(now).split(' ').at(0)
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
