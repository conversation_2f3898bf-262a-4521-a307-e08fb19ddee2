<template>
  <div class="page-operate">
    <div class="operate-title">查看详情</div>
  </div>
  <div class="info-base">
    <div class="operate">
      <p>出库信息</p>
    </div>
    <el-row :gutter="24" class="info">
      <el-col :span="8">
        <div class="info-item">
          <span class="label">电站名称：</span>
          <span class="value">{{ outStationName || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <span class="label">出库日期：</span>
          <span class="value">
            {{ (outTime as string).split(' ')?.at(0) || '--' }}
          </span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <span class="label">出库人：</span>
          <span class="value"> {{ outUser || '--' }} </span>
        </div>
      </el-col>
    </el-row>
  </div>

  <div class="info-tab info-tab-height-show">
    <div class="operate">
      <p>出库物资</p>
    </div>

    <SubstanceTable
      type="show"
      service-type="out"
      :get-table-data-fn="storeManage.getStoreOutDetail"
    />
  </div>
</template>

<script setup lang="ts">
import SubstanceTable from '../components/substanceTable.vue'
import { storeManage } from '@/api/index.ts'

const route = useRoute()
const { outStationName, outTime, outUser } = route.query
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
