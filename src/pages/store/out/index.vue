<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="100px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>出库列表</p>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新建出库单
        </el-button>
      </div>
      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="tableColumns"
        :total="tableData.total"
        :data="tableData.data"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="toDetail(row)">查看详情</el-button>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import { storeManage as storeManageAPI } from '@/api/index.ts'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const router = useRouter()

let searchData = ref({
  stationCode: '', //  电站code
  outOrderNo: '', // 出库单号
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    label: '电站名称',
    width: '74px',
    prop: 'stationCode',
    type: 'stationSelect'
  },
  { label: '出库单号', prop: 'outOrderNo', width: '75px' }
])
const tableColumns = [
  { prop: 'outOrderNo', label: '出库单号' },
  { prop: 'outStationName', label: '电站名称' },
  { prop: 'outUser', label: '出库人' },
  {
    prop: 'outTime',
    label: '出库日期',
    formatter: (row: Obj) => row.outTime?.split(' ')?.at(0) || '--'
  },
  { prop: 'outQuantity', label: '出库数量' },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 80,
    fixed: 'right'
  }
]
const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageNum = 1
  searchData.value.pageSize = 10
  getTableData()
}

const getTableData = async () => {
  try {
    let params = {
      outStationCode: searchData.value.stationCode,
      outOrderNo: searchData.value.outOrderNo, // 出库单号
      pageNum: searchData.value.pageNum,
      pageSize: searchData.value.pageSize
    }
    let { data } = await storeManageAPI.getStoreOutList(params, true)
    tableData.data = data?.data?.records || []
    tableData.total = data?.data?.total || 0
  } catch (e: any) {
    tableData.data = []
    tableData.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  companyCode.data && (await getTableData())
  startWatch = true
})

const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const handleAdd = async () => {
  router.push({
    name: 'StoreOutAdd'
  })
}
const toDetail = async (row: any) => {
  router.push({
    name: 'StoreOutDetail',
    query: {
      id: row.id,
      outStationName: row.outStationName,
      outTime: row.outTime,
      outUser: row.outUser,
      key: 'inventoryOutId'
    }
  })
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
