<template>
  <div class="page-operate">
    <div class="operate-title">新建出库单</div>
  </div>
  <div class="info-base">
    <div class="operate">
      <p>出库信息</p>
    </div>
    <div class="info">
      <el-form
        ref="formRef"
        :rules="formRules"
        :model="formData"
        label-suffix=""
        label-position="right"
        :inline="true"
        class="w-full"
      >
        <el-row :gutter="24" justify="space-between">
          <el-col :span="8">
            <el-form-item label="电站名称" prop="stationCode">
              <StationSelect
                v-model="formData.stationCode"
                @change="
                  (data: any) => (formData.stationName = data.stationName)
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库日期" prop="outTime">
              <el-date-picker
                v-model="formData.outTime"
                type="date"
                placeholder="请选择出库日期"
                :editable="false"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库人" prop="outUser">
              <el-input
                v-model.trim="formData.outUser"
                placeholder="请输入出库人"
                autocomplete="off"
                maxlength="10"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <div class="info-tab info-tab-height-edit">
    <div class="operate">
      <p>出库物资</p>
      <el-button type="primary" @click="selectGoods">
        <el-icon><Plus /></el-icon>
        添加物资
      </el-button>
    </div>
    <SubstanceTable
      ref="substanceTableRef"
      type="edit"
      service-type="out"
      @echo-data="echoData"
    />
  </div>
  <div class="page-footer">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button type="primary" @click="onSumbit">提交</el-button>
  </div>
  <SelectGoods
    :arr-list="arrList"
    :station-code="formData.stationCode"
    :is-show="isShow"
    @uplate-data="getGoodsData"
    @close-dialog="isShow = false"
  ></SelectGoods>
</template>

<script setup lang="ts">
import SubstanceTable from '../components/substanceTable.vue'
import SelectGoods from '../components/selectGoods.vue'
import StationSelect from '@/components/spic-station'
import { getCookieValue, getDateTime } from '@/utils'
import { storeManage as storeManageAPI } from '@/api/index.ts'

const router = useRouter()

const substanceTableRef = ref()
const isShow = ref<boolean>(false)
const selectGoods = () => {
  if (formData.value?.stationCode && formData.value?.stationName) {
    isShow.value = true
  } else {
    ElMessage({
      message: '请选择电站',
      type: 'warning'
    })
  }
}
const getGoodsData = (arr: Obj[]) => {
  substanceTableRef.value.add(
    arr.map((e: Obj) => {
      return {
        ...e,
        oldInventoryNum_: e.inventoryNum
      }
    })
  )
}
const arrList = ref([])
const echoData = (val: any) => {
  arrList.value = val
}

const formRef = ref()
const formRules = reactive<any>({
  stationCode: [{ required: true, message: '请选择电站', trigger: 'change' }],
  outTime: [{ required: true, message: '请选择出库日期', trigger: 'change' }],
  outUser: [{ required: true, message: '请输入出库人', trigger: 'blur' }]
})
const formData = ref<Record<string, any>>({
  stationCode: '',
  stationName: '',
  outTime: getDateTime(new Date()).split(' ').at(0),
  outUser: getCookieValue('userName')
})
watch(
  () => formData.value.stationCode,
  () => substanceTableRef.value && substanceTableRef.value.clear()
)
const onCancel = () => {
  router.go(-1)
}
const onSumbit = async () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const data = await substanceTableRef.value.submit()
      if (data) {
        let outQuantity: any
        outQuantity = data.reduce(
          (outQuantity: any, obj: any) =>
            (outQuantity += Number(obj.outQuantity)),
          0
        )
        const { code } = await storeManageAPI.saveStoreOut({
          outStationCode: formData.value.stationCode,
          outStationName: formData.value.stationName,
          outTime:
            formData.value.outTime +
            ' ' +
            getDateTime(new Date()).split(' ').at(1),
          outUser: formData.value.outUser,
          outDetailList: data.map((e: any) => {
            return {
              goodsId: e.goodsId,
              inventoryManageId: e.id,
              outQuantity: e.outQuantity
            }
          }),
          outQuantity
        })
        if (code == 200) {
          ElMessage({
            type: 'success',
            message: '提交成功！'
          })
          router.push('/store/out')
        } else {
          ElMessage({
            message: '提交失败',
            type: 'error'
          })
        }
      }
    }
    // else {
    //   ElMessage({
    //     type: 'warning',
    //     message: '请检查表单！'
    //   })
    // }
  })
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
