<template>
  <div class="page-operate">
    <div class="operate-title">查看详情</div>
  </div>
  <div class="info-base">
    <div class="operate">
      <p>入库信息</p>
    </div>
    <el-row :gutter="24" class="info">
      <el-col :span="8">
        <div class="info-item">
          <span class="label">电站名称：</span>
          <span class="value">{{ route.query.receiptStation || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <span class="label">入库日期：</span>
          <span class="value">{{
            (route.query.receiptTime as string).split(' ')?.at(0) || '--'
          }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <span class="label">入库人：</span>
          <span class="value">{{ route.query.receiptPeople || '--' }}</span>
        </div>
      </el-col>
    </el-row>
  </div>

  <div class="info-tab info-tab-height-show">
    <div class="operate">
      <p>入库物资</p>
    </div>
    <SubstanceTable :get-table-data-fn="storeManageApi.getStoreInDetailList" />
  </div>
</template>

<script setup lang="ts">
import SubstanceTable from '../components/substanceTable.vue'
import { storeManage as storeManageApi } from '@/api'

const route = useRoute()
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
