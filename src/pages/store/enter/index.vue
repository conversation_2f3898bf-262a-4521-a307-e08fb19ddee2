<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="100px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>入库列表</p>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新建入库单
        </el-button>
      </div>
      <vis-table-pagination
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="tableColumns"
        :total="tableData.total"
        :data="tableData.data"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click="toDetail(row)">查看详情</el-button>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import { storeManage as storeManageApi } from '@/api'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const router = useRouter()

let searchData = ref({
  receiptNo: '',
  receiptStationCode: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    label: '电站名称',
    width: '74px',
    prop: 'receiptStationCode',
    type: 'stationSelect'
  },
  { label: '入库单号', prop: 'receiptNo', width: '75px' }
])
const tableColumns = [
  { prop: 'receiptNo', label: '入库单号' },
  { prop: 'receiptStation', label: '电站名称' },
  { prop: 'receiptPeople', label: '入库人' },
  {
    prop: 'receiptTime',
    label: '入库日期',
    formatter: (row: Obj) => row.receiptTime?.split(' ')?.at(0) || '--'
  },
  { prop: 'quantity', label: '入库数量' },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 80,
    fixed: 'right'
  }
]
const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageNum = 1
  searchData.value.pageSize = 10
  getTableData()
}

const tableLoading = ref(false)
const getTableData = async () => {
  const { data } = await storeManageApi.getStoreInList(searchData.value, [
    tableLoading
  ])
  tableData.data = data?.records || []
  tableData.total = data?.total || 0
}

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getTableData()
  }
)
onMounted(async () => {
  companyCode.data && (await getTableData())
  startWatch = true
})

const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const handleAdd = async () => {
  router.push({
    name: 'StoreEnterAdd'
  })
}
const toDetail = async (row: any) => {
  router.push({
    name: 'StoreEnterDetail',
    query: {
      id: row.id,
      key: 'receiptId',
      receiptStation: row.receiptStation,
      receiptTime: row.receiptTime,
      receiptPeople: row.receiptPeople
    }
  })
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
