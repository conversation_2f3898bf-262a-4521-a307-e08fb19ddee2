<template>
  <div class="page-operate">
    <div class="operate-title">新建入库单</div>
  </div>
  <div class="info-base">
    <div class="operate">
      <p>入库信息</p>
    </div>
    <div class="info">
      <el-form
        ref="formRef"
        :rules="formRules"
        :model="formData"
        label-suffix=""
        label-position="right"
        :inline="true"
        class="w-full"
      >
        <el-row :gutter="24" justify="space-between">
          <el-col :span="8">
            <el-form-item label="电站名称" prop="stationCode">
              <StationSelect
                v-model="formData.stationCode"
                @change="
                  (data: any) => (formData.stationName = data.stationName)
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库日期" prop="date">
              <el-date-picker
                v-model="formData.date"
                type="date"
                placeholder="请选择入库日期"
                :editable="false"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库人" prop="user">
              <el-input
                v-model.trim="formData.user"
                placeholder="请输入入库人"
                autocomplete="off"
                maxlength="10"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <div class="info-tab info-tab-height-edit">
    <div class="operate">
      <p>入库物资</p>
      <el-button type="primary" @click="selectGoods">
        <el-icon><Plus /></el-icon>
        添加物资
      </el-button>
    </div>
    <SubstanceTable
      ref="substanceTableRef"
      type="edit"
      service-type="in"
      @echo-data="echoData"
    />
  </div>
  <div class="page-footer">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button type="primary" @click="onSumbit">提交</el-button>
  </div>
  <SelectGoods
    :is-show="isShow"
    :arr-list="arrList"
    @uplate-data="getGoodsData"
    @close-dialog="isShow = false"
  />
</template>

<script setup lang="ts">
import SubstanceTable from '../components/substanceTable.vue'
import SelectGoods from '../components/selectGoods.vue'
import StationSelect from '@/components/spic-station'
import { storeManage as storeManageApi } from '@/api'
import { getCookieValue, getDateTime } from '@/utils'

const router = useRouter()

const substanceTableRef = ref()
const isShow = ref<boolean>(false)
const selectGoods = () => {
  isShow.value = true
}
const getGoodsData = (arr: Obj[]) => {
  substanceTableRef.value.add(
    arr.map((e: Obj) => {
      return {
        ...e
      }
    })
  )
}
const arrList = ref([])
const echoData = (val: any) => {
  arrList.value = val
}

const formRef = ref()
const formRules = reactive<any>({
  stationCode: [{ required: true, message: '请选择电站', trigger: 'change' }],
  date: [{ required: true, message: '请选择入库日期', trigger: 'change' }],
  user: [{ required: true, message: '请输入入库人', trigger: 'blur' }]
})
const formData = ref<Record<string, any>>({
  stationCode: '',
  stationName: '',
  date: getDateTime(new Date()).split(' ').at(0),
  user: getCookieValue('userName')
})

const onCancel = () => {
  router.go(-1)
}
const onSumbit = async () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const data = await substanceTableRef.value.submit()
      if (data) {
        const { code, response } = await storeManageApi.saveStoreIn({
          receiptStation: formData.value.stationName,
          receiptStationCode: formData.value.stationCode,
          receiptPeople: formData.value.user,
          receiptTime:
            formData.value.date +
            ' ' +
            getDateTime(new Date()).split(' ').at(1),
          inventoryReceiptDetailList: data.map((e: any) => {
            return {
              goodsId: e.id,
              inQuantity: e.inQuantity
            }
          })
        })
        if (code == 500) {
          console.log(response.data.data)
          ElMessage({
            type: 'warning',
            message: `入库数量超出最大限制，请核对后重新输入`
          })
          substanceTableRef.value.checkInNum(response.data?.data || [])
        } else if (code == 200) {
          ElMessage({
            type: 'success',
            message: '提交成功！'
          })
          onCancel()
        } else {
          ElMessage({
            message: '提交失败',
            type: 'error'
          })
        }
      }
    }
    // else {
    //   ElMessage({
    //     type: 'warning',
    //     message: '请检查表单！'
    //   })
    // }
  })
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
