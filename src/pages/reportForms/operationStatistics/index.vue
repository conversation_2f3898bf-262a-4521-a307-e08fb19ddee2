<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>运维公司-电站类别统计列表</p>
        <div>
          <el-button
            type="primary"
            :loading="exportLoading"
            @click="handleExport"
            ><el-icon> <Download /> </el-icon>导出</el-button
          >
        </div>
      </div>
      <vis-table-pagination
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="columns"
        :total="listTotal"
        :data="listData"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #city="{ row }">
          {{ row.provinceName }}-{{ row.cityName }}
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reportForms as reportFormsAPI } from '@/api/index.ts'

import visTablePagination from '@/components/table-pagination.vue'
import address from '@/api/data/area.json'
import dayjs from 'dayjs'

const exportLoading = ref(false)

// 搜索
const searchData: any = ref({
  operationCompanyCode: '',
  area: '', // 行政区划
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  {
    prop: 'operationCompanyCode',
    label: '运维公司',
    span: 8,
    width: '68px',
    type: 'select',
    filterable: true,
    options: []
  },
  {
    label: '行政区划',
    placeholder: '请选择行政区划',
    type: 'cascader',
    class: 'cascader-station',
    prop: 'area',
    options: address,
    width: '68px',
    span: 8,
    props: {
      value: 'value',
      label: 'label',
      children: 'children',
      expandTrigger: 'hover',
      checkStrictly: true,
      emitPath: true
    }
  }
])
const handleSearch = (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  getTableData()
}

const columns = [
  {
    prop: 'companyName',
    label: '运维公司',
    minWidth: 270
  },
  {
    prop: 'cityName',
    label: '行政区划',
    minWidth: 150,
    slotName: 'city'
  },
  {
    prop: 'stationCount',
    label: '户数',
    minWidth: 70
  },
  {
    prop: 'capins',
    label: '容量（kW）',
    minWidth: 110
  },
  {
    prop: 'stationRateA',
    label: 'A类电站数量',
    minWidth: 110
  },
  {
    prop: 'roportionPercentA',
    label: 'A类电站占比',
    minWidth: 130
  },
  {
    prop: 'stationRateB',
    label: 'B类电站数量',
    minWidth: 110
  },
  {
    prop: 'roportionPercentB',
    label: 'B类电站占比',
    minWidth: 130
  },
  {
    prop: 'stationRateC',
    label: 'C类电站数量',
    minWidth: 110
  },
  {
    prop: 'roportionPercentC',
    label: 'C类电站占比',
    minWidth: 130
  },
  {
    prop: 'stationRateD',
    label: 'D类电站数量',
    minWidth: 110
  },
  {
    prop: 'roportionPercentD',
    label: 'D类电站占比',
    minWidth: 130
  },
  {
    prop: 'stationRateE',
    label: 'E类电站数量',
    minWidth: 110
  },
  {
    prop: 'roportionPercentE',
    label: 'E类电站占比',
    minWidth: 130
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    if (searchData.value.code) {
      searchData.value.code = searchData.value.code.join(',')
    }

    const params: any = getParams()

    let { data } = await reportFormsAPI.getOperationStationReport(
      { ...params },
      [tableLoading]
    )
    listTotal.value = data.total
    listData.value = data.data
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}

const getParams = () => {
  const params: any = { ...searchData.value }

  const area = params.area

  const [provinceCode, cityCode] = area || ['', '']
  params.provinceCode = provinceCode
  params.cityCode = cityCode

  delete params.area

  return params
}

const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const getCompany = async () => {
  try {
    const { data } = await reportFormsAPI.getOperationCompany({}, false)
    searchProps.value[0].options = data.data.map((item: any) => {
      return {
        label: item.companyName || '',
        value: item.companyCode || ''
      }
    })
  } catch (e: any) {
    searchProps.value[0].options = []
  }
}
/**
 * 导出
 */
const handleExport = async () => {
  try {
    exportLoading.value = true

    const params: any = getParams()

    let res = await reportFormsAPI.projectOperationReportExport({
      ...params
    })

    const date = dayjs().format('YYYY年MM月DD日')

    let str = res.headers['content-disposition']
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(res.data)
    link.download =
      `【${date}】-运维公司-电站类别数量统计报表.` +
      str.split('.')[str.split('.').length - 1]
    link.click()
  } catch (e) {
    console.log(e)
    ElMessage({
      type: 'warning',
      message: '导出失败'
    })
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  getTableData()
  getCompany()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss">
.cascader-station {
  .el-cascader-panel {
    .el-cascader-menu:nth-child(2) {
      .arrow-right {
        display: none !important;
      }
    }
    .el-cascader-menu:nth-child(3) {
      display: none !important;
    }
  }
}
</style>
