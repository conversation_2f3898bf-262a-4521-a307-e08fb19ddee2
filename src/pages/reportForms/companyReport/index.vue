<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <template v-if="listData.projectCompanyName">
        <div v-loading="tableLoading">
          <div class="operate">
            <p>{{ listData.projectCompanyName }}</p>
            <div>
              <el-button
                type="primary"
                :loading="exportLoading"
                @click="handleExport(listData)"
                ><el-icon> <Download /> </el-icon>导出</el-button
              >
            </div>
          </div>
          <div class="statistics-container">
            <div class="item">
              <div class="icon">
                <el-image :src="report01" />
              </div>
              <div class="content">
                <div class="number">{{ listData.capins }}<span>MW</span></div>
                <div class="name">装机容量</div>
              </div>
            </div>
            <div class="item">
              <div class="icon">
                <el-image :src="report02" />
              </div>
              <div class="content">
                <div class="number">
                  {{ listData.deviceNum }}<span>台</span>
                </div>
                <div class="name">运行台数</div>
              </div>
            </div>
            <div class="item">
              <div class="icon">
                <el-image :src="report03" />
              </div>
              <div class="content">
                <div class="number">
                  {{ listData.dayUTotalOrder }}<span>台</span>
                </div>
                <div class="name">检修台数</div>
              </div>
            </div>
            <div class="item">
              <div class="icon">
                <el-image :src="report04" />
              </div>
              <div class="content">
                <div class="number">
                  {{ listData.warnDeviceNum }}<span>台</span>
                </div>
                <div class="name">故障台数</div>
              </div>
            </div>
          </div>
          <div class="list">
            <div class="title">发电量(万kWh)</div>
            <el-row class="row">
              <el-col :span="8" class="pl-42px"
                >日发电：{{ listData.energy }}</el-col
              >
              <el-col :span="8">月累计发电：{{ listData.monPrdPower }}</el-col>
              <el-col :span="8">年累计发电：{{ listData.yearPrdPower }}</el-col>
            </el-row>
            <el-row class="row">
              <el-col :span="8" class="pl-14px"
                >月计划发电：{{ listData.monPlanPrdPower }}</el-col
              >
              <el-col :span="8"
                >月度完成率：{{ listData.monCompleteRate }}%</el-col
              >
              <el-col :span="8"
                >年计划发电：{{ listData.yearPlanPrdPower }}</el-col
              >
            </el-row>
            <el-row class="row">
              <el-col :span="8"
                >年累计完成率：{{ listData.yearCompleteRate }}%</el-col
              >
            </el-row>
            <div class="line"></div>
            <div class="title">故障损失电量(万kWh)</div>
            <el-row class="row">
              <el-col :span="8" class="pl-42px"
                >日损失：{{ listData.dayLossPower }}</el-col
              >
              <el-col :span="8">月累计损失：{{ listData.monLossPower }}</el-col>
              <el-col :span="8"
                >年累计损失：{{ listData.yearLossPower }}</el-col
              >
            </el-row>
            <div class="line"></div>
            <div class="title">派发工单数量</div>
            <el-row class="row">
              <el-col :span="8" class="pl-42px"
                >日派发：{{ listData.dayUTotalOrder }}</el-col
              >
              <el-col :span="8"
                >月累计派发：{{ listData.monUTotalOrder }}</el-col
              >
              <el-col :span="8"
                >年累计派发：{{ listData.yearUTotalOrder }}</el-col
              >
            </el-row>
            <div class="line"></div>
            <div class="title">处理工单数量</div>
            <el-row class="row">
              <el-col :span="8" class="pl-42px"
                >日处理：{{ listData.haveHandleOrder }}</el-col
              >
              <el-col :span="8"
                >月累计处理：{{ listData.monHaveHandleOrder }}</el-col
              >
              <el-col :span="8"
                >年累计处理：{{ listData.yearHaveHandleOrder }}</el-col
              >
            </el-row>
            <div class="line"></div>
            <div class="title">电站分级统计</div>
            <el-table
              :data="columns"
              border
              class="mt-24px"
              :header-cell-style="() => ({ background: '#F9F9F9' })"
            >
              <el-table-column prop="level" label="级别" />
              <el-table-column prop="number" align="right" label="个数" />
              <el-table-column
                prop="proportion"
                align="right"
                label="占比(%)"
              />
            </el-table>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="no-data">
          {{
            searchData.projectCompanyCode != '' ? '暂无数据' : '请选择项目公司'
          }}
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reportForms as reportFormsAPI } from '@/api/index.ts'
import dayjs from 'dayjs'

import report01 from '@/assets/images/report01.png'
import report02 from '@/assets/images/report02.png'
import report03 from '@/assets/images/report03.png'
import report04 from '@/assets/images/report04.png'

const time = dayjs(new Date()).subtract(1, 'day').format('YYYY-MM-DD')

const exportLoading = ref(false)

// 搜索
const searchData = ref({
  projectCompanyCode: '', //  100010
  reportDay: time,
  pageNum: 1,
  pageSize: 10,
  reportFlag: 1 //报表时间（日）  1-日 2-月
})
const searchProps = ref([
  {
    prop: 'projectCompanyCode',
    label: '项目公司',
    span: 8,
    width: '68px',
    type: 'select',
    filterable: true,
    options: []
  },
  {
    prop: 'reportDay',
    label: '选择时间',
    type: 'date',
    format: 'YYYY-MM-DD',
    span: 8,
    width: '110px',
    disabledDate: (time: Date) => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      return time.getTime() > yesterday.getTime()
    }
  }
])

const columns = [
  {
    level: 'A',
    number: 0,
    proportion: ''
  },
  {
    level: 'B',
    number: 1000,
    proportion: ''
  },
  {
    level: 'C',
    number: 1000,
    proportion: ''
  },
  {
    level: 'D',
    number: 1000,
    proportion: ''
  },
  {
    level: 'E',
    number: 1000,
    proportion: ''
  }
]

const handleSearch = (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  getTableData()
}

const listData: any = ref({})
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await reportFormsAPI.queryProjectCompanyDayReport(
      { ...searchData.value },
      [tableLoading]
    )
    listData.value = data.data

    const map = ['A', 'B', 'C', 'D', 'E']

    map.forEach((item) => {
      columns.forEach((column: any) => {
        if (column.level === item) {
          column.number = listData.value[`${item}num`]
          column.proportion = listData.value[`${item}rate`]
        }
      })
    })
  } catch (e) {
    listData.value = {}
  }
}
/**
 * 获取资产公司
 */
const getGetCompanyList = async () => {
  try {
    const { data } = await reportFormsAPI.getProjectCompany({}, false)
    data.forEach((item: any) => {
      item.label = item.projectCompanyName
      item.value = item.projectCompanyCode
    })
    searchProps.value[0].options = data || []
  } catch (e) {
    searchProps.value[0].options = []
  }
}

/**
 * 导出
 */
const handleExport = async (row) => {
  try {
    exportLoading.value = true

    let res = await reportFormsAPI.queryProjectCompanyDayReportExport({
      ...searchData.value
    })

    let str = res.headers['content-disposition']
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(res.data)
    link.download =
      `${row.projectCompanyName}日报.` +
      str.split('.')[str.split('.').length - 1]
    link.click()
  } catch (e) {
    console.log(e)
    ElMessage({
      type: 'warning',
      message: '导出失败'
    })
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  getTableData()
  getGetCompanyList()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.statistics-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 24px;
  .item {
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.1);
    height: 106px;
    padding: 25px 24px;
    display: flex;

    .icon {
      width: 56px;
      height: 56px;
      margin-right: 24px;
    }

    .content {
      .number {
        font-size: 28px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 28px;
        margin-bottom: 8px;

        span {
          margin-left: 8px;
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
  }
}
.list {
  margin-top: 24px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.1);
  padding: 24px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  overflow-y: auto;
  height: calc(100vh - 430px);

  .title {
    font-size: 16px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);
  }
  .row {
    margin: 24px 0;
  }
  .line {
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
  }
}
.no-data {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
