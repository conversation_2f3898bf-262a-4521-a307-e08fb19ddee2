<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>生产日报表</p>
      </div>
      <vis-table-pagination
        :loading="tableLoading"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="columns"
        :total="listTotal"
        :data="listData"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #status="{ row }">
          <el-tag :type="row.reportStatus == 1 ? 'success' : 'danger'">
            {{ row.reportStatusStr }}
          </el-tag>
        </template>
        <!-- 操作 -->
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button link @click.stop="downloadItem(row)"
              >{{ row.reportStatus == 1 ? '' : '重新' }}下载</el-button
            >
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  opsPersonnel as opsPersonnelAPI,
  reportForms as reportFormsAPI,
  baseApi as baseAPI
} from '@/api/index.ts'

import visTablePagination from '@/components/table-pagination.vue'

const mode = import.meta.env.MODE

// 搜索
const searchData = ref({
  companyCode: mode === 'uat' ? '' : 'C000004',
  pageNum: 1,
  pageSize: 10,
  reportType: 1 //时间维度1-日,2-月
})
const searchProps = ref([
  {
    prop: 'companyCode',
    label: '运维公司',
    span: 8,
    width: '68px',
    type: 'select',
    filterable: true,
    options: []
  }
])
const handleSearch = (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 10
  }
  getTableData()
}

const columns = [
  {
    prop: 'companyName',
    label: '运维公司',
    minWidth: 150
  },
  {
    prop: 'reportName',
    label: '报表名称',
    minWidth: 150
  },
  {
    prop: 'createTime',
    label: '创建时间',
    minWidth: 80
  },
  {
    prop: 'reportStatusStr',
    slotName: 'status',
    label: '报表状态',
    minWidth: 80
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作'
  }
]
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const tableLoading = ref(false)
const getTableData = async () => {
  try {
    let { data } = await reportFormsAPI.queryReportRecord(
      { ...searchData.value },
      [tableLoading]
    )
    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  }
}
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    searchProps.value[0].options = data.data.map((item: any) => {
      return {
        label: item.companyName || '',
        value: item.companyCode || ''
      }
    })
  } catch (e: any) {
    searchProps.value[0].options = []
  }
}
/**
 * 下载
 */
const downloadItem = async (val: any) => {
  try {
    const {
      response: { data, status }
    } = await baseAPI.downloadFile(val.fileUrl)
    if (status == 200) {
      if (data.type === 'application/json') {
        ElMessage({
          type: 'error',
          message: '下载文件失败'
        })
        return
      }
      const link = document.createElement('a')

      link.href = window.URL.createObjectURL(data)
      link.download = val.fileUrl.split('@')[1]
      link.click()
      data &&
        ElMessage({
          message: `下载成功!`,
          type: 'success'
        })
    }
  } catch (e: any) {}
}

onMounted(() => {
  getTableData()
  getCompany()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss"></style>
