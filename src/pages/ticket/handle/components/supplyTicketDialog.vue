<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑操作票"
    class="vis-dialog ticket-dialog"
    align-center
    :before-close="closeDialog"
    :close-on-click-modal="false"
    width="446"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      :rules="formRules"
      label-width="110px"
    >
      <el-form-item label="关联工作票" prop="workTicketId_">
        <el-select-v2
          v-model="formData.workTicketId_"
          :options="workTicketIdArr"
          placeholder="请选择关联工作票"
          filterable
          :props="workTicketIdProps"
          clearable
        >
        </el-select-v2>
      </el-form-item>

      <el-form-item label="备注" prop="ticketRemarks">
        <el-input
          v-model.trim="formData.ticketRemarks"
          :rows="3"
          type="textarea"
          placeholder="请输入备注"
          maxlength="1024"
          autocomplete="off"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button v-preventReClick="1000" type="primary" @click="handleSave"
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { handle as handleAPI } from '@/api/index.ts'
const route = useRoute()
let formData = ref({
  workTicketId_: '',
  ticketRemarks: '',
  workTicketType: '' // 工作票类型: 1-电气一种票 2-电气二种票
})
const workTicketIdProps = ref({
  label: 'workTicketNo',
  value: 'workTicketId_'
})

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  opTicketDetailVO: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const dialogVisible = ref(false)
const workTicketIdArr = ref([])
watch(
  () => props.isShow,
  async (val: boolean) => {
    //  === 关联操作票1 2 ===
    getWorkOneTwoTicketListFn()
    formData.value.workTicketId_ = props.opTicketDetailVO.workTicketId
      ? props.opTicketDetailVO.workTicketId +
        '_' +
        props.opTicketDetailVO.workTicketType
      : ''
    formData.value.workTicketType = props.opTicketDetailVO.workTicketType || ''
    formData.value.ticketRemarks = props.opTicketDetailVO.ticketRemarks || ''
    dialogVisible.value = val
  },
  {
    deep: true
  }
)

const formRef = ref<FormInstance>()
const formRules = reactive<FormRules<Record<string, any>>>({
  workTicketId_: [
    {
      required: false,
      message: '请选择关联工作票',
      trigger: 'change'
    }
  ],
  ticketRemarks: [
    {
      required: false,
      message: '请输入备注',
      trigger: 'blur'
    }
  ]
})

// 点击取消
const emit = defineEmits(['closeDialog', 'uplateData'])
const closeDialog = () => {
  dialogVisible.value = false
  emit('closeDialog')
  resetForm()
}
// 点击确认
const handleSave = () => {
  formRef.value?.validate(async (valid: any) => {
    if (valid) {
      try {
        let { data } = await handleAPI.updateTicketById({
          id: Number(route.params && route.params.opid) || '',
          ticketRemarks: formData.value.ticketRemarks,
          workTicketId: formData.value.workTicketId_
            ? formData.value.workTicketId_.split('_')[0]
            : '',
          workTicketType: formData.value.workTicketId_
            ? formData.value.workTicketId_.split('_')[1]
            : '' // 工作票类型: 1-电气一种票 2-电气二种票
        })
        if (data.code == 200) {
          ElMessage({
            type: 'success',
            message: '操作成功'
          })
          closeDialog()
          emit('uplateData')
        } else {
          ElMessage({
            type: 'warning',
            message: data.message
          })
        }
      } catch (e) {
        ElMessage({
          type: 'warning',
          message: '操作失败'
        })
      }
    }
  })
}
// 重置
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
const getWorkOneTwoTicketListFn = async () => {
  try {
    let { data } = await handleAPI.getWorkOneTwoTicketList({})
    if (data.code == 200) {
      data.data.length &&
        data.data.forEach((item: any) => {
          item.workTicketId_ = item.workTicketId + '_' + item.workTicketType
        })
      workTicketIdArr.value = data.data
    } else {
      workTicketIdArr.value = []
    }
  } catch (e) {
    workTicketIdArr.value = []
    console.log(e)
  }
}
onMounted(async () => {})
</script>

<style scoped src="../../assets/dialog.scss"></style>
<style lang="scss">
.ticket-dialog .el-form .el-form-item--default .el-form-item__label {
  height: 32px !important;
  line-height: 32px !important;
}
</style>
