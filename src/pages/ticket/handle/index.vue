<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>操作票列表</p>
        <el-button type="primary" @click="handleAdd">
          <el-icon> <Plus /> </el-icon> 新建操作票
        </el-button>
      </div>
      <card-pagination
        title-key="operateTicketNo"
        :total="listTotal"
        :loading="tableLoading"
        :finished="finished"
        layout="total, prev, pager, next, jumper"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :data="listData"
        :columns="columns"
        :card-label-width="`70`"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
        @click-card="clickCard"
      >
        <template #status="{ row }">
          <el-tag v-if="row.operateState == 1" class="tag tag1">草稿</el-tag>
          <el-tag v-if="row.operateState == 2" class="tag tag2">生效中</el-tag>
          <el-tag v-if="row.operateState == 3" class="tag tag3">已作废</el-tag>
          <el-tag v-if="row.operateState == 4" class="tag tag4">已终结</el-tag>
        </template>

        <template #footer="{ row }">
          <el-button type="primary"> 查看详情 </el-button>
          <div @click.stop>
            <el-dropdown placement="bottom">
              <span class="el-dropdown-link">
                下拉<el-icon><CaretBottom /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="onViewProcess(row)">
                    查看审批流程
                  </el-dropdown-item>
                  <el-dropdown-item @click="onCopyTicket(row)">
                    复制操作票
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </card-pagination>
    </div>
    <!-- 审核记录 -->
    <process-dialog
      :is-show="isShow"
      :rows="rows"
      @close-dialog="() => (isShow = false)"
    ></process-dialog>
  </div>
</template>

<script setup lang="ts">
import { handle as handleAPI } from '@/api/index.ts'
import CardPagination from '@/components/card-pagination.vue'
import useCompanyCodeStore from '@/store/companyCode'
import processDialog from './components/processDialog.vue'

const companyCode = useCompanyCodeStore()
const router = useRouter()

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getListData()
  }
)
onMounted(async () => {
  companyCode.data && (await getListData())
  startWatch = true
})

// 搜索
const searchData = ref({
  operateTicketNo: '',
  stationCode: '',
  pageNum: 1,
  pageSize: 6
})
const columns = [
  {
    prop: 'stationName',
    label: '电站名称'
  },
  {
    prop: 'operationCompanyName',
    label: '单位'
  },
  {
    prop: 'operationUser',
    label: '操作人'
  },
  {
    prop: 'createTime',
    label: '创建时间'
  }
]
const searchProps = ref([
  {
    prop: 'operateTicketNo',
    label: '操作票编码',
    placeholder: '请输入操作票编码',
    span: 8,
    width: '100px'
  },
  {
    label: '电站名称',
    prop: 'stationCode',
    type: 'stationSelect',
    width: '86px',
    placeholder: '请选择电站',
    span: 8
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 6
  }
  await getListData()
}
const tableLoading = ref(false)
const finished = ref(false)
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const getListData = async () => {
  try {
    let { data } = await handleAPI.operateTicketList(
      { ...searchData.value },
      true
    )

    data.data.records.length &&
      data.data.records.forEach((item: any) => {
        ;(item.operationUser = item.operationUser
          ? item.operationUser.split('_')[0]
          : '--'),
          (item.operationCompanyName = item.operationCompanyName
            ? item.operationCompanyName.split('_')[0]
            : '--')
      })
    listTotal.value = data.data.total
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  } finally {
    finished.value = true
  }
}
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getListData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  await getListData()
}
// 新建操作票
const handleAdd = () => {
  router.push({
    path: `/ticket/handle/handleInfo/add`,
    query: {
      type: 'add'
    }
  })
}
const clickCard = async (item: any) => {
  // 草稿状态下判断: 1-编辑 2-详情
  if (item.operateState == 1) {
    if (item.intoPageState == 1) {
      router.push({
        path: `/ticket/handle/handleInfo/add/${item.id}`,
        query: {
          type: 'edit'
        }
      })
    } else {
      router.push('/ticket/handle/handleInfo/look/' + item.id)
    }
  } else {
    router.push('/ticket/handle/handleInfo/look/' + item.id)
  }
}
// 查看审批流程
const rows = ref({})
const isShow = ref(false)
const onViewProcess = (item: any) => {
  isShow.value = true
  rows.value = item
}
// 复制操作票
const onCopyTicket = async (item: any) => {
  router.push({
    path: `/ticket/handle/handleInfo/add/${item.id}`,
    query: {
      type: 'copy'
    }
  })
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped lang="scss">
.el-dropdown {
  position: absolute;
  right: 20px;
  bottom: 20px;
  line-height: 34px;
  .el-dropdown-link {
    outline: 0;
    cursor: pointer;
  }
}
.tag3 {
  background: rgba(242, 242, 242, 1);
  color: rgba(0, 0, 0, 0.65);
}
</style>
