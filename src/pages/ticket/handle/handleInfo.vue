<template>
  <div class="page-operate">
    <div class="operate-title">
      {{ title_ }}
      <template v-if="isLook_">
        <el-tag
          v-if="!!opTicketDetailVO.operateState"
          :class="[
            opTicketDetailVO.operateState
              ? 'tag tag' + opTicketDetailVO.operateState
              : ''
          ]"
          >{{ statusType_[opTicketDetailVO.operateState] }}</el-tag
        >
      </template>
    </div>
  </div>
  <!-- 顶部按钮 -->
  <div v-if="isLook_" class="btns">
    <el-button
      v-if="isHandleSupply_"
      v-preventReClick="1000"
      type="primary"
      @click="handleSupply"
      >补充信息</el-button
    >
    <el-button
      v-if="isRevokeTicket_"
      v-preventReClick="1000"
      type="primary"
      @click="revokeTicket"
      >作废操作票</el-button
    >
    <el-button
      v-if="isDowloadBtn_"
      v-preventReClick="1000"
      type="primary"
      @click="dowloadBtn"
      >导出操作票</el-button
    >
    <el-button
      v-if="isUploadBtn_"
      v-preventReClick="1000"
      type="primary"
      @click="uploadBtn"
      >上传原件</el-button
    >
  </div>
  <!-- 基础信息 -->
  <div class="info-base">
    <div class="operate_">
      <p><span></span><span>基础信息</span></p>
    </div>
    <div class="info">
      <el-form
        ref="formRef"
        :rules="formRules"
        :model="formData"
        label-suffix=""
        label-position="right"
        :inline="true"
        class="w-full"
        label-width="130px"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="编号" prop="operateTicketNo">
              <span v-if="isLook_">
                {{ formData.operateTicketNo || '--' }}
              </span>
              <el-input
                v-else
                v-model.trim="formData.operateTicketNo"
                placeholder="保存后自动生成"
                :disabled="true"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="票类型" prop="ticketType">
              <span v-if="isLook_">
                {{ formData.ticketType || '--' }}
              </span>
              <el-input
                v-else
                v-model.trim="formData.ticketType"
                placeholder="电气操作票"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="!isShowRevokeTicke_ && !isLook_ && !isCopy" :span="12">
            <el-form-item label="选择典型票">
              <el-select
                v-model="formData.templateId"
                placeholder="请选择典型票"
                filterable
                clearable
                :disabled="!formData.stationCode"
                @change="changeTemplate"
              >
                <template v-if="templates.length">
                  <el-option
                    v-for="item in templates"
                    :key="item.id"
                    :label="item.modelName"
                    :value="item.id"
                  />
                  <el-option label="暂不使用" value="" />
                </template>
                <template v-else>
                  <el-option label="暂不使用" value="" />
                  <el-option label="新建典型票" :value="-1" />
                </template>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电站名称" prop="stationCode">
              <span v-if="isLook_">
                {{ formData.stationName || '--' }}
              </span>
              <StationSelect
                v-else
                v-model="formData.stationCode"
                v-model:label="formData.stationName"
                @change="changeStation"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="operationCompanyCode_">
              <span v-if="isLook_">
                {{ formData.operationCompanyName || '--' }}
              </span>

              <el-select
                v-else
                v-model="formData.operationCompanyCode_"
                placeholder="请选择单位"
                clearable
                filterable
                :disabled="true"
              >
                <template v-for="item in departList" :key="item.value">
                  <el-option :label="item.label" :value="item.value" />
                </template>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="监护人" prop="guardianUser">
              <span v-if="isLook_">
                {{
                  getName2(
                    userInfoArr,
                    formData.guardianUser,
                    'value',
                    'label'
                  ) || '--'
                }}
              </span>

              <el-select-v2
                v-else
                v-model="formData.guardianUser"
                :options="userInfoArr"
                placeholder="请选择监护人"
                filterable
                style="width: 100%"
                clearable
              >
              </el-select-v2>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="发令人" prop="sendingUser">
              <span v-if="isLook_">
                {{
                  getName2(
                    userInfoArr,
                    formData.sendingUser,
                    'value',
                    'label'
                  ) || '--'
                }}
              </span>

              <el-select-v2
                v-else
                v-model="formData.sendingUser"
                :options="userInfoArr"
                placeholder="请选择发令人"
                filterable
                style="width: 100%"
                clearable
                @change="changeSendingUser"
              >
              </el-select-v2>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="值班负责人(值长)" prop="shiftChiefUser">
              <span v-if="isLook_">
                {{
                  getName2(
                    userInfoArr,
                    formData.shiftChiefUser,
                    'value',
                    'label'
                  ) || '--'
                }}
              </span>

              <el-select-v2
                v-else
                v-model="formData.shiftChiefUser"
                :options="userInfoArr"
                placeholder="请选择值班负责人(值长)"
                filterable
                style="width: 100%"
                clearable
              >
              </el-select-v2>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="受令人" prop="receivingUser">
              <span v-if="isLook_">
                {{
                  getName2(
                    userInfoArr,
                    formData.receivingUser,
                    'value',
                    'label'
                  ) || '--'
                }}
              </span>
              <el-select-v2
                v-else
                v-model="formData.receivingUser"
                :options="userInfoArr"
                placeholder="请选择受令人"
                filterable
                style="width: 100%"
                clearable
                @change="changeReceivingUser"
              >
              </el-select-v2>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="操作人" prop="operationUser">
              <span v-if="isLook_">
                {{
                  getName2(
                    userInfoArr,
                    formData.operationUser,
                    'value',
                    'label'
                  ) || '--'
                }}
              </span>

              <el-select-v2
                v-else
                v-model="formData.operationUser"
                :options="userInfoArr"
                placeholder="请选择操作人"
                filterable
                style="width: 100%"
                clearable
              >
              </el-select-v2>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="操作时间" prop="operateTime">
              <span v-if="isLook_">
                {{
                  formData.operateStartTime && formData.operateEndTime
                    ? formData.operateStartTime +
                      '  -  ' +
                      formData.operateEndTime
                    : '--'
                }}
              </span>
              <el-date-picker
                v-else
                v-model="formData.operateTime"
                type="datetimerange"
                :editable="false"
                start-placeholder="请选择操作开始时间"
                end-placeholder="请选择操作结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="操作类型" prop="operateType">
              <span v-if="isLook_">
                {{
                  formData.operateType == 1
                    ? '监护操作'
                    : formData.operateType == 2
                      ? '单人操作'
                      : '--'
                }}
              </span>
              <el-radio-group v-else v-model="formData.operateType">
                <el-radio :value="1">监护操作</el-radio>
                <el-radio :value="2">单人操作</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="isLook_" :span="12">
            <el-form-item label="发令时间" prop="commandTime">
              <span v-if="!!formData.receivingTime">
                {{
                  formData.commandTime
                    ? formData.commandTime.slice(0, 16)
                    : '--'
                }}
              </span>
              <span v-else>--</span>
            </el-form-item>
          </el-col>

          <el-col :span="isLook_ ? 12 : 24">
            <el-form-item label="操作任务" prop="operateTask">
              <span v-if="isLook_" style="line-height: 40px">
                {{ formData.operateTask || '--' }}
              </span>
              <el-input
                v-else
                v-model.trim="formData.operateTask"
                :rows="3"
                type="textarea"
                placeholder="请输入操作任务"
                maxlength="1024"
                autocomplete="off"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col v-if="isLook_" :span="12">
            <el-form-item label="受令时间" prop="receivingTime">
              <span>
                {{
                  formData.receivingTime
                    ? formData.receivingTime.slice(0, 16)
                    : '--'
                }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>

  <!-- 操作内容 -->
  <div class="info-base">
    <div class="operate_">
      <p><span></span><span>操作内容</span></p>
    </div>
    <div class="tableRow" style="margin-bottom: 24px">
      <vis-table-pagination
        v-if="isLook_"
        :columns="tableColumnsLook"
        :data="supplementList"
        :border="true"
        background
        class="vis-table-pagination table_Row_"
        :default-border="true"
        :show-overflow-tooltip="true"
        height="auto"
      >
        <template #executeState="{ row }">
          <div
            :style="[row.executeState == 0 ? 'color: rgb(46, 141, 230)' : '']"
          >
            {{
              row.operateContent == '无'
                ? '无'
                : row.executeState == 0
                  ? '已执行'
                  : row.executeState == 1
                    ? '不执行'
                    : '未执行'
            }}
          </div>

          {{ row.contentDes ? row.contentDes : '' }}
        </template>
        <template #completeDate="{ row }">
          {{
            row.operateContent == '无'
              ? '无'
              : row.completeDate
                ? row.completeDate.slice(0, 16)
                : '--'
          }}
        </template>
        <template #proveImage="{ row }">
          <div v-if="row.operateContent == '无'">无</div>
          <div v-else>
            <SpicUpload
              v-if="row.proveImage_.length != 0"
              v-model="row.proveImage_"
              type="image"
              :limit="5"
              disabled
            />
            <span v-else>--</span>
          </div>
        </template>
      </vis-table-pagination>
      <table-row
        v-else
        ref="tableInfoRef"
        :key="tableInfoKey"
        :columns="tableColumns"
        :table-data="tableList"
        :maxlength="1024"
        :max-len="200"
        @get-tabel-data="getTabelData"
      ></table-row>
    </div>
  </div>
  <!-- 补充信息 -->
  <div v-if="isLook_" class="info-base">
    <div class="operate_">
      <p><span></span><span>补充信息</span></p>
    </div>
    <div class="info">
      <el-form class="w-full" label-width="130px" style="padding-right: 50px">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="备注">
              <div>{{ supplyInfo.ticketRemarks || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="操作票盖章文件">
              <SpicUpload
                v-if="supplyInfo.scriptFile.length != 0"
                :key="SpicUploadKey"
                v-model="supplyInfo.scriptFile"
                :file-ext="fileExt"
                type="file"
                :limit="20"
                :upload-display="true"
                :handle-remove-fn="handleRemoveFn"
                disabled
              />
              <span v-else>--</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <!-- 关联关系 -->
  <div class="info-base">
    <div class="operate_">
      <p><span></span><span>关联关系</span></p>
    </div>
    <div class="info">
      <el-form
        :model="glFormData"
        label-suffix=""
        label-position="right"
        :inline="true"
        class="w-full"
        label-width="130px"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="关联工作票" prop="workTicketId_">
              <div v-if="isLook_">
                <div
                  v-if="glFormData.workTicketNo"
                  style="
                    display: flex;
                    color: rgba(41, 204, 160, 1);
                    cursor: pointer;
                  "
                  @click="toWorkTitle(glFormData.workTicketId_)"
                >
                  <img :src="link" class="linkSvg" />
                  {{ glFormData.workTicketNo || '--' }}
                </div>
                <span v-else>--</span>
              </div>
              <el-select-v2
                v-else
                v-model="glFormData.workTicketId_"
                :options="workTicketIdArr"
                placeholder="请选择关联工作票"
                filterable
                style="width: 100%"
                :props="workTicketIdProps"
                clearable
              >
              </el-select-v2>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>

  <!-- 底部按钮 -->
  <div v-if="!isLook_" class="page-footer">
    <el-button plain @click="onCancel">返回</el-button>
    <el-button v-preventReClick="1000" type="primary" @click="onCgSumbit">
      保存草稿
    </el-button>
    <el-button v-preventReClick="1000" type="primary" @click="onApproval">
      提交生效审批
    </el-button>
    <el-button
      v-if="isShowRevokeTicke_"
      v-preventReClick="1000"
      type="primary"
      @click="revokeTicket"
    >
      作废操作票
    </el-button>
  </div>
  <!-- 补充信息 -->
  <supplyTicketDialog
    :is-show="showSupplyTicket"
    :op-ticket-detail-v-o="opTicketDetailVO"
    @close-dialog="showSupplyTicket = false"
    @uplate-data="uplateSupplyData"
  ></supplyTicketDialog>
  <!-- 上传原件 -->
  <upload
    :is-show="isShow"
    :stamp-file="stampFileArr"
    @close-dialog="isShow = false"
    @update-list="updateList"
  ></upload>
  <!-- 作废操作票 -->
  <revokeTicketDialog
    :is-show="showRevokeTicket"
    tid="'null'"
    type="4"
    @close-dialog="showRevokeTicket = false"
    @uplate-data="uplateRevokeData"
  ></revokeTicketDialog>
</template>
<script setup lang="ts">
import type { FormRules, FormInstance } from 'element-plus'
import StationSelect from '@/components/spic-station'
import upload from '../components/upload.vue'
import tableRow from '../components/tableRow.vue'
import link from '@/assets/svgs/link.svg'
import { ElLoading } from 'element-plus'
import {
  handle as handleAPI,
  approval as approvalAPI,
  ticket as ticketAPI
} from '@/api'
import * as api from '@/api/index.ts'

import revokeTicketDialog from '../components/revokeTicketDialog.vue'
import supplyTicketDialog from './components/supplyTicketDialog.vue'

const route = useRoute()
const router = useRouter()

const title_ = computed(() => {
  if (route.params && route.params.pageStatus == 'add') {
    return '新建操作票'
  }
  if (route.params && route.params.pageStatus == 'look') {
    return '查看详情'
  }
})
const isLook_ = computed(() => {
  return route.params && route.params.pageStatus == 'look'
})
const isShowRevokeTicke_ = computed(() => {
  return route.query && route.query.type == 'edit'
})
const isCopy = computed(() => {
  return route.query && route.query.type == 'copy'
})
const statusType_: any = {
  1: '草稿',
  2: '生效中',
  3: '已作废',
  4: '已终结'
}
// 基本信息
const opTicketDetailVO = ref({
  operateState: ''
})
const formRef = ref<FormInstance>()
const formRules = reactive<FormRules<Record<string, any>>>({
  operateTicketNo: [
    { required: true, message: '保存后自动生成', trigger: 'blur' }
  ],
  ticketType: [{ required: true, message: '电气操作票', trigger: 'blur' }],
  stationCode: [
    { required: true, message: '请选择电站名称', trigger: 'change' }
  ],
  sendingUser: [{ required: true, message: '请选择发令人', trigger: 'change' }],
  commandTime: [{ required: true }],
  receivingTime: [{ required: true }],
  receivingUser: [
    { required: true, message: '请选择受令人', trigger: 'change' }
  ],
  operationUser: [
    { required: true, message: '请选择操作人', trigger: 'change' }
  ],
  guardianUser: [
    { required: true, message: '请选择监护人', trigger: 'change' }
  ],
  shiftChiefUser: [
    { required: true, message: '请选择值班负责人(值长)', trigger: 'change' }
  ],
  operationCompanyCode_: [
    { required: true, message: '请选择单位', trigger: 'change' }
  ],
  operateTime: [
    { required: true, message: '请选择操作时间', trigger: 'change' }
  ],
  operateType: [
    { required: true, message: '请选择操作类型', trigger: 'change' }
  ],
  operateTask: [{ required: true, message: '请选择操作任务', trigger: 'blur' }]
})
const formData = ref<Record<string, any>>({
  operateTicketNo: '保存后自动生成',
  ticketType: '电气操作票',
  stationCode: '',
  stationName: '',
  sendingUser: '',
  receivingUser: '',
  operationUser: '',
  guardianUser: '',
  shiftChiefUser: '',
  operationCompanyCode_: '', // 单位
  operationCompanyName: '',
  operateTime: [],
  operateType: 1,
  operateTask: ''
})

const changeStation = (e: any) => {
  formData.value.stationName = e.stationName
  getCompany(e.stationCode)
  router.replace({
    path: route.fullPath,
    query: { ...route.query, station: e.stationName + '__' + e.stationCode }
  })
}

const templates = ref<any[]>([])
const getTemplates = async (operatorId: any) => {
  const { data } = await api.post({
    url: `/operate/modelTicketController/getTemplateByType`,
    data: {
      modelType: 3,
      operatorId
    },
    loading: false
  })
  templates.value = data || []
  formData.value.templateId = templates.value[0]?.id || ''
  changeTemplate(formData.value.templateId)
}

const changeTemplate = (val: any) => {
  if (!val) {
    tableList.value = []
    supplementList.value = []
  } else if (val === -1) {
    router.push(
      `/ticket/handle/dx/add/add?modelType=3&company=${company.value}&station=${formData.value.stationName}__${formData.value.stationCode}`
    )
  } else {
    getTemplateContent(val)
  }
}
const getTemplateContent = async (id: number) => {
  const { data } = await api.get({
    url: '/operate/modelTicketController/getModelTicketById',
    data: { id },
    loading: true
  })
  const res = data?.modelTemplateDto?.safeStepDtoList || []
  if (res.length) {
    res.forEach((item: any) => {
      item.proveImage_ = item.proveImage ? item.proveImage.split(',') : []
      item.operateContent = item.safeStepContext || ''
    })
    tableList.value = res
    supplementList.value = res
  }
}

const userInfoArr = ref([]) // 发令人  受令人 操作人 监护人 请选择值班负责人
const departList = ref<any[]>([]) // 单位
const changeSendingUser = (val: any) => {
  if (!formData.value.shiftChiefUser) {
    formData.value.shiftChiefUser = val
  }
}
const changeReceivingUser = (val: any) => {
  if (!formData.value.operationUser) {
    formData.value.operationUser = val
  }
}
// 操作内容
const tableInfoRef = ref<any>(null)
const tableList = ref([])
const tableInfoKey = ref<any>('')
const operateTicketContentList = ref<any>({})
const tableColumns = ref([
  { prop: 'index', label: '' },
  {
    prop: 'operateContent',
    label: '操作项目',
    slotName: 'operateContent',
    placeholder: '请输入操作项目'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 100,
    fixed: 'right'
  }
])
const getTabelData = (val = { valid: false, type: 'validate', data: {} }) => {
  if (
    (val.valid && val.type == 'validate') ||
    (!val.valid && val.type == 'noValidate')
  ) {
    operateTicketContentList.value = val.data
  } else {
    operateTicketContentList.value = {}
  }
}
// 补充信息
const tableColumnsLook = ref([
  {
    prop: 'operateContent',
    label: '操作项目'
  },
  {
    prop: 'executeState',
    label: '执行情况',
    slotName: 'executeState'
  },
  {
    prop: 'completeDate',
    label: '完成时间',
    slotName: 'completeDate'
  },
  {
    prop: 'proveImage',
    label: '证明照片',
    slotName: 'proveImage'
  }
])
const supplementList = ref([])
// 补充信息
const fileExt = ref(['jpg', 'png', 'pdf', 'doc'])
const supplyInfo = ref({
  ticketRemarks: '',
  scriptFile: []
})
const handleRemoveFn = (val: any) => {
  let arr =
    val &&
    val.map((item: any) => {
      return item.fileName + '@' + item.originalFileName
    })
  updateList(arr)
}
// 关联关系
const workTicketIdArr = ref([])
const glFormData = ref({
  workTicketId_: '',
  workTicketNo: ''
})
const workTicketIdProps = ref({
  label: 'workTicketNo',
  value: 'workTicketId_'
})
const getName = (id: any) => {
  let str = ''
  workTicketIdArr.value.length &&
    workTicketIdArr.value.forEach((item: any) => {
      if (item.workTicketId_ == id) {
        str = item.workTicketNo
      }
    })
  return str
}
const toWorkTitle = async (workTicketId_: any) => {
  let type = workTicketId_ ? workTicketId_.split('_')[1] : ''
  try {
    let { data } = await ticketAPI.checkGroup({
      orderNo: getName(workTicketId_),
      type: type == 1 ? 2 : 3 // 1-工单，2-一种票，3-二种票，4-操作票
    })
    if (data) {
      if (type == 1) {
        router.push(
          route.path + `/ticketWorkDetail/${workTicketId_.split('_')[0]}`
        )
      } else {
        router.push(
          route.path + `/ticketTwoDetail/${workTicketId_.split('_')[0]}`
        )
      }
    } else {
      ElMessage({
        message: '权限不足',
        type: 'warning'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: '权限不足',
      type: 'warning'
    })
    console.log(e)
  }
}
//  === 关联操作票1 2 ===
const getWorkOneTwoTicketListFn = async () => {
  try {
    let { data } = await handleAPI.getWorkOneTwoTicketList({})
    if (data.code == 200) {
      data.data.length &&
        data.data.forEach((item: any) => {
          item.workTicketId_ = item.workTicketId + '_' + item.workTicketType
        })
      workTicketIdArr.value = data.data
    } else {
      workTicketIdArr.value = []
    }
  } catch (e) {
    workTicketIdArr.value = []
    console.log(e)
  }
}
// === 获取uc 接口 ===
const getAllPersonnelFn = async () => {
  try {
    let { data } = await approvalAPI.getUserInfoList({
      pageSize: 10000,
      pageNo: 1
    })
    if (data && data.data) {
      let arr = data.data.list
        .filter((item: any) => item.id != null)
        .map(({ employeeName, id, employeePhone }: any) => ({
          label: employeeName + '/' + employeePhone,
          value: employeeName + '_' + id
        }))
      userInfoArr.value = arr
    } else {
      userInfoArr.value = []
    }
  } catch (e: any) {
    userInfoArr.value = []
  }
}
const company = ref<string>()
const getCompany = async (stationCode: string) => {
  try {
    const { data } = await api.get('/operate/workOrder/getOperatorsInfo', {
      stationCode
    })
    departList.value = [data].map((item: any) => {
      return {
        label: item.companyName || '',
        value: item.companyName + '_' + item.id || ''
      }
    })
    company.value = data.companyName + '__' + data.companyCode
    formData.value.operationCompanyCode_ = data.companyName + '_' + data.id
    getTemplates(data.id)
  } catch (e: any) {
    departList.value = []
  }
}
// 返回
const onCancel = () => {
  if (route.params && route.params.type == 'approval') {
    router.push('/approval')
  } else {
    router.push('/ticket/handle')
  }
}
// === 查看详情 ===
const isHandleSupply_ = ref(false)
const isRevokeTicket_ = ref(false)
const isDowloadBtn_ = ref(false)
const isUploadBtn_ = ref(false)

const getGetJsaTicketDetailVO = async (id: string | string[]) => {
  try {
    let { data } = await handleAPI.getOperateTicketById({ id })
    if (data.code == 200) {
      isHandleSupply_.value = data.data.operateState == 2
      isRevokeTicket_.value = data.data.operateState == 2
      isDowloadBtn_.value =
        data.data.operateState != 2 && data.data.receivingTime
      isUploadBtn_.value =
        data.data.operateState == 3 || data.data.operateState == 4
      opTicketDetailVO.value = data.data

      formData.value = {
        ...data.data,
        ticketType: '电气操作票',
        operationCompanyCode_: data.data.operationCompanyName
          ? data.data.operationCompanyName +
            '_' +
            data.data.operationCompanyCode
          : '',
        operateTime:
          data.data.operateStartTime && data.data.operateEndTime
            ? [data.data.operateStartTime, data.data.operateEndTime]
            : []
      }
      // 操作内容
      data.data.operateTicketContentList.length &&
        data.data.operateTicketContentList.forEach((item: any) => {
          item.proveImage_ = item.proveImage ? item.proveImage.split(',') : []
        })
      tableList.value = data.data.operateTicketContentList
        ? data.data.operateTicketContentList
        : []
      supplementList.value = data.data.operateTicketContentList
        ? data.data.operateTicketContentList
        : []
      // 补充信息
      supplyInfo.value.ticketRemarks = data.data.ticketRemarks || ''
      supplyInfo.value.scriptFile = data.data.scriptFile
        ? data.data.scriptFile.split(',')
        : []
      stampFileArr.value = data.data.scriptFile
        ? data.data.scriptFile.split(',')
        : []
      // 关联工作票-复制页面（关联工作票”“操作票编号“置空）
      if (route.query && route.query.type == 'copy') {
        formData.value.operateTicketNo = '保存后自动生成'
        glFormData.value.workTicketId_ = ''
        glFormData.value.workTicketNo = ''
        supplyInfo.value.ticketRemarks = ''
        supplyInfo.value.scriptFile = []
        stampFileArr.value = []
      } else {
        glFormData.value.workTicketId_ =
          data.data.workTicketId && data.data.workTicketType
            ? data.data.workTicketId + '_' + data.data.workTicketType
            : ''
        glFormData.value.workTicketNo = data.data.workTicketNo || ''
      }
    } else {
      opTicketDetailVO.value = {
        operateState: ''
      }
      formData.value = {}
      tableList.value = []
      glFormData.value = {
        workTicketId_: '',
        workTicketNo: ''
      }
      supplyInfo.value.ticketRemarks = ''
      supplyInfo.value.scriptFile = []
      stampFileArr.value = []
    }
    tableInfoKey.value = Date.now()
    SpicUploadKey.value = Date.now()
  } catch (e) {
    opTicketDetailVO.value = {
      operateState: ''
    }
    formData.value = {}
    tableList.value = []
    glFormData.value = { workTicketId_: '', workTicketNo: '' }
    supplyInfo.value.ticketRemarks = ''
    supplyInfo.value.scriptFile = []
    stampFileArr.value = []
  }
}
// ==== 按钮-补充信息 ===
const showSupplyTicket = ref<boolean>(false)
const handleSupply = () => {
  showSupplyTicket.value = true
}
const uplateSupplyData = async () => {
  getGetJsaTicketDetailVO((route.params && route.params.opid) || '')
}
const showRevokeTicket = ref<boolean>(false)
const revokeTicket = () => {
  showRevokeTicket.value = true
}
const uplateRevokeData = async () => {
  onCancel()
}
// ==== 按钮-导出操作票 ===
const dowloadBtn = async () => {
  try {
    let res = await handleAPI.download({
      id: Number(route.params.opid) || ''
    })
    let str = res.headers['content-disposition']
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(res.data)
    link.download = '电气操作票.' + str.split('.')[str.split('.').length - 1]
    link.click()
  } catch (e) {
    ElMessage({
      type: 'warning',
      message: '导出失败'
    })
  }
}
// ==== 按钮-上传原件 ===
const isShow = ref(false)
const SpicUploadKey = ref<any>('')
const stampFileArr = ref([])
const uploadBtn = () => {
  stampFileArr.value = supplyInfo.value.scriptFile
  isShow.value = true
}
const updateList = async (val: any) => {
  try {
    let { data } = await handleAPI.updateTicketById({
      id: Number(route.params && route.params.opid) || '',
      scriptFile: val.join(',')
    })
    if (data.code == 200) {
      getGetJsaTicketDetailVO((route.params && route.params.opid) || '')
    } else {
      ElMessage({
        type: 'warning',
        message: data.message
      })
    }
  } catch (e) {
    ElMessage({
      type: 'warning',
      message: '操作失败'
    })
  }
}

const getParams = () => {
  let params: any = {
    ...formData.value,
    id: null,
    ticketType: null,
    operateStartTime: formData.value.operateTime.length
      ? formData.value.operateTime[0]
      : '',
    operateEndTime: formData.value.operateTime.length
      ? formData.value.operateTime[1]
      : '',
    operationCompanyName: formData.value.operationCompanyCode_
      ? formData.value.operationCompanyCode_.split('_')[0]
      : '',
    operationCompanyCode: formData.value.operationCompanyCode_
      ? formData.value.operationCompanyCode_.split('_')[1]
      : '',
    operateTicketContentList: operateTicketContentList.value,
    workTicketId: glFormData.value.workTicketId_
      ? glFormData.value.workTicketId_.split('_')[0]
      : '',
    workTicketType: glFormData.value.workTicketId_
      ? glFormData.value.workTicketId_.split('_')[1]
      : '',
    ticketRemarks: '',
    scriptFile: ''
  }
  // 新建  编辑  查看
  params.id = Number(route.params && route.params.opid) || null
  if (route.query && route.query.type == 'copy') {
    params.id = null
    params.ticketRemarks = ''
    params.scriptFile = ''
    params.commandTime = ''
    params.receivingTime = ''

    let arr: any = []
    operateTicketContentList.value.length &&
      operateTicketContentList.value.forEach((item: any) => {
        arr.push({
          operateContent: item.operateContent
        })
      })
    params.operateTicketContentList = arr
  }
  return params
}
// ==== 草稿按钮 ====
const onCgSumbit = async () => {
  await tableInfoRef.value.isValidate()
  nextTick(async () => {
    try {
      let params = getParams()
      let res: any = { data: {} }
      if (
        route.query &&
        (route.query.type == 'add' || route.query.type == 'copy')
      ) {
        res = await handleAPI.addOperateTicket(params)
      } else if (route.query && route.query.type == 'edit') {
        res = await handleAPI.updateOperateTicket(params)
      }
      // ts-ignore
      if (res.data.code == 200) {
        ElMessage({
          type: 'success',
          message: '保存草稿成功'
        })
        onCancel()
      }
    } catch (e) {
      ElMessage({
        type: 'warning',
        message: '保存草稿失败'
      })
      console.log(e)
    }
  })
}
// ==== 点击生效审批按钮 ====
const onApproval = async () => {
  await tableInfoRef.value.submitTable()
  nextTick(async () => {
    // ts-ignore
    formRef.value?.validate(async (valid: any) => {
      if (valid) {
        if (JSON.stringify(operateTicketContentList.value) == '{}') {
          ElMessage({
            type: 'warning',
            message: '有必填内容未填写'
          })
          return Promise.reject()
        }
        // 新增/复制页面
        if (
          route.query &&
          (route.query.type == 'add' || route.query.type == 'copy')
        ) {
          try {
            let params = getParams()
            let { data } = await handleAPI.addOperateTicket(params)
            if (data.code == 200) {
              getOperateTicketComeIntoApprove(data.data)
            } else {
              ElMessage({
                type: 'warning',
                message: data.message
              })
            }
          } catch (e) {
            ElMessage({
              type: 'warning',
              message: '操作失败'
            })
          }
        } else {
          // 编辑页面
          getOperateTicketComeIntoApprove(
            Number(route.params && route.params.opid)
          )
        }
      }
    })
  })
}
// 操作票生效审批
const getOperateTicketComeIntoApprove = async (businessId: any) => {
  try {
    let { data } = await handleAPI.operateTicketComeIntoApprove({
      businessId: Number(businessId)
    })
    if (data.code == 200) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      onCancel()
    } else {
      ElMessage({
        type: 'warning',
        message: data.message
      })
    }
  } catch (e) {
    ElMessage({
      type: 'warning',
      message: '操作失败'
    })
    console.log(e)
  }
}
onMounted(() => {
  getAllPersonnelFn()
  if (route.params && route.params.pageStatus == 'look') {
  } else {
    getWorkOneTwoTicketListFn()
  }

  if (route.params && route.params.opid) {
    getGetJsaTicketDetailVO(route.params.opid)
  }
  if (route.query.station) {
    formData.value.stationName = String(route.query.station).split('__')[0]
    formData.value.stationCode = String(route.query.station).split('__')[1]
    changeStation({
      stationName: formData.value.stationName,
      stationCode: formData.value.stationCode
    })
  }
  // 解决下拉v2赋值问题
  const LoadingSvg = `<svg t="1699584736881" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27199" width="48" height="48"><path d="M224 224m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#e6e6e6" p-id="27200"></path><path d="M800 224m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#cdcdcd" p-id="27201"></path><path d="M800 800m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#8a8a8a" p-id="27202"></path><path d="M224 800m-192 0a192 192 0 1 0 384 0 192 192 0 1 0-384 0Z" fill="#515151" p-id="27203"></path></svg>`
  const loading = ElLoading.service({
    fullscreen: true,
    text: '1111',
    spinner: LoadingSvg,
    svgViewBox: '0 0 48 48'
  })
  setTimeout(() => {
    loading.close()
  }, 1900)
})

// 查找name
const getName2 = (arr: any, val: any, key: any, label: any) => {
  let str = ''
  arr.length &&
    arr.forEach((item: any) => {
      if (item[key] == val) {
        str = item[label]
      }
    })
  return str
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped src="../assets/tableRow.scss"></style>
<style scoped lang="scss">
.tag {
  margin-left: 16px !important;
}
.btns {
  padding: 16px 0 0 24px;
  box-sizing: border-box;
}
.info-base {
  margin: 16px 24px 24px !important;
}
.tag3 {
  background: rgba(242, 242, 242, 1);
  color: rgba(0, 0, 0, 0.65);
}
</style>
<style lang="scss">
.table_Row_ {
  .cell.el-tooltip {
    overflow: hidden !important;
  }
}
</style>
