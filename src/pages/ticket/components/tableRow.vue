<template>
  <div class="tableRow">
    <el-form ref="formRef" :key="formKey" :model="formData">
      <vis-table-pagination
        :key="formKey"
        :columns="columns"
        :data="data"
        border
        background
        class="vis-table-pagination"
        :default-border="true"
        :show-overflow-tooltip="true"
        height="auto"
      >
        <template
          v-for="item in columns.slice(0, columns.length - 1)"
          #[item.prop]="{ row, index }"
        >
          <template v-if="index >= 0">
            <template v-if="item.type === 'datetime'">
              <el-form-item
                :key="row.id"
                :prop="`row.${index}.${item.prop}`"
                :show-message="false"
                class="input7343"
                :rules="{
                  required: true,
                  message: '',
                  trigger: 'blur'
                }"
              >
                <el-date-picker
                  v-model.trim="row[item.prop]"
                  type="datetime"
                  :placeholder="item.placeholder || ''"
                  :format="item.format || 'YYYY-MM-DD HH:mm:ss'"
                  :value-format="item.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
                  :editable="false"
                />
              </el-form-item>
            </template>
            <template v-else>
              <el-form-item
                :key="row.id"
                :prop="`row.${index}.${item.prop}`"
                :show-message="false"
                class="input7343"
                :rules="{
                  required: true,
                  message: '',
                  trigger: 'blur'
                }"
              >
                <el-input
                  v-model.trim="row[item.prop]"
                  :placeholder="item.placeholder || '请输入内容'"
                  :maxlength="maxlength || 200"
                />
              </el-form-item>
            </template>
          </template>
        </template>
        <template #operate="{ row, index }">
          <div class="table-operate">
            <el-button
              link
              :disabled="index == 0"
              :class="index == 0 ? 'deleteClass_' : ''"
              @click="deleBtn(index, row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </vis-table-pagination>
    </el-form>
    <div class="itemAdd">
      <el-button link type="primary" class="add-btn" @click="addBtn">
        <el-icon> <Plus /> </el-icon> 新增
      </el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
type PropsType = {
  columns: any[]
  tableData?: any[]
  maxlength?: number
  maxLen?: number
}
const formRef = ref()
const formKey = ref<any>('')
const props = withDefaults(defineProps<PropsType>(), {})
let defaultObj = Object.fromEntries(
  props.columns.map((e) => [e.prop || e.slotName, ''])
)

const data = ref([JSON.parse(JSON.stringify(defaultObj))])
let formData = reactive(
  (function () {
    return {
      row: [
        Object.fromEntries(props.columns.map((e) => [e.prop || e.slotName, '']))
      ]
    }
  })()
)
formData.row = data.value

watch(
  () => props.tableData,
  (val = []) => {
    data.value =
      val.length == 0
        ? [
            Object.fromEntries(
              props.columns.map((e) => [e.prop || e.slotName, ''])
            )
          ]
        : JSON.parse(JSON.stringify(val))
    formData = reactive(
      (function () {
        return {
          row: [
            Object.fromEntries(
              props.columns.map((e) => [e.prop || e.slotName, ''])
            )
          ]
        }
      })()
    )
    formData.row = data.value
    formKey.value = Date.now()
  },
  {
    deep: true,
    immediate: true
  }
)
// 新增
const addBtn = async () => {
  const maxLen = props.maxLen || 50
  if (data.value.length >= maxLen) {
    ElMessage({
      message: `最多可新增${maxLen}个`,
      type: 'warning'
    })
    return false
  } else {
    nextTick(async () => {
      await formRef.value.validate(async (valid: boolean) => {
        if (valid) {
          data.value.push({
            ...JSON.parse(JSON.stringify(defaultObj))
          })
          // formKey.value = Date.now()
        }
      })
    })
  }
}
// 删除
const emit = defineEmits(['getTabelData', 'deleBtn'])
const delJsaSupplementIds = ref([])
const deleBtn = (index: number, row: any) => {
  nextTick(() => {
    if (row.id) {
      delJsaSupplementIds.value.push(row.id)
      emit('deleBtn', delJsaSupplementIds.value)
    }
    data.value = data.value.filter((e, i) => i !== index)
    formData.row = data.value
  })
  // formKey.value = Date.now()
}
// 校验 保存草稿1 签发审批2
const submitTable = async (type: number) => {
  if (type === 1) {
    if (data.value && data.value.length) {
      // emit('getTabelData', {
      //   data: data.value
      // })
      let arr = data.value.filter((item: any) => {
        item.flag = false
        for (let i in item) {
          if (item[i]) {
            item.flag = true
          }
        }
        return item.flag
      })

      emit('getTabelData', {
        data: arr
      })
    }
  } else {
    await formRef.value.validate(async (valid: boolean) => {
      isValidate(valid, 'validate')
    })
  }
}
const isValidate = (valid = false, type = 'noValidate') => {
  let arr = data.value.filter((item: any) => {
    item.flag = false
    for (let i in item) {
      if (item[i]) {
        item.flag = true
      }
    }
    return item.flag
  })
  emit('getTabelData', {
    valid,
    type,
    data: arr
  })
}
defineExpose({
  submitTable,
  isValidate
})
onMounted(() => {})
</script>
<style scoped src="../assets/tableRow.scss"></style>
<style scoped>
.el-table-fixed-column--right .el-button.is-link.deleteClass_ {
  color: #999 !important;
}
</style>
