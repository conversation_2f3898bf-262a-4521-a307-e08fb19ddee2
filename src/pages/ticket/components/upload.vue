<!--
 * @Author: 赵鹏鹏
 * @Date: 2024-03-20 15:14:02
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @Description: 页面名称
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="titleVal"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="500"
    class="vis-dialog upload__"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      :rules="formRules"
      class="elFrom2__"
    >
      <!-- 上传 -->
      <span class="text">上传：</span>
      <el-form-item label="" prop="fileName">
        <div class="upload-box">
          <div
            class="mask-box"
            v-if="fileList.length >= uploadNum"
            @click="showInfo"
          ></div>

          <el-upload
            class="upload-demo"
            drag
            :limit="uploadNum"
            :action="actionUrl"
            :on-exceed="handleExceed"
            :before-upload="beforeAvatarUpload"
            :on-success="handleAvatarSuccess"
            :on-change="handleChange"
            :headers="customHeaders"
            ref="upload"
            :accept="
              Array.isArray(props.fileExt) && props.fileExt.length
                ? props.fileExt.map((e) => `.${e}`).join(',')
                : undefined
            "
          >
            <div class="el-upload__text">
              <p>
                <el-icon size="22" color="#29CCA0"><FolderAdd /></el-icon
                >点击或将文件拖拽到这里上传
              </p>
              <p>（限制上传{{ uploadNum }}个）文件</p>
              <p>仅支持jpg、png、pdf、word格式，文件小于50MB</p>
            </div>
          </el-upload>

          <div class="fileBoxs">
            <div
              class="fileBox__"
              v-for="(item, index) in fileList"
              :key="index"
            >
              <el-icon size="16" color="#29CCA0"><Link /></el-icon>
              <el-tooltip
                class="box-item"
                effect="dark"
                trigger="hover"
                :disabled="item.originalFileName.length > 8 ? false : true"
                :content="item.originalFileName"
                placement="top-start"
              >
                <span class="fileText">
                  {{
                    item.originalFileName && item.originalFileName.length > 8
                      ? item.originalFileName.slice(0, 6) +
                        '...' +
                        getFileExtension(item.originalFileName)
                      : item.originalFileName
                  }}
                </span>
              </el-tooltip>

              <el-icon size="16" color="#29CCA0" @click="handleDownload(item)"
                ><Download
              /></el-icon>
              &nbsp;&nbsp;
              <el-icon
                size="16"
                color="#29CCA0"
                @click="deleteClick(item, index)"
                ><Delete
              /></el-icon>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="submitForm()">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { getCookieValue } from '@/utils'
import { genFileId } from 'element-plus'
import type {
  UploadInstance,
  UploadProps,
  UploadRawFile,
  FormInstance,
  FormRules
} from 'element-plus'
import { loadingOpen, loadingClose } from '@/utils/common.ts'
import request from '@/utils/request'
const upload = ref<UploadInstance>()
const formRef = ref<FormInstance>()
const fileList = ref([])
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  fileExt: {
    type: Array,
    default: () => {
      return ['jpg', 'png', 'pdf', 'doc', 'docx', 'dotx', 'docm', 'dotm']
    }
  },
  stampFile: {
    type: [String, Array],
    default: () => {
      return []
    },
    required: false
  }
})

let dialogVisible = ref(false)
const formData = ref<Record<string, any>>({
  fileName: '' // 原件
})

const DEFAULT_PATH: string = import.meta.env.VITE_APP_DEFAULT_PATH
// const DEFAULT_PATH: string = 'http://************:8065' // 常波
// const DEFAULT_PATH: string = 'http://10.205.51.135:8065' //  朱昌源
let actionUrl =
  DEFAULT_PATH + '/pvom/operate/operatorsManage/uploadContractInfoFile'

watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val

    if (val) {
      if (props.stampFile && props.stampFile.length) {
        let arr = props.stampFile.map((item: any) => {
          return {
            fileName: item.split('@')[0],
            originalFileName: item.split('@')[1]
          }
        })
        fileList.value = arr
      } else {
        fileList.value = []
      }
    }
  },
  {
    deep: true
  }
)
let titleVal = ref('上传原件')
const uploadNum = ref(20)
// 上传文件
const showInfo = async () => {
  ElMessage({
    message: `限制上传${uploadNum.value}个,请先删除已上传的文件！`,
    type: 'warning'
  })
}
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload && upload.value && upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.size / 1024 / 1024 > 50) {
    ElMessage.error('单个文件不超过50MB!')
    return false
  }
  if (Array.isArray(props.fileExt) && props.fileExt.length) {
    const extension = props.fileExt.includes(
      rawFile.name.replace(/.+\./, '').toLowerCase()
    )
    if (!extension) {
      ElMessage({
        message: '不支持的文件类型',
        type: 'error'
      })
      return false
    }
  }
  return true
}
const handleAvatarSuccess: UploadProps['onSuccess'] = (response: any) => {
  if (response.code == 200) {
    fileList.value.push(response.data)
  }
}
const handleChange: UploadProps['onChange'] = (uploadFile) => {
  if (Array.isArray(props.fileExt) && props.fileExt.length) {
    const extension = props.fileExt.includes(
      uploadFile.name.replace(/.+\./, '').toLowerCase()
    )
    if (!extension) {
      return false
    }
  }
  if (uploadFile.status == 'ready') {
    loadingOpen()
  }
  if (uploadFile.status == 'success' || uploadFile.status == 'fail') {
    if (uploadFile.status == 'fail') {
      ElMessage({
        message: '上传发生错误！',
        type: 'error'
      })
    }
    if (uploadFile.status == 'success') {
      ElMessage({
        message: '上传成功！',
        type: 'success'
      })
    }
    loadingClose()
  }
}
const customHeaders = ref({
  Authorization: getCookieValue('tsydev_spic_identify_uat')
})
const getFileExtension = (filename: any) => {
  return /[.]/.exec(filename) ? /[^.]+$/.exec(filename)[0] : undefined
}
// 下载
const handleDownload = async (file: any) => {
  try {
    const { data } = await request({
      url: '/operate/operatorsManage/downloadFaultKnowledgeFile',
      headers: { 'Content-Type': 'text/plan' },
      responseType: 'blob',
      method: 'post',
      data: `${file.fileName}@${file.originalFileName}`,
      loading: true
    })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(data)
    link.download = file.originalFileName
    link.click()
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

const formRules = reactive<FormRules<Record<string, any>>>({})

// 点击确定
const emit = defineEmits(['closeDialog', 'updateList'])
const submitForm = async () => {
  if (fileList.value.length) {
    let arr = fileList.value.map((item: any) => {
      return `${item.fileName}@${item.originalFileName}`
    })
    emit('updateList', arr)
    setTimeout(() => {
      dialogClose()
    }, 10)
  } else {
    ElMessage({
      message: '未上传文件！',
      type: 'success'
    })
  }
}
const deleteClick = (item: any, index: any) => {
  fileList.value = fileList.value.filter((item_, index_) => {
    return index_ != index
  })
  ElMessage({
    message: '删除成功！',
    type: 'success'
  })
}
// 关闭弹窗
const dialogClose = () => {
  fileList.value = []
  formRef.value && formRef.value.resetFields()
  upload && upload.value && upload.value!.clearFiles()
  emit('closeDialog', false)
}
</script>
<style lang="scss" scoped>
.upload-box {
  width: 100%;
  height: 188px;
  position: relative;
  .upload-demo {
    width: 100%;
    height: 188px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    :deep(.el-upload-dragger) {
      height: 188px;
      background: rgba(41, 204, 160, 0.04);
    }
    .el-upload__text {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      p:first-child {
        font-size: 14px;
        display: flex;
        align-items: center;
        i {
          margin-right: 8px;
        }
      }
      p:nth-child(2) {
        font-size: 12px;
        color: #ccc;
      }
      p:nth-child(3) {
        font-size: 12px;
        color: #ccc;
      }
    }
  }
  .mask-box {
    width: 100%;
    height: 146px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100;
  }
}
</style>
<style lang="scss">
.el-dialog.upload__ .el-dialog__body {
  margin-bottom: 24px;
  overflow-y: auto;
  padding: 24px 24px 30px 24px !important;
  box-sizing: border-box;
}
.elFrom2__ {
  .text {
    font-size: 14px;
    margin-bottom: 8px;
    display: block;
  }
  .el-form-item {
    margin-bottom: 0 !important;
  }
  .el-form-item__content {
    .upload-box {
      .fileBoxs {
        width: 100%;
        margin-top: 198px;
      }
      .fileBox__ {
        width: 100%;
        display: flex;
        align-items: center;
        background: rgba(246, 248, 250, 1);
        padding: 0 16px;
        box-sizing: border-box;
        margin-bottom: 10px;
        .fileText {
          width: 62%;
          margin: 0 30px 0 8px;
        }
        .el-icon {
          cursor: pointer;
        }
      }
      .el-upload-list {
        display: none;
      }
    }
  }
}
</style>
