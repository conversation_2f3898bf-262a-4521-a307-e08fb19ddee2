<template>
  <el-dialog
    v-model="transferDialogVisible"
    :title="dialogTitle"
    class="vis-dialog pb100__"
    align-center
    :close-on-click-modal="false"
    width="900"
    @close="closeDialog"
  >
    <GroupUsers
      v-if="transferDialogVisible"
      ref="selectRef"
      :employee-id-arr="employeeIdArr"
      :is-multiple="isMultiple"
    ></GroupUsers>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSave">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import GroupUsers from './groupUsers.vue'
const transferDialogVisible = ref(false)
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  dialogTitle: {
    type: String,
    default: '添加人员',
    required: false
  },
  isMultiple: {
    type: Boolean,
    default: () => {
      return true
    }
  },
  employeeIdArr: {
    type: Array,
    default: () => {
      return []
    },
    required: false
  }
})

watch(
  () => props.isShow,
  async (val: boolean) => {
    transferDialogVisible.value = val
  },
  {
    deep: true
  }
)
// 点击取消
const emit = defineEmits(['closeDialog', 'uplateData'])
const closeDialog = () => {
  transferDialogVisible.value = false
  emit('closeDialog')
}
const selectRef = ref()
// 点击确认
const handleSave = () => {
  if (selectRef.value.selectedData.length === 0) {
    ElMessage({
      message: '请选择人员',
      type: 'error'
    })
    return
  }
  if (selectRef.value.selectedData.length > 10) {
    ElMessage({
      message: '人员最多可添加10个',
      type: 'error'
    })
    return
  }
  emit('uplateData', selectRef.value.selectedData || [])
  closeDialog()
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
