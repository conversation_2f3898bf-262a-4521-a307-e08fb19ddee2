<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`作废${
      type === '2' ? 'JSA票' : type === '4' ? '操作票' : '工作票'
    }`"
    class="vis-dialog ticket-dialog"
    align-center
    :before-close="closeDialog"
    :close-on-click-modal="false"
    width="446"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="100px"
      label-position="top"
      :rules="formRules"
    >
      <div class="group">
        <div class="title">
          【 {{ typeNum[type] }}作废审批-发起人】需填写补充信息
        </div>
        <el-form-item label="作废原因" prop="cancellation">
          <el-input
            v-model.trim="formData.cancellation"
            placeholder="请输入作废原因"
            :rows="5"
            type="textarea"
            maxlength="100"
            show-word-limit
          >
          </el-input>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button v-preventReClick="1000" type="primary" @click="handleSave"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { ticket as ticketApi } from '@/api/index.ts'
import { handle as handleAPI } from '@/api/index.ts'

const route = useRoute()
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  // 类型 1：工作票 2：JSA票 3:二种票 4:操作票
  type: {
    type: String,
    default: '1',
    required: false
  }
})
const typeNum: any = {
  ['1']: '电气一种工作票',
  ['2']: 'JSA票',
  ['3']: '电气二种工作票',
  ['4']: '操作票'
}
const dialogVisible = ref(false)
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
  },
  {
    deep: true
  }
)

let formData = ref({
  cancellation: ''
})
const formRef = ref<FormInstance>()
const formRules = reactive<FormRules<Record<string, any>>>({
  cancellation: [
    {
      required: true,
      message: '请输入作废原因',
      trigger: 'blur'
    }
  ]
})

// 点击取消
const emit = defineEmits(['closeDialog', 'uplateData'])
const closeDialog = () => {
  dialogVisible.value = false
  emit('closeDialog')
  resetForm()
}
// 点击确认
const handleSave = () => {
  formRef.value?.validate(async (valid: any) => {
    if (valid) {
      if (props.type == '2') {
        try {
          let { data } = await ticketApi.jsaLaunchProcess({
            businessId: Number(route.params.id),
            businessKey: 'JSA_OBSOLETE',
            ticketWorkVO: formData.value
          })
          if (data.code == 200) {
            ElMessage({
              type: 'success',
              message: '操作成功'
            })
            closeDialog()
            emit('uplateData')
          } else {
            ElMessage({
              type: 'warning',
              message: data.message
            })
          }
        } catch (e) {
          ElMessage({
            type: 'warning',
            message: '操作失败'
          })
        }
      } else if (props.type == '1') {
        try {
          const { data } = await ticketApi.revokeWorkTicket(
            {
              businessId: Number(route.params.id),
              ticketWorkVO: formData.value
            },
            true
          )

          if (data.code === '200') {
            ElMessage({
              message: `操作成功`,
              type: 'success'
            })
            closeDialog()
            emit('uplateData')
          } else {
            ElMessage({
              message: data.message,
              type: 'error'
            })
          }
        } catch (e: any) {
          ElMessage({
            message: `操作失败`,
            type: 'error'
          })
        }
      } else if (props.type == '3') {
        try {
          let { data } = await ticketApi.twoInvalidWorkApprove({
            businessId: Number(route.params.id),
            ticketWorkVO: formData.value
          })
          if (data.code == 200) {
            ElMessage({
              type: 'success',
              message: '操作成功'
            })
            closeDialog()
            emit('uplateData')
          } else {
            ElMessage({
              type: 'warning',
              message: data.message
            })
          }
        } catch (e) {
          ElMessage({
            type: 'warning',
            message: '操作失败'
          })
        }
      } else if (props.type == '4') {
        try {
          let { data } = await handleAPI.operateTicketVoidApprove({
            businessId: Number(route.params.opid) || '',
            ticketWorkVO: formData.value
          })
          if (data.code == 200) {
            ElMessage({
              type: 'success',
              message: '操作成功'
            })
            closeDialog()
            emit('uplateData')
          }
        } catch (e) {
          ElMessage({
            type: 'warning',
            message: '操作失败'
          })
        }
      }
    }
  })
}
// 重置
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
</script>

<style scoped src="../assets/dialog.scss"></style>
<style lang="scss">
.ticket-dialog .el-form .el-form-item--default .el-form-item__label {
  height: 22px !important;
  line-height: 22px !important;
}
</style>
