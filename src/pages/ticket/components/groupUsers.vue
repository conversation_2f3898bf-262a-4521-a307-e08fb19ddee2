<template>
  <searchForm
    :search-props="searchProps"
    :search-data="searchData"
    label-width="60px"
    @submit-emits="handleSearch"
  ></searchForm>
  <vis-table-pagination
    :loading="tableLoading"
    layout="total, sizes, prev, pager, next"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="searchData.pageSize"
    :current-page="searchData.pageNum"
    :columns="tableColumns"
    :total="tableData.total"
    :data="tableData.data"
    :show-overflow-tooltip="true"
    :is-multiple="isMultiple"
    background
    class="vis-table-pagination"
    row-key="username"
    :employee-id-arr="employeeIdArr"
    :multiple-selection="employeeIdArr"
    @handle-selection-change="handleSelectionChange"
    @handle-size-change="handleSizeChange"
    @handle-current-change="handleCurrentChange"
  >
  </vis-table-pagination>
</template>
<script setup lang="ts">
import visTablePagination from '@/components/table-pagination.vue'
import request from '@/utils/request'
defineProps({
  employeeIdArr: {
    type: Array,
    default: () => {
      return []
    },
    required: false
  },
  isMultiple: {
    type: Boolean,
    default: true
  }
})
const tableColumns = ref<any[]>([])
tableColumns.value = [
  {
    prop: 'selection',
    label: '选择',
    fixed: 'left',
    reserve: true,
    selectable: (row: { isSelect: string }) => {
      return row.isSelect !== '1'
    }
  },
  {
    prop: 'employeeName',
    label: '姓名'
  },
  {
    prop: 'username',
    label: '用户名'
  },
  {
    prop: 'employeePhone',
    label: '手机号'
  }
]

const selectedData = ref<Record<string, any>[]>([])
const handleSelectionChange = (rows: Record<string, any>[]) => {
  selectedData.value = [...rows]
}

let tableData = reactive<Record<string, any>>({
  total: 0,
  data: []
})

const searchProps = ref([
  {
    prop: 'keyWord',
    label: '关键字',
    placeholder: '请输入姓名/用户名/手机号',
    span: 16
  }
])
let searchData = ref({
  employeeName: '',
  username: '',
  employeePhone: '',
  keyWord: '',
  pageSize: 10,
  pageNum: 1
})
const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageSize = 10
  searchData.value.pageNum = 1
  getTableData()
}

let tableLoading = ref(false)
const getTableData = async () => {
  try {
    const { data } = await request({
      url: '/operate/uc/getUserInfoList',
      method: 'get',
      loading: [tableLoading],
      params: {
        pageSize: searchData.value.pageSize,
        pageNo: searchData.value.pageNum,
        keyWord: searchData.value.keyWord
      }
    })
    tableData.data = data?.data?.list || []
    tableData.total = data?.data?.total || 0
  } catch (e: any) {
    tableData.data = []
    tableData.total = 0
  }
}
const handleSizeChange = (params: Record<string, any>) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = (params: Record<string, any>) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

onMounted(async () => {
  getTableData()
})

defineExpose({
  selectedData,
  getTableData,
  tableData
})
</script>
