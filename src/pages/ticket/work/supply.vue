<template>
  <div class="work-supply">
    <div class="page-operate">
      <div class="operate-title">补充信息</div>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="122px"
      :rules="formRules"
    >
      <!-- 工作人员增加 -->
      <div class="info-base safe-content">
        <div class="safe-title">
          <div class="line"></div>
          工作人员增加（增加人员姓名、日期及时间）
        </div>
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="增加人员" prop="addMember">
              <el-input
                v-model.trim="formData.addMember"
                placeholder="请输入增加人员"
                maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="2">
            <el-form-item label="增加日期及时间" prop="addTime">
              <el-date-picker
                v-model="formData.addTime"
                type="datetime"
                placeholder="请选择增加日期及时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                :editable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="工作负责人" prop="gzfzr">
              <el-input
                :value="workLeader"
                placeholder="自动带出"
                maxlength="64"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 工作人员离去 -->
      <div class="info-base safe-content">
        <div class="safe-title">
          <div class="line"></div>
          工作人员离去（离去人员姓名、日期及时间）
        </div>
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="离去人员" prop="leaveMember">
              <el-input
                v-model.trim="formData.leaveMember"
                placeholder="请输入离去人员"
                maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="2">
            <el-form-item label="离去日期及时间" prop="leaveTime">
              <el-date-picker
                v-model="formData.leaveTime"
                type="datetime"
                placeholder="请选择离去日期及时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                :editable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="工作负责人" prop="gzfzr">
              <el-input
                :value="workLeader"
                placeholder="自动带出"
                maxlength="64"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 每日开工和收工时间 -->
      <div class="info-base safe-content safe-contentmb24">
        <div class="safe-title">
          <div class="line"></div>
          每日开工和收工时间（使用一天的工作票不必填写）
        </div>
        <table-info
          ref="tableInfoRef"
          :columns="tableColumns"
          :table-data="tableDataList"
          :maxlength="maxlength"
          @get-tabel-data="getTabelData"
        ></table-info>
      </div>

      <!-- 工作负责人交代事项 -->
      <!-- <div class="info-base safe-content">
        <div class="safe-title">
          <div class="line"></div>
          工作负责人交代事项
        </div>
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="交代事项" prop="instructions">
              <el-input
                v-model.trim="formData.instructions"
                placeholder="请输入交代事项"
                maxlength="64"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div> -->

      <!-- 备注 -->
      <div class="info-base safe-content">
        <div class="safe-title">
          <div class="line"></div>
          备注
        </div>
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="指定专责监护人" prop="guardian">
              <el-input
                v-model.trim="formData.guardian"
                placeholder="请输入指定专责监护人"
                maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="2">
            <el-form-item label="负责监护" prop="tutelage">
              <el-input
                v-model.trim="formData.tutelage"
                placeholder="请输入负责监护"
                maxlength="64"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="其他事项" prop="otherContext">
              <el-input
                v-model.trim="formData.otherContext"
                placeholder="请输入其他事项"
                maxlength="64"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <div class="page-footer">
      <el-button plain @click="onCancel">取消</el-button>
      <el-button type="primary" @click="onSubmit">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import tableInfo from '../components/tableRow.vue'
import { ticket as ticketAPI } from '@/api/index.ts'
import { dealTime } from '@/utils/index'

const router = useRouter()
const route = useRoute()

const maxlength = ref(64)

const ticketWorkId = route.query.id ? Number(route.query.id) : '' // 工作票id
let workLeader = ref() // 工作负责人
workLeader.value = route.query.workLeader
workLeader.value = workLeader.value ? workLeader.value.split('_')[0] : ''

onMounted(async () => {
  if (ticketWorkId) {
    getWorkTicketSupply()
  }
})

const formData = ref({
  addMember: '', // 增加人员
  addTime: '', // 增加日期及时间
  gzfzr: workLeader.value, // 工作负责人
  leaveMember: '', // 离去人员
  leaveTime: '', // 离去日期及时间
  instructions: '', // 交代事项
  guardian: '', // 指定专责监护人
  tutelage: '', // 负责监护
  otherContext: '' // 其他事项
})

const formRules = reactive<FormRules>({
  addMember: [{ required: true, message: '请输入增加人员', trigger: 'blur' }],
  addTime: [
    { required: true, message: '请选择增加日期及时间', trigger: 'blur' }
  ],
  gzfzr: [{ required: true, message: '请输入工作负责人', trigger: 'blur' }],
  leaveMember: [{ required: true, message: '请输入离去人员', trigger: 'blur' }],
  leaveTime: [
    { required: true, message: '请选择离去日期及时间', trigger: 'blur' }
  ],
  instructions: [
    { required: true, message: '请输入交代事项', trigger: 'blur' }
  ],
  guardian: [
    { required: true, message: '请输入指定专责监护人', trigger: 'blur' }
  ],
  tutelage: [{ required: true, message: '请输入负责监护', trigger: 'blur' }],
  otherContext: [{ required: true, message: '请输入其他事项', trigger: 'blur' }]
})

// 表格
const tableColumns = ref([
  {
    prop: 'endTime',
    label: '收工时间',
    slotName: 'endTime',
    minWidth: 150,
    placeholder: '请选择收工时间',
    type: 'datetime',
    format: 'YYYY-MM-DD HH:mm',
    valueFormat: 'YYYY-MM-DD HH:mm'
  },
  {
    prop: 'endWorkLeader',
    label: '工作负责人',
    slotName: 'endWorkLeader',
    minWidth: 100,
    placeholder: '请输入工作负责人'
  },
  {
    prop: 'endLicensor',
    label: '工作许可人',
    slotName: 'endLicensor',
    minWidth: 100,
    placeholder: '请输入工作许可人'
  },
  {
    prop: 'startTime',
    label: '开工时间',
    slotName: 'startTime',
    minWidth: 150,
    placeholder: '请选择开工时间',
    type: 'datetime',
    format: 'YYYY-MM-DD HH:mm',
    valueFormat: 'YYYY-MM-DD HH:mm'
  },
  {
    prop: 'startWorkLeader',
    label: '工作负责人',
    slotName: 'startWorkLeader',
    minWidth: 100,
    placeholder: '请输入工作负责人'
  },
  {
    prop: 'startLicensor',
    label: '工作许可人',
    slotName: 'startLicensor',
    minWidth: 100,
    placeholder: '请输入工作许可人'
  },

  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 100,
    fixed: 'right'
  }
])

const tableDataList = ref<any[]>([])
// 获取补充信息
const getWorkTicketSupply = async () => {
  try {
    const { data } = await ticketAPI.getWorkTicketSupply(
      {
        id: ticketWorkId
      },
      true
    )
    if (data.code === '200') {
      formData.value = { ...formData.value, ...data.data }
      if (formData.value.addTime) {
        formData.value.addTime = dealTime(formData.value.addTime)
        formData.value.leaveTime = dealTime(formData.value.leaveTime)
      }
      tableDataList.value = data.data?.workList || []
      if (tableDataList.value?.length) {
        tableDataList.value = tableDataList.value.map((item: any) => {
          if (item.startTime) {
            item.startTime = dealTime(item.startTime)
          }
          if (item.endTime) {
            item.endTime = dealTime(item.endTime)
          }
          return item
        })
      }
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

// 开工和收工时间
const tableInfoRef = ref<any>(null)
const tableInfoData = ref<any>([])
defineEmits(['submitTable'])
const getTabelData = (val: any) => {
  tableInfoData.value = val.data
}

const formRef = ref<FormInstance>()
const onSubmit = async () => {
  formRef.value?.validate(async (valid: any) => {
    if (valid) {
      await tableInfoRef.value.submitTable(1)
      try {
        let workList = tableInfoData.value
        if (workList?.length) {
          workList = workList.map((item: any) => {
            if (item.startTime) {
              item.startTime = item.startTime + ':00'
            }
            if (item.endTime) {
              item.endTime = item.endTime + ':00'
            }

            return item
          })
        }
        let params = {
          ...formData.value,
          ticketWorkId: ticketWorkId || '',
          workList: workList
        }
        params.addTime = params.addTime + ':00'
        params.leaveTime = params.leaveTime + ':00'

        const { data } = await ticketAPI.addWorkTicketSupply(params, true)
        if (data.code === '200') {
          ElMessage({
            message: `操作成功!`,
            type: 'success'
          })
          onCancel()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}
const onCancel = () => {
  router.go(-1)
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped src="./assets/work.scss"></style>
