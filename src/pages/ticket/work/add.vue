<template>
  <div class="work-add">
    <div class="page-operate">
      <div class="operate-title">新建工作票</div>
    </div>

    <div>
      <a1
        ref="a1Ref"
        :role-list1="roleList1"
        :role-list2="roleList2"
        :role-list3="roleList3"
        :role-list4="roleList4"
        :work-ticket-data="workTicketData"
        @get-a1-data="getA1Data"
      >
      </a1>
      <a2
        ref="a2Ref"
        :role-list5="roleList5"
        :work-ticket-data="workTicketData"
        @get-a2-data="getA2Data"
      >
      </a2>
      <a3
        ref="a3Ref"
        :work-ticket-data="workTicketData"
        @get-a3-data="getA3Data"
      >
      </a3>
      <!-- <a4
        ref="a4Ref"
        :work-ticket-data="workTicketData"
        @get-a4-data="getA4Data"
      >
      </a4> -->

      <jsaSupply
        ref="a5Ref"
        :work-ticket-data="workTicketData"
        @get-a5-data="getA5Data"
      ></jsaSupply>
    </div>

    <div class="page-footer">
      <el-button plain @click="onCancel">返回</el-button>
      <el-button v-preventReClick="1000" type="primary" @click="onSumbit(1)">
        保存草稿
      </el-button>
      <el-button v-preventReClick="1000" type="primary" @click="onSumbit(2)">
        提交签发审批
      </el-button>
      <el-button
        v-if="route.query && route.query.type == 'edit'"
        v-preventReClick="1000"
        type="primary"
        @click="revokeTicket"
      >
        作废工作票
      </el-button>
    </div>
  </div>
  <!-- 作废工作票 -->
  <revokeTicketDialog
    :is-show="showRevokeTicket"
    type="1"
    @uplate-data="getRevokeTicketData"
    @close-dialog="showRevokeTicket = false"
  ></revokeTicketDialog>
</template>

<script setup lang="ts">
import useRoles from '@/hooks/useRoles'
import a1 from './components/add/aAddMain.vue'
import a2 from './components/add/bAddBase.vue'
import a3 from './components/add/cAddSafe.vue'
// import a4 from './components/add/dAddConnect.vue'
import jsaSupply from './components/add/jsaSupply.vue'
import revokeTicketDialog from '../components/revokeTicketDialog.vue'
import { ticket as ticketAPI } from '@/api/index.ts'

const route = useRoute()
const router = useRouter()
const { roleList1, roleList2, roleList3, roleList4, roleList5 } = useRoles()

onMounted(async () => {
  if (
    route.query &&
    (route.query.type == 'copy' || route.query.type == 'edit')
  ) {
    getWorkTicketDetail()
  }
})

let workTicketData = ref<Obj>({})
// 获取工作票详情
const getWorkTicketDetail = async () => {
  try {
    let { data } = await ticketAPI.getWorkTicketDetail(
      { id: route.params.id },
      true
    )
    if (route.query && route.query.type == 'copy') {
      // - 复制时不填充“关联检修工单”、“关联操作票”、“工作票编号”、“JSA票编号”字段
      data.data.id = null
      data.data.workOrderNo = ''
      data.data.operateTickets = []
      data.data.jsaTicketNo = ''
      data.data.ticketWorkNo = '保存后自动生成'
    }
    workTicketData.value = data.data
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

// 第一部分
const a1Data = ref({})
const a1Valid = ref(false)
const a1Ref = ref<any>(null)
const getA1Data = (val: any) => {
  a1Valid.value = val.valid
  a1Data.value = val.data
  workTicketData.value.ticketWorkSafeSteps =
    val.templateContent?.modelTemplateDto?.safeStepDtoList || []
  workTicketData.value.homeworkType =
    val.templateContent?.modelTemplateDto?.jsaTicketDto?.homeworkType || ''
  workTicketData.value.homeworkRisk =
    val.templateContent?.modelTemplateDto?.jsaTicketDto?.homeworkRisk || ''
  workTicketData.value.ticketWorkProcessRiskList =
    val.templateContent?.modelTemplateDto?.jsaTicketDto?.processRiskDtoList ||
    []
  a3Ref?.value?.update()
  a5Ref?.value?.update()
}
// 第二部分
const a2Data = ref({})
const a2Valid = ref(false)
const a2Ref = ref<any>(null)
const getA2Data = (val: any) => {
  a2Valid.value = val.valid
  a2Data.value = val.data
}
// 第三部分
const a3Data = ref({})
const a3Valid = ref(false)
const a3Ref = ref<any>(null)
const getA3Data = (val: any) => {
  a3Valid.value = val.valid
  a3Data.value = val.data
}
// 第四部分
// const a4Data = ref({})
// const a4Ref = ref<any>(null)
// const getA4Data = (val: any) => {
//   a4Data.value = val.data
// }
// 第五部分- jsa补充信息
const a5Data: any = ref({})
const a5Valid = ref(false)
const a5Ref = ref<any>(null)
const getA5Data = (val: any) => {
  a5Valid.value = val.valid
  a5Data.value = val.data
}

// 提交签发审批
// - 提交签发审批时需要校验：工作负责人、工作票签发人、工作许可人皆不相同，否则进行校验提示：三种人不可重复，请重新填写！
// - 如果关联JSA票，则需要校验JSA票的状态为“生效中”“已终结”，如果不是则提示：请先签发JSA票后再尝试！
// 保存草稿1 签发审批2
const onSumbit = async (type: number) => {
  await a1Ref.value?.submitForm(type)
  await a2Ref.value?.submitForm(type)
  await a3Ref.value?.submitForm(type)
  // await a4Ref.value?.submitForm(type)
  await a5Ref.value?.submitForm(type)

  if (type === 2) {
    if (!a1Valid.value) {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return
    }
    if (!a2Valid.value) {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return
    }
    if (!a3Valid.value) {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return
    }
    if (!a5Valid.value) {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return
    }
  }

  try {
    let draftId = null
    if (route.query && route.query.type == 'edit') {
      draftId = route.params.id
    }

    const params: any = {
      id: draftId,
      status: 1,
      ...a1Data.value,
      ...a2Data.value,
      ...a3Data.value,
      // ...a4Data.value,
      ...a5Data.value
    }

    // 校验
    if (route.query && route.query.type == 'copy') {
      params.id = ''
    }
    // 校验 工作负责人 workLeader、工作票签发人 signer、工作许可人 licensor 皆不相同
    if (
      (params.workLeader &&
        params.signer &&
        params.workLeader == params.signer) ||
      (params.workLeader &&
        params.licensor &&
        params.workLeader == params.licensor) ||
      (params.signer && params.licensor && params.signer == params.licensor)
    ) {
      ElMessage({
        message: '工作负责人、工作票签发人、工作许可人不可重复，请重新填写！',
        type: 'warning'
      })
      return
    }
    const { data } = await ticketAPI.addWorkTicket(
      {
        ...params,
        operateTickets: params?.operateTickets
          ? params?.operateTickets?.join(',')
          : ''
      },
      true
    )
    if (data.code === '200') {
      if (type === 2) {
        const res = await ticketAPI.initIssuance(
          {
            businessId: data.data
          },
          true
        )
        if (res.data.code === '200') {
          ElMessage({
            message: `操作成功!`,
            type: 'success'
          })
          router.push('/ticket/work')
        } else {
          ElMessage({
            message: res.data.message,
            type: 'error'
          })
        }
      } else if (type === 1) {
        ElMessage({
          message: `操作成功!`,
          type: 'success'
        })
        router.push('/ticket/work')
      }
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}

// 作废工作票
const showRevokeTicket = ref<boolean>(false)
const revokeTicket = () => {
  showRevokeTicket.value = true
}
const getRevokeTicketData = () => {
  router.push('/ticket/work')
}

const onCancel = () => {
  router.go(-1)
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped src="./assets/work.scss"></style>
