<template>
  <div class="work-detail">
    <div class="page-operate">
      <div class="operate-title">
        查看详情
        <el-tag v-if="workTicketData?.status == 1" class="tag1 tag">
          草稿
        </el-tag>
        <el-tag v-if="workTicketData?.status == 2" class="tag2 tag">
          生效中
        </el-tag>
        <el-tag v-if="workTicketData?.status == 3" class="tag3 tag">
          已许可
        </el-tag>
        <el-tag v-if="workTicketData?.status == 4" class="tag4 tag">
          已终结
        </el-tag>
        <el-tag v-if="workTicketData?.status == 5" class="tag5 tag">
          已作废
        </el-tag>
      </div>
    </div>
    <div class="btn-list">
      <!-- 按钮禁用 class="disabled" disabled -->
      <el-button
        v-if="showSupplyBtn"
        v-preventReClick="1000"
        type="primary"
        @click="toSupply"
      >
        补充信息
      </el-button>
      <el-button
        v-if="showFinishBtn"
        v-preventReClick="1000"
        type="primary"
        @click="finishTicket"
      >
        终结工作票
      </el-button>
      <el-button
        v-if="showChangeBtn"
        v-preventReClick="1000"
        type="primary"
        @click="changePerson"
      >
        变更工作负责人
      </el-button>
      <el-button
        v-if="showDelayBtn"
        v-preventReClick="1000"
        type="primary"
        @click="delayTicket"
      >
        工作票延期
      </el-button>
      <el-button
        v-if="showRevokeBtn"
        v-preventReClick="1000"
        type="primary"
        @click="revokeTicket"
      >
        作废工作票
      </el-button>
      <el-button
        v-if="showEquipBtn"
        v-preventReClick="1000"
        type="primary"
        @click="equipOperate"
      >
        设备试运行
      </el-button>
      <el-button
        v-if="showRecoveBtn"
        v-preventReClick="1000"
        type="primary"
        @click="recoveWork"
      >
        恢复工作
      </el-button>
      <el-button
        v-if="showExportBtn"
        v-preventReClick="1000"
        type="primary"
        @click="exportTicket"
      >
        导出工作票
      </el-button>
      <el-button
        v-if="showUploadBtn"
        v-preventReClick="1000"
        type="primary"
        @click="uploadBtn"
      >
        上传原件
      </el-button>
    </div>

    <d1 :work-ticket-data="workTicketData"></d1>
    <d2 :work-ticket-data="workTicketData"></d2>
    <d3 :work-ticket-data="workTicketData"></d3>
    <d4 :work-ticket-data="workTicketData"></d4>
    <d5 :work-ticket-data="workTicketData"></d5>
    <d6 :work-ticket-data="workTicketData"></d6>
    <d7 :work-ticket-data="workTicketData"></d7>
    <!-- JSA票补充填写信息 -->
    <div class="page-footer">
      <el-button type="primary" @click="onCancel">返回</el-button>
    </div>
  </div>

  <!-- 终结工作票 -->
  <finishTicketDialog
    :is-show="showFinishTicket"
    @uplate-data="getWorkTicketDetail"
    @close-dialog="showFinishTicket = false"
  ></finishTicketDialog>

  <!-- 变更工作负责人 -->
  <changePersonDialog
    :is-show="showChangePerson"
    :role-list5="roleList5"
    @uplate-data="getWorkTicketDetail"
    @close-dialog="showChangePerson = false"
  ></changePersonDialog>

  <!-- 工作票延期 -->
  <delayTicketDialog
    :is-show="showDelayTicket"
    @uplate-data="getWorkTicketDetail"
    @close-dialog="showDelayTicket = false"
  ></delayTicketDialog>

  <!-- 作废工作票 -->
  <revokeTicketDialog
    :is-show="showRevokeTicket"
    type="1"
    @uplate-data="getWorkTicketDetail"
    @close-dialog="showRevokeTicket = false"
  ></revokeTicketDialog>

  <!-- 上传原件 -->
  <upload
    :is-show="showUpload"
    :stamp-file="stampFileArr"
    @close-dialog="showUpload = false"
    @update-list="updateList"
  ></upload>
</template>

<script setup lang="ts">
import useRoles from '@/hooks/useRoles'
import d1 from './components/detail/aDetailMain.vue'
import d2 from './components/detail/bDetailBase.vue'
import d3 from './components/detail/cDetailSafe.vue'
import d4 from './components/detail/dDetailStatus.vue'
import d5 from './components/detail/eDetailOther.vue'
import d6 from './components/detail/fDetailConnect.vue'
import d7 from './components/detail/jsaSupply.vue'
import finishTicketDialog from './components/finishTicketDialog.vue'
import changePersonDialog from './components/changePersonDialog.vue'
import delayTicketDialog from './components/delayTicketDialog.vue'
import revokeTicketDialog from '../components/revokeTicketDialog.vue'
import upload from '../components/upload.vue'
import { ticket as ticketAPI } from '@/api/index.ts'

const router = useRouter()
const route = useRoute()
const { roleList5 } = useRoles()

onMounted(async () => {
  await getWorkTicketDetail()
})
let workTicketData: any = ref()

const stampFileArr = ref([])
// 获取工作票详情
const getWorkTicketDetail = async () => {
  try {
    let { data } = await ticketAPI.getWorkTicketDetail(
      { id: route.params.id },
      true
    )
    workTicketData.value = data.data
    stampFileArr.value = data.data.fileUrl ? data.data.fileUrl.split(',') : []
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

// 补充信息
const toSupply = () => {
  if (route.fullPath.indexOf('/approval/workbench/detail') >= 0) {
    router.push({
      name: 'TicketWorkDetailSupplyApproval',
      query: {
        id: workTicketData.value?.id || '11',
        workLeader: workTicketData.value?.workLeader || 'xx工作负责人'
      }
    })
  } else {
    router.push({
      name: 'TicketWorkDetailSupply',
      query: {
        id: workTicketData.value?.id || '11',
        workLeader: workTicketData.value?.workLeader || 'xx工作负责人'
      }
    })
  }
}

// 终结工作票
const showFinishTicket = ref<boolean>(false)
const finishTicket = () => {
  showFinishTicket.value = true
}

// 变更工作负责人
const showChangePerson = ref<boolean>(false)
const changePerson = () => {
  showChangePerson.value = true
}

// 工作票延期
const showDelayTicket = ref<boolean>(false)
const delayTicket = () => {
  showDelayTicket.value = true
}

// 作废工作票
const showRevokeTicket = ref<boolean>(false)
const revokeTicket = () => {
  showRevokeTicket.value = true
}

// 设备试运行
const equipOperate = () => {
  ElMessageBox.confirm('确定设备试运行审批?', '审批', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      equipFn()
    })
    .catch(() => {
      console.log('设备试运行审批')
    })
}
const equipFn = async () => {
  try {
    let { data } = await ticketAPI.equipOperateWorkTicket({
      businessId: Number(route.params.id)
    })
    if (data.code == 200) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      getWorkTicketDetail()
    } else {
      ElMessage({
        type: 'error',
        message: data.message
      })
    }
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
    console.log(e)
  }
}

// 恢复工作
const recoveWork = () => {
  ElMessageBox.confirm('确定恢复工作?', '恢复工作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      recoveFn()
    })
    .catch(() => {})
}
const recoveFn = async () => {
  try {
    let { data } = await ticketAPI.recoveWorkTicket({
      businessId: Number(route.params.id)
    })
    if (data.code == 200) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      getWorkTicketDetail()
    } else {
      ElMessage({
        type: 'error',
        message: data.message
      })
    }
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

// 导出工作票
const exportTicket = async () => {
  try {
    let res = await ticketAPI.downloadWorkTicket({
      id: String(route.params.id) || ''
    })
    let str = res.headers['content-disposition']
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(res.data)
    link.download =
      '电气第一种工作票.' + str.split('.')[str.split('.').length - 1]
    link.click()
  } catch (e) {
    console.log('导出失败！')
  }
}

// 上传原件
const showUpload = ref(false)
const uploadBtn = () => {
  stampFileArr.value = workTicketData.value.fileUrl
    ? workTicketData.value.fileUrl.split(',')
    : []
  showUpload.value = true
}

const updateList = async (val: any) => {
  try {
    let { data } = await ticketAPI.uploadWorkTicketFiles({
      id: Number(route.params.id),
      fileUrl: val.join(',')
    })
    if (data.code == 200) {
      getWorkTicketDetail()
    }
  } catch (e) {}
}

// 按钮显示规则：
// 1 草稿状态： 保存草稿、提交签发审批、作废工作票   草稿&签发审批中，不展示任何按钮
// 2 生效中：工作负责人变更、工作票延期、作废工作票
// 3 已许可：工作负责人变更、工作票延期、终结工作票、设备试运行、恢复工作、作废工作票、补充信息
// 4 已终结：导出工作票、上传工作票原件
// 5 已作废：导出工作票、上传工作票原件
const showSupplyBtn = computed(() => {
  return workTicketData.value?.status == 3
})
const showFinishBtn = computed(() => {
  return workTicketData.value?.status == 3
})
const showChangeBtn = computed(() => {
  return workTicketData.value?.status == 2 || workTicketData.value?.status == 3
})
const showDelayBtn = computed(() => {
  return workTicketData.value?.status == 2 || workTicketData.value?.status == 3
})
const showRevokeBtn = computed(() => {
  return (
    // workTicketData.value?.status == 1 ||
    workTicketData.value?.status == 2 || workTicketData.value?.status == 3
  )
})
const showEquipBtn = computed(() => {
  return workTicketData.value?.status == 3
})
const showRecoveBtn = computed(() => {
  return workTicketData.value?.status == 3
})
const showExportBtn = computed(() => {
  if (workTicketData.value?.status == 4) {
    return true
  }
  // 草稿状态下作废的工作票，可以不用导出
  if (workTicketData.value?.status == 5) {
    if (workTicketData.value?.workIssuanceApprovalList?.length >= 6) {
      // 如果有签发审批流 显示导出按钮
      return true
    } else {
      // 否则，不显示按钮
      return false
    }
  }
  // return workTicketData.value?.status == 4 || workTicketData.value?.status == 5
})
const showUploadBtn = computed(() => {
  return workTicketData.value?.status == 4 || workTicketData.value?.status == 5
})

// const showSupplyBtn = computed(() => {
//   return true
// })
// const showFinishBtn = computed(() => {
//   return true
// })
// const showChangeBtn = computed(() => {
//   return true
// })
// const showDelayBtn = computed(() => {
//   return true
// })
// const showRevokeBtn = computed(() => {
//   return true
// })
// const showEquipBtn = computed(() => {
//   return true
// })
// const showRecoveBtn = computed(() => {
//   return true
// })
// const showExportBtn = computed(() => {
//   return true
// })
// const showUploadBtn = computed(() => {
//   return true
// })

// 返回
const onCancel = () => {
  router.go(-1)
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped src="./assets/work.scss"></style>
