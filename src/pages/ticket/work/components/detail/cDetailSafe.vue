<template>
  <el-form class="info-base safe-content">
    <div class="safe-title">
      <div class="line"></div>
      安全措施
    </div>
    <div v-for="item in safeListArr" :key="item.safeType">
      <div class="breif">
        <el-text class="mx-1" type="danger">*</el-text>{{ item.title }}
      </div>
      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :columns="safeColumns"
        :data="item.tableList"
        background
        border
        :height="'auto'"
        :show-overflow-tooltip="true"
        class="vis-table-pagination safe-table1"
        highlight-current-row
      >
        <template #files="{ row }">
          <template v-if="row.safeStepContext == '无'">无</template>
          <template v-else>
            <SpicUpload v-model="row.files" type="image" :limit="5" disabled />
          </template>
        </template>
        <template #execution="{ row }">
          <span v-if="row.safeStepContext == '无'">无</span>
          <span v-else-if="row.execution == 1" class="fhc"> 已执行 </span>
          <span v-else-if="row.execution == 2"> 未执行 </span>
          <div v-if="row.safeStepContext != '无'">
            {{ row.exposition || '' }}
          </div>
        </template>
        <template #operator="{ row }">
          <span v-if="row.safeStepContext == '无'">无</span>
          <template v-else-if="row.execution == 1">
            {{ row.operator ? row.operator.split('_')[0] : '--' }}
          </template>
        </template>
        <template #recheckExecution="{ row }">
          <span v-if="row.safeStepContext == '无'">无</span>
          <span v-else-if="row.recheckExecution == 1" class="fhc">
            已复核
          </span>
          <span v-else-if="row.recheckExecution == 2"> 未复核 </span>
        </template>
      </vis-table-pagination>
    </div>
  </el-form>
</template>
<script setup lang="ts">
import SpicUpload from '@/components/spic-upload'
import visTablePagination from '@/components/table-pagination.vue'
const props = defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  }
})

onMounted(() => {})
watch(
  () => props.workTicketData,
  (val: any) => {
    if (val) {
      initSafeData()
    }
  }
)
const safeColumns = [
  { prop: 'index', label: '序号', minWidth: 100 },
  { prop: 'safeStepContext', label: '安全措施', minWidth: 400 },
  {
    prop: 'execution',
    label: '措施执行情况',
    minWidth: 110,
    slotName: 'execution'
  },
  {
    prop: 'files',
    label: '证明照片',
    slotName: 'files',
    minWidth: 193
  },
  { prop: 'operator', label: '执行人', minWidth: 80, slotName: 'operator' },

  {
    prop: 'recheckExecution',
    label: '措施复核情况',
    minWidth: 110,
    slotName: 'recheckExecution'
  }
]

let safeListArr = ref([
  {
    safeType: 1,
    title:
      '应拉开的断路器、隔离开关，应取下的熔断器。（包括填写前已断开、取下的，注明编号）',
    tableList: []
  },
  {
    safeType: 2,
    title: '应解除的继电保护连接片等（包括填写前已解除的，注明编号）',
    tableList: []
  },
  {
    safeType: 3,
    title: '应装接地线、应合接地刀闸（注明确实地点、名称及接地线编号）',
    tableList: []
  },
  {
    safeType: 4,
    title: '应设遮拦、应挂标识牌及防止二次回路误碰等措施',
    tableList: []
  },
  {
    safeType: 5,
    title: '应取下下列开关的合闸、操作、信号保险',
    tableList: []
  }
  // {
  //   safeType: 6,
  //   title: '热机部分采取的安全措施',
  //   tableList: []
  // }
])

const initSafeData = () => {
  let safeList = props.workTicketData?.ticketWorkSafeSteps
  if (safeList?.length) {
    safeList.forEach((item: any) => {
      item.files = item.imageUrl ? item.imageUrl.split(',') : []
      if (item.safeType <= 5) {
        safeListArr.value[item.safeType - 1].tableList.push(item)
      }
    })
  }
}
</script>
<style scoped src="../../assets/work.scss"></style>
<style lang="scss">
.safe-table1 {
  .el-table_1_column_4 {
    .cell {
      padding-right: 0 !important;
    }
  }
}
</style>
