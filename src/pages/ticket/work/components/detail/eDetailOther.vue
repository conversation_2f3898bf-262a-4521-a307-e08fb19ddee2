<template>
  <el-form class="info-base priviate-label">
    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text>工作负责人变动
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="原工作负责人">
          {{
            bgTimeObj.time3
              ? workTicketData?.beforeWorkLeader.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="变更后负责人">
          {{
            bgTimeObj.time3
              ? workTicketData?.afterWorkLeader.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="变动时间">
          {{ bgTimeObj.time3 ? dealTime(bgTimeObj.time3) : '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作票签发人">
          <template v-if="bgTimeObj.time3">
            {{
              workTicketData?.signer.split('_')[0] +
              ' ' +
              dealTime(bgTimeObj.time2)
            }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作许可人签名">
          <template v-if="bgTimeObj.time3">
            {{
              workTicketData?.licensor.split('_')[0] +
              ' ' +
              dealTime(bgTimeObj.time3)
            }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >工作人员增加（增加人员姓名、日期及时间）
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="增加人员">
          <template v-if="otherData?.addMember">
            {{ otherData?.addMember }}
            {{ dealTime(otherData.addTime) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人">
          <template v-if="otherData?.addMember">
            {{ workTicketData?.workLeader.split('_')[0] }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >工作人员离去（离去人员姓名、日期及时间）
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="离去人员">
          <template v-if="otherData?.leaveMember">
            {{ otherData?.leaveMember }}
            {{ dealTime(otherData.leaveTime) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人">
          <template v-if="otherData?.leaveMember">
            {{ workTicketData?.workLeader.split('_')[0] }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text>工作票延期
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="有效期延长到">
          {{ yqTimeObj.time4 ? dealTime(workTicketData.delayTime) : '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="值长签名">
          <template v-if="yqTimeObj.time4">
            {{ workTicketData?.shiftChiefOperator.split('_')[0] }}
            {{ dealTime(yqTimeObj.time2) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作许可人">
          <template v-if="yqTimeObj.time4">
            {{ workTicketData?.licensor.split('_')[0] }}
            {{ dealTime(yqTimeObj.time3) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人">
          <template v-if="yqTimeObj.time4">
            <!-- {{ workTicketData?.workLeader.split('_')[0] }} -->
            {{ computedWorkerLeader(yqTimeObj.time4) }}
            {{ dealTime(yqTimeObj.time4) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >每日开工和收工时间（使用一天的工作票不必填写）
    </div>
    <vis-table-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :columns="workTimeColumns"
      :data="workTimeList"
      background
      border
      :height="'auto'"
      :show-overflow-tooltip="true"
      class="vis-table-pagination safe-table1"
      highlight-current-row
    >
      <template #startTime="{ row }">
        {{ row.startTime ? dealTime(row.startTime) : '--' }}
      </template>
      <template #endTime="{ row }">
        {{ row.endTime ? dealTime(row.endTime) : '--' }}
      </template>
    </vis-table-pagination>

    <div class="breif">
      <el-text class="mx-1" type="danger">*</el-text
      >检修设备试运，填写试运行单，工作票交回，所列安全措施已拆除，可以试运行
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="工作许可人">
          <template v-if="syxTimeObj.time3">
            {{ workTicketData?.licensor.split('_')[0] }}
            <!-- {{ dealTime(syxTimeObj.time2) }} -->
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人">
          <template v-if="syxTimeObj.time3">
            <!-- {{ workTicketData?.workLeader.split('_')[0] }} -->
            {{ computedWorkerLeader(syxTimeObj.time3) }}
            {{ dealTime(syxTimeObj.time3) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >检修设备试运后，填写恢复工作联系单，工作票所列安全措施已全部执行，可以重新工作
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="工作许可人">
          <template v-if="hfTimeObj.time3">
            {{ workTicketData?.licensor.split('_')[0] }}
            <!-- {{ dealTime(hfTimeObj.time2) }} -->
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人">
          <template v-if="hfTimeObj.time3">
            <!-- {{ workTicketData?.workLeader.split('_')[0] }} -->
            {{ computedWorkerLeader(hfTimeObj.time3) }}
            {{ dealTime(hfTimeObj.time3) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>

    <!-- <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text>工作负责人交代事项
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="交代事项">
          {{ otherData?.instructions || '--' }}
        </el-form-item>
      </el-col>
    </el-row> -->

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >工作票终结（全部工作已结束，设备及安全措施已恢复至开工前状态，现场已清理完毕，工作班人员已全部撤离。临时遮拦、标识牌已拆除，常设遮拦已恢复）
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="工作结束时间">
          {{ zjTimeObj.time4 ? dealTime(workTicketData?.finalTime) : '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作许可人签名">
          <template v-if="zjTimeObj.time4">
            {{ workTicketData?.licensor.split('_')[0] }}
            {{ dealTime(zjTimeObj.time2) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人签名">
          <template v-if="zjTimeObj.time4">
            {{ workTicketData?.workLeader.split('_')[0] }}
            {{ dealTime(zjTimeObj.time3) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12"> </el-col>
      <el-col :span="12">
        <el-form-item label="未拆除或未拉开的接地线编号">
          {{ zjTimeObj.time4 ? workTicketData?.noOne : '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="共多少组">
          {{ zjTimeObj.time4 ? workTicketData?.numOne : '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="未拆除或未拉开的接地刀闸编号">
          {{ zjTimeObj.time4 ? workTicketData?.noTwo : '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="共多少组">
          {{ zjTimeObj.time4 ? workTicketData?.numTwo : '--' }}
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text>备注
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="指定专责监护人">
          {{ otherData?.guardian || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="负责监护">
          {{ otherData?.tutelage || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="其他事项">
          {{ otherData?.otherContext || '--' }}
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text>工作票盖章文件
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="24">
        <el-form-item
          label="文件"
          :class="otherData.files?.length ? 'mb16' : 'mb24'"
        >
          <SpicUpload
            v-if="otherData.files?.length"
            v-model="otherData.files"
            :file-size="50"
            :file-ext="fileExt"
            type="file"
            :limit="20"
            :upload-display="true"
            disabled
          />
          <span v-else> -- </span>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import SpicUpload from '@/components/spic-upload'
import visTablePagination from '@/components/table-pagination.vue'
import { dealTime } from '@/utils/index'
const props = defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  }
})

let otherData: any = ref({}) // 其他事项
let workTimeList = ref<any[]>([]) // 	每日开工和收工时间集合
// 终结审批流程的时间处理
const zjTimeObj = ref<any>({
  time2: '',
  time3: '',
  time4: ''
})
// 作废 暂不需要在详情页面显示
// 延期审批流程的时间处理 workDelayedApprovalList
const yqTimeObj = ref<any>({
  time2: '',
  time3: '',
  time4: ''
})
// 工作负责人变更 审批流程的时间处理
const bgTimeObj = ref<any>({
  time3: '',
  time2: ''
})
// 试运行审批流程的时间处理
const syxTimeObj = ref<any>({
  time2: '',
  time3: ''
})
// 恢复审批流程的时间处理
const hfTimeObj = ref<any>({
  time2: '',
  time3: ''
})

watch(
  () => props.workTicketData,
  (val: any) => {
    workTimeList.value = val.ticketOtherStepWorkTimes || []
    otherData.value = val.ticketOtherStep || {}
    otherData.value.files = props.workTicketData?.fileUrl
      ? props.workTicketData?.fileUrl.split(',')
      : []
    if (val.workTerminationOfApprovalList?.length) {
      val.workTerminationOfApprovalList.forEach((item: any) => {
        if (item.timeType === 2) {
          zjTimeObj.value.time2 = item.changeTime
        } else if (item.timeType === 3) {
          zjTimeObj.value.time3 = item.changeTime
        } else if (item.timeType === 4) {
          zjTimeObj.value.time4 = item.changeTime
        }
      })
    }
    if (val.workDelayedApprovalList?.length) {
      val.workDelayedApprovalList.forEach((item: any) => {
        if (item.timeType === 2) {
          yqTimeObj.value.time2 = item.changeTime
        } else if (item.timeType === 3) {
          yqTimeObj.value.time3 = item.changeTime
        } else if (item.timeType === 4) {
          yqTimeObj.value.time4 = item.changeTime
        }
      })
    }
    if (val.workResponsiblePersonChangeApprovalList?.length) {
      val.workResponsiblePersonChangeApprovalList.forEach((item: any) => {
        if (item.timeType === 3) {
          bgTimeObj.value.time3 = item.changeTime
        }
        if (item.timeType === 2) {
          bgTimeObj.value.time2 = item.changeTime
        }
      })
    }
    if (val.workEquipmentTrialOperationProvalLsit?.length) {
      val.workEquipmentTrialOperationProvalLsit.forEach((item: any) => {
        if (item.timeType === 2) {
          syxTimeObj.value.time2 = item.changeTime
        } else if (item.timeType === 3) {
          syxTimeObj.value.time3 = item.changeTime
        } else if (item.timeType === 4) {
          syxTimeObj.value.time4 = item.changeTime
        }
      })
    }
    if (val.workRestoreWorkApprovalList?.length) {
      val.workRestoreWorkApprovalList.forEach((item: any) => {
        if (item.timeType === 2) {
          hfTimeObj.value.time2 = item.changeTime
        } else if (item.timeType === 3) {
          hfTimeObj.value.time3 = item.changeTime
        } else if (item.timeType === 4) {
          hfTimeObj.value.time4 = item.changeTime
        }
      })
    }
  }
)

//  延期流、设备试运行流、恢复工作流的工作负责人显示
const computedWorkerLeader = computed(() => (t: any) => {
  // 默认的工作负责人
  let v = props.workTicketData.workLeader
    ? props.workTicketData.workLeader.split('_')[0]
    : ''
  // 有变更过工作负责人  工作流也走完了
  if (bgTimeObj.value?.time3) {
    //  变更在前 显示变更后的负责人
    if (t >= bgTimeObj.value.time3) {
      v = props.workTicketData?.afterWorkLeader
        ? props.workTicketData?.afterWorkLeader.split('_')[0]
        : ''
    } else {
      // 显示变更前的负责人
      v = props.workTicketData?.beforeWorkLeader
        ? props.workTicketData?.beforeWorkLeader.split('_')[0]
        : ''
    }
  }
  return v
})

const workTimeColumns = [
  {
    prop: 'endTime',
    label: '收工时间',
    minWidth: 120,
    slotName: 'endTime'
  },
  { prop: 'endWorkLeader', label: '工作负责人', minWidth: 100 },
  {
    prop: 'endLicensor',
    label: '工作许可人',
    minWidth: 100
  },
  {
    prop: 'startTime',
    label: '开工时间',
    minWidth: 120,
    slotName: 'startTime'
  },
  { prop: 'startWorkLeader', label: '工作负责人', minWidth: 100 },
  { prop: 'startLicensor', label: '工作许可人', minWidth: 100 }
]

const fileExt = ref(['jpg', 'png', 'pdf', 'doc'])
</script>
<style scoped src="../../assets/work.scss"></style>
<style scoped lang="scss">
.mb16 {
  margin-bottom: 16px !important;
}
.mb24 {
  margin-bottom: 24px !important;
}
</style>
