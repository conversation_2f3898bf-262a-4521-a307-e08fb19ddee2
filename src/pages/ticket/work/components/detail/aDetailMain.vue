<template>
  <el-form class="info-base">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="工作票类型"> 电气一种工作票 </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="电站名称">
          {{ workTicketData?.stationName || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作票签发人">
          {{
            workTicketData?.signer ? workTicketData?.signer.split('_')[0] : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作许可人">
          {{
            workTicketData?.licensor
              ? workTicketData?.licensor.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="值班负责人">
          {{
            workTicketData?.principal
              ? workTicketData?.principal.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="值长">
          {{
            workTicketData?.shiftChiefOperator
              ? workTicketData?.shiftChiefOperator.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="就地工作许可人">
          {{
            workTicketData?.localLicensor
              ? workTicketData?.localLicensor.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  }
})
</script>
<style scoped src="../../assets/work.scss"></style>
