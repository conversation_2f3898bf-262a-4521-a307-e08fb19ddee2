<template>
  <el-form class="info-base priviate-label">
    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >工作地点保留带电部分或注意事项（工作票签发人填写）
    </div>
    <el-form-item label="" prop="workCondition" label-width="0">
      <el-input
        v-model="tableList7_"
        :rows="3"
        type="textarea"
        placeholder="请输入内容"
        autocomplete="off"
        show-word-limit
        :disabled="true"
        v-if="qfTimeObj.time5"
      />
      <span v-else>--</span>
    </el-form-item>
    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >工作地点保留带电部分和补充安全措施（工作许可人填写）
    </div>
    <el-form-item label="" prop="workCondition" label-width="0">
      <el-input
        v-model="tableList8_"
        :rows="3"
        type="textarea"
        placeholder="请输入内容"
        autocomplete="off"
        show-word-limit
        :disabled="true"
        v-if="qfTimeObj.time5"
      />
      <span v-else>--</span>
    </el-form-item>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="工作票签发人">
          <template v-if="qfTimeObj.time2">
            {{ workTicketData?.signer.split('_')[0] }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="签发时间">
          <template v-if="qfTimeObj.time2">
            {{ dealTime(qfTimeObj.time2) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作许可人">
          <template v-if="qfTimeObj.time2">
            {{ workTicketData?.licensor.split('_')[0] }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="时间">
          <template v-if="qfTimeObj.time2">
            {{ dealTime(qfTimeObj.time3) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="收到工作票时间">
          <template v-if="qfTimeObj.time2">
            {{ dealTime(qfTimeObj.time3) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="值班负责人">
          <template v-if="qfTimeObj.time2">
            {{ workTicketData?.principal.split('_')[0] }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>
    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text>批准工作时间
      {{
        qfTimeObj.time2
          ? dealTime(
              workTicketData?.approveStartTime
                ? workTicketData.approveStartTime.replace('T', ' ')
                : ''
            ) +
            ' - ' +
            dealTime(workTicketData?.approveTime)
          : '--'
      }}
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="值长签名">
          <template v-if="qfTimeObj.time2">
            {{ workTicketData?.shiftChiefOperator.split('_')[0] }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >上述安全措施已全部执行，共同到现场核对无误
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="许可工作开始时间">
          {{
            xkTimeObj.time3 ? dealTime(workTicketData?.permissionTime) : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12"> </el-col>
      <el-col :span="12">
        <el-form-item label="工作许可人签名">
          <template v-if="xkTimeObj.time3">
            {{ workTicketData?.licensor.split('_')[0] }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人签名">
          <template v-if="xkTimeObj.time3">
            <!-- {{ workTicketData?.workLeader.split('_')[0] }} -->
            {{ computedWorkerLeader(xkTimeObj.time3) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >确认工作负责人布置的工作任务和安全措施（附页：作业安全风险控制卡）
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="工作班组人员签名">
          --
          <!-- <template v-if="xkTimeObj.time3">
            {{ groupMember }}
          </template>
          <template v-else> -- </template> -->
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import { dealTime } from '@/utils/index'
const props = defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  }
})
watch(
  () => props.workTicketData,
  (val: any) => {
    if (val) {
      initSafeData()
    }
  }
)
const tableList7_ = ref('')
const tableList8_ = ref('')
const groupMember: any = ref('') // 班组人员签名
// 签发审批流程的时间处理
const qfTimeObj = ref<any>({
  time2: '',
  time3: '',
  time4: '',
  time5: '',
  time6: '',
  time7: ''
})
// 许可审批流程的时间处理
const xkTimeObj = ref<any>({
  time2: '',
  time3: ''
  // time4: ''
})
// 工作负责人变更 审批流程的时间处理
const bgTimeObj = ref<any>({
  time3: ''
})
watch(
  () => props?.workTicketData,
  (val: any) => {
    if (val.groupMember) {
      let groupMemberList = val.groupMember.split(',')
      groupMemberList = groupMemberList.map((item: any) => {
        return item.split('_')[0]
      })
      groupMember.value = groupMemberList.join(',')
    }
    if (val.workIssuanceApprovalList?.length) {
      val.workIssuanceApprovalList.forEach((item: any) => {
        if (item.timeType === 2) {
          qfTimeObj.value.time2 = item.changeTime
        } else if (item.timeType === 3) {
          qfTimeObj.value.time3 = item.changeTime
        } else if (item.timeType === 4) {
          qfTimeObj.value.time4 = item.changeTime
        } else if (item.timeType === 5) {
          qfTimeObj.value.time5 = item.changeTime
        } else if (item.timeType === 6) {
          qfTimeObj.value.time6 = item.changeTime
        } else if (item.timeType === 7) {
          qfTimeObj.value.time7 = item.changeTime
        }
      })
    }
    if (val.workLicenseApprovalList?.length) {
      val.workLicenseApprovalList.forEach((item: any) => {
        if (item.timeType === 2) {
          xkTimeObj.value.time2 = item.changeTime
        } else if (item.timeType === 3) {
          xkTimeObj.value.time3 = item.changeTime
        }
      })
    }
    if (val.workResponsiblePersonChangeApprovalList?.length) {
      val.workResponsiblePersonChangeApprovalList.forEach((item: any) => {
        if (item.timeType === 3) {
          bgTimeObj.value.time3 = item.changeTime
        }
      })
    }
  }
)

//  许可流的工作负责人显示
const computedWorkerLeader = computed(() => (t: any) => {
  // 默认的工作负责人
  let v = props.workTicketData.workLeader
    ? props.workTicketData.workLeader.split('_')[0]
    : ''
  // 有变更过工作负责人  工作流也走完了
  if (bgTimeObj.value?.time3) {
    //  变更在前 显示变更后的负责人
    if (t >= bgTimeObj.value.time3) {
      v = props.workTicketData?.afterWorkLeader
        ? props.workTicketData?.afterWorkLeader.split('_')[0]
        : ''
    } else {
      // 显示变更前的负责人
      v = props.workTicketData?.beforeWorkLeader
        ? props.workTicketData?.beforeWorkLeader.split('_')[0]
        : ''
    }
  }
  return v
})

const initSafeData = () => {
  let safeList = props.workTicketData?.ticketWorkSafeSteps
  if (safeList?.length) {
    safeList.forEach((item: any) => {
      item.files = item.imageUrl ? item.imageUrl.split(',') : []
      if (item.safeType == 7) {
        tableList7_.value = item.safeStepContext || ''
      } else if (item.safeType == 8) {
        tableList8_.value = item.safeStepContext || ''
      }
    })
  }
}
</script>
<style scoped src="../../assets/work.scss"></style>
