<template>
  <el-form class="info-base">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="部门(单位)">
          {{ workTicketData?.department || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="编号">
          {{ workTicketData?.ticketWorkNo || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人(监护人)">
          {{
            workTicketData?.workLeader
              ? workTicketData?.workLeader.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="班组">
          {{
            workTicketData?.groupName
              ? workTicketData?.groupName.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="班成员">
          {{ workTicketData?.groupMember || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="班成员人数">
          {{ workTicketData?.groupNum || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设备名称">
          {{ workTicketData?.deviceName || '--' }}
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="工作地点及设备双重名称">
          {{ workTicketData?.baseDeviceName || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计划工作时间">
          {{ dealTime(workTicketData?.startTime) }} -
          {{ dealTime(workTicketData?.endTime) }}
        </el-form-item>
      </el-col>
      <el-col :span="12"> </el-col>
      <el-col :span="24">
        <el-form-item label="工作内容">
          {{ workTicketData?.context || '--' }}
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import { dealTime } from '@/utils/index'
// const props =
defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  }
})

// const groupMember: any = ref('')
// watch(
//   () => props?.workTicketData,
//   (val: any) => {
//     if (val.groupMember) {
//       let groupMemberList = val.groupMember.split(',')
//       groupMemberList = groupMemberList.map((item: any) => {
//         return item.split('_')[0]
//       })
//       groupMember.value = groupMemberList.join(',')
//     }
//   }
// )
</script>
<style scoped src="../../assets/work.scss"></style>
