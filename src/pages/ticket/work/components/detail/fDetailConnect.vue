<!--
 * @Author: zhaopengpeng006 <EMAIL>
 * @Date: 2024-05-30 17:32:29
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-09-13 16:07:39
 * @FilePath: \pv-om-web\src\pages\ticket\work\components\detail\fDetailConnect.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-form class="info-base">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="关联检修工单">
          {{ workTicketData?.workOrderNo?.split('_')?.[0] || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="关联操作票">
          <div v-if="workTicketData?.operateTickets">
            <el-popover placement="top" :width="400">
              <template #reference>
                <div
                  style="
                    display: flex;
                    color: rgba(41, 204, 160, 1);
                    cursor: pointer;
                  "
                  @click="
                    toWorkTitle(workTicketData.operateTickets.split(',')[0])
                  "
                >
                  <img :src="link" class="linkSvg" />
                  {{
                    workTicketData.operateTickets
                      ? workTicketData.operateTickets.split(',')[0] + '...'
                      : '--'
                  }}
                </div>
              </template>
              <div
                style="
                  display: flex;
                  color: rgba(41, 204, 160, 1);
                  cursor: pointer;
                  flex-wrap: wrap;
                "
              >
                <p
                  v-for="(item, index) in workTicketData.operateTickets.split(
                    ','
                  )"
                  :key="index"
                  style="display: flex"
                  @click="toWorkTitle(item)"
                >
                  <img :src="link" class="linkSvg" />
                  {{ item
                  }}<span
                    v-if="
                      index !=
                      workTicketData.operateTickets.split(',').length - 1
                    "
                    >，</span
                  >
                </p>
              </div>
            </el-popover>
          </div>
          <div v-else>--</div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import { ticket as ticketAPI } from '@/api/index.ts'
import link from '@/assets/svgs/link.svg'
const route = useRoute()
const router = useRouter()

defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  }
})

const toWorkTitle = async (workTicketId_: any) => {
  try {
    let { data } = await ticketAPI.checkGroup({
      orderNo: workTicketId_,
      type: 4 // 1-工单，2-一种票，3-二种票，4-操作票
    })
    if (data) {
      try {
        let { data } = await ticketAPI.getIdByNo({
          operateTicketNo: workTicketId_
        })
        router.push(route.path + `/handleInfo/look/${data.data}`)
      } catch (e: any) {
        console.log(e)
      }
    } else {
      ElMessage({
        message: '权限不足',
        type: 'warning'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: '权限不足',
      type: 'warning'
    })
    console.log(e)
  }
}
</script>
<style scoped src="../../assets/work.scss"></style>
