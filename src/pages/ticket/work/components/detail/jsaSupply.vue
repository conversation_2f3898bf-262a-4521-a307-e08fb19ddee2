<template>
  <el-form class="info-base two-jsa">
    <div class="safe-title">
      <div class="line"></div>
      JSA票补充填写信息
    </div>
    <el-row :gutter="24" class="mt-6">
      <el-col :span="24">
        <el-form-item label="JSA编号">
          {{ workTicketData?.jsaTicketNo || '--' }}
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 作业类别 -->
    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text>作业类别
    </div>
    <el-checkbox-group v-model="homeworkType" disabled>
      <el-checkbox
        v-for="item in homeworkTyepList"
        :key="item.value"
        :label="item.value"
        :value="item.value"
        class="checkbox__"
      >
        {{ item.name }}
        <el-form-item
          v-if="item.value == 20 && homeworkType.includes(20)"
          prop="value1"
          class="lastItem"
        >
          <!-- <el-input
            v-model.trim="ruleForm.value1"
            placeholder="请输入"
            clearable
            maxlength="64"
            disabled
          /> -->
          <el-tooltip :content="ruleForm.value1" placement="top">
            <el-input v-model.trim="ruleForm.value1" disabled />
          </el-tooltip>
        </el-form-item>
      </el-checkbox>
    </el-checkbox-group>

    <!-- 作业风险 -->
    <div class="brief">
      <el-text class="mx-1" type="danger">*</el-text>作业风险
    </div>
    <el-checkbox-group v-model="homeworkRisk" disabled>
      <el-checkbox
        v-for="item in homeworkRiskList"
        :key="item.value"
        :label="item.value"
        :value="item.value"
        class="checkbox__"
      >
        {{ item.name }}
        <el-form-item
          v-if="item.value == 13 && homeworkRisk.includes(13)"
          prop="value3"
          class="lastItem"
        >
          <!-- <el-input
            v-model.trim="ruleForm.value3"
            placeholder="请输入"
            clearable
            disabled
            maxlength="64"
          /> -->
          <el-tooltip :content="ruleForm.value3" placement="top">
            <el-input v-model.trim="ruleForm.value3" disabled />
          </el-tooltip>
        </el-form-item>
      </el-checkbox>
    </el-checkbox-group>

    <div class="brief mb-4">
      <el-text class="mx-1" type="danger">*</el-text>过程风险预控
    </div>
    <vis-table-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :columns="tableColumns"
      :data="workTicketData?.ticketWorkProcessRiskList || []"
      background
      border
      :height="'auto'"
      :show-overflow-tooltip="true"
      class="vis-table-pagination safe-table1 mb-6"
      highlight-current-row
    >
      <template #executeStatue="{ row }">
        <span v-if="row.controlMeasure == '无'">无</span>
        <span v-else-if="row.executeStatue == 1"> 已执行 </span>
        <span v-else-if="row.executeStatue == 2"> 未执行 </span>
        <div v-if="row.controlMeasure != '无'">
          {{ row.description || '' }}
        </div>
      </template>
    </vis-table-pagination>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >经检查确认，作业过程风险评估符合实际，管控措施执行到位，相关注意事项已交代清楚。
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="工作负责人签名">
          <template v-if="workTicketData?.permissionTime">
            {{ approvalUser && approvalUser.split('_')[0] }}
            {{ approvalHeadTime ? dealTime(approvalHeadTime) : '' }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作班成员签名">
          {{ workTicketData.groupMember }}
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import useJsa from '../../hooks/work.ts'
import visTablePagination from '@/components/table-pagination.vue'
import { dealTime } from '@/utils/index'
const formKey = ref(0)
formKey.value = Date.now()

const homeworkType = ref<any>([]) // 作业类别
const homeworkRisk = ref<any>([]) // 作业风险
const { homeworkTyepList, homeworkRiskList } = useJsa()

const props = defineProps({
  workTicketData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const approvalHeadTime = ref('')
const approvalUser = ref('')
watch(
  () => props.workTicketData,
  async (val: any) => {
    homeworkType.value = val.homeworkType?.length
      ? val.homeworkType.split('_').map((item: any) => {
          return Number(item)
        })
      : []
    ruleForm.value.value1 = val.homeworkTypeRestock || ''

    homeworkRisk.value = val.homeworkRisk?.length
      ? val.homeworkRisk.split('_').map((item: any) => {
          return Number(item)
        })
      : []
    ruleForm.value.value3 = val.homeworkRiskRestock || ''

    let workLicenseApprovalList = val.workLicenseApprovalList || []
    workLicenseApprovalList.length &&
      workLicenseApprovalList.forEach((item: any) => {
        if (item.timeType == 3) {
          approvalHeadTime.value = item.changeTime || ''
          approvalUser.value = item.approvalUser || ''
        }
      })
  }
)

const tableColumns = [
  { prop: 'index', label: '序号', minWidth: 100 },
  { prop: 'workProcess', label: '作业过程', minWidth: 100 },
  { prop: 'riskDesc', label: '风险描述', minWidth: 100 },
  { prop: 'riskLevel', label: '风险等级', minWidth: 100 },
  { prop: 'controlMeasure', label: '管控措施', minWidth: 100 },
  {
    prop: 'executeStatue',
    label: '执行情况',
    minWidth: 100,
    slotName: 'executeStatue'
  }
]

// 表单
const ruleForm = ref({
  value1: '',
  value3: ''
})
</script>
<style src="../../assets/work.scss"></style>
