<template>
  <el-form
    ref="ruleFormRef"
    :key="formKey"
    :model="ruleForm"
    :rules="rules"
    class="info-base two-jsa"
  >
    <div class="safe-title">
      <div class="line"></div>
      JSA票补充填写信息
    </div>
    <!-- 作业类别 -->
    <div class="breif">
      <el-text class="mx-1" type="danger">*</el-text>作业类别
    </div>
    <el-checkbox-group v-model="homeworkType" :disabled="isDisabled">
      <el-checkbox
        v-for="item in homeworkTyepList"
        :key="item.value"
        :label="item.value"
        :value="item.value"
        class="checkbox__"
      >
        {{ item.name }}
        <el-form-item
          v-if="item.value == 20 && homeworkType.includes(20)"
          prop="value1"
          class="lastItem"
        >
          <el-input
            v-model.trim="ruleForm.value1"
            placeholder="请输入补充说明"
            clearable
            maxlength="64"
            :disabled="isDisabled"
          />
        </el-form-item>
      </el-checkbox>
    </el-checkbox-group>

    <!-- 作业风险 -->
    <div class="brief">
      <el-text class="mx-1" type="danger">*</el-text>作业风险
    </div>
    <el-checkbox-group v-model="homeworkRisk" :disabled="isDisabled">
      <el-checkbox
        v-for="item in homeworkRiskList"
        :key="item.value"
        :label="item.value"
        :value="item.value"
        class="checkbox__"
      >
        {{ item.name }}
        <el-form-item
          v-if="item.value == 13 && homeworkRisk.includes(13)"
          prop="value3"
          class="lastItem"
        >
          <el-input
            v-model.trim="ruleForm.value3"
            placeholder="请输入补充说明"
            clearable
            :disabled="isDisabled"
            maxlength="64"
          />
        </el-form-item>
      </el-checkbox>
    </el-checkbox-group>

    <div class="brief">
      <el-text class="mx-1" type="danger">*</el-text>过程风险预控
    </div>
    <table-info
      ref="riskRef"
      class="mb-6"
      :columns="riskColumns"
      :table-data="ticketWorkProcessRiskList"
      :maxlength="1024"
      @get-tabel-data="getRiskData"
    ></table-info>
  </el-form>
</template>
<script setup lang="ts">
import useJsa from '../../hooks/work.ts'
import type { FormInstance, FormRules } from 'element-plus'
import tableInfo from '../../../components/tableRow.vue'
interface RuleForm {
  value1: string
  value3: string
}
const formKey = ref(0)
formKey.value = Date.now()
const route = useRoute()
const isDisabled = computed(() => {
  return route.params && route.params.pageStatus == 'look'
})
const homeworkType = ref<any>([]) // 作业类别
const homeworkRisk = ref<any>([]) // 作业风险
const { homeworkTyepList, homeworkRiskList } = useJsa()
const props = defineProps({
  workTicketData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const riskColumns = ref([
  { prop: 'index', label: '' },
  {
    prop: 'workProcess',
    label: '作业过程',
    slotName: 'workProcess',
    minWidth: 100,
    placeholder: '请输入作业过程'
  },
  {
    prop: 'riskDesc',
    label: '风险描述',
    slotName: 'riskDesc',
    minWidth: 100,
    placeholder: '请输入风险描述'
  },
  {
    prop: 'riskLevel',
    label: '风险等级',
    slotName: 'riskLevel',
    minWidth: 100,
    placeholder: '请输入风险等级'
  },
  {
    prop: 'controlMeasure',
    label: '管控措施',
    slotName: 'controlMeasure',
    minWidth: 100,
    placeholder: '请输入管控措施'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 66,
    fixed: 'right'
  }
])
const ticketWorkProcessRiskList = ref<any[]>([])
watch(
  () => props.workTicketData,
  async () => {
    update()
  }
)
const update = () => {
  if (props.workTicketData?.homeworkType?.length) {
    homeworkType.value =
      props.workTicketData?.homeworkType.split('_').map((item: any) => {
        if (item.includes('20@')) {
          ruleForm.value.value1 = item.replace('20@', '')
          return 20
        }
        return Number(item)
      }) || []
  }

  if (props.workTicketData?.homeworkRisk?.length) {
    homeworkRisk.value =
      props.workTicketData?.homeworkRisk.split('_').map((item: any) => {
        if (item.includes('13@')) {
          ruleForm.value.value3 = item.replace('13@', '')
          return 13
        }
        return Number(item)
      }) || []
  }

  if (props.workTicketData?.ticketWorkProcessRiskList?.length) {
    ticketWorkProcessRiskList.value =
      props.workTicketData?.ticketWorkProcessRiskList?.map((item: any) => ({
        ...item,
        workProcess: item.process || item.workProcess || '',
        controlMeasure: item.control || item.controlMeasure || ''
      })) || []
  }

  if (props.workTicketData?.homeworkTypeRestock) {
    ruleForm.value.value1 = props.workTicketData?.homeworkTypeRestock
  }
  if (props.workTicketData?.homeworkRiskRestock) {
    ruleForm.value.value3 = props.workTicketData?.homeworkRiskRestock
  }
}
// 过程风险预控
const riskRef = ref<any>(null)
const riskData = ref<any>([])
const riskValid = ref(false)
const getRiskData = (val: any) => {
  riskValid.value = val.valid
  if (val.data?.length) {
    riskData.value = val.data
  }
}

// 表单
const ruleFormRef = ref<FormInstance>()
const ruleForm = ref({
  value1: '',
  value3: ''
})
const rules = reactive<FormRules<RuleForm>>({
  value1: [{ required: true, message: '请输入补充说明', trigger: 'blur' }],
  value3: [{ required: true, message: '请输入补充说明', trigger: 'blur' }]
})

// 校验
const emit = defineEmits(['getA5Data'])
const submitForm = async (type: number) => {
  let homeworkTypeRestock: any = ''
  if (homeworkType.value?.includes(20) && ruleForm.value.value1) {
    homeworkTypeRestock = ruleForm.value.value1 || ''
  } else {
    homeworkTypeRestock = ''
  }

  let homeworkRiskRestock: any = ''
  if (homeworkRisk.value?.includes(13) && ruleForm.value.value3) {
    homeworkRiskRestock = ruleForm.value.value3
  } else {
    homeworkRiskRestock = ''
  }

  // jsa部分的必填校验
  let valid: boolean = true
  // 未勾选 作业类别、作业风险
  if (
    !homeworkType.value ||
    !homeworkType.value.length ||
    !homeworkRisk ||
    !homeworkRisk.value.length
  ) {
    valid = false
  }
  // 作业类别和作业风险，勾选了其他，未填写输入框的内容
  await ruleFormRef.value?.validate((v: boolean) => {
    if (!v) {
      valid = false
    }
  })

  // 过程风险预防未填写
  await riskRef.value.submitTable(type)
  if (!riskValid.value) {
    valid = false
  }

  // 重新组装数据
  emit('getA5Data', {
    valid,
    data: {
      homeworkType: homeworkType.value.length
        ? homeworkType.value?.join('_')
        : '',
      homeworkTypeRestock,
      homeworkRisk: homeworkRisk.value.length
        ? homeworkRisk.value?.join('_')
        : '',
      homeworkRiskRestock,
      ticketWorkProcessRiskList: riskData.value
    }
  })
}
defineExpose({
  submitForm,
  update
})
</script>
<style src="../../assets/work.scss"></style>
