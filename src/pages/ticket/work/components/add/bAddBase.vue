<template>
  <div class="info-base">
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="100px"
      :rules="formRules"
      style="padding-right: 50px"
    >
      <el-row :gutter="24">
        <!-- <el-col :span="12">
          <el-form-item label="部门(单位)" prop="departmentId">
            <el-select
              v-model="formData.departmentId"
              placeholder="请选择部门(单位)"
              filterable
              clearable
              @change="changeDepart"
            >
              <el-option
                v-for="item in departList"
                :key="item.id"
                :label="item.companyName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="编号" prop="ticketWorkNo">
            <el-input
              v-model="formData.ticketWorkNo"
              placeholder="保存后自动生成"
              maxlength="64"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作负责人(监护人)" prop="workLeader">
            <el-select
              v-model="formData.workLeader"
              placeholder="请选择工作负责人(监护人)"
              filterable
              clearable
            >
              <el-option
                v-for="item in props.roleList5"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="班组" prop="groupName">
            <el-select
              v-model="formData.groupName"
              placeholder="请选择班组"
              filterable
              clearable
            >
              <el-option
                v-for="item in teamList"
                :key="item.id"
                :label="item.teamsGroupsName"
                :value="`${item.teamsGroupsName}_${item.id}`"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <!--
        <el-col :span="12">
          <el-form-item label="班成员" prop="groupMember">
            <div :class="[isGetInfo ? 'tagBorder__' : 'redB_ tagBorder__']">
              <div v-if="attendArr.length == 0" class="placeholeder__">
                请选择班成员
              </div>

              <el-tooltip
                v-for="(item, index) in attendArr.slice(0, 3)"
                v-else
                :key="index"
                effect="light"
                :content="item.employeeName"
                placement="top-start"
              >
                <span class="bcy-w"> {{ item.employeeName }}</span>
              </el-tooltip>
              <el-popover
                v-if="attendArr.length > 3"
                placement="top-start"
                trigger="hover"
                width="300px"
              >
                <div style="display: flex; flex-wrap: wrap">
                  <span
                    v-for="(item, index) in attendArr.slice(
                      3,
                      attendArr.length
                    )"
                    :key="index"
                    class="sps__"
                  >
                    {{ item.employeeName }}
                  </span>
                </div>
                <template #reference>
                  <span class="bcy-w">+{{ attendArr.length - 3 }}...</span>
                </template>
              </el-popover>
            </div>
            <div v-if="attendArr.length > 0" class="num__">
              班成员人数共{{ attendArr.length }}人
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="2">
          <el-button type="primary" size="large" @click="addStaff()">
            <el-icon><Plus /></el-icon> 添加人员
          </el-button>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="班成员" prop="groupMember">
            <el-input
              v-model.trim="formData.groupMember"
              placeholder="请输入班成员"
              clearable
              maxlength="64"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="班成员人数" prop="groupNum">
            <el-input
              v-model.trim="formData.groupNum"
              placeholder="请输入班成员人数"
              clearable
              maxlength="2"
              @input="() => limitGroupNum(formData.groupNum)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input
              v-model.trim="formData.deviceName"
              placeholder="请输入设备名称"
              maxlength="64"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item
            label="工作地点及设备双重名称"
            prop="baseDeviceName"
            class="special-label"
          >
            <el-input
              v-model.trim="formData.baseDeviceName"
              placeholder="请输入工作地点及设备双重名称"
              maxlength="64"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划工作时间" prop="timeRange">
            <el-date-picker
              v-model="formData.timeRange"
              type="datetimerange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12"></el-col>
        <el-col :span="23">
          <el-form-item label="工作内容" prop="context">
            <el-input
              v-model.trim="formData.context"
              :rows="3"
              type="textarea"
              placeholder="请输入工作内容"
              maxlength="1024"
              autocomplete="off"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 添加人员弹窗 -->
    <!-- <selectStaff
      :is-show="isShow"
      :employee-id-arr="employeeIdArr"
      @uplate-data="uplateData"
      @close-dialog="isShow = false"
    ></selectStaff> -->
  </div>
</template>
<script setup lang="ts">
// import selectStaff from '../../../components/selectStaff.vue'
import type { FormInstance, FormRules } from 'element-plus'
import { dealTime } from '@/utils/index'
import { ticket as ticketAPI } from '@/api/index.ts'
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'

onMounted(() => {
  getCompany()
  resetForm()
})
// 角色人员 roleList5 工作负责人
type PropsType = {
  roleList5: any[]
  workTicketData: any
}
const props = withDefaults(defineProps<PropsType>(), {})

// 修改部门(单位)  重新选择班组
const changeDepart = (val: any) => {
  departList.value.forEach((item: any) => {
    if (val == item.id) {
      formData.value.department = item.companyName
    }
  })
  getTeamsGroupsList(Number(val))
  formData.value.groupName = ''
}

const departList = ref<any[]>([]) // 部门(单位)list
const teamList = ref<any[]>([]) // 班组list
// 获取部门（单位）list
const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    departList.value = data.data
  } catch (e: any) {}
}
// 获取班组list
const getTeamsGroupsList = async (operatorsId: any) => {
  try {
    const { data } = await ticketAPI.getTeamsGroupByOperatorsId(
      {
        operatorsId
      },
      false
    )
    teamList.value = data.data
  } catch (e: any) {}
}

const formData = ref<Record<string, any>>({
  // department: '', // 部门(单位)
  // departmentId: '', // 部门(单位)ID
  ticketWorkNo: '保存后自动生成', // 编号
  workLeader: '', // 工作负责人
  // groupName: '', // 班组
  groupMember: '', // 班成员
  groupNum: '', // 班成员人数
  deviceName: '', // 设备名称
  timeRange: '', // 计划工作时间
  startTime: '', // 计划工作开始时间
  endTime: '', //	计划工作结束时间
  baseDeviceName: '', // 工作地点及设备双重名称
  context: '' // 工作内容
})

const formRules = reactive<FormRules>({
  departmentId: [
    { required: true, message: '请选择部门(单位)', trigger: 'change' }
  ],
  ticketWorkNo: [{ required: true, message: '请输入编号', trigger: 'blur' }],
  workLeader: [
    { required: true, message: '请选择工作负责人(监护人)', trigger: 'change' }
  ],
  groupName: [{ required: true, message: '请选择班组', trigger: 'change' }],
  groupMember: [{ required: true, message: '请输入班成员', trigger: 'blur' }],
  groupNum: [{ required: true, message: '请输入班成员人数', trigger: 'blur' }],
  deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  timeRange: [
    { required: true, message: '请选择计划工作时间', trigger: 'change' }
  ],
  baseDeviceName: [
    { required: true, message: '请输入工作地点及设备双重名称', trigger: 'blur' }
  ],
  context: [{ required: true, message: '请输入工作内容', trigger: 'blur' }]
})
// 限制输入班成员人数 0-99
const limitGroupNum = (val: any) => {
  if (val === '0') {
    formData.value.groupNum = val
  } else {
    formData.value.groupNum = String(val).replace(/^(0)|\D/g, '') || ''
  }
}

const attendArr = ref<any[]>([]) // 班成员
// 添加班成员
// const isShow = ref(false)
// // const arrList = ref<any[]>([])
// const isGetInfo = ref(true)
// const addStaff = () => {
//   isShow.value = true
//   // arrList.value = attendArr.value
//   employeeIdArr.value = attendArr.value.map((item: any) => {
//     return item.id
//   })
// }
// const employeeIdArr = ref([])

watch(
  () => props?.workTicketData,
  (val: any) => {
    resetForm()
    formData.value = val
    if (val.departmentId) {
      getTeamsGroupsList(val.departmentId)
    }
    if (val.startTime && val.endTime) {
      formData.value.timeRange = [
        dealTime(val.startTime),
        dealTime(val.endTime)
      ]
    }
    // if (val.groupMember) {
    //   let arr = val.groupMember.split(',')
    //   attendArr.value = arr.map((item: any) => {
    //     return {
    //       employeeName: item.split('_')[0],
    //       id: item.split('_')[1]
    //     }
    //   })
    //   employeeIdArr.value = val.groupMember.split(',').map((item: any) => {
    //     return item.split('_')[1]
    //   })
    // } else {
    //   employeeIdArr.value = []
    // }
  }
)
watch(
  () => attendArr.value,
  (val) => {
    if (val.length) {
      let arr2: any = []
      val.forEach((item: any) => {
        arr2.push(`${item.employeeName}_${item.id}`)
      })
      formData.value.groupMember = arr2?.join(',')
      nextTick(() => {
        formRef.value?.validateField('groupMember')
      })
    } else {
      formData.value.groupMember = ''
    }
  },
  {
    deep: true
  }
)
// const uplateData = (arr: Obj[]) => {
//   attendArr.value = arr
//   isGetInfo.value = arr.length ? true : false
// }

// 提交 保存草稿1 签发审批2
const formRef = ref<FormInstance>()
const emit = defineEmits(['getA2Data'])
const submitForm = async (type: number) => {
  if (!formRef.value) return

  if (formData.value.timeRange?.length) {
    formData.value.startTime = formData.value.timeRange[0] + ':00'
    formData.value.endTime = formData.value.timeRange[1] + ':00'
  }
  if (type === 1) {
    emit('getA2Data', {
      data: { ...formData.value }
    })
  } else {
    // isGetInfo.value = formData.value.groupMember ? true : false
    await formRef.value.validate((valid) => {
      emit('getA2Data', {
        valid,
        data: { ...formData.value }
      })
      return valid
    })
  }
}
// 重置
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})
</script>
<style scoped src="../../assets/work.scss"></style>

<style scoped lang="scss">
.sps__ {
  border-radius: 2px;
  background: #f6f8fa;
  font-size: 14px;
  height: 30px;
  line-height: 30px;
  padding: 0 8px;
  color: #000;
  margin: 0 10px 10px 0;
}
.redB_ {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
  border: none !important;
}
.tagBorder__ {
  width: 100%;
  height: 40px;
  line-height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px 12px;
  box-sizing: border-box;
  display: flex;
  span {
    border-radius: 2px;
    background: #f6f8fa;
    font-size: 14px;
    height: 30px;
    line-height: 30px;
    padding: 0 8px;
    color: #000;
    margin-right: 10px;
  }
  > span:last-child {
    margin-right: 0;
  }
  .placeholeder__ {
    color: rgba(0, 0, 0, 0.25) !important;
    line-height: 28px !important;
  }
}
.num__ {
  color: rgba(230, 135, 46, 1) !important;
  // line-height: 28px !important;
  line-height: 20px !important;
  font-size: 10px;
}
.waringInfoBas__ {
  padding: 0 0 20px 69px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  color: #ff9900;
  font-size: 12px;
  .el-icon {
    margin: 0 4px 0 0;
    font-size: 16px;
  }
}
.cardPhotoClass__.el-form-item--default {
  margin-bottom: 10px !important;
}
.trainingContente__ {
  .el-textarea.el-input--default {
    position: relative;
    .el-input__count {
      position: absolute;
      bottom: 10px !important;
      right: 10px !important;
    }
  }
}
.patorSps {
  line-height: 40px !important;
}
.bcy-w {
  width: 60px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 2px;
  background: #f6f8fa;
  font-size: 14px;
  height: 30px;
  line-height: 30px;
  padding: 0 8px;
  color: #000;
}
</style>
