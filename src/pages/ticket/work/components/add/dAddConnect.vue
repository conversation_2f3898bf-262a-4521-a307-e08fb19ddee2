<template>
  <div class="info-base">
    <el-form :model="formData" label-suffix="" label-width="100px">
      <el-row :gutter="24">
        <!-- <el-col :span="10">
          <el-form-item label="关联检修工单" prop="workOrderNo">
            <el-select
              v-model="formData.workOrderNo"
              placeholder="请选择关联检修工单"
              filterable
              clearable
              remote
              remote-show-suffix
              :remote-method="getJxList"
            >
              <el-option
                v-for="(item, index) in selectList1"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="10" :offset="3">
          <el-form-item label="关联操作票" prop="operateTickets">
            <el-select
              v-model="formData.operateTickets"
              placeholder="请选择关联操作票"
              filterable
              clearable
              remote
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="1"
              remote-show-suffix
              :remote-method="getCzList"
            >
              <el-option
                v-for="(item, index) in selectList2"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { ticket as ticketAPI } from '@/api/index.ts'

onMounted(async () => {
  getJxList('')
  // getCzList('')
})

const props = defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  }
})

watch(
  () => props.workTicketData,
  (val: any) => {
    formData.value.workOrderNo = val.workOrderNo || '' // 关联检修工单
    if (Array.isArray(val.operateTickets)) {
      formData.value.operateTickets =
        val.operateTickets.length == 0 ? [] : val.operateTickets // 关联操作票
    } else {
      formData.value.operateTickets = val.operateTickets
        ? val.operateTickets.split(',')
        : [] // 关联操作票
    }
  }
)
const formData = ref({
  workOrderNo: '', // 关联检修工单
  operateTickets: [] // 关联操作票
})
const selectList1 = ref<any[]>([])
const selectList2 = ref<any[]>([])

// 获取关联检修工单列表
const getJxList = async (orderNo: any) => {
  try {
    let { data } = await ticketAPI.getJianXiuNo({
      orderNo
    })
    selectList1.value = data.data
  } catch (e) {
    selectList1.value = []
  }
}

// 获取关联操作票列表
// const getCzList = async (orderNo: any) => {
//   try {
//     let { data } = await ticketAPI.getCaoZuoTicketNo({
//       orderNo
//     })
//     selectList2.value = data.data
//   } catch (e) {
//     selectList2.value = []
//   }
// }

const emit = defineEmits(['getA4Data'])
const submitForm = async () => {
  let operateTickets: string = ''
  if (formData.value.operateTickets?.length) {
    operateTickets = formData.value?.operateTickets?.join(',')
  } else {
    operateTickets = ''
  }

  emit('getA4Data', {
    data: { ...formData.value, operateTickets }
  })
}
defineExpose({
  submitForm
})
</script>
<style scoped src="../../assets/work.scss"></style>
