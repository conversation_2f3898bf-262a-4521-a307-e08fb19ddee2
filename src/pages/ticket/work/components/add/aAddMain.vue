<template>
  <div class="info-base">
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="100px"
      :rules="formRules"
      style="padding-right: 50px"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="工作票类型">
            <el-input
              placeholder="请输入工作票类型"
              value="电气一种工作票"
              maxlength="64"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col v-if="!isCopy && !isEdit" :span="12">
          <el-form-item label="选择典型票">
            <el-select
              v-model="formData.templateId"
              placeholder="请选择典型票"
              filterable
              clearable
              :disabled="!formData.stationCode"
              @change="changeTemplate"
            >
              <template v-if="templates.length">
                <el-option
                  v-for="item in templates"
                  :key="item.id"
                  :label="item.modelName"
                  :value="item.id"
                />
                <el-option label="暂不使用" value="" />
              </template>
              <template v-else>
                <el-option label="暂不使用" value="" />
                <el-option label="新建典型票" :value="-1" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电站名称" prop="stationCode">
            <StationSelect
              v-model="formData.stationCode"
              v-model:label="formData.stationName"
              @change="changeStation"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门(单位)" prop="departmentId">
            <el-select
              v-model="formData.departmentId"
              placeholder="请选择部门(单位)"
              filterable
              clearable
              :disabled="true"
              @change="changeDepart"
            >
              <el-option
                v-for="item in departList"
                :key="item.id"
                :label="item.companyName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="班组" prop="groupName">
            <el-select
              v-model="formData.groupName"
              placeholder="请选择班组"
              filterable
              clearable
            >
              <el-option
                v-for="item in teamList"
                :key="item.id"
                :label="item.teamsGroupsName"
                :value="`${item.teamsGroupsName}_${item.id}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作票签发人" prop="signer">
            <el-select
              v-model="formData.signer"
              placeholder="请选择工作票签发人"
              filterable
              clearable
            >
              <el-option
                v-for="item in props.roleList1"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作许可人" prop="licensor">
            <el-select
              v-model="formData.licensor"
              placeholder="请选择工作许可人"
              filterable
              clearable
            >
              <el-option
                v-for="item in roleList2"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="值班负责人" prop="principal">
            <el-select
              v-model="formData.principal"
              placeholder="请选择值班负责人"
              filterable
              clearable
            >
              <el-option
                v-for="item in roleList3"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="值长" prop="shiftChiefOperator">
            <el-select
              v-model="formData.shiftChiefOperator"
              placeholder="请选择值长"
              filterable
              clearable
            >
              <el-option
                v-for="item in roleList4"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="就地工作许可人" prop="localLicensor">
            <el-select
              v-model="formData.localLicensor"
              placeholder="请选择就地工作许可人"
              filterable
              clearable
            >
              <el-option
                v-for="item in roleList2"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import StationSelect from '@/components/spic-station'
import type { FormInstance, FormRules } from 'element-plus'
import * as api from '@/api/index.ts'
import { ticket as ticketAPI } from '@/api/index.ts'

// 角色人员 roleList1 工作票签发人、roleList2 工作许可人、roleList3 值班负责人、roleList4 值长
type PropsType = {
  roleList1: any[]
  roleList2: any[]
  roleList3: any[]
  roleList4: any[]
  workTicketData: any
}
const props = withDefaults(defineProps<PropsType>(), {})

const route = useRoute()
const isEdit = route.params.id
const isCopy = route.query.type === 'copy'
const templates = ref<any[]>([])
const getTemplates = async (operatorId: any) => {
  const { data } = await api.post({
    url: `/operate/modelTicketController/getTemplateByType`,
    data: {
      modelType: 1,
      operatorId
    },
    loading: false
  })
  templates.value = data || []
  formData.value.templateId = templates.value[0]?.id || ''
  changeTemplate(formData.value.templateId)
}
const router = useRouter()
const changeTemplate = (val: any) => {
  if (!val) {
    emit('getA1Data', {
      data: { ...formData.value },
      templateContent: {}
    })
  } else if (val === -1) {
    router.push(
      `/ticket/work/dx/add?modelType=1&company=${company.value}&station=${formData.value.stationName}__${formData.value.stationCode}`
    )
  } else {
    getTemplateContent(val)
  }
}
const templateContent = ref<any>({})
const getTemplateContent = async (id: number) => {
  const { data } = await api.get({
    url: '/operate/modelTicketController/getModelTicketById',
    data: { id },
    loading: true
  })
  templateContent.value = data || {}
  emit('getA1Data', {
    data: { ...formData.value },
    templateContent: templateContent.value
  })
}
onMounted(() => {
  if (route.query.station) {
    formData.value.stationName = String(route.query.station).split('__')[0]
    formData.value.stationCode = String(route.query.station).split('__')[1]
    changeStation({
      stationName: formData.value.stationName,
      stationCode: formData.value.stationCode
    })
  }
})
watch(
  () => props?.workTicketData,
  (val: any) => {
    console.log(val)
    formData.value = val
    departList.value = [
      {
        id: props?.workTicketData?.departmentId,
        companyName: props?.workTicketData?.department
      }
    ]
    if (props?.workTicketData?.departmentId) {
      changeDepart(props?.workTicketData?.departmentId)
    }
  }
)

const formData = ref({
  templateId: '',
  stationCode: '', // 电站code
  stationName: '', // 电站名称
  departmentId: '',
  department: '',
  groupName: '',
  signer: '', // 工作票签发人
  licensor: '', //  工作许可人
  principal: '', // 值班负责人
  shiftChiefOperator: '', // 值长
  localLicensor: '' // 就地工作许可人
})
const formRules = reactive<FormRules>({
  stationCode: [{ required: true, message: '请选择电站', trigger: 'change' }],
  signer: [
    { required: true, message: '请选择工作票签发人', trigger: 'change' }
  ],
  licensor: [
    { required: true, message: '请选择工作许可人', trigger: 'change' }
  ],
  principal: [
    { required: true, message: '请选择值班负责人', trigger: 'change' }
  ],
  shiftChiefOperator: [
    { required: true, message: '请选择值长', trigger: 'change' }
  ],
  groupName: [{ required: true, message: '请选择班组', trigger: 'change' }]
})

const changeStation = (e: any) => {
  formData.value.stationName = e.stationName
  getCompany(e.stationCode)
  router.replace({
    path: route.fullPath,
    query: { station: e.stationName + '__' + e.stationCode }
  })
}
const departList = ref<any[]>([]) // 部门(单位)list
const teamList = ref<any[]>([]) // 班组list
const company = ref<string>()
// 获取部门（单位）list
const getCompany = async (stationCode: string) => {
  try {
    const { data } = await api.get('/operate/workOrder/getOperatorsInfo', {
      stationCode
    })
    departList.value = [data]
    formData.value.departmentId = data.id
    company.value = data.companyName + '__' + data.companyCode
    getTemplates(formData.value.departmentId)
    changeDepart(formData.value.departmentId)
  } catch (e: any) {}
}

const changeDepart = async (val: any) => {
  departList.value.forEach((item: any) => {
    if (val == item.id) {
      formData.value.department = item.companyName
      formData.value.departmentId = item.id
    }
  })
  await getTeamsGroupsList(Number(val))
  if (
    !teamList.value
      .map((e: any) => `${e.teamsGroupsName}_${e.id}`)
      .includes(formData.value.groupName)
  ) {
    formData.value.groupName = ''
  }
}

const getTeamsGroupsList = async (operatorsId: any) => {
  try {
    const { data } = await ticketAPI.getTeamsGroupByOperatorsId(
      {
        operatorsId
      },
      false
    )
    teamList.value = data.data
  } catch (e: any) {}
}

// 提交 保存草稿1 签发审批2
const formRef = ref<FormInstance>()
const emit = defineEmits(['getA1Data'])
const submitForm = async (type: number) => {
  if (!formRef.value) return
  if (type === 1) {
    emit('getA1Data', {
      data: { ...formData.value }
    })
  } else {
    await formRef.value.validate((valid: any) => {
      emit('getA1Data', {
        valid,
        data: { ...formData.value }
      })
      return valid
    })
  }
}
// 重置
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
defineExpose({
  submitForm,
  resetForm
})
</script>
<style scoped src="../../assets/work.scss"></style>
