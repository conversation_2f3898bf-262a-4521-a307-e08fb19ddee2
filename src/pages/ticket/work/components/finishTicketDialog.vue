<template>
  <el-dialog
    v-model="dialogVisible"
    title="填写信息"
    class="vis-dialog ticket-dialog finish-dialog"
    align-center
    :before-close="closeDialog"
    :close-on-click-modal="false"
    width="446"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="100px"
      label-position="top"
      :rules="formRules"
    >
      <div class="describe">【电气一种票终结审批-发起人】需填写补充信息</div>
      <div class="group">
        <div class="title">
          <div class="line"></div>
          设备及安全措施已恢复至开工前状态，现场清理完毕，工作班人员已全部撤离。
        </div>
        <el-form-item label="结束时间" prop="finalTime">
          <el-date-picker
            v-model="formData.finalTime"
            type="datetime"
            placeholder="请选择结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            :editable="false"
          />
        </el-form-item>
      </div>

      <!-- <div class="group">
        <div class="title">
          <div class="line"></div>
          临时遮拦、标示牌已拆除，常设遮拦已恢复。
        </div>
        <el-form-item label="未拆除或未拉开的接地线编号" prop="noOne">
          <el-input
            v-model.trim="formData.noOne"
            placeholder="请输入未拆除或未拉开的接地线编号"
            autocomplete="off"
            maxlength="64"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="共多少组" prop="numOne">
          <el-input
            v-model.trim="formData.numOne"
            placeholder="请输入共多少组"
            autocomplete="off"
            maxlength="5"
            clearable
            @input="limitNum1"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="未拆除或未拉开的接地刀闸编号" prop="noTwo">
          <el-input
            v-model.trim="formData.noTwo"
            placeholder="请输入未拆除或未拉开的接地刀闸编号"
            autocomplete="off"
            maxlength="64"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="共多少组" prop="numTwo">
          <el-input
            v-model.trim="formData.numTwo"
            placeholder="请输入共多少组"
            autocomplete="off"
            maxlength="5"
            clearable
            @input="limitNum2"
          >
          </el-input>
        </el-form-item>
        <div class="txt">(已汇报值班负责人)</div>
      </div> -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button v-preventReClick="1000" type="primary" @click="handleSave"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { ticket as ticketAPI } from '@/api/index.ts'
const route = useRoute()

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  }
})
const dialogVisible = ref(false)
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
  },
  {
    deep: true
  }
)

let formData = ref({
  finalTime: '', // 结束时间（终结审批）
  noOne: '', // 	未拆除或未拉开的接地线编号
  noTwo: '', // 接地刀闸编号
  numOne: '', // 	组数 1
  numTwo: '' //	组数 2
})
const limitNum1 = (val: any) => {
  formData.value.numOne = val.replace(/^(0)|\D/g, '')
}
const limitNum2 = (val: any) => {
  formData.value.numTwo = val.replace(/^(0)|\D/g, '')
}
const formRef = ref<FormInstance>()
const formRules = reactive<FormRules<Record<string, any>>>({
  finalTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  noOne: [
    {
      required: true,
      message: '请输入未拆除或未拉开的接地线编号',
      trigger: 'blur'
    }
  ],
  numOne: [{ required: true, message: '请输入共多少组', trigger: 'blur' }],
  noTwo: [
    {
      required: true,
      message: '请输入未拆除或未拉开的接地刀闸编号多少组',
      trigger: 'blur'
    }
  ],
  numTwo: [{ required: true, message: '请输入共多少组', trigger: 'blur' }]
})

// 点击取消
const emit = defineEmits(['closeDialog', 'uplateData'])
const closeDialog = () => {
  dialogVisible.value = false
  emit('closeDialog')
  resetForm()
}
// 点击确认
const handleSave = () => {
  formRef.value?.validate(async (valid: any) => {
    if (valid) {
      try {
        const { data } = await ticketAPI.finishWorkTicket(
          {
            businessId: Number(route.params.id),
            ticketWorkVO: formData.value
          },
          true
        )

        if (data.code === '200') {
          ElMessage({
            message: `操作成功!`,
            type: 'success'
          })
          closeDialog()
          emit('uplateData')
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}
// 重置
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
</script>

<style scoped src="../../assets/dialog.scss"></style>
<style lang="scss">
.ticket-dialog .el-form .el-form-item--default .el-form-item__label {
  height: 22px !important;
  line-height: 22px !important;
}
</style>
