.info-base {
  padding-bottom: 0;
  .operate {
    margin-bottom: 24px;
    margin-bottom: 0;
    padding-left: 0;
    // p {
    //   font-size: 16px;
    //   font-weight: 500;
    //   color: rgba(0, 0, 0, 0.85);
    //   line-height: normal;
    // }
  }
  .info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 24px;
    .info-item {
      padding-bottom: 24px;
    }
    .label {
      color: rgba(0, 0, 0, 0.45);
    }
  }
}

// .page-main,
// .info-tab {
//   .operate {
//     padding-left: 0;
//     // p {
//     //   font-size: 16px;
//     //   font-weight: 500;
//     //   color: rgba(0, 0, 0, 0.85);
//     //   line-height: normal;
//     // }
//   }
// }

.info-tab {
  .operate {
    margin-bottom: 24px;
    .el-icon {
      margin-right: 4px;
    }
  }
}
.info-tab-height-show {
  height: calc(100vh - 292px);
}
.info-tab-height-edit {
  height: calc(100vh - 384px);
}

.page-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
  line-height: 64px;
  margin: 0 24px 24px;
  height: 64px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  & > .el-button + .el-button {
    margin-left: 16px !important;
  }
  & > .el-button {
    min-width: 72px !important;
    height: 40px !important;
  }
}
// tag相关
.tag {
  font-size: 14px;
  font-weight: 400;
  padding: 1px 12px;
}
.tag1 {
  background: rgba(92, 66, 255, 0.1);
  color: rgba(92, 66, 255, 1);
}

.tag2 {
  background: rgba(230, 135, 46, 0.15);
  color: rgba(230, 135, 46, 1);
}

.tag3 {
  background: rgba(16, 140, 255, 0.1);
  color: rgba(16, 140, 255, 1);
}
.tag4 {
  background: rgba(41, 204, 160, 0.1);
  color: rgba(41, 204, 160, 1);
}
.tag5 {
  background: rgba(242, 242, 242, 1);
  color: rgba(0, 0, 0, 0.65);
}
