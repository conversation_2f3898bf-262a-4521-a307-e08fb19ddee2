.describe {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  padding-bottom: 20px;
}

.group {
  .title {
    display: flex;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 12px;
    .line {
      flex-shrink: 0;
      width: 4px;
      height: 16px;
      background: rgba(42, 203, 160, 1);
      margin-right: 8px;
      margin-top: 3px;
    }
  }
}
// 终结弹窗
.finish-dialog {
  .el-form .el-form-item--default {
    margin-bottom: 16px !important;
  }
  .txt {
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    padding-bottom: 24px;
  }
}
