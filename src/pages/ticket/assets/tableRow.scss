.tableRow {
  margin-top: 24px;
}
.itemAdd {
  border: 1px solid #e6e6e8;
  border-top: none;
  height: 50px;
  line-height: 50px;
  padding-left: 20px;
  box-sizing: border-box;
}
.el-form-item.input7343 {
  margin-bottom: 0;
}

:deep(.el-table .el-form-item) {
  margin-bottom: 0 !important;
  .el-input__wrapper {
    // height: inherit !important;
    // line-height: inherit !important;
    height: 32px !important;
    line-height: 32px !important;
  }
}
:deep(.el-form .el-date-editor.el-input) {
  height: 32px !important;
  line-height: 32px !important;
}
.el-table-fixed-column--right {
  .el-button.is-link {
    color: #29cca0 !important;
    &:hover {
      color: var(--el-button-hover-link-text-color) !important;
    }
  }
}
:deep(.el-table .cell) {
  overflow: inherit;
}
.add-btn {
  .el-icon {
    margin-right: 4px;
  }
}
