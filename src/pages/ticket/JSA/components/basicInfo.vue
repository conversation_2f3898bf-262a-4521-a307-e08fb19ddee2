<template>
  <div class="info">
    <el-form
      ref="formRef"
      :rules="formRules"
      :model="formData"
      label-suffix=""
      label-position="right"
      :inline="true"
      class="w-full"
      label-width="110px"
    >
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="电站名称" prop="stationCode">
            <span v-if="isPageType">
              {{ formData.stationName || '--' }}
            </span>
            <StationSelect
              v-else
              v-model="formData.stationCode"
              v-model:label="formData.stationName"
              @change="(data: any) => (formData.stationName = data.stationName)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4"></el-col>

        <el-col :span="10" v-if="isPageType">
          <el-form-item label="关联工作票" prop="ticketWorkNo">
            <span>
              {{ formData.ticketWorkNo || '--' }}
            </span>
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="JSA票编号" prop="jsaTicketNo">
            <span v-if="isPageType">
              {{ formData.jsaTicketNo || '--' }}
            </span>
            <!-- <el-input
              v-else
              v-model="formData.jsaTicketNo"
              placeholder="保存后自动生成"
              :disabled="true"
            /> -->
            <el-select
              v-model="formData.jsaTicketNo"
              placeholder="保存后自动生成"
              :disabled="true"
              v-else
            >
              <template v-for="item in jsaTicketNoArr" :key="item.value">
                <el-option :label="item.label" :value="item.value" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4" v-if="isPageType"></el-col>

        <el-col :span="10">
          <el-form-item label="工作票签发人" prop="issuer">
            <span v-if="isPageType">
              {{ formData.issuer ? formData.issuer.split('_')[0] : '--' }}
            </span>

            <el-select
              v-model="formData.issuer"
              placeholder="请选择工作票签发人"
              clearable
              filterable
              v-else
            >
              <template v-for="item in IssuerArr" :key="item.ucUserId">
                <el-option :label="item.ucUserName" :value="item.ucUserId" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4" v-if="!isPageType"></el-col>

        <el-col :span="10">
          <el-form-item label="工作负责人" prop="head">
            <span v-if="isPageType">
              {{ formData.head ? formData.head.split('_')[0] : '--' }}
            </span>

            <el-select
              v-model="formData.head"
              placeholder="请选择工作负责人"
              clearable
              v-else
            >
              <template v-for="item in headArr" :key="item.ucUserId">
                <el-option :label="item.ucUserName" :value="item.ucUserId" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4" v-if="isPageType"></el-col>

        <el-col :span="10">
          <el-form-item label="工作部门" prop="workDepartment">
            <span v-if="isPageType">
              {{
                formData.workDepartment
                  ? formData.workDepartment.split('_')[0]
                  : '--'
              }}
            </span>

            <el-select
              v-model="formData.workDepartment"
              placeholder="请选择工作部门"
              clearable
              filterable
              @change="getTeamsGroupsList(formData.workDepartment)"
              v-else
            >
              <template v-for="item in workDepartmentArr" :key="item.value">
                <el-option :label="item.label" :value="item.value" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4" v-if="!isPageType"></el-col>

        <el-col :span="10">
          <el-form-item label="班组" prop="teamsGroupsName">
            <span v-if="isPageType">
              {{
                formData.teamsGroupsName
                  ? formData.teamsGroupsName.split('_')[0]
                  : '--'
              }}
            </span>

            <el-select
              v-model="formData.teamsGroupsName"
              placeholder="请选择班组"
              clearable
              v-else
            >
              <template
                v-for="item in teamsGroupsNameArr"
                :key="item.teamsGroupsId"
              >
                <el-option :label="item.label" :value="item.value" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" v-if="isPageType"></el-col>
        <el-col :span="10">
          <el-form-item label="班成员" prop="teamsGroupUser">
            <div v-if="isPageType" class="valueWord__">
              {{ formData.teamsGroupUser || '--' }}
            </div>
            <el-input
              v-model.trim="formData.teamsGroupUser"
              placeholder="请输入班成员"
              maxLength="64"
              clearable
              v-else
            />
          </el-form-item>
          <!-- <el-form-item label="班成员" prop="teamsGroupUser">
            <div v-if="isPageType" class="patorSps">
              {{
                formData.teamsGroupUser
                  .split(',')
                  .map((item: any) => {
                    return item.split('@')[0]
                  })
                  .join(',') || '--'
              }}
            </div>
            <div
              v-else
              :class="[isGetInfo ? 'tagBorder__' : 'redB_ tagBorder__']"
            >
              <div v-if="attendArr.length == 0" class="placeholeder__">
                请选择班成员
              </div>
              <el-tooltip
                v-for="(item, index) in attendArr.slice(0, 3)"
                v-else
                :key="index"
                effect="light"
                :content="item.employeeName"
                placement="top-start"
              >
                <span class="bcy-w"> {{ item.employeeName }}</span>
              </el-tooltip>
              <el-popover
                v-if="attendArr.length > 3"
                placement="top-start"
                trigger="hover"
                width="300px"
              >
                <div style="display: flex; flex-wrap: wrap">
                  <span
                    class="sps__"
                    v-for="(item, index) in attendArr.slice(
                      3,
                      attendArr.length
                    )"
                    :key="index"
                    >{{ item.employeeName }}</span
                  >
                </div>
                <template #reference>
                  <span>+{{ attendArr.length - 3 }}...</span>
                </template>
              </el-popover>
            </div>
            <div class="num__" v-if="attendArr.length > 0">
              班成员人数共{{ attendArr.length }}人
            </div>
          </el-form-item> -->
        </el-col>
        <el-col :span="4" v-if="!isPageType"></el-col>
        <!-- <el-col :span="4" v-if="!isPageType">
          <el-button
            type="primary"
            size="large"
            @click="addStaff()"
            v-if="!isPageType"
            ><el-icon><Plus /></el-icon>添加人员</el-button
          ></el-col
        > -->
        <el-col :span="10">
          <el-form-item label="班成员人数" prop="teamsUserNum">
            <div v-if="isPageType" class="patorSps">
              {{ formData.teamsUserNum || '--' }}
            </div>
            <el-input
              v-model.trim="formData.teamsUserNum"
              placeholder="请输入班成员人数"
              maxLength="2"
              clearable
              @input="() => limitGroupNum(formData.teamsUserNum)"
              v-else
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" v-if="isPageType"></el-col>
        <el-col :span="10">
          <el-form-item label="工作地点" prop="workAddress">
            <div v-if="isPageType" class="valueWord__">
              {{ formData.workAddress || '--' }}
            </div>

            <el-input
              v-model.trim="formData.workAddress"
              placeholder="请输入工作地点"
              maxLength="64"
              clearable
              v-else
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="工作内容"
            prop="workContent"
            class="trainingContente__ valueWord__"
          >
            <span v-if="isPageType">
              {{ formData.workContent || '--' }}
            </span>
            <el-input
              v-else
              v-model="formData.workContent"
              :autosize="{ minRows: 5, maxRows: 7 }"
              type="textarea"
              placeholder="请输入工作内容"
              maxlength="1024"
              :show-word-limit="true"
            /> </el-form-item
        ></el-col>
      </el-row>
    </el-form>

    <!-- 添加人员弹窗 -->
    <select-staff
      :is-show="isShow"
      :employeeIdArr="employeeIdArr"
      @uplate-data="uplateData"
      @close-dialog="isShow = false"
    ></select-staff>
  </div>
</template>
<script setup lang="ts">
import type { FormRules, FormInstance } from 'element-plus'
import StationSelect from '@/components/spic-station'
import selectStaff from '../../components/selectStaff.vue'
import { ticket as ticketApi } from '@/api/index.ts'
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'

const jsaTicketNoArr = ref<any[]>([]) // JSA票编号
const attendArr = ref<any[]>([]) // 班成员
const isPageType = inject<Ref<string>>('isPageType')
const employeeIdArr = ref([])
// 查找name||id
const workDepartmentArr = ref<any[]>([]) // 工作部门
const getVal = (arr: any, val: string, key: string, label: string) => {
  let str = ''
  arr.length &&
    arr.forEach((item: any) => {
      if (item[key] == val) {
        str = item[label]
      }
    })
  return str
}
// 表单
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  stationCode: '',
  stationName: '',
  jsaTicketNo: '保存后自动生成',
  ticketWorkNo: '--',
  issuer: '',
  head: '',
  workDepartment: '',
  teamsGroupsName: '',
  teamsGroupUser: '',
  workAddress: '',
  workContent: '',
  teamsUserNum: ''
})
const props = defineProps({
  JsaTicketDetailVO: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
watch(
  () => props.JsaTicketDetailVO,
  async (val: object) => {
    // 电站名称
    formData.value.stationCode = val.stationCode
    formData.value.stationName = val.stationName
    // JSA编号
    formData.value.jsaTicketNo = val.jsaTicketNo
    // 关联工作票
    formData.value.ticketWorkNo = val.ticketWorkNo
    // 工作票签发人
    formData.value.issuer = val.issuer
    // 工作票负责人
    formData.value.head = val.head
    // 工作部门
    formData.value.workDepartment = val.workDepartment
    // 班组
    getTeamsGroupsList(val.workDepartment, val.teamsGroupsName)
    // 班成员
    formData.value.teamsGroupUser = val.teamsGroupUser || ''
    // if (val.teamsGroupUser) {
    //   attendArr.value = val.teamsGroupUser.split(',').map((item: any) => {
    //     return {
    //       employeeName: item.split('@')[0],
    //       id: item.split('@')[1]
    //     }
    //   })

    //   let teamsGroupUser = val.teamsGroupUser.split(',').map((item: any) => {
    //     return item.split('@')[0]
    //   })
    //   formData.value.teamsGroupUser = teamsGroupUser.join(',')
    //   employeeIdArr.value = val.teamsGroupUser.split(',').map((item: any) => {
    //     return item.split('@')[1]
    //   })
    // } else {
    //   attendArr.value = []
    //   formData.value.teamsGroupUser = ''
    //   employeeIdArr.value = []
    // }
    // 班成员人数
    formData.value.teamsUserNum = val.teamsUserNum
    // 工作地点
    formData.value.workAddress = val.workAddress
    // 工作内容
    formData.value.workContent = val.workContent
  },
  {
    deep: true
  }
)
const limitGroupNum = (val: any) => {
  if (val === '0') {
    formData.value.teamsUserNum = val
  } else {
    formData.value.teamsUserNum = String(val).replace(/^(0)|\D/g, '') || ''
  }
}
const formRules = reactive<FormRules<Record<string, any>>>({
  stationCode: [
    { required: true, message: '请选择电站名称', trigger: 'change' }
  ],
  jsaTicketNo: [
    { required: true, message: '请选择JSA票编号', trigger: 'change' }
  ],
  ticketWorkNo: [
    { required: true, message: '请输入关联工作票', trigger: 'change' }
  ],
  issuer: [
    { required: true, message: '请选择工作票签发人', trigger: 'change' }
  ],
  head: [{ required: true, message: '请选择工作负责人', trigger: 'change' }],
  workDepartment: [
    { required: true, message: '请选择工作部门', trigger: 'change' }
  ],
  teamsGroupsName: [
    { required: true, message: '请选择班组', trigger: 'change' }
  ],
  teamsGroupUser: [
    { required: true, message: '请输入班成员', trigger: 'blur' }
  ],
  teamsUserNum: [
    { required: true, message: '请输入班成员人数', trigger: 'blur' }
  ],
  workAddress: [{ required: true, message: '请输入工作地点', trigger: 'blur' }],
  workContent: [{ required: true, message: '请输入工作内容', trigger: 'blur' }]
})

// 添加班成员
const isShow = ref(false)
const isGetInfo = ref(true)
// const addStaff = () => {
//   isShow.value = true
//   employeeIdArr.value = attendArr.value.map((item: any) => {
//     return item.id
//   })
// }
// watch(
//   () => attendArr.value,
//   (val) => {
//     if (val.length) {
//       let arr: any = []
//       val.forEach((item: any) => {
//         arr.push(item.employeeName + '@' + item.id)
//       })
//       formData.value.teamsGroupUser = arr.join(',')
//       formRef.value?.validateField('teamsGroupUser')
//     } else {
//       formData.value.teamsGroupUser = ''
//     }
//   },
//   {
//     deep: true
//   }
// )
const uplateData = (arr: Obj[]) => {
  attendArr.value = arr
  isGetInfo.value = arr.length ? true : false

  employeeIdArr.value = arr.map((item: any) => {
    return item.id
  })
}
const IssuerArr = ref<any[]>([]) // 工作票签发人
const headArr = ref<any[]>([]) // 工作负责人
const teamsGroupsNameArr = ref<any[]>([]) // 班组
const getRoleUserList = async () => {
  try {
    let { data } = await ticketApi.getRoleUserList({})
    // JS-0001  工作票签发人  JS-0005  工作负责人
    IssuerArr.value = data['JS-0001'].map((item: any) => {
      return {
        ucUserId: item.ucUserName + '_' + item.ucUserId,
        ucUserName: item.ucUserName
      }
    })
    headArr.value = data['JS-0005'].map((item: any) => {
      return {
        ucUserId: item.ucUserName + '_' + item.ucUserId,
        ucUserName: item.ucUserName
      }
    })
  } catch (e) {
    IssuerArr.value = []
    headArr.value = []
  }
}
const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    workDepartmentArr.value = data.data.map((item: any) => {
      return {
        label: item.companyName,
        value: item.companyName + '_' + item.id
      }
    })
  } catch (e: any) {
    workDepartmentArr.value = []
  }
}
const getTeamsGroupsList = async (val: any, sty = '') => {
  try {
    const { data } = await ticketApi.getTeamsGroupByOperatorsId(
      {
        operatorsId: val ? val.split('_')[1] : ''
      },
      false
    )
    teamsGroupsNameArr.value = data.data.map((item: any) => {
      return {
        label: item.teamsGroupsName,
        value: item.teamsGroupsName + '_' + item.id
      }
    })
    formData.value.teamsGroupsName = sty ? sty : ''
  } catch (e: any) {
    teamsGroupsNameArr.value = []
  }
}
// 校验
const emit = defineEmits(['getFromData'])
const submitForm = async () => {
  isGetInfo.value = formData.value.teamsGroupUser ? true : false
  await formRef.value?.validate(async (valid) => {
    isValidate(valid, 'validate')
  })
}
const isValidate = (valid = false, type = 'noValidate') => {
  emit('getFromData', {
    valid,
    type,
    data: {
      ...formData.value
    }
  })
}
defineExpose({
  submitForm,
  isValidate
})
onMounted(() => {
  getRoleUserList()
  getCompany()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style lang="scss">
.sps__ {
  border-radius: 2px;
  background: #f6f8fa;
  font-size: 14px;
  height: 30px;
  line-height: 30px;
  padding: 0 8px;
  color: #000;
  margin: 0 10px 10px 0;
}
.redB_ {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}
.tagBorder__ {
  width: 100%;
  height: 40px;
  line-height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px 12px;
  box-sizing: border-box;
  display: flex;
  span {
    border-radius: 2px;
    background: #f6f8fa;
    font-size: 14px;
    height: 30px;
    line-height: 30px;
    padding: 0 8px;
    color: #000;
    margin-right: 10px;
  }
  .placeholeder__ {
    color: rgba(0, 0, 0, 0.25) !important;
    line-height: 28px !important;
  }
}
.num__ {
  color: rgba(230, 135, 46, 1) !important;
  line-height: 28px !important;
  font-size: 10px;
}
.waringInfoBas__ {
  padding: 0 0 20px 69px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  color: #ff9900;
  font-size: 12px;
  .el-icon {
    margin: 0 4px 0 0;
    font-size: 16px;
  }
}
.cardPhotoClass__.el-form-item--default {
  margin-bottom: 10px !important;
}
.trainingContente__ {
  .el-textarea.el-input--default {
    position: relative;
    .el-input__count {
      position: absolute;
      bottom: 8px !important;
      right: 26px !important;
    }
  }
}
.patorSps {
  line-height: 40px !important;
  width: 100% !important;
  vertical-align: middle;
  word-wrap: break-word;
  position: relative;
  overflow: hidden;
  word-break: break-all;
}
.bcy-w {
  width: 60px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 2px;
  background: #f6f8fa;
  font-size: 14px;
  height: 30px;
  line-height: 30px;
  padding: 0 8px;
  color: #000;
}
.valueWord__ {
  vertical-align: middle;
  word-wrap: break-word;
  position: relative;
  line-height: 40px;
  word-break: break-all;
}
</style>
