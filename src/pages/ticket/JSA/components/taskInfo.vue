<template>
  <el-form ref="ruleFormRef" :key="formKey" :model="ruleForm" :rules="rules">
    <!-- 作业类别 -->
    <div class="itembox">
      <div class="title">
        <span>*</span>
        <span>作业类别（请勾选或在其他类填写）</span>
      </div>
      <div class="checklbox">
        <el-checkbox-group v-model="homeworkTyep" :disabled="isDisabled">
          <el-checkbox
            v-for="item in list1"
            :label="item.value"
            :key="item.value"
            class="checkbox__"
          >
            {{ item.name }}
            <el-form-item
              prop="value1"
              v-if="item.value == '15@' && homeworkTyep.includes('15@')"
              class="lastItem"
            >
              <el-input
                v-model.trim="ruleForm.value1"
                placeholder="请输入"
                clearable
                :disabled="isDisabled"
                maxLength="64"
              />
            </el-form-item>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <!-- 作业环境 -->
    <div class="itembox">
      <div class="title">
        <span>*</span>
        <span>作业环境（请勾选或在其他类填写）</span>
      </div>
      <div class="checklbox">
        <el-checkbox-group v-model="homeworkEnvironment" :disabled="isDisabled">
          <el-checkbox
            v-for="item in list2"
            :label="item.value"
            :key="item.value"
            class="checkbox__"
          >
            {{ item.name }}
            <el-form-item
              prop="value2"
              v-if="item.value == '12@' && homeworkEnvironment.includes('12@')"
              class="lastItem"
            >
              <el-input
                v-model.trim="ruleForm.value2"
                placeholder="请输入"
                clearable
                :disabled="isDisabled"
                maxLength="64"
              />
            </el-form-item>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>

    <!-- 作业风险 -->
    <div class="itembox">
      <div class="title">
        <span>*</span>
        <span>作业风险（请勾选或在其他类填写）</span>
      </div>
      <div class="checklbox">
        <el-checkbox-group v-model="homeworkRisk" :disabled="isDisabled">
          <el-checkbox
            v-for="item in list3"
            :label="item.value"
            :key="item.value"
            class="checkbox__"
          >
            {{ item.name }}

            <el-form-item
              prop="value3"
              v-if="item.value == '15@' && homeworkRisk.includes('15@')"
              class="lastItem"
            >
              <el-input
                v-model.trim="ruleForm.value3"
                placeholder="请输入"
                clearable
                :disabled="isDisabled"
                maxLength="64"
              />
            </el-form-item>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
  </el-form>
</template>
<script setup lang="ts">
import useEnums from '../hooks/useEnums'
import type { FormInstance, FormRules } from 'element-plus'
interface RuleForm {
  value1: string
  value2: string
  value3: string
}
const formKey = ref(0)
formKey.value = Date.now()
const route = useRoute()
const isDisabled = computed(() => {
  return route.params && route.params.pageStatus == 'look'
})
const homeworkTyep = ref([]) // 作业类别
const homeworkEnvironment = ref([]) // 作业环境
const homeworkRisk = ref([]) // 作业风险
const { list1, list2, list3 } = useEnums()

const props = defineProps({
  JsaTicketDetailVO: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
watch(
  () => props.JsaTicketDetailVO,
  async (val: object) => {
    if (val.homeworkTyep.length) {
      let strArr = [],
        arr = []
      val.homeworkTyep.split('_').forEach((item: any) => {
        if (item.indexOf('15@') != -1) {
          strArr = ['15@', item.split('@')[1]]
        } else {
          arr.push(Number(item))
        }
      })
      if (strArr.length) {
        arr.push('15@')
        ruleForm.value.value1 = strArr[1]
        homeworkTyep.value = arr
      } else {
        homeworkTyep.value = arr
      }
    } else {
      homeworkTyep.value = []
    }

    if (val.homeworkEnvironment.length) {
      let strArr = [],
        arr = []
      val.homeworkEnvironment.split('_').forEach((item: any) => {
        if (item.indexOf('12@') != -1) {
          strArr = ['12@', item.split('@')[1]]
        } else {
          arr.push(Number(item))
        }
      })
      if (strArr.length) {
        arr.push('12@')
        ruleForm.value.value2 = strArr[1]
        homeworkEnvironment.value = arr
      } else {
        homeworkEnvironment.value = arr
      }
    } else {
      homeworkEnvironment.value = []
    }

    if (val.homeworkRisk.length) {
      let strArr = [],
        arr = []
      val.homeworkRisk.split('_').forEach((item: any) => {
        if (item.indexOf('15@') != -1) {
          strArr = ['15@', item.split('@')[1]]
        } else {
          arr.push(Number(item))
        }
      })
      if (strArr.length) {
        arr.push('15@')
        ruleForm.value.value3 = strArr[1]
        homeworkRisk.value = arr
      } else {
        homeworkRisk.value = arr
      }
    } else {
      homeworkRisk.value = []
    }
  }
)
// 表单
const ruleFormRef = ref<FormInstance>()
const ruleForm = ref<RuleForm>({
  value1: '',
  value2: '',
  value3: ''
})
const rules = reactive<FormRules<RuleForm>>({
  value1: [{ required: true, message: '请输入其他类', trigger: 'blur' }],
  value2: [{ required: true, message: '请输入其他类', trigger: 'blur' }],
  value3: [{ required: true, message: '请输入其他类', trigger: 'blur' }]
})

// 校验
const emit = defineEmits(['getCheckBoxData'])
const submitCheckbox = async () => {
  await ruleFormRef.value?.validate((valid: boolean) => {
    isValidate(valid, 'validate')
  })
}
const isValidate = (valid = false, type = 'noValidate') => {
  let homeworkTyepArr: any = []
  if (homeworkTyep.value?.includes('15@') && ruleForm.value.value1) {
    homeworkTyepArr = homeworkTyep.value.filter((item) => {
      return item != '15@'
    })
    homeworkTyepArr.push('15@' + ruleForm.value.value1)
  } else {
    homeworkTyepArr = homeworkTyep.value
  }

  let homeworkEnvironmentArr: any = []
  if (homeworkEnvironment.value?.includes('12@') && ruleForm.value.value2) {
    homeworkEnvironmentArr = homeworkEnvironment.value.filter((item) => {
      return item != '12@'
    })
    homeworkEnvironmentArr.push('12@' + ruleForm.value.value2)
  } else {
    homeworkEnvironmentArr = homeworkEnvironment.value
  }

  let homeworkRiskArr: any = []
  if (homeworkRisk.value?.includes('15@') && ruleForm.value.value3) {
    homeworkRiskArr = homeworkRisk.value.filter((item) => {
      return item != '15@'
    })
    homeworkRiskArr.push('15@' + ruleForm.value.value3)
  } else {
    homeworkRiskArr = homeworkRisk.value
  }

  emit('getCheckBoxData', {
    valid,
    type,
    data: {
      homeworkTyep: homeworkTyepArr.length ? homeworkTyepArr.join('_') : '',
      homeworkEnvironment: homeworkEnvironmentArr.length
        ? homeworkEnvironmentArr.join('_')
        : '',
      homeworkRisk: homeworkRiskArr.length ? homeworkRiskArr.join('_') : ''
    }
  })
}
defineExpose({
  submitCheckbox,
  isValidate
})
</script>
<style scoped src="../assets/index.scss"></style>
<style scoped lang="scss">
.itembox {
  margin-top: 24px;
  .checklbox {
    margin-top: 12px;
    background: rgba(246, 248, 250, 1);
    padding: 20px 20px 0;
    box-sizing: border-box;
    border-radius: 4px;
  }

  .checklbox {
    .el-checkbox-group {
      .el-checkbox {
        margin-bottom: 20px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        height: 22px !important;
        :deep(.el-checkbox__label) {
          display: flex;
          align-items: center;
          .lastItem.el-form-item--default {
            margin: 0 0 0 10px !important;
          }
        }
      }
      :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
        color: rgba(0, 0, 0, 0.85);
      }
      :deep(.el-checkbox__input.is-disabled + span.el-checkbox__label) {
        color: rgba(0, 0, 0, 0.85);
      }
      :deep(
          .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after
        ) {
        border-color: #fff;
      }

      :deep(.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner) {
        background: #ccc;
      }
      :deep(.el-input.is-disabled .el-input__wrapper) {
        background: rgba(246, 248, 250, 1) !important;
      }
    }
  }
}
</style>
