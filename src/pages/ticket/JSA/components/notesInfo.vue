<template>
  <el-form
    ref="formRef"
    :rules="formRules"
    :model="formData"
    label-suffix=""
    label-position="right"
    :inline="true"
    class="w-full"
  >
    <el-row :gutter="24"
      ><el-col :span="24">
        <el-form-item label="备注" prop="name1" class="trainingContente__">
          <span v-if="route.params.type == 'add'">
            {{ formData.name1 }}
          </span>
          <el-input
            v-else
            v-model="formData.name1"
            :autosize="{ minRows: 5, maxRows: 7 }"
            type="textarea"
            placeholder="请输入备注内容"
            maxlength="100"
            :show-word-limit="true"
          /> </el-form-item
      ></el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import type { FormRules, FormInstance } from 'element-plus'
const route = useRoute()
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  name1: ''
})
const formRules = reactive<FormRules<Record<string, any>>>({
  name1: [{ required: true, message: '请输入备注内容', trigger: 'blur' }]
}) // 校验
const onSubmit = async () => {
  await formRef.value?.validate((valid: boolean) => {
    if (valid) {
      console.log(122)
    }
  })
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style lang="scss"></style>
