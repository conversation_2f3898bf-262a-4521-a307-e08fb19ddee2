<template>
  <el-form ref="ruleFormRef" :key="formKey" :model="ruleForm" :rules="rules">
    <!-- (1)确认与该项作业相关的现场处置方案，填写现场处置方案名称及编号： -->
    <div class="itembox">
      <div class="title">
        <span>*</span>
        <span
          >(1)确认与该项作业相关的现场处置方案，填写现场处置方案名称及编号：</span
        >
      </div>
      <el-form-item
        label=""
        prop="dispositionSchemeNo"
        class="trainingContente__ mt2"
      >
        <div v-if="isDisabled" class="valueWord__">
          {{ ruleForm.dispositionSchemeNo }}
        </div>
        <el-input
          v-else
          v-model="ruleForm.dispositionSchemeNo"
          :autosize="{ minRows: 5, maxRows: 7 }"
          type="textarea"
          placeholder="请输入内容"
          maxlength="1024"
          :show-word-limit="true"
        />
      </el-form-item>
    </div>
    <!-- (2)依据该现场处置方案进行交底： -->
    <div class="itembox">
      <div class="title">
        <span>*</span>
        <span>(2)依据该现场处置方案进行交底：</span>
      </div>
    </div>
    <el-form-item
      label=""
      prop="dispositionSchemeCommunication"
      class="trainingContente__ mt2"
    >
      <div v-if="isDisabled" class="valueWord__">
        {{ ruleForm.dispositionSchemeCommunication }}
      </div>
      <el-input
        v-else
        v-model="ruleForm.dispositionSchemeCommunication"
        :autosize="{ minRows: 5, maxRows: 7 }"
        type="textarea"
        placeholder="请输入内容"
        maxlength="1024"
        :show-word-limit="true"
      />
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
interface RuleForm {
  dispositionSchemeNo: string
  dispositionSchemeCommunication: string
}
const props = defineProps({
  JsaTicketDetailVO: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const route = useRoute()
const isDisabled = computed(() => {
  return route.params && route.params.pageStatus == 'look'
})
const ruleFormRef = ref<FormInstance>()
const formKey = ref(0)
formKey.value = Date.now()
const ruleForm = ref<RuleForm>({
  dispositionSchemeNo: '',
  dispositionSchemeCommunication: ''
})
watch(
  () => props.JsaTicketDetailVO,
  async (val: object) => {
    ruleForm.value.dispositionSchemeNo = val.dispositionSchemeNo
    ruleForm.value.dispositionSchemeCommunication =
      val.dispositionSchemeCommunication
  }
)
const rules = reactive<FormRules<RuleForm>>({
  dispositionSchemeNo: [
    { required: true, message: '请输入内容', trigger: 'blur' }
  ],
  dispositionSchemeCommunication: [
    { required: true, message: '请输入内容', trigger: 'blur' }
  ]
})
// 校验
const emit = defineEmits(['getTextareaData'])
const submitTextarea = async () => {
  await ruleFormRef.value?.validate((valid: boolean) => {
    isValidate(valid, 'validate')
  })
}
const isValidate = (valid = false, type = 'noValidate') => {
  emit('getTextareaData', {
    valid,
    type,
    data: {
      ...ruleForm.value
    }
  })
}
defineExpose({
  submitTextarea,
  isValidate
})
</script>

<style scoped lang="scss">
.itembox {
  margin-top: 24px;
  .title {
    display: flex;
    align-items: center;
    span:first-child {
      font-size: 14px;
      color: rgba(255, 0, 0, 1);
    }
    span:last-child {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
.valueWord__ {
  vertical-align: middle;
  word-wrap: break-word;
  position: relative;
  // overflow: hidden;
  line-height: 40px;
  word-wrap: break-word;
}
</style>
