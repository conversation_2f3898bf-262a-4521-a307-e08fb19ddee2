<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="60px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>JSA票列表</p>
        <el-button type="primary" @click="handleAdd">
          <el-icon> <Plus /> </el-icon> 新建JSA票
        </el-button>
      </div>
      <card-pagination
        title-key="jsaTicketNo"
        :total="listTotal"
        :loading="tableLoading"
        :finished="finished"
        layout="total, prev, pager, next, jumper"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :data="listData"
        :columns="columns"
        :card-label-width="`70`"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
        @click-card="clickCard"
      >
        <template #status="{ row }">
          <el-tag v-if="row.status == 1" class="tag tag1">草稿</el-tag>
          <el-tag v-if="row.status == 2" class="tag tag2">生效中</el-tag>
          <el-tag v-if="row.status == 3" class="tag tag4">已终结</el-tag>
          <el-tag v-if="row.status == 4" class="tag tag5">已作废</el-tag>
        </template>

        <template #footer="{ row }">
          <el-button type="primary">查看详情</el-button>
        </template>
      </card-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ticket as ticketApi } from '@/api/index.ts'
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'
import CardPagination from '@/components/card-pagination.vue'
import useCompanyCodeStore from '@/store/companyCode'

const companyCode = useCompanyCodeStore()
const router = useRouter()

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getListData()
  }
)
onMounted(async () => {
  getCompany()
  companyCode.data && (await getListData())
  startWatch = true
})

// 搜索
const searchData = ref({
  jsaTicketNo: '',
  workDepartment: '',
  stationCode: '',
  pageNum: 1,
  pageSize: 6
})
const columns = [
  {
    prop: 'stationName',
    label: '电站名称'
  },
  {
    prop: 'workDepartment',
    label: '所属公司'
  },
  {
    prop: 'head',
    label: '负责人'
  },
  {
    prop: 'createTime',
    label: '创建时间'
  }
]
const searchProps = ref([
  {
    prop: 'jsaTicketNo',
    label: 'JSA票编码',
    placeholder: '请输入JSA票编码',
    span: 8,
    width: '80px'
  },
  {
    type: 'select',
    label: '所属公司',
    prop: 'workDepartment',
    span: 7,
    width: '80px',
    options: [],
    filterable: true
  },
  {
    label: '电站名称',
    prop: 'stationCode',
    type: 'stationSelect',
    width: '86px',
    placeholder: '请选择电站',
    span: 8
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 6
  }
  await getListData()
}

const tableLoading = ref(false)
const finished = ref(false)
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const getListData = async () => {
  try {
    let { data } = await ticketApi.getjsaTicketList(
      { ...searchData.value },
      true
    )
    listTotal.value = data.data.total

    data.data.records.length &&
      data.data.records.forEach((item: any) => {
        ;(item.head = item.head ? item.head.split('_')[0] : ''),
          (item.workDepartment = item.workDepartment
            ? item.workDepartment.split('_')[0]
            : '')
      })
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  } finally {
    finished.value = true
  }
}
const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    searchProps.value[1].options = data.data.map((item: any) => {
      return {
        label: item.companyName || '',
        value: item.companyName || ''
      }
    })
  } catch (e: any) {
    searchProps.value[1].options = []
  }
}
const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getListData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  await getListData()
}
// 操作
const handleAdd = () => {
  router.push('/ticket/JSA/JSAInfo/add')
}
const clickCard = async (item: any) => {
  // 草稿
  if (item.status == 1) {
    try {
      const { data } = await ticketApi.canUpdate(
        {
          id: item.id,
          type: 2
        },
        false
      )
      if (data.data) {
        router.push('/ticket/JSA/JSAInfo/add/' + item.id)
      } else {
        router.push('/ticket/JSA/JSAInfo/look/' + item.id)
      }
    } catch (e: any) {
      console.log(e)
    }
  } else {
    router.push('/ticket/JSA/JSAInfo/look/' + item.id)
  }
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped lang="scss"></style>
