.info-base {
  .info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 24px;
    .info-item {
      padding-bottom: 24px;
    }
    .label {
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
.task-base {
  padding: 24px 24px 12px 24px;
  margin: 0 24px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  position: relative;
  overflow: hidden;
}
.handle-info {
  padding: 24px 24px 12px 24px;
  margin: 0 24px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  position: relative;
  overflow: hidden;
}
.supply-base {
  padding: 24px 24px 12px 24px;
  margin: 16px 24px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  position: relative;
  overflow: hidden;
}
.notes-base {
  padding: 24px 24px 12px 24px;
  margin: 0 24px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  position: relative;
  overflow: hidden;
}
.plug-info {
  padding: 24px 24px 12px 24px;
  margin: 0 24px 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  position: relative;
  overflow: hidden;
}
.title {
  display: flex;
  align-items: center;
  span:first-child {
    font-size: 14px;
    color: rgba(255, 0, 0, 1);
  }
  span:last-child {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }
}
.operate_ {
  margin-bottom: 24px;
  margin-bottom: 0;
  padding-left: 0;
  p {
    display: flex;
    align-items: center;
    span:first-child {
      width: 4px;
      height: 16px;
      background-color: rgba(42, 203, 160, 1);
      margin-right: 8px;
    }
    span:last-child {
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: normal;
    }
  }
}

.page-main,
.info-tab {
  .operate {
    padding-left: 0;
    p {
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      line-height: normal;
    }
  }
}

.info-tab {
  .operate {
    margin-bottom: 24px;
    .el-icon {
      margin-right: 4px;
    }
  }
}
.info-tab-height-show {
  height: calc(100vh - 292px);
}
.info-tab-height-edit {
  height: calc(100vh - 384px);
}

.page-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
  line-height: 64px;
  margin: 24px;
  height: 64px;
  background: #fff;
  border-radius: 8px;
  align-content: center;
  flex-wrap: wrap;
  box-shadow: 0 0 16px 0 rgb(21 102 80 / 10%);
  & > .el-button + .el-button {
    margin-left: 16px !important;
  }
  & > .el-button {
    min-width: 72px !important;
    height: 40px !important;
  }
}
