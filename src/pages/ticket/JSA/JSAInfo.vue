<template>
  <div class="page-operate">
    <div class="operate-title">
      {{ pageName }}
      <template v-if="route.params && route.params.pageStatus == 'look'">
        <el-tag v-if="JsaTicketDetailVO.status == 1" class="tag tag1"
          >草稿</el-tag
        >
        <el-tag v-if="JsaTicketDetailVO.status == 2" class="tag tag2"
          >生效中</el-tag
        >
        <el-tag v-if="JsaTicketDetailVO.status == 3" class="tag tag4"
          >已终结</el-tag
        >
        <el-tag v-if="JsaTicketDetailVO.status == 4" class="tag tag5"
          >已作废</el-tag
        >
      </template>
    </div>
  </div>
  <div v-if="route.params && route.params.pageStatus == 'look'" class="btns">
    <el-button
      type="primary"
      v-preventReClick="1000"
      @click="handleSupply"
      v-if="JsaTicketDetailVO.status == 2"
      >补充信息</el-button
    >
    <!-- <el-button
      type="primary"
      v-preventReClick="1000"
      @click="voidItem"
      v-if="JsaTicketDetailVO.status == 2"
      >终结JSA票</el-button
    > -->
    <el-button
      type="primary"
      v-preventReClick="1000"
      @click="revokeTicket"
      v-if="JsaTicketDetailVO.status == 2"
      >作废JSA票</el-button
    >
    <el-button
      type="primary"
      v-preventReClick="1000"
      @click="dowloadBtn"
      v-if="
        (JsaTicketDetailVO.status == 4 && JsaTicketDetailVO.jsaObsoleteFlage) ||
        JsaTicketDetailVO.status == 3
      "
      >导出JSA票</el-button
    >
    <el-button
      type="primary"
      v-preventReClick="1000"
      @click="uploadBtn"
      v-if="JsaTicketDetailVO.status == 4 || JsaTicketDetailVO.status == 3"
      >上传原件</el-button
    >
  </div>
  <!-- 基础信息 -->
  <div class="info-base">
    <div class="operate_">
      <p><span></span><span>基础信息</span></p>
    </div>
    <basicInfo
      ref="basicInfoRef"
      @getFromData="getFromData"
      :JsaTicketDetailVO="JsaTicketDetailVO"
    ></basicInfo>
  </div>
  <!-- 作业类别&&作业环境&&作业风险 -->
  <div class="task-base">
    <div class="operate_">
      <p><span></span><span>危害辨识及控制</span></p>
    </div>
    <task-info
      ref="taskInfoRef"
      @getCheckBoxData="getCheckBoxData"
      :JsaTicketDetailVO="JsaTicketDetailVO"
    ></task-info>

    <div
      class="tableRow"
      v-if="route.params && route.params.pageStatus == 'look'"
    >
      <vis-table-pagination
        :columns="homeworkEnvironmentListColumnsLook"
        :data="homeworkEnvironmentList"
        border
        background
        class="vis-table-pagination table_Row_"
        :default-border="true"
        :show-overflow-tooltip="true"
        height="auto"
      >
        <!-- 证明照片 -->
        <template #provePhoto="{ row }">
          <span v-if="row.provePhoto.length == 0">--</span>
          <SpicUpload
            :key="uploadKey"
            v-model="row.provePhoto"
            type="image"
            :limit="5"
            :disabled="true"
            v-else
          />
        </template>
        <!-- 执行情况 -->
        <template #executeStatue="{ row }">
          {{
            row.executeStatue == 1
              ? '未执行'
              : row.executeStatue == 2
              ? '已执行'
              : '--'
          }}
        </template>
        <!-- 执行人 -->
        <template #executeUser="{ row }">
          {{ row.executeUser || '--' }}
        </template>
      </vis-table-pagination>
    </div>
    <table-info
      ref="tableInfoRef1"
      :columns="homeworkEnvironmentListColumns"
      :tableData="homeworkEnvironmentList"
      @getTabelData="getTabelData1"
      @deleBtn="deleBtn1"
      :key="tableInfoKey"
      :maxlength="64"
      v-else
    ></table-info>

    <div
      class="tableRow"
      v-if="route.params && route.params.pageStatus == 'look'"
    >
      <vis-table-pagination
        :columns="homeworkStepsListColumnsLook"
        :data="homeworkStepsList"
        border
        background
        class="vis-table-pagination table_Row_"
        :default-border="true"
        :show-overflow-tooltip="true"
        height="auto"
      >
        <!-- 证明照片 -->
        <template #provePhoto="{ row }">
          <span v-if="row.provePhoto.length == 0">--</span>
          <SpicUpload
            :key="uploadKey"
            v-model="row.provePhoto"
            type="image"
            :limit="5"
            :disabled="true"
            v-else
          />
        </template>
        <!-- 执行情况 -->
        <template #executeStatue="{ row }">
          {{
            row.executeStatue == 1
              ? '未执行'
              : row.executeStatue == 2
              ? '已执行'
              : '--'
          }}
        </template>
        <!-- 执行人 -->
        <template #executeUser="{ row }">
          {{ row.executeUser || '--' }}
        </template>
      </vis-table-pagination>
    </div>
    <table-info
      ref="tableInfoRef2"
      :columns="homeworkStepsListColumns"
      :tableData="homeworkStepsList"
      @getTabelData="getTabelData2"
      @deleBtn="deleBtn2"
      :key="tableInfoKey"
      :maxlength="64"
      v-else
    ></table-info>
  </div>
  <!-- 应急处置 -->
  <div class="handle-info">
    <div class="operate_">
      <p><span></span><span>应急处置（结合现场处置方案，进行交底）</span></p>
    </div>
    <handle-info
      ref="handleInfoRef"
      @getTextareaData="getTextareaData"
      :JsaTicketDetailVO="JsaTicketDetailVO"
    ></handle-info>
  </div>

  <!-- ============= 补充信息 ============= -->
  <div
    class="plug-info"
    v-if="route.params && route.params.pageStatus == 'look'"
  >
    <div class="title">
      <span>*</span>
      <span
        >经审核确认，工作票上所列的安全措施已正确执行，作业风险控制卡（JSA票）危害辨识与风险分析正确，所采取的风险预防和控制措施完善。</span
      >
    </div>
    <el-row :gutter="24" style="margin-top: 16px">
      <el-col :span="1"></el-col>
      <el-col :span="10">
        <el-form-item label="工作负责人签名">
          <span>{{ supplyInfo.head || '--' }}</span>
        </el-form-item>
      </el-col>
      <!-- <el-col :span="4"></el-col> -->
      <el-col :span="10">
        <el-form-item label="工作票签发人签名">
          <span>{{ supplyInfo.issuer || '--' }}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 班组成员安全交底-表格 -->
    <div class="title">
      <span>*</span>
      <span
        >班组成员安全交底（该项从开工第一天到工作结束，每天交底，可附页）。已清楚工作内容、工作风险，了解安全措施、注意事项和应急处理。</span
      >
    </div>

    <div
      class="tableRow"
      v-if="route.params && route.params.pageStatus == 'look'"
    >
      <vis-table-pagination
        :columns="tableColumnsLook"
        :data="jsaSupplementList"
        border
        background
        class="vis-table-pagination table_Row_"
        :default-border="true"
        :show-overflow-tooltip="true"
        height="auto"
      >
      </vis-table-pagination>
    </div>
    <table-info
      ref="tableInfoRef"
      :columns="tableColumns"
      :tableData="jsaSupplementList"
      :key="tableInfoKey"
      :maxlength="64"
      v-else
    ></table-info>
    <!-- 备注 -->
    <div class="title" style="margin-top: 32px">
      <span>*</span>
      <span
        >备注（如工作负责人变更，班组成员变更在工作票已经填写，不用再在此处填写）。</span
      >
    </div>
    <el-row :gutter="24" style="margin-top: 16px">
      <el-col :span="1"></el-col>
      <el-col :span="23">
        <el-form-item label="备注">
          <div>{{ supplyInfo.remark || '--' }}</div>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 工作票盖章文件 -->
    <div class="title">
      <span>*</span>
      <span>工作票盖章文件</span>
    </div>
    <el-row :gutter="24" style="margin-top: 16px">
      <el-col :span="1"></el-col>
      <el-col :span="23">
        <el-form-item label="文件">
          <span v-if="supplyInfo.stampFile == ''">--</span>
          <SpicUpload
            v-else
            :key="SpicUploadKey"
            v-model="supplyInfo.stampFile"
            :fileExt="fileExt"
            type="file"
            :limit="20"
            :uploadDisplay="true"
            :handleRemoveFn="handleRemoveFn"
            disabled
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
  <!-- ============= 补充信息 ============= -->

  <!-- 底部按钮 -->
  <div
    class="page-footer"
    v-if="!(route.params && route.params.pageStatus == 'look')"
  >
    <el-button plain @click="onCancel">返回</el-button>
    <el-button
      type="primary"
      @click="onCgSumbit"
      v-preventReClick="1000"
      v-if="
        (route.params && route.params.pageStatus == 'add') ||
        JsaTicketDetailVO.status == 1
      "
      >保存草稿</el-button
    >
    <el-button
      type="primary"
      @click="onApproval"
      v-preventReClick="1000"
      v-if="
        (route.params && route.params.pageStatus == 'add') ||
        JsaTicketDetailVO.status == 1
      "
      >提交</el-button
    >
    <el-button
      type="primary"
      v-preventReClick="1000"
      @click="revokeTicket"
      v-if="route.params && route.params.pageStatus == 'add' && route.params.id"
    >
      作废JSA票
    </el-button>
  </div>
  <!-- 上传原件 -->
  <upload
    :isShow="isShow"
    :stampFile="stampFileArr"
    @closeDialog="isShow = false"
    @updateList="updateList"
  ></upload>
  <!-- 废除jsa票 -->
  <revokeTicketDialog
    :is-show="showRevokeTicket"
    tid="'null'"
    type="2"
    @close-dialog="showRevokeTicket = false"
    @uplateData="uplateData"
  ></revokeTicketDialog>
</template>
<script setup lang="ts">
import useTableColumns from './hooks/useTableColumns'
import basicInfo from './components/basicInfo.vue'
import taskInfo from './components/taskInfo.vue'
import tableInfo from '../components/tableRow.vue'
import handleInfo from './components/handleInfo.vue'
import SpicUpload from '@/components/spic-upload'
import upload from '../components/upload.vue'
import visTablePagination from '@/components/table-pagination.vue'
import revokeTicketDialog from '../components/revokeTicketDialog.vue'
import { ticket as ticketApi } from '@/api/index.ts'
const {
  homeworkEnvironmentListColumns,
  homeworkEnvironmentListColumnsLook,
  homeworkStepsListColumns,
  homeworkStepsListColumnsLook,
  tableColumnsLook,
  tableColumns
} = useTableColumns()

const stampFileArr = ref([])
const route = useRoute()
const router = useRouter()
const fileExt = ref(['jpg', 'png', 'pdf', 'doc'])
const pageName = computed(() => {
  if (route.params && route.params.pageStatus == 'add') {
    return '新建JSA票'
  }
  if (route.params && route.params.pageStatus == 'look') {
    return '查看详情'
  }
})
provide(
  'isPageType',
  route.params && route.params.pageStatus == 'look' ? true : false
)
// 表格
const homeworkEnvironmentList = ref([])
const homeworkStepsList = ref([])
// 补充信息
const supplyInfo = ref({
  head: '',
  issuer: '',
  remark: '',
  stampFile: []
})
const tableInfoRef = ref<any>(null)
const jsaSupplementList = ref([])
// 返回
const onCancel = () => {
  if (route.params && route.params.type == 'approval') {
    router.push('/approval')
  } else {
    router.push('/ticket/JSA')
  }
}
// 上传原件
const uploadKey = ref<any>('')
const SpicUploadKey = ref<any>('')
const updateList = async (val: any) => {
  try {
    let { data } = await ticketApi.uploadOriginal({
      id: Number(route.params.id),
      stampFile: val.join(',')
    })
    if (data.code == 200) {
      getGetJsaTicketDetailVO(route.params.id)
    }
  } catch (e) {
    console.log(e)
  }
}
// 删除组件
const handleRemoveFn = (val: any) => {
  let arr =
    val &&
    val.map((item: any) => {
      return item.fileName + '@' + item.originalFileName
    })
  updateList(arr)
}
// ==== 提交签发审批&&草稿 ====
// 基础信息
const basicInfoRef = ref<any>(null)
const basicInfoData = ref({})
const getFromData = (val: object) => {
  if (
    (val.valid && val.type == 'validate') ||
    (!val.valid && val.type == 'noValidate')
  ) {
    basicInfoData.value = val.data
  } else {
    basicInfoData.value = {}
  }
}
// 危害辨识及控制 checkbox
const taskInfoRef = ref<any>(null)
const taskInfoData = ref({})
const getCheckBoxData = (val: object) => {
  if (
    (val.valid && val.type == 'validate') ||
    (!val.valid && val.type == 'noValidate')
  ) {
    taskInfoData.value = val.data
  } else {
    taskInfoData.value = {}
  }
}
// 危害辨识及控制 表格
const tableInfoRef1 = ref<any>(null)
const tableInfoData1 = ref({})
const delJsaHomeworkIds1 = ref([])
const deleBtn1 = (val: any) => {
  delJsaHomeworkIds1.value = val
}
const getTabelData1 = (val: object) => {
  if (
    (val.valid && val.type == 'validate') ||
    (!val.valid && val.type == 'noValidate')
  ) {
    tableInfoData1.value = val.data
  } else {
    tableInfoData1.value = {}
  }
}
const tableInfoRef2 = ref<any>(null)
const tableInfoData2 = ref({})
const delJsaHomeworkIds2 = ref([])
const deleBtn2 = (val: any) => {
  delJsaHomeworkIds2.value = val
}
const getTabelData2 = (val: object) => {
  if (
    (val.valid && val.type == 'validate') ||
    (!val.valid && val.type == 'noValidate')
  ) {
    tableInfoData2.value = val.data
  } else {
    tableInfoData2.value = {}
  }
}
// 应急处置
const handleInfoRef = ref<any>(null)
const handleInfoData = ref({})
const getTextareaData = (val: object) => {
  if (
    (val.valid && val.type == 'validate') ||
    (!val.valid && val.type == 'noValidate')
  ) {
    handleInfoData.value = val.data
  } else {
    handleInfoData.value = {}
  }
}
// 查看详情
const statusObj = {
  1: '草稿',
  2: '生效中',
  3: '已终结',
  4: '已作废'
}
const JsaTicketDetailVO = ref({})
const tableInfoKey = ref<any>('')
const getGetJsaTicketDetailVO = async (id: string) => {
  try {
    let { data } = await ticketApi.getJsaTicketDetailVO({ id })
    if (data.code == 200) {
      JsaTicketDetailVO.value = data.data

      data.data.homeworkEnvironmentList.length &&
        data.data.homeworkEnvironmentList.forEach((item: any) => {
          item.provePhoto = item.provePhoto ? item.provePhoto.split(',') : ''
        })
      homeworkEnvironmentList.value = data.data.homeworkEnvironmentList || []

      data.data.homeworkStepsList.length &&
        data.data.homeworkStepsList.forEach((item: any) => {
          item.provePhoto = item.provePhoto ? item.provePhoto.split(',') : ''
        })
      homeworkStepsList.value = data.data.homeworkStepsList || []
      // 补充信息赋值
      if (JsaTicketDetailVO.value.status == 3) {
        supplyInfo.value.head = data.data.head
          ? data.data.head.split('_')[0] +
            '  ' +
            (data.data.headTime ? data.data.headTime.slice(0, 16) : '')
          : ''
        supplyInfo.value.issuer = data.data.issuer
          ? data.data.issuer.split('_')[0] +
            '  ' +
            (data.data.issuerTime ? data.data.issuerTime.slice(0, 16) : '')
          : ''
      } else {
        supplyInfo.value.head = '--'
        supplyInfo.value.issuer = '--'
      }

      supplyInfo.value.remark = data.data.remark
      supplyInfo.value.stampFile = data.data.stampFile
        ? data.data.stampFile.split(',')
        : ''
      stampFileArr.value = supplyInfo.value.stampFile
      jsaSupplementList.value = data.data.jsaSupplementList || []

      tableInfoKey.value = Date.now()
      uploadKey.value = Date.now()
      SpicUploadKey.value = Date.now()
    }
  } catch (e) {
    JsaTicketDetailVO.value = {}
    console.log(e)
  }
}
// ============== 底部按钮操作 ==============
// 草稿按钮
const onCgSumbit = async () => {
  // 触发基础信息
  await basicInfoRef.value.isValidate()
  // 危害辨识及控制-复选框
  await taskInfoRef.value.isValidate()
  // 危害辨识及控制-表格
  await tableInfoRef1.value.isValidate()
  await tableInfoRef2.value.isValidate()
  // 应急处置
  await handleInfoRef.value.isValidate()

  nextTick(async () => {
    try {
      let params = {
        ...basicInfoData.value,
        ...taskInfoData.value,
        homeworkEnvironmentList: tableInfoData1.value,
        homeworkStepsList: tableInfoData2.value,
        ...handleInfoData.value,
        delJsaHomeworkIds: delJsaHomeworkIds1.value.concat(
          delJsaHomeworkIds2.value
        )
      }
      if (route.params && route.params.id) {
        params.id = Number(route.params && route.params.id)
      }
      let { data } = await ticketApi.addJsaTicket(params)
      if (data.code == 200) {
        ElMessage({
          type: 'success',
          message: '保存草稿成功'
        })
        onCancel()
      }
    } catch (e) {
      ElMessage({
        type: 'warning',
        message: '保存草稿失败'
      })
      console.log(e)
    }
  })
}
// 签发审批按钮
const onApproval = async () => {
  // 触发基础信息
  await basicInfoRef.value.submitForm()
  // 危害辨识及控制-复选框
  await taskInfoRef.value.submitCheckbox()
  // 危害辨识及控制-表格
  await tableInfoRef1.value.submitTable()
  await tableInfoRef2.value.submitTable()
  // 应急处置
  await handleInfoRef.value.submitTextarea()

  nextTick(async () => {
    if (JSON.stringify(basicInfoData.value) == '{}') {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return false
    }
    if (JSON.stringify(taskInfoData.value) == '{}') {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return false
    }
    if (JSON.stringify(tableInfoData1.value) == '{}') {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return false
    }
    if (JSON.stringify(tableInfoData2.value) == '{}') {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return false
    }
    if (JSON.stringify(handleInfoData.value) == '{}') {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return false
    }
    try {
      let params = {
        ...basicInfoData.value,
        ...taskInfoData.value,
        homeworkEnvironmentList: tableInfoData1.value,
        homeworkStepsList: tableInfoData2.value,
        ...handleInfoData.value,
        delJsaHomeworkIds: delJsaHomeworkIds1.value.concat(
          delJsaHomeworkIds2.value
        ),
        id: Number(route.params.id) || ''
      }
      let { data } = await ticketApi.addJsaTicket(params)
      if (data.code == 200) {
        getJsaLaunchProcess(data.data, 'JSA_ISSUED')
      } else {
        ElMessage({
          type: 'warning',
          message: data.message
        })
      }
    } catch (e) {
      ElMessage({
        type: 'warning',
        message: '操作失败'
      })
      console.log(e)
    }
  })
}
// 作废jas票
const uplateData = async () => {
  onCancel()
}
// ============== 顶部按钮操作 ==============
// 补充信息
const handleSupply = async () => {
  // router.push(
  //   '/ticket/JSA/JSAInfo/look/' +
  //     route.params.id +
  //     '/1_' +
  //     '/supplyInfo?pageStatus=look&pageType=supply'
  // )
  if (route.fullPath.indexOf('/approval/workbench/JSAInfo') >= 0) {
    router.push(
      '/approval/workbench/JSAInfo/look/' +
        route.params.id +
        '/1_' +
        '/supplyInfo?pageStatus=look&pageType=supply'
    )
  } else {
    router.push(
      '/ticket/JSA/JSAInfo/look/' +
        route.params.id +
        '/1_' +
        '/supplyInfo?pageStatus=look&pageType=supply'
    )
  }
}
// 上传原件
const isShow = ref(false)
const uploadBtn = () => {
  stampFileArr.value = supplyInfo.value.stampFile
  isShow.value = true
}
// 终结JSA票
const voidItem = () => {
  ElMessageBox.confirm('确定终结JSA票？', '终结', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      getJsaLaunchProcess(route.params.id || '', 'JSA_SUMMARY')
    })
    .catch(() => {
      console.log('终结JSA票')
    })
}
const getJsaLaunchProcess = async (businessId: any, businessKey = '') => {
  try {
    let { data } = await ticketApi.jsaLaunchProcess({
      businessId: Number(businessId),
      businessKey
    })
    if (data.code == 200) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      onCancel()
    } else {
      ElMessage({
        type: 'warning',
        message: data.message
      })
    }
  } catch (e) {
    ElMessage({
      type: 'warning',
      message: '操作失败'
    })
    console.log(e)
  }
}
// 导出JSA票
const dowloadBtn = async () => {
  try {
    let res = await ticketApi.download({
      id: String(route.params.id) || ''
    })
    let str = res.headers['content-disposition']
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(res.data)
    link.download =
      '作业安全风险控制卡.' + str.split('.')[str.split('.').length - 1]
    link.click()
  } catch (e) {
    ElMessage({
      type: 'warning',
      message: '导出失败'
    })
  }
}
// 作废jsa票
const showRevokeTicket = ref<boolean>(false)
const revokeTicket = () => {
  showRevokeTicket.value = true
}
// ============== 页面初始化 ==============
onMounted(() => {
  if (
    (route.params && route.params.pageStatus == 'look') ||
    (route.params && route.params.pageStatus == 'add' && route.params.id)
  ) {
    getGetJsaTicketDetailVO((route.params && route.params.id) || '')
  }
  tableInfoKey.value = Date.now()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped src="../assets/tableRow.scss"></style>
<style scoped lang="scss">
.tag {
  margin-left: 16px !important;
}
.btns {
  padding: 16px 0 0 24px;
  box-sizing: border-box;
}
.info-base {
  margin: 16px 24px 24px !important;
}
</style>
<style lang="scss">
.table_Row_ {
  .cell.el-tooltip {
    overflow: hidden !important;
  }
}
</style>
