export default function () {
  // 表格一
  const homeworkEnvironmentListColumns = ref([
    { prop: 'index', label: '' },
    {
      prop: 'environment',
      label: '作业环境',
      slotName: 'environment',
      placeholder: '请输入作业环境'
    },
    {
      prop: 'risk',
      label: '可能存在的风险',
      slotName: 'risk',
      placeholder: '请输入可能存在的风险'
    },
    {
      prop: 'riskControl',
      label: '风险预防和控制措施',
      slotName: 'riskControl',
      placeholder: '请输入风险预防和控制措施'
    },
    {
      prop: 'operate',
      slotName: 'operate',
      label: '操作',
      minWidth: 100,
      fixed: 'right'
    }
  ])
  const homeworkEnvironmentListColumnsLook = ref([
    { prop: 'index', label: '' },
    {
      prop: 'environment',
      label: '作业环境'
    },
    {
      prop: 'risk',
      label: '可能存在的风险'
    },
    {
      prop: 'riskControl',
      label: '风险预防和控制措施'
    },
    { prop: 'provePhoto', label: '证明照片', slotName: 'provePhoto' },
    { prop: 'executeStatue', label: '执行情况', slotName: 'executeStatue' },
    { prop: 'executeUser', label: '执行人签名', slotName: 'executeUser' }
  ])
  // 表格二
  const homeworkStepsListColumns = ref([
    { prop: 'index', label: '' },
    {
      prop: 'environment',
      label: '作业步骤',
      slotName: 'environment',
      placeholder: '请输入作业步骤'
    },
    {
      prop: 'risk',
      label: '可能存在的风险',
      slotName: 'risk',
      placeholder: '请输入可能存在的风险'
    },
    {
      prop: 'riskControl',
      label: '风险预防和控制措施',
      slotName: 'riskControl',
      placeholder: '请输入风险预防和控制措施'
    },
    {
      prop: 'operate',
      slotName: 'operate',
      label: '操作',
      minWidth: 100,
      fixed: 'right'
    }
  ])
  const homeworkStepsListColumnsLook = ref([
    { prop: 'index', label: '' },
    {
      prop: 'environment',
      label: '作业步骤'
    },
    {
      prop: 'risk',
      label: '可能存在的风险'
    },
    {
      prop: 'riskControl',
      label: '风险预防和控制措施'
    },
    { prop: 'provePhoto', label: '证明照片', slotName: 'provePhoto' },
    { prop: 'executeStatue', label: '执行情况', slotName: 'executeStatue' },
    { prop: 'executeUser', label: '执行人签名', slotName: 'executeUser' }
  ])
  // 补充信息
  const tableColumnsLook = ref([
    {
      prop: 'disclosureTime',
      label: '交底时间'
    },
    {
      prop: 'hazardousWork',
      label: '危险作业提示（根据前项填写序号）'
    },
    {
      prop: 'measureNoRemark',
      label: '危害辨识及控制措施编号或新增的危害辨识及控制措施',
      width: '400px'
    },
    { prop: 'head', label: '工作负责人' },
    { prop: 'signature', label: '工作班成员签名' }
  ])
  const tableColumns = ref([
    {
      prop: 'disclosureTime',
      label: '交底时间',
      slotName: 'disclosureTime',
      type: 'datetime',
      placeholder: '请选择交底时间'
    },
    {
      prop: 'hazardousWork',
      label: '危险作业提示（根据前项填写序号）',
      slotName: 'hazardousWork',
      placeholder: '请输入危险作业提示（根据前项填写序号）'
    },
    {
      prop: 'measureNoRemark',
      label: '危害辨识及控制措施编号或新增的危害辨识及控制措施',
      slotName: 'measureNoRemark',
      width: '400px',
      placeholder: '请输入危害辨识及控制措施编号或新增的危害辨识及控制措施）'
    },
    {
      prop: 'head',
      label: '工作负责人',
      slotName: 'head',
      placeholder: '请输入工作负责人'
    },
    {
      prop: 'signature',
      label: '工作班成员签名',
      slotName: 'signature',
      placeholder: '请输入工作班成员签名'
    },
    {
      prop: 'operate',
      slotName: 'operate',
      label: '操作',
      minWidth: 100,
      fixed: 'right'
    }
  ])
  return {
    homeworkEnvironmentListColumns,
    homeworkEnvironmentListColumnsLook,
    homeworkStepsListColumns,
    homeworkStepsListColumnsLook,
    tableColumnsLook,
    tableColumns
  }
}
