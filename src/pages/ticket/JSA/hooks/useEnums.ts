import { ticket as ticketApi } from '@/api/index.ts'
export default function () {
  // 获取列表借口
  const list1 = ref<any[]>([])
  const list2 = ref<any[]>([])
  const list3 = ref<any[]>([])
  const getHomeworkList = async () => {
    // 危险辨识及控制--作业类别
    try {
      let { data } = await ticketApi.getHomeworkTyepList({})
      data.data.forEach((item) => {
        if (item.name == '其他') {
          item.value = item.value + '@'
        }
      })
      list1.value = data.data
    } catch (e) {
      list1.value = []
    }
    // 危险辨识及控制--作业环境
    try {
      let { data } = await ticketApi.getHomeworkEnvironmentList({})
      data.data.forEach((item) => {
        if (item.name == '其他') {
          item.value = item.value + '@'
        }
      })
      list2.value = data.data
    } catch (e) {
      list2.value = []
    }
    // 危险辨识及控制--作业风险
    try {
      let { data } = await ticketApi.getHomeworkRiskList({})
      data.data.forEach((item) => {
        if (item.name == '其他') {
          item.value = item.value + '@'
        }
      })
      list3.value = data.data
    } catch (e) {
      list3.value = []
    }
  }
  onMounted(() => {
    getHomeworkList()
  })

  return {
    list1,
    list2,
    list3
  }
}
