<template>
  <div class="page-operate">
    <div class="operate-title">补充信息</div>
  </div>
  <!-- 班组成员安全交底-表格 -->
  <div class="supply-base">
    <div class="operate_">
      <p>
        <span></span
        ><span
          >班组成员安全交底（该项从开工第一天到工作结束，每天交底，可附页）。已清楚工作内容、工作风险，了解安全措施、注意事项和应急处理</span
        >
      </p>
    </div>
    <table-info
      ref="tableInfoRef"
      :columns="tableColumns"
      :tableData="jsaSupplementList"
      @getTabelData="getTabelData"
      @deleBtn="deleBtn"
      :statusType="false"
      :maxlength="64"
    ></table-info>
  </div>
  <!-- （如工作负责人变更，班组成员变更在工作票已经-备注 -->
  <div class="notes-base">
    <div class="operate_">
      <p>
        <span></span
        ><span
          >备注（如工作负责人变更，班组成员变更在工作票已经填写，不用再在此处填写）</span
        >
      </p>
    </div>
    <div class="notes">
      <el-form
        ref="formRef"
        :rules="formRules"
        :model="formData"
        label-suffix=""
        label-position="right"
        :inline="true"
        class="w-full"
      >
        <el-row :gutter="24"
          ><el-col :span="24">
            <el-form-item label="备注" prop="remark" class="trainingContente__">
              <el-input
                v-model="formData.remark"
                :autosize="{ minRows: 5, maxRows: 7 }"
                type="textarea"
                placeholder="请输入备注内容"
                maxlength="1024"
                :show-word-limit="true"
              /> </el-form-item
          ></el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <div class="page-footer">
    <el-button plain @click="onCancel">取消</el-button>
    <el-button type="primary" v-preventReClick="1000" @click="onSubmitSave"
      >保存</el-button
    >
  </div>
</template>
<script setup lang="ts">
import tableInfo from '../components/tableRow.vue'
import { ticket as ticketApi } from '@/api/index.ts'

// 表格
const jsaSupplementList = ref([])
const tableColumns = ref([
  {
    prop: 'disclosureTime',
    label: '交底时间',
    slotName: 'disclosureTime',
    type: 'datetime',
    placeholder: '请选择交底时间'
  },
  {
    prop: 'hazardousWork',
    label: '危险作业提示（根据前项填写序号）',
    slotName: 'hazardousWork',
    placeholder: '请输入危险作业提示'
  },
  {
    prop: 'measureNoRemark',
    label: '危害辨识及控制措施编号或新增的危害辨识及控制措施',
    slotName: 'measureNoRemark',
    width: '400px',
    placeholder: '请输入危害辨识及控制措施编号或新增的危害辨识及控制措施）'
  },
  {
    prop: 'head',
    label: '工作负责人',
    slotName: 'head',
    placeholder: '请输入工作负责人'
  },
  {
    prop: 'signature',
    label: '工作班成员签名',
    slotName: 'signature',
    placeholder: '请输入工作班成员签名'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 100,
    fixed: 'right'
  }
])
// 备注
import type { FormRules, FormInstance } from 'element-plus'
const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()
const formData = ref<Record<string, any>>({
  remark: ''
})
const formRules = reactive<FormRules<Record<string, any>>>({
  remark: [{ required: false, message: '请输入备注内容', trigger: 'blur' }]
})
// 表格
const tableInfoRef = ref<any>(null)
const tableInfoData = ref({})
const getTabelData = (val: object) => {
  if (
    (val.valid && val.type == 'validate') ||
    (!val.valid && val.type == 'noValidate')
  ) {
    tableInfoData.value = val.data
  } else {
    tableInfoData.value = {}
  }
}
// 保存
const delJsaSupplementIds = ref([])
const deleBtn = (val: any) => {
  delJsaSupplementIds.value = val
}
const onSubmitSave = async () => {
  await tableInfoRef.value.isValidate()

  nextTick(async () => {
    try {
      tableInfoData.value &&
        tableInfoData.value.forEach((item: any) => {
          item.jsaTicketId = Number(route.params && route.params.id)
        })
      let params = {
        ...formData.value,
        jsaSupplementList: tableInfoData.value,
        jsaTicketId: Number(route.params && route.params.id),
        delJsaSupplementIds: delJsaSupplementIds.value
      }
      let { data } = await ticketApi.addReplenishInfo(params)
      if (data.code == 200) {
        ElMessage({
          type: 'success',
          message: '保存成功'
        })
        onCancel()
      } else {
        ElMessage({
          type: 'warning',
          message: data.message
        })
      }
    } catch (e) {
      ElMessage({
        type: 'warning',
        message: '保存失败'
      })
      console.log(e)
    }
  })
}
// 取消
const onCancel = () => {
  router.go(-1)
}
// ============== 页面初始化 ==============
const getGetJsaTicketDetailVO = async () => {
  try {
    let { data } = await ticketApi.getJsaTicketDetailVO({ id: route.params.id })
    if (data.code == 200) {
      formData.value.remark = data.data.remark
      jsaSupplementList.value = data.data.jsaSupplementList || []
    }
  } catch (e) {
    console.log(e)
  }
}
onMounted(() => {
  getGetJsaTicketDetailVO()
})
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="./assets/index.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped lang="scss">
.notes {
  margin-top: 24px;
}
</style>
