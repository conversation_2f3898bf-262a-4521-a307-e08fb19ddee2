<template>
  <div class="work-supply">
    <div class="page-operate">
      <div class="operate-title">补充信息</div>
    </div>

    <el-form ref="formRef" :model="formData" :rules="formRules">
      <!-- 工作人员增加 -->
      <div class="info-base safe-content">
        <div class="safe-title">
          <div class="line"></div>
          工作人员变动情况
        </div>
        <el-row :gutter="24">
          <el-col :span="23">
            <div class="mb-3">
              <el-text type="danger">*</el-text>
              变动人员姓名、日期及时间
            </div>
            <el-form-item label="" prop="userChanges" label-width="0">
              <el-input
                v-model.trim="formData.userChanges"
                placeholder="请输入变动人员姓名、日期及时间"
                maxlength="64"
              />
            </el-form-item>
          </el-col>

          <el-col :span="23">
            <div class="mb-3"><el-text type="danger">*</el-text> 备注</div>
            <el-form-item label="" prop="remark" label-width="0">
              <el-input
                v-model.trim="formData.remark"
                :rows="3"
                type="textarea"
                placeholder="请输入备注"
                maxlength="1024"
                autocomplete="off"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <div class="page-footer">
      <el-button plain @click="onCancel">取消</el-button>
      <el-button type="primary" @click="onSubmit">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { ticket as ticketAPI } from '@/api/index.ts'

const router = useRouter()
const route = useRoute()

onMounted(async () => {})

const formData = ref({
  id: route.query.id ? Number(route.query.id) : '', // 工作票id
  userChanges: route.query.userChanges || '', // 变动人员姓名、日期及时间
  remark: route.query.remark || '' // 备注
})

const formRules = reactive<FormRules>({
  userChanges: [
    {
      required: true,
      message: '请输入变动人员姓名、日期及时间',
      trigger: 'blur'
    }
  ],
  remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
})

const formRef = ref<FormInstance>()
const onSubmit = async () => {
  formRef.value?.validate(async (valid: any) => {
    if (valid) {
      try {
        const { data } = await ticketAPI.addTwoSeddSupply(formData.value, true)
        if (data.code === '200') {
          ElMessage({
            message: `操作成功!`,
            type: 'success'
          })
          onCancel()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}
const onCancel = () => {
  router.go(-1)
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped src="./assets/work.scss"></style>
