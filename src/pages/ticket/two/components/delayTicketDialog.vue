<template>
  <el-dialog
    v-model="dialogVisible"
    title="工作票延期"
    class="vis-dialog ticket-dialog"
    align-center
    :before-close="closeDialog"
    :close-on-click-modal="false"
    width="446"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="100px"
      label-position="top"
      :rules="formRules"
    >
      <div class="group">
        <div class="title">【电气二种工作票延期审批-发起人】需填写补充信息</div>
        <el-form-item label="变更后工作结束时间" prop="delayTime">
          <el-date-picker
            v-model="formData.delayTime"
            type="datetime"
            placeholder="请选择变更后工作结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            :editable="false"
            :disabled-date="disabledDateFn"
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button v-preventReClick="1000" type="primary" @click="handleSave"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { ticket as ticketAPI } from '@/api/index.ts'

const route = useRoute()

const props = defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  },
  isShow: {
    type: Boolean,
    default: false,
    required: true
  }
})
const dialogVisible = ref(false)
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
  },
  {
    deep: true
  }
)

// 延期时间的选择范围 只能选择≥批准工作时间的开始时间
const disabledDateFn = (time: any) => {
  const approveStartTime = props.workTicketData?.approveStartTime
  if (
    approveStartTime &&
    time <= new Date(approveStartTime).getTime() - 24 * 60 * 60 * 1000
  ) {
    return (
      approveStartTime &&
      time <= new Date(approveStartTime).getTime() - 24 * 60 * 60 * 1000
    )
  }
  return false
}

let formData = ref({
  delayTime: ''
})
const formRef = ref<FormInstance>()
const formRules = reactive<FormRules<Record<string, any>>>({
  delayTime: [
    {
      required: true,
      message: '请选择变更后工作结束时间',
      trigger: 'blur'
    }
  ]
})

// 点击取消
const emit = defineEmits(['closeDialog', 'uplateData'])
const closeDialog = () => {
  dialogVisible.value = false
  emit('closeDialog')
  resetForm()
}
// 点击确认
const handleSave = () => {
  formRef.value?.validate(async (valid: any) => {
    if (valid) {
      try {
        const { data } = await ticketAPI.twoDelayWorkApprove(
          {
            businessId: Number(route.params.id),
            ticketWorkVO: formData.value
          },
          true
        )
        if (data.code === '200') {
          ElMessage({
            message: `操作成功!`,
            type: 'success'
          })
          closeDialog()
          emit('uplateData')
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    }
  })
}
// 重置
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
</script>

<style scoped src="../../assets/dialog.scss"></style>
<style lang="scss">
.ticket-dialog .el-form .el-form-item--default .el-form-item__label {
  height: 22px !important;
  line-height: 22px !important;
}
</style>
