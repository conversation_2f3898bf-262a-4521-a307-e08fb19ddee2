<!--  审批流程弹框  -->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="审核记录"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    width="576px"
    class="vis-dialog"
  >
    <h3 class="timeline-title">{{ title }}</h3>
    <el-scrollbar max-height="402">
      <div v-if="!activities || !activities.length" class="mb-6">
        暂未发起审批
      </div>
      <el-timeline v-else class="timeline">
        <el-timeline-item
          v-for="(item, index) in activities"
          :key="index"
          :timestamp="item.approvalTime"
          :type="item.type"
          :hollow="true"
        >
          <span :title="getNames(item.userName, true)">
            {{ getNames(item.userName) }}（{{ item.approvalPost }}）
          </span>
          <el-tag v-if="index === 0" type="success">发起审批</el-tag>
          <template v-else>
            <el-tag v-if="item.approvalState == 0" type="danger"
              >审核驳回</el-tag
            >
            <el-tag v-if="item.approvalState == 1" type="success"
              >审核通过</el-tag
            >
            <el-tag v-if="item.approvalState == 2" type="warning"
              >审核中</el-tag
            >
            <el-tag v-if="item.approvalState == 3" type="info">待流转</el-tag>
          </template>
          <div v-if="item.approvalMsg">
            <div class="view-btn" @click="showDesc = !showDesc">
              查看驳回意见
              <el-icon
                ><CaretBottom v-if="!showDesc" /> <CaretTop v-else
              /></el-icon>
            </div>
            <div v-show="showDesc" class="desc">
              <span class="label">驳回原因：</span
              ><span class="msg">{{ item.approvalMsg }}</span>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="dialogClose">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
// import { approval as approvalAPI } from '@/api/index.ts'
import { ticket as ticketAPI } from '@/api/index.ts'
import filter from '@/utils/filter.js'
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
    required: true
  },
  rows: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const activities: any = ref([])

const showDesc = ref(false)
const dialogVisible = ref(false)
watch(
  () => props.isShow,
  async (val: boolean) => {
    dialogVisible.value = val
    if (val) {
      getData()
    }
  },
  {
    deep: true
  }
)

let title = ref('')
/**
 * 获取名称
 */
const getNames = (val: string, isAll: boolean = false) => {
  const names: string[] = val.split('_')
  const allName = names.join('、')

  return isAll
    ? allName
    : names.length > 2
    ? `${names.slice(0, 2).join('、')}等`
    : allName
}

// 点击确定
const emit = defineEmits(['closeDialog'])

// 关闭弹窗
const dialogClose = () => {
  activities.value = []
  emit('closeDialog', false)
}

const getData = async () => {
  try {
    let { data } = await ticketAPI.getApprovalProcess({
      businessId: props.rows.id,
      businessType: 5 // 业务类型: 1-工作票,2-JSA票,3-经验反馈单,4-应急演练,5-电气二种票
    })
    activities.value = data.data?.approvalTableUserVoList || []
    activities.value?.forEach((item: Obj) => {
      if (item.approvalState !== 3) {
        item.type = 'success'
      }
    })
    title.value = filter.instanceType(data.data.instanceType) || ''
  } catch (err) {
    console.log('err', err)
  }
}
</script>
<style lang="scss" scoped>
.timeline-title {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  padding-left: 8px;
  border-left: 4px solid #2acba0;
  line-height: 16px;
  margin-bottom: 16px;
  font-weight: normal;
}
.timeline {
  margin-left: 40px;
  padding-left: 150px;
  padding-top: 10px;
  :deep(.el-timeline-item__timestamp.is-bottom) {
    position: absolute;
    left: -148px;
    top: 0;
    margin-top: 2px;
  }
  :deep(.el-timeline-item__tail) {
    border-left: 2px dashed #e6e9ec;
  }
  :deep(.el-timeline-item) {
    padding-bottom: 40px;
  }
  .view-btn {
    color: #29cca0;
    margin-top: 15px;
    margin-right: 4px;
    cursor: pointer;
  }
  .desc {
    margin-top: 8px;
    color: rgba(0, 0, 0, 0.45);
    line-height: 22px;
    padding-right: 32px;
    display: flex;

    .label {
      flex: none;
    }

    .msg {
      white-space: pre-wrap;
    }
  }
  :deep(.el-tag.el-tag--info) {
    background-color: #ededed;
    border-color: #ededed;
    color: rgba(0, 0, 0, 0.65);
  }
}
</style>
