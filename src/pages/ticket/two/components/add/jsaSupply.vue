<template>
  <el-form
    ref="ruleFormRef"
    :key="formKey"
    :model="ruleForm"
    :rules="rules"
    class="info-base two-jsa"
  >
    <div class="safe-title">
      <div class="line"></div>
      JSA票补充填写信息
    </div>
    <!-- 作业类别 -->
    <div class="breif">
      <el-text class="mx-1" type="danger">*</el-text>作业类别
    </div>
    <el-checkbox-group v-model="homeworkTyep" :disabled="isDisabled">
      <el-checkbox
        v-for="item in homeworkTyepList"
        :key="item.value"
        :label="item.value"
        :value="item.value"
        class="checkbox__"
      >
        {{ item.name }}
        <el-form-item
          v-if="item.value == '20@' && homeworkTyep.includes('20@')"
          prop="value1"
          class="lastItem"
        >
          <el-input
            v-model.trim="ruleForm.value1"
            placeholder="请输入补充说明"
            clearable
            maxlength="64"
            :disabled="isDisabled"
          />
        </el-form-item>
      </el-checkbox>
    </el-checkbox-group>

    <!-- 作业风险 -->
    <div class="brief">
      <el-text class="mx-1" type="danger">*</el-text>作业风险
    </div>
    <el-checkbox-group v-model="homeworkRisk" :disabled="isDisabled">
      <el-checkbox
        v-for="item in homeworkRiskList"
        :key="item.value"
        :label="item.value"
        :value="item.value"
        class="checkbox__"
      >
        {{ item.name }}
        <el-form-item
          v-if="item.value == '13@' && homeworkRisk.includes('13@')"
          prop="value3"
          class="lastItem"
        >
          <el-input
            v-model.trim="ruleForm.value3"
            placeholder="请输入补充说明"
            clearable
            :disabled="isDisabled"
            maxlength="64"
          />
        </el-form-item>
      </el-checkbox>
    </el-checkbox-group>

    <div class="brief">
      <el-text class="mx-1" type="danger">*</el-text>过程风险预控
    </div>
    <table-info
      ref="riskRef"
      class="mb-6"
      :columns="riskColumns"
      :table-data="riskList"
      :maxlength="1024"
      @dele-btn="deleBtn1"
      @get-tabel-data="getRiskData"
    ></table-info>
  </el-form>
</template>
<script setup lang="ts">
import useJsa from '../../hooks/useJsa'
import type { FormInstance, FormRules } from 'element-plus'
import tableInfo from '../../../components/tableRow.vue'
interface RuleForm {
  value1: string
  value3: string
}
const formKey = ref(0)
formKey.value = Date.now()
const route = useRoute()
const isDisabled = computed(() => {
  return route.params && route.params.pageStatus == 'look'
})
const homeworkTyep = ref<any>([]) // 作业类别
const homeworkRisk = ref<any>([]) // 作业风险
const { homeworkTyepList, homeworkRiskList } = useJsa()

const props = defineProps({
  workTicketData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  isCopy: {
    type: Boolean,
    default: false
  }
})

const riskColumns = ref([
  { prop: 'index', label: '' },
  {
    prop: 'process',
    label: '作业过程',
    slotName: 'process',
    minWidth: 100,
    placeholder: '请输入作业过程'
  },
  {
    prop: 'riskDesc',
    label: '风险描述',
    slotName: 'riskDesc',
    minWidth: 100,
    placeholder: '请输入风险描述'
  },
  {
    prop: 'riskLevel',
    label: '风险等级',
    slotName: 'riskLevel',
    minWidth: 100,
    placeholder: '请输入风险等级'
  },
  {
    prop: 'control',
    label: '管控措施',
    slotName: 'control',
    minWidth: 100,
    placeholder: '请输入管控措施'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 66,
    fixed: 'right'
  }
])
const riskList = ref<any[]>([])

watch(
  () => props.workTicketData,
  async () => {
    update()
  }
)
const update = () => {
  if (props.workTicketData?.processRiskControlList?.length) {
    riskList.value = props.workTicketData?.processRiskControlList
  }
  if (props.workTicketData?.homeworkTyep?.length) {
    let strArr: any = [],
      arr: any = []
    props.workTicketData?.homeworkTyep.split('_').forEach((item: any) => {
      if (item.indexOf('20@') != -1) {
        strArr = ['20@', item.split('@')[1]]
      } else {
        arr.push(Number(item))
      }
    })
    if (strArr.length) {
      arr.push('20@')
      ruleForm.value.value1 = strArr[1]
      homeworkTyep.value = arr
    } else {
      homeworkTyep.value = arr
    }
  }

  if (props.workTicketData?.homeworkRisk?.length) {
    let strArr: any = [],
      arr: any = []
    props.workTicketData?.homeworkRisk.split('_').forEach((item: any) => {
      if (item.indexOf('13@') != -1) {
        strArr = ['13@', item.split('@')[1]]
      } else {
        arr.push(Number(item))
      }
    })
    if (strArr.length) {
      arr.push('13@')
      ruleForm.value.value3 = strArr[1]
      homeworkRisk.value = arr
    } else {
      homeworkRisk.value = arr
    }
  }
}
// 过程风险预控
const riskRef = ref<any>(null)
const riskData = ref<any>([])
const riskValid = ref(false)
const getRiskData = (val: any) => {
  riskValid.value = val.valid
  if (val.data?.length) {
    riskData.value = val.data.map((item: any) => {
      return {
        // id: item.id || '',
        id: props?.isCopy ? '' : item.id || '',
        process: item.process,
        riskDesc: item.riskDesc,
        riskLevel: item.riskLevel,
        control: item.control
      }
    })
  }
}

// 删除过程风险预控的id
const delJsaHomeworkIds1 = ref([])
const deleBtn1 = (val: any) => {
  delJsaHomeworkIds1.value = val
}

// 表单
const ruleFormRef = ref<FormInstance>()
const ruleForm = ref({
  value1: '',
  value3: ''
})
const rules = reactive<FormRules<RuleForm>>({
  value1: [{ required: true, message: '请输入补充说明', trigger: 'blur' }],
  value3: [{ required: true, message: '请输入补充说明', trigger: 'blur' }]
})

// 校验
// const emit = defineEmits(['submitTable', 'getA3Data'])
const emit = defineEmits(['getA3Data'])
const submitForm = async (type: number) => {
  let homeworkTyepArr: any = []
  if (homeworkTyep.value?.includes('20@') && ruleForm.value.value1) {
    homeworkTyepArr = homeworkTyep.value.filter((item: any) => item != '20@')
    homeworkTyepArr.push('20@' + ruleForm.value.value1)
  } else {
    homeworkTyepArr = homeworkTyep.value
  }

  let homeworkRiskArr: any = []
  if (homeworkRisk.value?.includes('13@') && ruleForm.value.value3) {
    homeworkRiskArr = homeworkRisk.value.filter((item: any) => item != '13@')
    homeworkRiskArr.push('13@' + ruleForm.value.value3)
  } else {
    homeworkRiskArr = homeworkRisk.value
  }

  // jsa部分的必填校验
  let valid: boolean = true
  // 未勾选 作业类别、作业风险
  if (
    !homeworkTyep.value ||
    !homeworkTyep.value.length ||
    !homeworkRisk ||
    !homeworkRisk.value.length
  ) {
    valid = false
  }
  // 作业类别和作业风险，勾选了其他，未填写输入框的内容
  await ruleFormRef.value?.validate((v: boolean) => {
    if (!v) {
      valid = false
    }
  })

  // 过程风险预防未填写
  await riskRef.value.submitTable(type)
  if (!riskValid.value) {
    valid = false
  }

  emit('getA3Data', {
    valid,
    data: {
      homeworkTyep: homeworkTyepArr.length ? homeworkTyepArr.join('_') : '',
      homeworkRisk: homeworkRiskArr.length ? homeworkRiskArr.join('_') : '',
      processRiskControlList: riskData.value,
      delprocessRiskControlIds: delJsaHomeworkIds1.value // 删除的过程风险预控id
    }
  })
}
defineExpose({
  submitForm,
  update
})
</script>
<style src="../../assets/work.scss"></style>
