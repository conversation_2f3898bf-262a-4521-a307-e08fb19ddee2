<template>
  <el-form ref="formRef" :model="formData" class="info-base">
    <div class="safe-title">
      <div class="line"></div>
      安全措施
    </div>
    <table-info
      ref="safeRef"
      class="safe-content"
      :columns="safeColumns"
      :table-data="safeList"
      :maxlength="1024"
      @dele-btn="deleBtn1"
      @get-tabel-data="getSafeData"
    ></table-info>
  </el-form>
</template>
<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import tableInfo from '../../../components/tableRow.vue'

const props = defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  },
  isCopy: {
    type: Boolean,
    default: false
  }
})

onMounted(async () => {
  // getCzList('')
})

const formData = ref({
  securityMeasureList: [], // 安全措施
  relevancyOperate: [] // 关联操作票
})

const safeList = ref<any[]>([])

watch(
  () => props.workTicketData,
  () => {
    update()
  }
)
const update = () => {
  formData.value = {
    securityMeasureList: props?.isCopy
      ? ''
      : props.workTicketData?.securityMeasureList,
    relevancyOperate: props?.isCopy
      ? []
      : props.workTicketData?.relevancyOperate?.length
      ? props.workTicketData?.relevancyOperate.split(',')
      : [] // 关联操作票
  }
  if (props.workTicketData?.securityMeasureList?.length) {
    safeList.value =
      props.workTicketData?.securityMeasureList?.map((item: any) => ({
        ...item,
        measure: item.safeStepContext || item.measure
      })) || []
    console.log(safeList.value)
  }
}
// 删除安全措施的id
const delJsaHomeworkIds1 = ref([])
const deleBtn1 = (val: any) => {
  delJsaHomeworkIds1.value = val
}

// 表格
const safeColumns = ref([
  { prop: 'index', label: '' },
  {
    prop: 'measure',
    label: '安全措施(注意事项)',
    slotName: 'measure',
    minWidth: 300,
    placeholder: '请输入安全措施(注意事项)'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 66,
    fixed: 'right'
  }
])

// 安全措施
const safeRef = ref<any>(null)
const safeData = ref<any>([])
const safeValid = ref(false)
const getSafeData = (val: any) => {
  safeValid.value = val.valid
  if (val.data?.length) {
    safeData.value = val.data.map((item: any) => {
      return {
        id: props?.isCopy ? '' : item.id || '',
        measure: item.measure || ''
      }
    })
  }
}

// 提交 保存草稿1 签发审批2
const formRef = ref<FormInstance>()
const emit = defineEmits(['submitTable', 'getA2Data'])
const submitForm = async (type: number) => {
  if (!formRef.value) return
  await safeRef.value.submitTable(type)
  formData.value.securityMeasureList = safeData.value
  let relevancyOperate: any = ''
  if (formData.value.relevancyOperate?.length) {
    relevancyOperate = formData.value.relevancyOperate.join(',')
  } else {
    relevancyOperate = ''
  }
  let valid = safeValid.value
  emit('getA2Data', {
    valid,
    data: {
      ...formData.value,
      delsecurityMeasureIds: delJsaHomeworkIds1.value, // 删除的安全措施id
      relevancyOperate
    }
  })
}

defineExpose({
  submitForm,
  update
})
</script>
<style scoped src="../../assets/work.scss"></style>
