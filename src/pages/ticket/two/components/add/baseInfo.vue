<template>
  <div class="info-base">
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="122px"
      :rules="formRules"
      style="padding-right: 50px"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="工作票类型">
            <el-input value="电气二种工作票" disabled />
          </el-form-item>
        </el-col>
        <el-col v-if="!isCopy && !isEdit" :span="12">
          <el-form-item label="选择典型票">
            <el-select
              v-model="formData.templateId"
              placeholder="请选择典型票"
              filterable
              clearable
              :disabled="!formData.stationCode"
              @change="changeTemplate"
            >
              <template v-if="templates.length">
                <el-option
                  v-for="item in templates"
                  :key="item.id"
                  :label="item.modelName"
                  :value="item.id"
                />
                <el-option label="暂不使用" value="" />
              </template>
              <template v-else>
                <el-option label="暂不使用" value="" />
                <el-option label="新建典型票" :value="-1" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编号" prop="ticketNo">
            <el-input
              v-model="formData.ticketNo"
              placeholder="保存后自动生成"
              disabled
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="电站名称" prop="stationCode">
            <StationSelect
              v-model="formData.stationCode"
              v-model:label="formData.stationName"
              @change="changeStation"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门(单位)" prop="workDepartment">
            <el-select
              v-model="formData.workDepartment"
              placeholder="请选择部门(单位)"
              filterable
              clearable
              :disabled="true"
              @change="changeDepart"
            >
              <el-option
                v-for="item in departList"
                :key="item.id"
                :label="item.companyName"
                :value="`${item.companyName}_${item.id}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作票签发人" prop="issuer">
            <el-select
              v-model="formData.issuer"
              placeholder="请选择工作票签发人"
              filterable
              clearable
            >
              <el-option
                v-for="item in props.roleList1"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作许可人" prop="approval">
            <el-select
              v-model="formData.approval"
              placeholder="请选择工作许可人"
              filterable
              clearable
            >
              <el-option
                v-for="item in roleList2"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="值长" prop="workGrow">
            <el-select
              v-model="formData.workGrow"
              placeholder="请选择值长"
              filterable
              clearable
            >
              <el-option
                v-for="item in roleList4"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="工作负责人" prop="head">
            <el-select
              v-model="formData.head"
              placeholder="请选择工作负责人"
              filterable
              clearable
            >
              <el-option
                v-for="item in props.roleList5"
                :key="item.ucUserId"
                :label="item.ucUserName"
                :value="`${item.ucUserName}_${item.ucUserId}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="班组" prop="teamsGroupsName">
            <el-select
              v-model="formData.teamsGroupsName"
              placeholder="请选择班组"
              filterable
              clearable
            >
              <el-option
                v-for="item in teamList"
                :key="item.id"
                :label="item.teamsGroupsName"
                :value="`${item.teamsGroupsName}_${item.id}`"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作班成员" prop="teamsGroupUser">
            <el-input
              v-model.trim="formData.teamsGroupUser"
              placeholder="请输入工作班成员"
              clearable
              maxlength="64"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="参加工作人数" prop="teamsUserNum">
            <el-input
              v-model.trim="formData.teamsUserNum"
              placeholder="请输入参加工作人数"
              clearable
              maxlength="2"
              @input="() => limitGroupNum(formData.teamsUserNum)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备(系统)名称" prop="equipmentName">
            <el-input
              v-model.trim="formData.equipmentName"
              placeholder="请输入设备(系统)名称"
              maxlength="64"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item
            label="工作地点或地段"
            prop="workAddress"
            class="special-label"
          >
            <el-input
              v-model.trim="formData.workAddress"
              placeholder="请输入工作地点或地段"
              maxlength="64"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="工作内容"
            prop="workContent"
            class="special-label"
          >
            <el-input
              v-model.trim="formData.workContent"
              placeholder="请输入工作内容"
              maxlength="64"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划工作时间" prop="timeRange">
            <el-date-picker
              v-model="formData.timeRange"
              type="datetimerange"
              :editable="false"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12"></el-col>
        <el-col :span="24">
          <div class="mb-3">
            <el-text class="mx-1" type="danger">*</el-text>
            工作条件（停电或不停电，或邻近及保留带电设备名称）
          </div>
          <el-form-item label="" prop="workCondition" label-width="0">
            <el-input
              v-model.trim="formData.workCondition"
              :rows="3"
              type="textarea"
              placeholder="请输入工作条件（停电或不停电，或邻近及保留带电设备名称）"
              maxlength="64"
              autocomplete="off"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import StationSelect from '@/components/spic-station'
import type { FormInstance, FormRules } from 'element-plus'
import { dealTime } from '@/utils/index'
import { ticket as ticketAPI } from '@/api/index.ts'
import * as api from '@/api/index.ts'

// 角色人员 roleList1 工作票签发人、roleList2 工作许可人、roleList3 值班负责人、roleList4 值长
type PropsType = {
  roleList1: any[]
  roleList2: any[]
  roleList3: any[]
  roleList4: any[]
  roleList5: any[]
  workTicketData: any
  isCopy: boolean
}
const props = withDefaults(defineProps<PropsType>(), {})

const route = useRoute()
const isEdit = route.params.id

const templates = ref<any[]>([])
const getTemplates = async (operatorId: any) => {
  const { data } = await api.post({
    url: `/operate/modelTicketController/getTemplateByType`,
    data: {
      modelType: 2,
      operatorId
    },
    loading: false
  })
  templates.value = data || []
  formData.value.templateId = templates.value[0]?.id || ''
  changeTemplate(formData.value.templateId)
}

const router = useRouter()
const changeTemplate = (val: any) => {
  if (!val) {
    emit('getA1Data', {
      data: { ...formData.value },
      templateContent: {}
    })
  } else if (val === -1) {
    router.push(
      `/ticket/two/dx/add?modelType=2&company=${company.value}&station=${formData.value.stationName}__${formData.value.stationCode}`
    )
  } else {
    getTemplateContent(val)
  }
}
const templateContent = ref<any>({})
const getTemplateContent = async (id: number) => {
  const { data } = await api.get({
    url: '/operate/modelTicketController/getModelTicketById',
    data: { id },
    loading: true
  })
  templateContent.value = data || {}
  emit('getA1Data', {
    data: { ...formData.value },
    templateContent: templateContent.value
  })
}
onMounted(() => {
  resetForm()
  if (route.query.station) {
    formData.value.stationName = String(route.query.station).split('__')[0]
    formData.value.stationCode = String(route.query.station).split('__')[1]
    changeStation({
      stationName: formData.value.stationName,
      stationCode: formData.value.stationCode
    })
  }
})
const formData = ref<Record<string, any>>({
  templateId: '',
  ticketNo: '保存后自动生成', // 编号
  stationCode: '', // 电站code
  stationName: '', // 电站名称
  issuer: '', // 工作票签发人
  approval: '', //  工作许可人
  workGrow: '', // 值长
  head: '', // 工作负责人
  workDepartment: '', // 部门单位
  teamsGroupsName: '', // 班组
  teamsGroupUser: '', // 班成员
  teamsUserNum: '', // 班成员人数
  equipmentName: '', // 设备(系统)名称
  workAddress: '', //工作地点或地段
  workContent: '', // 工作内容
  timeRange: '', // 计划工作时间
  workStartTime: '', // 计划工作开始时间
  workEndTime: '', //	计划工作结束时间
  workCondition: '' //工作条件（停电或不停电，或邻近及保留带电设备名称）
})
const formRules = reactive<FormRules>({
  ticketNo: [{ required: true, message: '请输入编号', trigger: 'blur' }],
  stationCode: [{ required: true, message: '请选择电站', trigger: 'change' }],
  issuer: [
    { required: true, message: '请选择工作票签发人', trigger: 'change' }
  ],
  approval: [
    { required: true, message: '请选择工作许可人', trigger: 'change' }
  ],
  workGrow: [{ required: true, message: '请选择值长', trigger: 'change' }],
  head: [{ required: true, message: '请选择工作负责人', trigger: 'change' }],
  workDepartment: [
    { required: true, message: '请选择部门(单位)', trigger: 'change' }
  ],
  teamsGroupsName: [
    { required: true, message: '请选择班组', trigger: 'change' }
  ],
  teamsGroupUser: [
    { required: true, message: '请输入工作班成员', trigger: 'blur' }
  ],
  teamsUserNum: [
    { required: true, message: '请输入参加工作人数', trigger: 'blur' }
  ],
  equipmentName: [
    { required: true, message: '请输入设备(系统)名称', trigger: 'blur' }
  ],
  workAddress: [
    { required: true, message: '请输入工作地点或地段', trigger: 'blur' }
  ],
  workContent: [{ required: true, message: '请输入工作内容', trigger: 'blur' }],
  timeRange: [
    { required: true, message: '请选择计划工作时间', trigger: 'change' }
  ],
  workCondition: [
    {
      required: true,
      message: '请输入工作条件（停电或不停电，或邻近及保留带电设备名称）',
      trigger: 'blur'
    }
  ]
})

watch(
  () => props?.workTicketData,
  () => {
    update()
  }
)
const update = () => {
  resetForm()
  for (const key in formData.value) {
    formData.value[key] = props?.workTicketData[key]
  }
  if (
    props?.workTicketData?.workDepartment &&
    props?.workTicketData?.workDepartment.indexOf('_') >= 0
  ) {
    getTeamsGroupsList(props?.workTicketData?.workDepartment.split('_')[1])
  }
  if (
    props?.workTicketData?.workStartTime &&
    props?.workTicketData?.workEndTime
  ) {
    formData.value.timeRange = [
      dealTime(props?.workTicketData?.workStartTime),
      dealTime(props?.workTicketData?.workEndTime)
    ]
  }
  if (props?.isCopy) {
    formData.value.ticketNo = '保存后自动生成'
  }
}
// 修改部门(单位)  重新选择班组
const changeDepart = (val: any) => {
  getTeamsGroupsList(Number(val.split('_')[1]))
  formData.value.teamsGroupsName = ''
}

const changeStation = (e: any) => {
  formData.value.stationName = e.stationName
  formData.value.stationCode = e.stationCode
  getCompany(e.stationCode)
  router.replace({
    path: route.fullPath,
    query: { station: e.stationName + '__' + e.stationCode }
  })
}
const departList = ref<any[]>([]) // 部门(单位)list
const teamList = ref<any[]>([]) // 班组list
const company = ref<string>()
// 获取部门（单位）list
const getCompany = async (stationCode: string) => {
  try {
    const { data } = await api.get('/operate/workOrder/getOperatorsInfo', {
      stationCode
    })
    departList.value = [data]
    formData.value.departmentId = data.id
    company.value = data.companyName + '__' + data.companyCode
    formData.value.workDepartment = data.companyName + '_' + data.id
    getTemplates(formData.value.departmentId)
    changeDepart(formData.value.workDepartment)
  } catch (e: any) {}
}
// 获取班组list
const getTeamsGroupsList = async (operatorsId: any) => {
  try {
    const { data } = await ticketAPI.getTeamsGroupByOperatorsId(
      {
        operatorsId
      },
      false
    )
    teamList.value = data.data
  } catch (e: any) {}
}

// 限制输入班成员人数 0-99
const limitGroupNum = (val: any) => {
  if (val === '0') {
    formData.value.teamsUserNum = val
  } else {
    formData.value.teamsUserNum = String(val).replace(/^(0)|\D/g, '') || ''
  }
}

// 提交 保存草稿1 签发审批2
const formRef = ref<FormInstance>()
const emit = defineEmits(['getA1Data'])
const submitForm = async (type: number) => {
  if (!formRef.value) return
  if (formData.value.timeRange?.length) {
    formData.value.workStartTime = formData.value.timeRange[0] + ':00'
    formData.value.workEndTime = formData.value.timeRange[1] + ':00'
  }
  if (type === 1) {
    emit('getA1Data', {
      data: formData.value
    })
  } else {
    await formRef.value.validate((valid: any) => {
      emit('getA1Data', {
        valid,
        data: formData.value
      })
      return valid
    })
  }
}
// 重置
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
defineExpose({
  submitForm,
  update
})
</script>

<style scoped src="../../assets/work.scss"></style>
