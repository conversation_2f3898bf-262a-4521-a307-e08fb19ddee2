<template>
  <el-form class="info-base">
    <div class="safe-title">
      <div class="line"></div>
      安全措施
    </div>
    <div class="breif"></div>
    <vis-table-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :columns="safeColumns"
      :data="tableList"
      background
      border
      :height="'auto'"
      :show-overflow-tooltip="true"
      class="vis-table-pagination safe-table1"
      highlight-current-row
    >
      <template #photo="{ row }">
        <template v-if="row.measure == '无'">无</template>
        <template v-else>
          <SpicUpload v-model="row.photo" type="image" :limit="5" disabled />
        </template>
      </template>
      <template #executeStatue="{ row }">
        <span v-if="row.measure == '无'">无</span>
        <span v-else-if="row.executeStatue == 1"> 未执行 </span>
        <span v-else-if="row.executeStatue == 2"> 已执行 </span>
        <div v-if="row.measure != '无'">
          {{ row.description || '' }}
        </div>
      </template>
      <template #executeUser="{ row }">
        <span v-if="row.measure == '无'">无</span>
        <template v-else-if="row.executeStatue == 2">
          {{ row.executeUser || '--' }}
        </template>
      </template>
    </vis-table-pagination>

    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="工作票签发人签名">
          <template v-if="workTicketData?.approveStartTime">
            {{
              workTicketData?.issuer
                ? workTicketData?.issuer.split('_')[0]
                : '--'
            }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="签发日期">
          <template v-if="workTicketData?.approveStartTime">
            {{
              workTicketData?.issuerTime
                ? dealTime(workTicketData?.issuerTime)
                : '--'
            }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >补充安全措施（工作许可人填写）
    </div>
    <vis-table-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :columns="safeColumns2"
      :data="tableList2"
      background
      border
      :height="'auto'"
      :show-overflow-tooltip="true"
      class="vis-table-pagination"
      highlight-current-row
    >
    </vis-table-pagination>

    <div class="breif">
      <el-text class="mx-1" type="danger">*</el-text>确认本工作票上述各项内容
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="工作负责人签名">
          <template v-if="workTicketData?.permissionTime">
            {{ workTicketData?.head.split('_')[0] }}
            <!-- {{ dealTime(workTicketData?.approvalHeadTime) }} -->
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作许可人签名">
          <template v-if="workTicketData?.permissionTime">
            {{ workTicketData?.approval.split('_')[0] }}
            <!-- {{ dealTime(workTicketData?.approvalTime) }} -->
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="许可工作时间">
          {{ workTicketData?.permissionTime || '--' }}
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >确认工作负责人布置的工作任务和安全措施（附页：作业安全风险控制卡）
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="工作班组人员签名"> -- </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text>工作票延期
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="有效期延长到">
          <template v-if="workTicketData?.delayapprovalTime">
            {{
              workTicketData?.alterWorkEndTime
                ? dealTime(workTicketData.alterWorkEndTime)
                : '--'
            }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="值长签名">
          <template v-if="workTicketData?.delayapprovalTime">
            {{ workTicketData?.workGrow.split('_')[0] }}
            {{ dealTime(workTicketData?.delayWorkGrowTime) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人签名">
          <template v-if="workTicketData?.delayapprovalTime">
            {{ workTicketData?.head.split('_')[0] }}
            {{ dealTime(workTicketData?.delayHeadTime) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作许可人签名">
          <template v-if="workTicketData?.delayapprovalTime">
            {{ workTicketData?.approval.split('_')[0] }}
            {{ dealTime(workTicketData?.delayapprovalTime) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text>工作负责人变动情况
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="姓名日期及时间">
          {{ workTicketData?.userChanges || '--' }}
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="工作负责人签名">
          {{
            workTicketData?.userChanges
              ? workTicketData?.head.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注">
          {{ workTicketData?.remark || '--' }}
        </el-form-item>
      </el-col>
    </el-row>
    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text
      >工作票终结（全部工作已结束，工作人员已全部撤离，材料工具已清理完毕）
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="12">
        <el-form-item label="工作结束时间">
          <template
            v-if="
              workTicketData?.endApprovalTime && workTicketData?.summaryTime
            "
          >
            {{
              workTicketData?.summaryTime
                ? dealTime(workTicketData.summaryTime)
                : '--'
            }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人签名">
          <template v-if="workTicketData?.endApprovalTime">
            {{ workTicketData?.head.split('_')[0] }}
            {{
              workTicketData?.endHeadTime
                ? dealTime(workTicketData.endHeadTime)
                : ''
            }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作许可人签名">
          <template v-if="workTicketData?.endApprovalTime">
            {{ workTicketData?.approval.split('_')[0] }}
            {{
              workTicketData?.endApprovalTime
                ? dealTime(workTicketData.endApprovalTime)
                : ''
            }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
    </el-row>
    <div class="breif breif-nopa">
      <el-text class="mx-1" type="danger">*</el-text>工作票盖章文件
    </div>
    <el-row :gutter="24" class="mt16">
      <el-col :span="24">
        <el-form-item
          label="文件"
          :class="otherData.stampFile?.length ? 'mb16' : 'mb24'"
        >
          <SpicUpload
            v-if="otherData.stampFile?.length"
            v-model="otherData.stampFile"
            :file-size="50"
            :file-ext="fileExt"
            type="file"
            :limit="20"
            :upload-display="true"
            disabled
          />
          <span v-else> -- </span>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="safe-title mb-6">
      <div class="line"></div>
      关联关系
    </div>
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="关联工单">
          <div
            style="display: flex; color: rgba(41, 204, 160, 1); cursor: pointer"
            v-if="workTicketData?.relevancyWorkOrder"
            @click="toWorkTitle()"
          >
            <img :src="link" class="linkSvg" />
            {{ workTicketData?.relevancyWorkOrder.split('_')[0] || '--' }}
          </div>
          <span v-else>--</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="关联操作票">
          <div v-if="workTicketData?.relevancyOperate">
            <el-popover placement="top" :width="400">
              <template #reference>
                <div
                  style="
                    display: flex;
                    color: rgba(41, 204, 160, 1);
                    cursor: pointer;
                  "
                  @click="
                    toHandlers(workTicketData.relevancyOperate.split(',')[0])
                  "
                >
                  <img :src="link" class="linkSvg" />
                  {{
                    workTicketData.relevancyOperate
                      ? workTicketData.relevancyOperate.split(',')[0] + '...'
                      : '--'
                  }}
                </div>
              </template>
              <div
                style="
                  display: flex;
                  color: rgba(41, 204, 160, 1);
                  cursor: pointer;
                  flex-wrap: wrap;
                "
              >
                <p
                  v-for="(item, index) in workTicketData.relevancyOperate.split(
                    ','
                  )"
                  :key="index"
                  style="display: flex"
                  @click="toHandlers(item)"
                >
                  <img :src="link" class="linkSvg" />
                  {{ item
                  }}<span
                    v-if="
                      index !=
                      workTicketData.relevancyOperate.split(',').length - 1
                    "
                    >，</span
                  >
                </p>
              </div>
            </el-popover>
          </div>
          <div v-else>--</div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import SpicUpload from '@/components/spic-upload'
import visTablePagination from '@/components/table-pagination.vue'
import { dealTime } from '@/utils/index'
import link from '@/assets/svgs/link.svg'
import { ticket as ticketAPI } from '@/api/index.ts'
const route = useRoute()
const router = useRouter()
const fileExt = ref(['jpg', 'png', 'pdf'])

const props = defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  }
})

let otherData: any = ref({}) // 其他事项

const tableList = ref<any>([]) // 安全措施
let tableList2 = ref<any[]>([]) // 补充安全措施

watch(
  () => props.workTicketData,
  (val: any) => {
    val?.securityMeasureList?.forEach((item: any) => {
      item.photo = item.photo ? item.photo.split(',') : []
    })
    tableList.value = val?.securityMeasureList || []
    otherData.value = val.ticketOtherStep || {}
    otherData.value.stampFile = props.workTicketData?.stampFile
      ? props.workTicketData?.stampFile.split(',')
      : []
    if (val.replenishSecurity && val.approveStartTime) {
      tableList2.value = [{ measure: val.replenishSecurity }]
    }
  }
)

const safeColumns = [
  { prop: 'index', label: '序号', minWidth: 100 },
  { prop: 'measure', label: '安全措施(注意事项)', minWidth: 200 },
  {
    prop: 'executeStatue',
    label: '安全措施(注意事项)执行情况',
    minWidth: 210,
    slotName: 'executeStatue'
  },
  {
    prop: 'photo',
    label: '证明照片',
    slotName: 'photo',
    minWidth: 193
  },
  {
    prop: 'executeUser',
    label: '执行人',
    minWidth: 80,
    slotName: 'executeUser'
  }
]
const safeColumns2 = [{ prop: 'measure', label: '填写内容', minWidth: 400 }]
const toWorkTitle = () => {
  router.push(
    route.path +
      `/lookorder/${props.workTicketData?.relevancyWorkOrder.split('_')[1]}`
  )
}

const toHandlers = async (workTicketId_: any) => {
  try {
    let { data } = await ticketAPI.checkGroup({
      orderNo: workTicketId_,
      type: 4 // 1-工单，2-一种票，3-二种票，4-操作票
    })
    if (data) {
      try {
        let { data } = await ticketAPI.getIdByNo({
          operateTicketNo: workTicketId_
        })
        router.push(route.path + `/handleInfo/look/${data.data}`)
      } catch (e: any) {
        console.log(e)
      }
    } else {
      ElMessage({
        message: '权限不足',
        type: 'warning'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: '权限不足',
      type: 'warning'
    })
    console.log(e)
  }
}
</script>
<style scoped src="../../assets/work.scss"></style>
<style lang="scss">
.safe-table1 {
  .el-table_1_column_4 {
    .cell {
      padding-right: 0 !important;
    }
  }
}
</style>
