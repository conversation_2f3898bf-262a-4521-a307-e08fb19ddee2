<template>
  <el-form class="info-base" label-width="106px">
    <el-row :gutter="24">
      <el-col :span="12">
        <el-form-item label="工作票类型"> 电气二种工作票 </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="编号">
          {{ workTicketData?.ticketNo || '--' }}
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="电站名称">
          {{ workTicketData?.stationName || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作票签发人">
          {{
            workTicketData?.issuer ? workTicketData?.issuer.split('_')[0] : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作许可人">
          {{
            workTicketData?.approval
              ? workTicketData?.approval.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="值长">
          {{
            workTicketData?.workGrow
              ? workTicketData?.workGrow.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作负责人">
          {{ workTicketData?.head ? workTicketData?.head.split('_')[0] : '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="部门(单位)">
          {{
            workTicketData?.workDepartment
              ? workTicketData?.workDepartment.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="班组">
          {{
            workTicketData?.teamsGroupsName
              ? workTicketData?.teamsGroupsName.split('_')[0]
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作班成员">
          {{ workTicketData?.teamsGroupUser || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="参加工作人数">
          {{ workTicketData?.teamsUserNum || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设备(系统)名称">
          {{ workTicketData?.equipmentName || '--' }}
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="工作地点或地段">
          {{ workTicketData?.workAddress || '--' }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工作内容">
          {{ workTicketData?.workContent || '--' }}
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="计划工作时间">
          {{
            workTicketData?.workStartTime
              ? `${dealTime(workTicketData?.workStartTime)}-${dealTime(
                  workTicketData?.workEndTime
                )} `
              : '--'
          }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="批准工作时间">
          <template v-if="workTicketData?.approveStartTime">
            {{ dealTime(workTicketData?.approveStartTime) }} -
            {{ dealTime(workTicketData?.approveEndTime) }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="值长签名">
          <template v-if="workTicketData?.approveStartTime">
            {{
              workTicketData?.workGrow
                ? workTicketData?.workGrow.split('_')[0]
                : '--'
            }}
          </template>
          <template v-else> -- </template>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <div class="mb-3" style="color: rgba(0, 0, 0, 0.45)">
          工作条件（停电或不停电，或邻近及保留带电设备名称）
        </div>
        <el-form-item label-width="0">
          {{ workTicketData?.workCondition || '--' }}
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup lang="ts">
import { dealTime } from '@/utils/index'
defineProps({
  workTicketData: {
    type: Object,
    require: true,
    default: null
  }
})
</script>
<style scoped src="../../assets/work.scss"></style>
