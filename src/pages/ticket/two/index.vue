<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>二种工作票列表</p>
        <el-button type="primary" @click="handleAdd">
          <el-icon> <Plus /> </el-icon> 新建工作票
        </el-button>
      </div>
      <card-pagination
        title-key="ticketNo"
        :total="listTotal"
        :loading="tableLoading"
        :finished="finished"
        layout="total, prev, pager, next, jumper"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :data="listData"
        :columns="columns"
        :card-label-width="`70`"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
        @click-card="clickCard"
      >
        <template #head="{ row }">
          {{ row.head ? row.head.split('_')[0] : '--' }}
        </template>
        <template #status="{ row }">
          <el-tag v-if="row.status == 1" class="tag tag1">草稿</el-tag>
          <el-tag v-if="row.status == 2" class="tag tag2">生效中</el-tag>
          <el-tag v-if="row.status == 3" class="tag tag3">已许可</el-tag>
          <el-tag v-if="row.status == 4" class="tag tag4">已终结</el-tag>
          <el-tag v-if="row.status == 5" class="tag tag5">已作废</el-tag>
        </template>

        <template #footer="{ row }">
          <el-button type="primary"> 查看详情 </el-button>
          <div @click.stop>
            <el-dropdown placement="bottom">
              <span class="el-dropdown-link">
                下拉<el-icon><CaretBottom /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="onViewProcess(row)">
                    查看审批流程
                  </el-dropdown-item>
                  <el-dropdown-item @click="onCopyTicket(row)">
                    复制工作票
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </card-pagination>
    </div>
  </div>
  <!-- 审核记录 -->
  <process-dialog
    :is-show="isShow"
    :rows="rows"
    @close-dialog="() => (isShow = false)"
  ></process-dialog>
</template>

<script setup lang="ts">
import CardPagination from '@/components/card-pagination.vue'
import processDialog from './components/processDialog.vue'
import { ticket as ticketAPI } from '@/api/index.ts'
import { opsPersonnel as opsPersonnelAPI } from '@/api/index.ts'
import useCompanyCodeStore from '@/store/companyCode'
const companyCode = useCompanyCodeStore()
const router = useRouter()

let startWatch = false
watch(
  () => companyCode.data,
  () => {
    startWatch && getListData()
  }
)

onMounted(async () => {
  getCompany()
  companyCode.data && (await getListData())
  startWatch = true
})

// 搜索
const searchData = ref({
  stationCode: '',
  ticketNo: '',
  workDepartment: '',
  pageNum: 1,
  pageSize: 6
})
const columns = [
  {
    prop: 'stationName',
    label: '电站名称：'
  },
  {
    prop: 'workDepartment',
    label: '所属公司：'
  },
  {
    prop: 'head',
    label: '负责人：',
    slotName: 'head'
  },
  {
    prop: 'createTime',
    label: '创建时间：'
  }
]
const searchProps = ref([
  {
    prop: 'ticketNo',
    label: '工作票编码',
    placeholder: '请输入工作票编码',
    span: 8,
    width: '84px'
  },
  {
    type: 'select',
    label: '所属公司',
    prop: 'workDepartment',
    span: 7,
    width: '80px',
    options: [],
    filterable: true
  },
  {
    label: '电站名称',
    prop: 'stationCode',
    type: 'stationSelect',
    width: '86px',
    placeholder: '请选择电站',
    span: 8
  }
])
const handleSearch = async (val: any) => {
  searchData.value = {
    ...val,
    pageNum: 1,
    pageSize: 6
  }
  await getListData()
}

const tableLoading = ref(false)
const finished = ref(false)
const listTotal = ref<number>(0)
const listData = ref<Record<string, any>[]>([])
const getListData = async () => {
  try {
    let { data } = await ticketAPI.getTwoSeedTicketList(
      { ...searchData.value, code: localStorage.getItem('PVOM_COMPANY_CODE') },
      true
    )
    listTotal.value = data.data.total
    data.data.records &&
      data.data.records.forEach((item: any) => {
        item.workDepartment = item.workDepartment
          ? item.workDepartment.split('_')[0]
          : '--'
      })
    listData.value = data.data.records
  } catch (e) {
    listData.value = []
    listTotal.value = 0
  } finally {
    finished.value = true
  }
}
const clickCard = async (item: any) => {
  //  - 如果工作票的状态为“草稿”且签发审批未提交或已驳回，则进入新建工作票页面
  //    - 此时新建工作票页面增加按钮“作废工作票”
  //    - 如果存在未完成的签发审批，则点击详情进入查看详情页面，查看详情页所有按钮不可点击
  //  - 如果工作票为其他状态，则进入工作票详情页面
  // if (val.status == 1) {
  // router.push(`/ticket/two/add?id=${item.id}`)
  // 草稿
  if (item.status == 1) {
    try {
      const { data } = await ticketAPI.canUpdate(
        {
          id: item.id,
          type: 5
        },
        false
      )
      if (data.data) {
        router.push(`/ticket/two/add/${item.id}`)
      } else {
        handleLookDetaile(item)
      }
    } catch (e: any) {
      console.log(e)
    }
  } else {
    handleLookDetaile(item)
  }
}

const rows = ref({})
const isShow = ref(false)
const onViewProcess = (item: any) => {
  isShow.value = true
  rows.value = item
}

const onCopyTicket = async (item: any) => {
  router.push({
    path: `/ticket/two/add/${item.id}`,
    query: {
      copy: 1
    }
  })
}

const handleSizeChange = async (params: any) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  await getListData()
}
const handleCurrentChange = async (params: any) => {
  searchData.value.pageNum = params.currentPage
  await getListData()
}
// 获取部门（单位）list
const getCompany = async () => {
  try {
    const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    searchProps.value[1].options = data.data.map((item: any) => {
      return {
        label: item.companyName || '',
        value: item.companyName || ''
      }
    })
  } catch (e: any) {
    searchProps.value[1].options = []
  }
}
// 操作
const handleAdd = () => {
  router.push(`/ticket/two/add`)
}
const handleLookDetaile = async (item: any) => {
  router.push(`/ticket/two/detail/${item.id}`)
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped lang="scss">
.el-dropdown {
  position: absolute;
  right: 20px;
  bottom: 20px;
  line-height: 34px;
  .el-dropdown-link {
    outline: 0;
    cursor: pointer;
  }
}
</style>
