/*
 * @Author: zhaopengpeng006 <EMAIL>
 * @Date: 2024-08-30 16:16:17
 * @LastEditors: zhaopengpeng006 <EMAIL>
 * @LastEditTime: 2024-09-03 11:24:29
 * @FilePath: \pv-om-web\src\pages\ticket\two\hooks\useJsa.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ticket as ticketAPI } from '@/api/index.ts'
export default function () {
  // 工作票的jsa作业类别和作业风险
  const homeworkTyepList = ref<any[]>([])
  const homeworkRiskList = ref<any[]>([])
  const getHomeworkList = async () => {
    //  作业类别
    try {
      const { data } = await ticketAPI.getTwoTicketTyepList({})
      data.data.forEach((item: any) => {
        if (item.name == '其他') {
          item.value = item.value + '@'
        }
      })
      homeworkTyepList.value = data.data
    } catch (e) {
      homeworkTyepList.value = []
    }

    //  作业风险
    try {
      const { data } = await ticketAPI.getTwoTicketRiskList({})
      data.data.forEach((item: any) => {
        if (item.name == '其他伤害') {
          item.value = item.value + '@'
        }
      })
      homeworkRiskList.value = data.data
    } catch (e) {
      homeworkRiskList.value = []
    }
  }
  onMounted(() => {
    getHomeworkList()
  })

  return {
    homeworkTyepList,
    homeworkRiskList
  }
}
