<template>
  <div class="work-add">
    <div class="page-operate">
      <div class="operate-title">新建工作票</div>
    </div>

    <div>
      <baseInfo
        ref="a1Ref"
        :role-list1="roleList1"
        :role-list2="roleList2"
        :role-list3="roleList3"
        :role-list4="roleList4"
        :role-list5="roleList5"
        :work-ticket-data="workTicketData"
        :is-copy="isCopy"
        @get-a1-data="getA1Data"
      >
      </baseInfo>
      <safeMeasures
        ref="a2Ref"
        :work-ticket-data="workTicketData"
        :is-copy="isCopy"
        @get-a2-data="getA2Data"
      >
      </safeMeasures>
      <jsaSupply
        ref="a3Ref"
        :work-ticket-data="workTicketData"
        :is-copy="isCopy"
        @get-a3-data="getA3Data"
      ></jsaSupply>
    </div>

    <div class="page-footer">
      <el-button plain @click="onCancel">返回</el-button>
      <el-button v-preventReClick="1000" type="primary" @click="onSumbit(1)">
        保存草稿
      </el-button>
      <el-button v-preventReClick="1000" type="primary" @click="onSumbit(2)">
        提交签发审批
      </el-button>
      <el-button
        v-if="
          route.params.id && !isCopy && route.path.indexOf('/lookorder') === -1
        "
        v-preventReClick="1000"
        type="primary"
        @click="revokeTicket"
      >
        作废工作票
      </el-button>
    </div>
  </div>
  <!-- 作废工作票 -->
  <revokeTicketDialog
    :is-show="showRevokeTicket"
    type="3"
    @uplate-data="getRevokeTicketData"
    @close-dialog="showRevokeTicket = false"
  ></revokeTicketDialog>
</template>

<script setup lang="ts">
import useRoles from '@/hooks/useRoles'
import baseInfo from './components/add/baseInfo.vue'
import safeMeasures from './components/add/safeMeasures.vue'
import jsaSupply from './components/add/jsaSupply.vue'
import revokeTicketDialog from '../components/revokeTicketDialog.vue'
import { ticket as ticketAPI } from '@/api/index.ts'
import useWorkorderStore from '@/store/workorder'
const useWorkorder = useWorkorderStore()

const route = useRoute()
const router = useRouter()
const isCopy = route.query?.copy ? true : false

const { roleList1, roleList2, roleList3, roleList4, roleList5 } = useRoles()

onMounted(async () => {
  // 编辑草稿
  if (route.path.indexOf('/lookorder') === -1) {
    if (route.params.id) {
      getWorkTicketDetail()
    }
  }
})

let workTicketData = ref<any>({})
// 获取工作票详情
const getWorkTicketDetail = async () => {
  try {
    let { data } = await ticketAPI.getTwoSeedTicketDetailVO(
      { id: route.params.id },
      true
    )
    workTicketData.value = data.data
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

// 第一部分
const a1Data: any = ref({})
const a1Valid = ref(false)
const a1Ref = ref<any>(null)
const getA1Data = (val: any) => {
  a1Valid.value = val.valid
  a1Data.value = val.data
  workTicketData.value.securityMeasureList =
    val.templateContent?.modelTemplateDto?.safeStepDtoList || []
  workTicketData.value.homeworkTyep =
    val.templateContent?.modelTemplateDto?.jsaTicketDto?.homeworkType || ''
  workTicketData.value.homeworkRisk =
    val.templateContent?.modelTemplateDto?.jsaTicketDto?.homeworkRisk || ''
  workTicketData.value.processRiskControlList =
    val.templateContent?.modelTemplateDto?.jsaTicketDto?.processRiskDtoList ||
    []

  a2Ref?.value?.update()
  a3Ref?.value?.update()
}
// 第二部分
const a2Data: any = ref({})
const a2Valid = ref(false)
const a2Ref = ref<any>(null)
const getA2Data = (val: any) => {
  a2Valid.value = val.valid
  a2Data.value = val.data
}
// 第三部分
const a3Data: any = ref({})
const a3Valid = ref(false)
const a3Ref = ref<any>(null)
const getA3Data = (val: any) => {
  a3Valid.value = val.valid
  a3Data.value = val.data
}

// 提交签发审批
// - 提交签发审批时需要校验：工作负责人、工作票签发人、工作许可人皆不相同，否则进行校验提示：三种人不可重复，请重新填写！
// - 如果关联JSA票，则需要校验JSA票的状态为“生效中”“已终结”，如果不是则提示：请先签发JSA票后再尝试！
// 保存草稿1 签发审批2
const onSumbit = async (type: number) => {
  await a1Ref.value.submitForm(type)
  await a2Ref.value.submitForm(type)
  await a3Ref.value.submitForm(type)
  if (type === 2) {
    if (!a1Valid.value) {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return
    }
    if (!a2Valid.value) {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return
    }
    if (!a3Valid.value) {
      ElMessage({
        type: 'warning',
        message: '有必填内容未填写'
      })
      return
    }
  }

  try {
    let draftId = route.params.id || '' // 编辑草稿id
    let ticketNo: any = a1Data.value.ticketNo
    // 新增 或者 复制
    if (!draftId || isCopy || route.path.indexOf('/lookorder') !== -1) {
      draftId = ''
      ticketNo = ''
    }
    const params: any = {
      id: draftId,
      ticketNo,
      ...a1Data.value,
      ...a2Data.value,
      ...a3Data.value
    }

    // 校验 工作负责人 head、工作票签发人 issuer、工作许可人 approval 皆不相同
    if (
      (params.head && params.issuer && params.head == params.issuer) ||
      (params.head && params.approval && params.head == params.approval) ||
      (params.issuer && params.approval && params.issuer == params.approval)
    ) {
      ElMessage({
        message: '工作负责人、工作票签发人、工作许可人不可重复，请重新填写！',
        type: 'warning'
      })
      return
    }
    const { data } = await ticketAPI.addTwoSeedTicket(params, true)
    if (data.code === '200') {
      if (type === 2) {
        const res = await ticketAPI.twoIssuedWorkApprove(
          {
            businessId: data.data
          },
          true
        )
        if (res.data.code === '200') {
          ElMessage({
            message: `操作成功!`,
            type: 'success'
          })
          onCancel()

          // ==工单详情处理==
          if (route.path.indexOf('/lookorder') >= 0) {
            wordrPageData(data.data, 'null')
          }
        } else {
          ElMessage({
            message: res.data.message,
            type: 'error'
          })
        }
      } else if (type === 1) {
        ElMessage({
          message: `操作成功!`,
          type: 'success'
        })
        onCancel()

        // ==工单详情处理==
        if (route.path.indexOf('/lookorder') >= 0) {
          wordrPageData(data.data, '1')
        }
      }
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
// 从工作票详情跳转回去传值
const wordrPageData = async (dataID: any, status: any) => {
  try {
    let { data } = await ticketAPI.getTwoSeedTicketDetailVO(
      { id: dataID },
      true
    )
    let str = '1'
    if (status === 'null') {
      str = data.data.status
    }
    useWorkorder.setTwoSeedTicket({
      status: str,
      ticketNo: data.data.ticketNo || '',
      type: '电气二种工作票',
      relevancyOperate: data.data.relevancyOperate || '',
      issuer: data.data.issuer || '',
      approval: data.data.approval || '',
      head: data.data.head || '',
      workGrow: data.data.workGrow || '',
      workStartTime: data.data.workStartTime || '',
      workEndTime: data.data.workEndTime || '',
      id: data.data.id || ''
    })
  } catch (e: any) {
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}
// 作废工作票
const showRevokeTicket = ref<boolean>(false)
const revokeTicket = () => {
  showRevokeTicket.value = true
}
const getRevokeTicketData = () => {
  router.push('/ticket/two')
}

const onCancel = () => {
  router.go(-1)
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped src="../assets/index.scss"></style>
<style scoped src="./assets/work.scss"></style>
