<template>
  <div class="page-main">
    <div class="page-search">
      <searchForm
        :search-props="searchProps"
        :search-data="searchData"
        label-width="70px"
        @submit-emits="handleSearch"
      ></searchForm>
    </div>
    <div v-auto-height class="main">
      <div class="operate">
        <p>运维报表列表</p>
        <el-button type="primary" @click="handleAdd()"> 生成报表 </el-button>
      </div>
      <vis-table-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchData.pageSize"
        :current-page="searchData.pageNum"
        :columns="tableColumns"
        :total="tableData.total"
        :data="tableData.data"
        :show-overflow-tooltip="true"
        background
        class="vis-table-pagination"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      >
        <template #reportName="{ row }"> {{ row.reportName }} </template>
        <template #createUser="{ row }">
          {{ row.createUser ? row.createUser.split('_')[0] : '--' }}
        </template>
        <template #reportStatus="{ row }">
          <el-tag v-if="row.reportStatus == 1" type="success" color="#F0F5FA">
            已生成
          </el-tag>
          <el-tag v-else-if="row.reportStatus == 2" type="info">
            生成中
          </el-tag>
          <el-tag v-else-if="row.reportStatus == 3" type="danger">
            生成失败
          </el-tag>
        </template>
        <template #operate="{ row }">
          <div class="table-operate">
            <el-button
              v-if="row.reportStatus == 3"
              link
              @click="regenerateItem(row)"
            >
              重新生成
            </el-button>
            <el-button
              v-if="row.reportStatus == 1 || row.reportStatus == 2"
              link
              :disabled="row.reportStatus == 2"
              @click="downloadItem(row)"
            >
              下载
            </el-button>
            <el-button
              link
              :disabled="row.reportStatus == 2"
              @click="deleteItem(row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </vis-table-pagination>
    </div>
  </div>

  <el-dialog
    v-model="dialogVisible"
    title="生成报表"
    align-center
    :before-close="dialogClose"
    :close-on-click-modal="false"
    class="vis-dialog"
    width="446px"
  >
    <div class="tips">
      <el-icon color="rgba(230, 135, 46, 1)" size="16px">
        <WarningFilled />
      </el-icon>
      报表内容包含巡检工单、检修工单、故障单、培训记录、考试记录、工作票、操作票、隐患记录、事故记录。
    </div>
    <el-form
      ref="formRef"
      :model="formData"
      label-suffix=""
      label-width="80px"
      :rules="formRules"
    >
      <el-form-item label="报表维度" prop="reportDimension">
        <el-select
          v-model="formData.reportDimension"
          placeholder="请选择报表维度"
          @change="changeReportDimension"
        >
          <el-option label="资产公司" :value="1"></el-option>
          <el-option label="运维公司" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择公司" prop="selectCompanyInput">
        <el-select
          v-model="formData.selectCompanyInput"
          placeholder="请选择公司"
          filterable
          clearable
        >
          <el-option
            v-for="item in companyList"
            :key="item.companyCode"
            :label="item.companyName"
            :value="`${item.companyName}_${item.companyCode}`"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间维度" prop="timeDimension">
        <el-select
          v-model="formData.timeDimension"
          placeholder="请选择时间维度"
          @change="changeTimeDimension"
        >
          <el-option label="年度" :value="1"></el-option>
          <el-option label="季度" :value="2"></el-option>
          <el-option label="月度" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择时间" prop="selectTime">
        <QuarterPicker
          v-if="formData.timeDimension == 2"
          v-model="formData.selectTime"
          format="YYYY年第q季度"
          value-format="YYYYQ"
          :readonly="true"
          :clearable="false"
          :disabled-date="disabledDateFun"
        />
        <el-date-picker
          v-else
          v-model="formData.selectTime"
          :type="computedPickerType"
          placeholder="请选择时间"
          :clearable="false"
          :value-format="computedPickerType == 'year' ? 'YYYY' : 'YYYY-MM'"
          :editable="false"
          :disabled-date="disabledDateFun"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import visTablePagination from '@/components/table-pagination.vue'
import searchForm from '@/components/search-form.vue'
import { operation as operationAPI } from '@/api/index.ts'
import { baseApi as baseAPI } from '@/api/index.ts'
import QuarterPicker from '@/components/datePickerQuarter.vue'

let searchData = ref({
  reportName: '',
  selectTime: '',
  pageNum: 1,
  pageSize: 10
})
const searchProps = ref([
  { label: '报表名称', prop: 'reportName' },
  {
    label: '选择时间',
    prop: 'selectTime',
    placeholder: '请选择时间',
    type: 'date',
    pickerType: 'year',
    format: 'YYYY',
    disabledDate: (time: Date) => {
      if (time.getTime() > new Date().getTime()) {
        return time.getTime() > new Date().getTime() //时间范围必须是时间戳
      }
      // if (time.getTime() < new Date('2022-01-01').getTime()) {
      //   return time.getTime() < new Date().getTime() //时间范围必须是时间戳
      // }
      return false
    }
  }
])
const tableColumns = [
  {
    prop: 'reportName',
    label: '报表名称',
    minWidth: 220,
    slotName: 'reportName'
  },
  { prop: 'createUser', label: '创建人', minWidth: 76, slotName: 'createUser' },
  { prop: 'createTime', label: '创建时间', minWidth: 140 },
  {
    prop: 'reportStatus',
    label: '报表状态',
    minWidth: 96,
    slotName: 'reportStatus'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    minWidth: 130,
    fixed: 'right'
  }
]
const tableData = reactive<{ data: any[]; total: number }>({
  data: [],
  total: 0
})
const handleSearch = async (val: any) => {
  searchData.value = val
  searchData.value.pageNum = 1
  searchData.value.pageSize = 10
  getTableData()
}

const getTableData = async () => {
  try {
    let { data } = await operationAPI.getOperationReportList(
      searchData.value,
      true
    )
    tableData.data = data?.data?.records || []
    tableData.total = data?.data?.total || 0
  } catch (e: any) {
    tableData.data = []
    tableData.total = 0
    ElMessage({
      type: 'error',
      message: e.message
    })
  }
}

onMounted(async () => {
  getTableData()
})

const handleSizeChange = async (params: { pageSize: number }) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = params.pageSize
  getTableData()
}
const handleCurrentChange = async (params: { currentPage: number }) => {
  searchData.value.pageNum = params.currentPage
  getTableData()
}

// 新增编辑弹框
let dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const formRules = reactive<FormRules<Record<string, any>>>({
  reportDimension: [
    { required: true, message: '请选择报表维度', trigger: 'change' }
  ],
  selectCompanyInput: [
    { required: true, message: '请选择公司', trigger: 'change' }
  ],
  timeDimension: [
    { required: true, message: '请选择时间维度', trigger: 'change' }
  ],
  selectTime: [{ required: true, message: '请选择时间', trigger: 'change' }]
})

let formData = ref<Record<string, any>>({
  reportDimension: 1,
  selectCompanyInput: '',
  timeDimension: 1,
  selectTime: ''
})

const handleAdd = async () => {
  formRef.value?.resetFields()
  dialogVisible.value = true
  getCompanyList(1)
}

// 生成报表
const handleSave = () => {
  formRef.value?.validate(async (valid: any) => {
    if (valid) {
      let selectTime = formData.value.selectTime
      // 时间维度是季度，单独处理一下选择时间
      if (formData.value.timeDimension == 2) {
        let year = formData.value.selectTime.slice(0, 4) // 年份
        let quarter = formData.value.selectTime.slice(4) // 季度，值为1 2 3 4
        if (quarter == 1) {
          selectTime = `${year}-01至${year}-03`
        } else if (quarter == 2) {
          selectTime = `${year}-04至${year}-06`
        } else if (quarter == 3) {
          selectTime = `${year}-07至${year}-09`
        } else if (quarter == 4) {
          selectTime = `${year}-10至${year}-12`
        }
      }
      let params = {
        reportDimension: formData.value.reportDimension,
        timeDimension: formData.value.timeDimension,
        selectTime,
        selectCompanyName: formData.value.selectCompanyInput.split('_')[0],
        selectCompany: formData.value.selectCompanyInput.split('_')[1]
      }
      addReportFn(params)
    }
  })
}
// 重新生成报表
const regenerateItem = async (val: any) => {
  let params = {
    id: val.id,
    reportDimension: val.reportDimension,
    timeDimension: val.timeDimension,
    selectTime: val.selectTime,
    selectCompanyName: val.selectCompanyName,
    selectCompany: val.selectCompany
  }
  addReportFn(params)
}
const addReportFn = async (params: any) => {
  try {
    const { data } = await operationAPI.addOperationReport(
      params,

      true
    )
    if (data.code === '200') {
      ElMessage({
        message: `${params.id ? '重新' : ''}生成报表成功!`,
        type: 'success'
      })
      dialogClose()
      getTableData()
    } else {
      ElMessage({
        message: data.message,
        type: 'error'
      })
    }
  } catch (e: any) {
    ElMessage({
      message: e.message,
      type: 'error'
    })
  }
}
const computedPickerType = computed(() => {
  if (formData.value.timeDimension == 1) {
    return 'year'
  } else if (formData.value.timeDimension == 2) {
    return 'date'
  } else if (formData.value.timeDimension == 3) {
    return 'month'
  }
  return 'year'
})
// 修改报表维度
const changeReportDimension = (val: any) => {
  if (val) {
    companyList.value = []
    formRef.value?.resetFields('selectCompanyInput')
    getCompanyList(val)
  }
}
const companyList = ref<Record<string, any>[]>([])
// 获取资产公司
const getCompanyList = async (type: any) => {
  try {
    const { data } = await operationAPI.getCompanyList({ type }, false)
    if (data.code === '200') {
      companyList.value = data.data.map((item: any) => {
        return {
          companyName: item.name || '',
          companyCode: item.code || ''
        }
      })
    } else {
      companyList.value = []
    }
  } catch (e: any) {
    companyList.value = []
  }
}

// 修改时间维度
const changeTimeDimension = (val: any) => {
  if (val) {
    formRef.value?.resetFields('selectTime')
  }
}

// 下载
const downloadItem = async (val: any) => {
  try {
    const {
      response: { data }
    } = await baseAPI.downloadFile(val.fileUrl)
    const link = document.createElement('a')

    link.href = window.URL.createObjectURL(data)
    link.download = val.fileUrl.split('@')[1]
    link.click()
    data &&
      ElMessage({
        message: `下载成功!`,
        type: 'success'
      })
  } catch (e: any) {}
}
// 删除
const deleteItem = async (val: any) => {
  ElMessageBox.confirm('确定删除?', '删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const { data } = await operationAPI.operateDelete({ id: val.id }, true)
        if (data.code === '200') {
          ElMessage({
            message: `删除成功!`,
            type: 'success'
          })
          getTableData()
        } else {
          ElMessage({
            message: data.message,
            type: 'error'
          })
        }
      } catch (e: any) {
        ElMessage({
          message: e.message,
          type: 'error'
        })
      }
    })
    .catch(() => {})
}

// 时间选择器 禁用范围
const disabledDateFun = (time: Date) => {
  if (time.getTime() > new Date().getTime()) {
    return time.getTime() > new Date().getTime() //时间范围必须是时间戳
  }
  // if (time.getTime() < new Date('2022-01-01').getTime()) {
  //   return time.getTime() < new Date().getTime() //时间范围必须是时间戳
  // }
  return false
}

const dialogClose = () => {
  formRef.value?.resetFields()
  dialogVisible.value = false
}
</script>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.tips {
  background: rgba(255, 244, 235, 1);
  border: 1px solid rgba(230, 135, 46, 1);
  border-radius: 8px;
  padding: 8px 8px 8px 16px;
  font-size: 12px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  margin-bottom: 24px;
  .el-icon {
    margin-right: 8px;
    margin-top: 3px;
    flex-shrink: 0;
  }
}
.dialog-footer {
  .el-button {
    width: 72px;
    height: 40px;
  }
}
.table-operate {
  .el-button.is-link.is-disabled {
    color: var(--el-button-disabled-text-color) !important;
  }
}
</style>
