<template>
  <div class="page-title">{{ $route.query?.id ? '新建预警' : '预警详情' }}</div>
  <div class="info-base">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :inline="true"
      label-width="110px"
    >
      <el-row :gutter="24" class="border-bottom mb-24px">
        <el-col :span="10">
          <el-form-item label="预警类型" prop="warnType">
            <el-select
              v-model="formData.warnType"
              placeholder="请选择预警类型"
              clearable
            >
              <el-option
                v-for="item in filter.types"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="预警名称" prop="warnName">
            <el-select
              v-model="formData.warnName"
              placeholder="请选择预警名称"
              clearable
              @change="changeName"
            >
              <el-option
                v-for="item in filter.names"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="10">
          <el-form-item label="预警规则配置" prop="warnRuleDesc">
            <el-select
              v-model="formData.warnRuleDesc"
              placeholder="请选择预警规则配置"
            >
              <el-option
                v-for="item in filterRules"
                :key="item.value"
                :label="item.label"
                :value="item.label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24" class="background mb-24px">
        <el-col :span="10">
          <el-form-item
            :label="formData.warnName === 5 ? '还剩' : '超过'"
            required
          >
            <el-form-item prop="warnRuleTime" class="width-1">
              <el-input
                v-model.number="formData.warnRuleTime"
                maxlength="5"
                clearable
                @input="
                  (val: any) =>
                    formatNumber(val, 'warnRuleTime', 'warnRuleTimeType')
                "
              ></el-input>
            </el-form-item>
            <el-select
              v-model="formData.warnRuleTimeType"
              placeholder="请选择"
              class="width-1 ml-10px"
              @change="() => restField('warnRuleTime')"
            >
              <el-option
                v-for="item in filter.ruleScopes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="且" prop="warnRuleConditions">
            <el-select
              v-model="formData.warnRuleConditions"
              placeholder="请选择"
            >
              <el-option
                v-for="item in ruleStatus"
                :key="item.value"
                :label="item.label"
                :value="item.label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24" class="border-top pt-24px">
        <el-col :span="10">
          <el-form-item label="预警发送频率" prop="warnSendRule">
            <el-select
              v-model="formData.warnSendRule"
              placeholder="请选择预警发送频率"
            >
              <el-option
                v-for="item in filter.frequencies"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 暂时不需要自定义
          <el-col v-if="formData.warnSendRule === 4" :span="10">
            <el-form-item label="自定义" required>
              <span class="sign-1">每</span>
              <el-form-item prop="fieldx12" class="width-2">
                <el-input
                  v-model.number="formData.fieldx12"
                  maxlength="5"
                  clearable
                  @input="
                    (val: any) => formatNumber(val, 'fieldx12', 'fieldx14')
                  "
                ></el-input>
              </el-form-item>
              <el-select
                v-model="formData.fieldx14"
                placeholder="请选择"
                class="width-2 ml-10px"
                @change="() => restField('fieldx12')"
              >
                <el-option
                  v-for="item in filter.ruleScopes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span class="sign-1">推送</span>
              <el-form-item prop="fieldx13" class="width-2">
                <el-input
                  v-model.number="formData.fieldx13"
                  maxlength="5"
                  clearable
                  @input="(val: any) => formatNumber(val, 'fieldx13')"
                ></el-input>
              </el-form-item>
              <span class="sign-1">次</span>
            </el-form-item>
          </el-col> -->
        <el-col :span="10" :offset="2">
          <el-form-item label="通知群体" prop="notificationGroup">
            <el-select
              v-model="formData.notificationGroup"
              placeholder="请选择通知群体"
              :disabled="disabledGroups"
              clearable
            >
              <el-option
                v-for="item in groups"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="通知方式" prop="notificationMode">
            <el-select
              v-model="formData.notificationMode"
              placeholder="请选择通知方式"
              clearable
            >
              <el-option
                v-for="item in filter.ways"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10" :offset="2">
          <el-form-item label="生效范围" prop="operationCompanyCode">
            <el-select
              v-model="formData.operationCompanyCode"
              placeholder="请选择生效范围"
              clearable
              :disabled="!!id"
              @change="
                (val: any) =>
                  (formData.operationCompanyName = companyList.find(
                    (item: any) => item.value === val
                  )?.label)
              "
            >
              <el-option
                v-for="item in companyList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="预警描述" prop="warnDesc">
            <el-input
              v-model.trim="formData.warnDesc"
              maxlength="1024"
              placeholder="请输入预警描述"
              show-word-limit
              :rows="5"
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <div class="page-footer">
    <el-button plain @click="() => router.back()">返回</el-button>
    <el-button type="primary" @click="onSumbit">提交</el-button>
  </div>
</template>
<script setup lang="ts">
import useFilter from './hooks/useFilter'
// import { opsPersonnel as opsPersonnelAPI } from '@/api'
import * as api from '@/api/index.ts'
const filter = useFilter()
const router = useRouter()
const route = useRoute()
const id = route.params.id

const formRef = ref()
const onSumbit = async () => {
  if (!formRef.value) return
  let url = '/operate/foreWarningController/addForeWarning'
  if (id) {
    url = 'operate/foreWarningController/updateForeWarningById'
  }
  await formRef.value.validate(async (valid: any) => {
    if (valid) {
      await api.post({
        url,
        data: formData.value,
        loading: true
      })
      router.back()
    }
  })
}

const formData = ref<any>({
  notificationGroup: '',
  notificationMode: '',
  operationCompanyCode: '',
  operationCompanyName: '',
  warnName: '',
  warnRuleDesc: '自工单创建后',
  warnRuleConditions: '未接单',
  warnRuleTime: '',
  warnRuleTimeType: '',
  warnSendRule: '',
  warnState: '',
  warnType: '',
  warnDesc: ''
})

const rules = reactive<any>({
  warnType: [{ required: true, message: '请选择预警类型', trigger: 'change' }],
  warnName: [{ required: true, message: '请选择预警名称', trigger: 'change' }],
  warnRuleDesc: [
    { required: true, message: '请选择预警规则配置', trigger: 'change' }
  ],
  warnRuleTime: [
    { required: true, message: '请配置预警规则', trigger: 'blur' }
  ],
  warnRuleConditions: [
    { required: true, message: '请配置预警规则', trigger: 'change' }
  ],
  warnSendRule: [
    { required: true, message: '请选择预警发送频率', trigger: 'change' }
  ],
  notificationGroup: [
    { required: true, message: '请选择通知群体', trigger: 'change' }
  ],
  notificationMode: [
    { required: true, message: '请选择通知方式', trigger: 'change' }
  ],
  operationCompanyCode: [
    { required: true, message: '请选择生效范围', trigger: 'change' }
  ],
  warnDesc: [{ required: true, message: '请输入预警描述', trigger: 'blur' }],
  fieldx12: [
    { required: true, message: '请配置预警发送频率', trigger: 'blur' }
  ],
  fieldx13: [{ required: true, message: '请配置预警发送频率', trigger: 'blur' }]
})

const companyList = ref<any>([])
onMounted(async () => {
  getCompany()
  if (id) {
    const { data } = await api.get({
      url: '/operate/foreWarningController/getForeWarningById',
      data: { id },
      loading: true
    })
    formData.value = data || {}
    groups.value = filter.groups.filter((e: any) => {
      if (!formData.value.warnName) return true
      if ([3, 4].includes(formData.value.warnName)) {
        disabledGroups.value = true
        return e.value == 1
      } else if ([5, 6].includes(formData.value.warnName)) {
        disabledGroups.value = false
        return e.value == 2 || e.value == 3
      } else {
        disabledGroups.value = false
        return e.value == 2 || e.value == 3
      }
    })
  }
})
const getCompany = async () => {
  try {
    // const { data } = await opsPersonnelAPI.getOperatorsList({}, false)
    const { data } = await api.get(
      '/operate/operationUser/getOperatorsListByUser'
    )
    companyList.value =
      data?.map((item: any) => {
        return {
          label: item.companyName || '',
          value: item.companyCode || ''
        }
      }) || []
    if (companyList.value.length === 1) {
      formData.value.operationCompanyCode = companyList.value[0].value
    }
  } catch (e: any) {
    companyList.value = []
  }
}

const oldNum = ref<Obj>({})
const formatNumber = (value: any, field: string, astrict: string = '') => {
  formData.value[field] = value.replace(/[^0-9]/g, '').replace(/^0+(\d)/, '$1')
  if (astrict) {
    if (formData.value[astrict] === 1) {
      if (formData.value[field] > 60) {
        formData.value[field] = oldNum.value[field]
      }
    }
    if (formData.value[astrict] === 2) {
      if (formData.value[field] > 24) {
        formData.value[field] = oldNum.value[field]
      }
    }
    oldNum.value[field] = formData.value[field]
  }
}
const restField = (field: string) => {
  formData.value[field] = ''
}

const disabledGroups = ref(false)
const filterRules = ref<Obj[]>(filter.rules)
const ruleStatus = ref<Obj[]>(filter.ruleStatus)
const groups = ref<Obj[]>(filter.groups)
const changeName = () => {
  if ([3, 4].includes(formData.value.warnName)) {
    formData.value.notificationGroup = 1
    ruleStatus.value[0].label = '未指派'
    filterRules.value[0].label = '自工单创建后'
    formData.value.warnRuleConditions = '未指派'
    formData.value.warnRuleDesc = '自工单创建后'
    groups.value = filter.groups.filter((e: any) => e.value == 1)
    formData.value.notificationGroup = 1
    disabledGroups.value = true
  } else if ([5, 6].includes(formData.value.warnName)) {
    formData.value.notificationGroup = 3
    ruleStatus.value[0].label = '已接单，未取消，未提交验证'
    filterRules.value[0].label = '距考核时间'
    formData.value.warnRuleConditions = '已接单，未取消，未提交验证'
    formData.value.warnRuleDesc = '距考核时间'
    groups.value = filter.groups.filter(
      (e: any) => e.value == 2 || e.value == 3
    )
    formData.value.notificationGroup = 3
    disabledGroups.value = false
  } else {
    formData.value.notificationGroup = 3
    ruleStatus.value[0].label = '未接单'
    filterRules.value[0].label = '自工单派发后'
    formData.value.warnRuleConditions = '未接单'
    formData.value.warnRuleDesc = '自工单派发后'
    groups.value = filter.groups.filter(
      (e: any) => e.value == 2 || e.value == 3
    )
    formData.value.notificationGroup = 2
    disabledGroups.value = false
  }

  if (formData.value.warnName == 5) {
    formData.value.warnRuleTime = 24
    formData.value.warnRuleTimeType = 2
  } else {
    formData.value.warnRuleTime = ''
    formData.value.warnRuleTimeType = 2
  }
}
</script>
<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style lang="scss" scoped>
.info-base {
  box-sizing: border-box;
}
.border-bottom {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.border-top {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.background {
  background: #f6f8fa;
  padding: 20px 0;
  box-sizing: border-box;
  .el-form-item {
    margin-bottom: 0 !important;
  }
}
.width-1 {
  width: 138px !important;
}
.width-2 {
  width: 110px !important;
}
.sign-1 {
  margin: 0 8px;
}
</style>
