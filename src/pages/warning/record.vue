<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import * as api from '@/api/index.ts'
import useFilter from './hooks/useFilter'
import useTableData from '@/hooks/useTableData'
// import useCompanyCodeStore from '@/store/companyCode'

// const companyCode = useCompanyCodeStore()
const router = useRouter()
const filter = useFilter()
const searchProps = ref<Obj[]>([
  {
    prop: 'warnType',
    label: '预警类型',
    width: '80px',
    type: 'select',
    options: filter.types
  },
  {
    prop: 'warnName',
    label: '预警名称',
    width: '80px',
    type: 'select',
    options: filter.names
  },
  {
    prop: 'dataScope',
    label: '选择时间',
    width: '80px',
    type: 'date',
    pickerType: 'daterange'
  },
  {
    prop: 'workNo',
    label: '工单编号',
    width: '80px'
  }
])
let searchData = reactive<Obj>({
  warnType: null,
  warnName: null,
  dataScope: null,
  workNo: '',
  pageSize: 10,
  pageNum: 1
})
const columns = [
  {
    prop: 'warnRecordNo',
    label: '预警ID'
  },
  {
    prop: 'warnType',
    label: '预警类型',
    formatter: (val: any) => {
      return (
        filter.types.find((item: any) => item.value == val.warnType)?.label ||
        '--'
      )
    }
  },
  {
    prop: 'warnName',
    label: '预警名称',
    formatter: (val: any) => {
      return (
        filter.names.find((item: any) => item.value == val.warnName)?.label ||
        '--'
      )
    }
  },
  {
    prop: 'workNo',
    label: '关联内容'
  },
  {
    prop: 'foreWarningTime',
    label: '最近预警时间'
  },
  {
    prop: 'foreWarningNum',
    label: '预警次数'
  },
  {
    prop: 'operate',
    slotName: 'operate',
    label: '操作',
    width: 60,
    fixed: 'right'
  }
]
const getTableData = async (data: Obj, loading: Loading) => {
  if (Array.isArray(data.dataScope)) {
    data.startTime = data.dataScope?.[0] + ' 00:00:00'
    data.endTime = data.dataScope?.[1] + ' 23:59:59'
  }
  delete data.dataScope
  const requestData = {
    ...data,
    code: 'root'
  }
  return await api.post(
    '/operate/foreWarningRecordsController/getForeWarningRecordList',
    requestData,
    loading
  )
}
const {
  tableData,
  tablePage,
  tableLoading,
  changeCurrent,
  changeSize,
  changeData
} = useTableData(getTableData, searchData, { immediate: false })

// watch(
//   () => companyCode.data,
//   () => {
//     companyCode.data && changeData()
//   },
//   { immediate: true }
// )
onMounted(() => {
  changeData()
})

const handleSearch = async (val: any) => {
  Object.keys(searchData).forEach((key) => {
    searchData[key] = val[key]
  })
  changeData(true)
}

const handleDelete = async (row: any) => {
  await api.post({
    url:
      '/operate/foreWarningRecordsController/deleteForeWarningRecordById?id=' +
      row.id,
    loading: true
  })
  ElMessage({
    message: '删除成功！',
    type: 'success'
  })
  changeData('delete')
}

const handleAdd = () => {
  router.push(`/warning-record/add`)
}
</script>

<template>
  <div class="info-base" style="padding-bottom: 0">
    <searchForm
      :search-props="searchProps"
      :search-data="searchData"
      label-width="60px"
      @submit-emits="handleSearch"
    ></searchForm>
  </div>
  <div v-auto-height class="info-base pb-24px">
    <div class="operate">
      <p>预警记录</p>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        <span>新建预警</span>
      </el-button>
    </div>
    <TablePagination
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 50, 100]"
      :show-overflow-tooltip="true"
      background
      :columns="columns"
      :data="tableData.data"
      :total="tableData.total"
      :loading="tableLoading"
      :current-page="tablePage.pageNum"
      :page-size="tablePage.pageSize"
      class="table-pagination"
      @handle-size-change="changeSize"
      @handle-current-change="changeCurrent"
    >
      <template #operate="{ row }">
        <el-popconfirm title="确认删除？" @confirm="handleDelete(row)">
          <template #reference>
            <el-button type="primary" link>删除</el-button>
          </template>
        </el-popconfirm>
      </template>
    </TablePagination>
  </div>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.pb-24px {
  padding-bottom: 24px;
}
</style>
