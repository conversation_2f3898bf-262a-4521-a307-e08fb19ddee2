<script setup lang="ts">
import searchForm from '@/components/search-form.vue'
import * as api from '@/api/index.ts'
// import useCompanyCodeStore from '@/store/companyCode'
import useFilter from './hooks/useFilter'

const filter = useFilter()
// const companyCode = useCompanyCodeStore()
const searchProps = reactive<any>([
  {
    prop: 'warnType',
    label: '预警类型',
    width: '80px',
    type: 'select',
    options: filter.types
  }
])
let searchData = ref<Partial<Record<string, any>>>({
  warnType: '',
  pageSize: 8,
  pageNum: 1
})

const handleSearch = async (val: any) => {
  searchData.value = { ...val }
  searchData.value.pageNum = 1
  searchData.value.pageSize = 8
  getTableData()
}

const router = useRouter()
let tableData = reactive<Record<string, any>>({
  data: [],
  total: 0
})
const showAddElement = ref(false)
const getTableData = async () => {
  // if (!companyCode.data) return
  // showAddElement.value = false
  const { data } = await api.post({
    url: '/operate/foreWarningController/getForeWarningList',
    data: {
      ...searchData.value,
      code: 'root'
    },
    loading: true
  })
  showAddElement.value = true
  tableData.data = data?.records || []
  tableData.total = data?.total || 0
}
const handleSizeChange = (num: number) => {
  searchData.value.pageNum = 1
  searchData.value.pageSize = num
  getTableData()
}
const handleCurrentChange = (num: number) => {
  searchData.value.pageNum = num
  getTableData()
}

onMounted(() => {
  getTableData()
})
// watch(
//   () => companyCode.data,
//   () => {
//     getTableData()
//   },
//   { immediate: true }
// )

const changeStatus = async (item: Obj) => {
  if (item.warnState === 1) {
    return true
  }
  try {
    await ElMessageBox.confirm(
      '关闭后将停止预警消息推送，是否关闭？',
      '确认提示',
      {
        type: 'warning',
        center: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )
    return true
  } catch (e) {
    return false
  }
}
const updateStatus = async (val: any, item: Obj) => {
  try {
    await api.post({
      url: '/operate/foreWarningController/updateForeWarnStateById',
      data: {
        id: item.id,
        warnState: val
      },
      loading: true
    })
    getTableData()
  } catch (e) {
    item.warnState = val === 1 ? 0 : 1
  }
}
const handleAdd = () => {
  router.push(`/warning/add`)
}
const handleEdit = (item: any) => {
  router.push(`/warning/edit/${item.id}`)
}
const handleDelete = async (item: any) => {
  if (item.warnState === 0) {
    ElMessageBox.alert('请先关闭当前预警，再进行删除操作', '提示', {
      confirmButtonText: '确认'
    })
    return
  }
  try {
    await ElMessageBox.confirm('是否确认删除本条预警？', '确认提示', {
      type: 'warning',
      center: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })
    await api.post({
      url: '/operate/foreWarningController/deleteForeWarningById?id=' + item.id,
      loading: true
    })
    ElMessage({
      message: '删除成功！',
      type: 'success'
    })
    const totalPage = Math.ceil(tableData.total / searchData.value.pageSize)
    searchData.value.pageNum =
      searchData.value.pageNum > totalPage
        ? totalPage
        : searchData.value.pageNum
    getTableData()
  } catch (e: any) {}
}
</script>

<template>
  <div class="info-base" style="padding-bottom: 0">
    <searchForm
      :search-props="searchProps"
      :search-data="searchData"
      label-width="60px"
      @submit-emits="handleSearch"
    ></searchForm>
  </div>
  <div v-auto-height class="card-main">
    <el-scrollbar style="height: calc(100% - 48px)">
      <div class="card-list">
        <div class="item create" @click="handleAdd">
          <img src="@/assets/svgs/warning-add.svg" />
          <div class="text">新建预警</div>
        </div>
        <div v-for="item in tableData.data" :key="item.id" class="item">
          <div class="heaeder">
            <div class="title">
              {{
                filter.names.find((e: any) => e.value === item.warnName)
                  ?.label || '--'
              }}
              <span class="type">
                {{
                  filter.types.find((e: any) => e.value === item.warnType)
                    ?.label || '--'
                }}
              </span>
            </div>
            <el-switch
              v-model="item.warnState"
              inline-prompt
              :active-value="0"
              :inactive-value="1"
              active-text="启用"
              inactive-text="停用"
              style="
                --el-switch-on-color: #29cca0;
                --el-switch-off-color: #dcdfe6;
              "
              :before-change="() => changeStatus(item)"
              @change="(val: any) => updateStatus(val, item)"
            />
          </div>
          <div class="content">
            <div class="content-item">
              <span class="label">发送方式</span>
              <span class="value">
                {{
                  filter.ways.find(
                    (e: any) => e.value === item.notificationMode
                  )?.label || '--'
                }}
              </span>
            </div>
            <div class="content-item">
              <span class="label">修改时间</span>
              <span class="value">{{ item.updateTime || '--' }}</span>
            </div>
            <div class="content-item">
              <span class="label">修改人</span>
              <span class="value">
                {{ item.updateUser?.split('_')[0] || '--' }}
              </span>
            </div>
          </div>
          <div class="opration">
            <div class="button" @click="() => handleEdit(item)">编辑</div>
            <div class="button" @click="() => handleDelete(item)">删除</div>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <el-pagination
      v-if="tableData.total"
      ref="paginationRef"
      :current-page="searchData.pageNum"
      :page-size="searchData.pageSize"
      :total="tableData.total"
      layout="total, sizes, prev, pager, next"
      :page-sizes="[8, 20, 50, 100]"
      :background="true"
      class="m-t-16px justify-end"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped src="@/assets/styles/layoutDefault.scss"></style>
<style scoped lang="scss">
.card-main {
  margin: 0 24px 24px 24px;
}
.card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  .item {
    width: calc((100% - 48px) / 3);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.1);
    border-radius: 8px;
    padding: 24px;
    border-bottom: 2px solid #fff;
    cursor: pointer;
    position: relative;
    &.create {
      border: 1px dashed rgba(41, 204, 160, 1);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 16px;
      image {
        display: block;
        width: 32px;
        height: 32px;
      }
      .text {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
      }
    }
    &:hover {
      padding-bottom: 23px;
      border-bottom: 2px solid rgba(41, 204, 160, 1);
    }
    .heaeder {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 26px;
      line-height: 26px;
      margin-bottom: 16px;
      .title {
        font-size: 18px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        .type {
          display: inline-block;
          width: 80px;
          height: 26px;
          line-height: 26px;
          padding: 0 12px;
          margin-left: 10px;
          border-radius: 4px;
          font-size: 14px;
          color: rgba(41, 204, 160, 1);
          background: rgba(41, 204, 160, 0.1);
        }
      }
    }
    .content {
      .content-item {
        height: 26px;
        line-height: 26px;
        margin-bottom: 12px;
        font-size: 14px;
        &:last-child {
          margin-bottom: 0;
        }
        .label {
          display: inline-block;
          min-width: 70px;
          color: rgba(0, 0, 0, 0.45);
        }
        .value {
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
    .opration {
      position: absolute;
      bottom: 24px;
      right: 24px;
      display: flex;
      gap: 8px;
      .button {
        height: 24px;
        line-height: 24px;
        padding: 0 8px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        &:hover {
          background: #eee;
        }
      }
    }
  }
}
</style>
