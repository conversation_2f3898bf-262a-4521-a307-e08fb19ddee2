export default function () {
  return {
    types: [{ label: '工单预警', value: 1 }],
    names: [
      { label: '接单临期预警', value: 1 },
      { label: '接单超期预警', value: 2 },
      { label: '派单临期预警', value: 3 },
      { label: '派单超期预警', value: 4 },
      { label: '运维工单临期预警', value: 5 },
      { label: '运维工单超期预警', value: 6 }
    ],
    rules: [{ label: '自工单创建后', value: 1 }],
    ruleScopes: [
      { label: '分钟', value: 1 },
      { label: '小时', value: 2 },
      { label: '天', value: 3 }
    ],
    ruleStatus: [{ label: '未接单', value: 1 }],
    ways: [
      { label: '站内信', value: 1 },
      { label: '短信', value: 2 },
      { label: '站内信+短信', value: 3 }
    ],
    groups: [
      { label: '区域监盘人', value: 1 },
      { label: '工程师', value: 2 },
      { label: '区域监盘人+工程师', value: 3 }
    ],
    frequencies: [
      { label: '仅推送一次', value: 1 },
      { label: '每小时推送一次', value: 2 },
      { label: '每天推送一次', value: 3 }
      // { label: '自定义', value: 4 }
    ]
  }
}
