import { defineStore } from 'pinia'

export default defineStore('selectedElectric', () => {
  const twoSeedTicket = ref({
    id: '',
    status: '',
    ticketNo: '',
    type: '',
    relevancyOperate: '',
    issuer: '',
    approval: '',
    head: '',
    workGrow: '',
    workStartTime: '',
    workEndTime: ''
  })
  const ticketWorkVo = ref<any>({
    createTime: '',
    department: '',
    endTime: '',
    id: '',
    licensor: '',
    operateTickets: '',
    shiftChiefOperator: '',
    signer: '',
    startTime: '',
    stationName: '',
    status: '',
    ticketWorkNo: '',
    workLeader: ''
  })
  const setTwoSeedTicket = (data: any) => {
    twoSeedTicket.value = data
  }
  const setTicketWorkVo = (data: any) => {
    twoSeedTicket.value = data
  }
  return {
    twoSeedTicket,
    ticketWorkVo,
    setTwoSeedTicket,
    setTicketWorkVo
  }
})
