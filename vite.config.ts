import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import * as path from 'path'
import {
  createStyleImportPlugin,
  ElementPlusResolve
} from 'vite-plugin-style-import'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Unocss from './config/unocss'
import { loadEnv } from 'vite'
import { vitePluginFakeServer } from 'vite-plugin-fake-server'

const pathSrc = path.resolve(__dirname, 'src')

export default ({ mode }) => {
  return defineConfig({
    base: loadEnv(mode, process.cwd()).VITE_APP_BASE_PATH,
    resolve: {
      //设置别名
      alias: {
        '@': pathSrc
      }
    },
    plugins: [
      vue(),
      Unocss(),
      createStyleImportPlugin({
        resolves: [ElementPlusResolve()],
        libs: [
          {
            libraryName: 'element-plus',
            esModule: true,
            resolveStyle: (name) => {
              return `element-plus/theme-chalk/${name}.css`
            },
            ensureStyleFile: true
          }
        ]
      }),
      AutoImport({
        include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.md$/],
        imports: [
          'vue',
          'vue-router',
          {
            '@vueuse/core': ['useMouse', ['useFetch', 'useMyFetch']],
            axios: [['default', 'axios']],
            'await-to-js': ['to']
          }
        ],
        eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.json'
        },
        resolvers: [ElementPlusResolver()],
        dts: path.resolve('types', 'auto-imports.d.ts')
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        dts: path.resolve('types', 'components.d.ts')
      }),
      vitePluginFakeServer({
        enableDev: false
      })
    ],
    server: {
      host: true,
      // host: 'localhost.spic-iset.com', // 测试环境
      // host: 'localhost.spic.com.cn', // 生产环境
      // host: '0.0.0.0', // 生产环境
      port: 8081,
      open: true,
      proxy: {
        '/dev-api': {
          // target: 'http://*************:8065', // 张孟羽电脑
          // target: 'http://*************:8080', // 安鹏电脑
          // target: 'http://406iq77844ju.vicp.fun', // 安鹏电脑
          target: 'http://**************:8080', // 服务器
          // target: 'https://tsyhtest.spic-iset.com/pvom', // 测试环境
          // target: 'https://tsyh.spic.com.cn/pvom', // 生产环境
          changeOrigin: true,
          secure: false,
          headers: {
            // referer: 'https://tsyhtest.spic-iset.com'
            // referer: 'https://tsyh.spic.com.cn',
            // basic:
            //   'e3447b2f4e90c2d4e0c488696f9dd27684d82afe86dc38145ca24fd863a9149997d6b2295bdaf778e89cb0d39b9f58150d139636cc8fd26b276bbe58505b80f6807bda2db41e89b3c6c236a4dc74b0f2',
            // ciphertext:
            //   '049f575ef93171c05f5cc3511833f8cc4df7e21988bd6c4b26686fce6b1d9c38f56544366ed2c1f13286ae87c21f214eadad113d1f13cec273a292b190ffb64e0b5497f80beed99c410d943de8c5146210c79aa4958c3b6498e5f603970444bedb5fe1d9004a0266a7942ee735835fe7ac8441a82799f598f22eddfd3332e3ebd1'
          },
          rewrite: (path: string) => path.replace(/^\/dev-api/, '')
        }
      },
      hmr: { overlay: false }
    },
    define: {
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false'
    },
    build: { chunkSizeWarningLimit: 3000 }
  })
}
